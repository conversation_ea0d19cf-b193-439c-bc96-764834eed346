import { resolve } from 'path'
import { version } from '../package.json'
import type { UserConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import svgLoader from 'vite-svg-loader'
import imagemin from 'vite-plugin-imagemin'
import { createHtmlPlugin } from 'vite-plugin-html'
import { vitePluginForArco } from '@arco-plugins/vite-vue'
import autoprefixer from 'autoprefixer'
import cssnano from 'cssnano'

// 使用版本号而不是时间戳，确保相同版本的资源路径一致
const assetsDir = `assets-v${version.replace(/\./g, '-')}`

const baseConfig: UserConfig = {
  base: '/',
  plugins: [
    vue(),
    vueJsx(),
    svgLoader(),
    vitePluginForArco({
      style: 'css'
    }),
    // 图片压缩 - 优化压缩设置
    imagemin({
      gifsicle: {
        optimizationLevel: 7,
        interlaced: false
      },
      optipng: {
        optimizationLevel: 7
      },
      mozjpeg: {
        quality: 85, // 提高质量以减少重复压缩
        progressive: true
      },
      pngquant: {
        quality: [0.8, 0.95], // 提高质量范围
        speed: 4
      },
      svgo: {
        plugins: [
          {
            name: 'removeViewBox',
            active: false
          },
          {
            name: 'removeEmptyAttrs',
            active: false
          },
          {
            name: 'cleanupIds',
            active: true
          },
          {
            name: 'removeUnusedNS',
            active: true
          }
        ]
      },
      webp: {
        quality: 85
      }
    }),
    // HTML 插件配置
    createHtmlPlugin({
      inject: {
        tags: [
          // 字体预连接已在 index.html 中配置，避免重复
          // 这里可以添加其他需要动态注入的标签
        ]
      },
      minify: true
    })
  ],
  build: {
    assetsDir,
    rollupOptions: {
      output: {
        // @ts-ignore - Vite 类型定义问题
        manualChunks(id: string): string | undefined {
          if (id.includes('node_modules')) {
            // 大型UI库单独分包
            if (id.includes('@arco-design')) {
              return 'arco'
            }
            // 支付相关库单独分包
            if (id.includes('stripe')) {
              return 'stripe'
            }
            // Vue生态系统核心库
            if (id.includes('vue') || id.includes('pinia') || id.includes('@vueuse')) {
              return 'vue-vendor'
            }
            // 图表和可视化库
            if (id.includes('@antv') || id.includes('motion') || id.includes('animejs')) {
              return 'visualization'
            }
            // 工具库合并
            if (
              id.includes('lodash') ||
              id.includes('dayjs') ||
              id.includes('uuid') ||
              id.includes('nanoid')
            ) {
              return 'utils'
            }
            // 音频视频相关
            if (id.includes('howler') || id.includes('swiper')) {
              return 'media'
            }
            // 其他第三方库合并到vendor
            return 'vendor'
          }

          // 合并小页面，减少分包数量
          if (id.includes('src/mobile/views/')) {
            const matchResult = id.match(/src\/mobile\/views\/([^/]+)/)
            if (matchResult) {
              const pageName = matchResult[1]
              // 将小页面合并到一个chunk中
              if (
                [
                  'terms',
                  'privacy',
                  'accessibility',
                  'region-restricted',
                  'recharge-success',
                  'payment'
                ].includes(pageName)
              ) {
                return 'page-static'
              }
              // 主要页面保持独立
              if (['chat', 'chat2', 'chat3', 'stories', 'editor', 'user'].includes(pageName)) {
                return `page-${pageName}`
              }
              // 其他页面合并
              return 'page-others'
            }
          }

          // PC端页面也进行类似处理
          if (id.includes('src/pc/views/')) {
            const matchResult = id.match(/src\/pc\/views\/([^/]+)/)
            if (matchResult) {
              const pageName = matchResult[1]
              if (['chat', 'stories', 'user'].includes(pageName)) {
                return `pc-${pageName}`
              }
              return 'pc-others'
            }
          }

          return undefined
        },
        entryFileNames: `${assetsDir}/js/[name].[hash].js`,
        chunkFileNames: `${assetsDir}/js/[name].[hash].js`,
        assetFileNames: (assetInfo: any) => {
          const fileName = assetInfo.name || assetInfo.names?.[0] || 'unknown'
          if (!fileName) return `${assetsDir}/assets/[name].[hash].[ext]`
          const info = fileName.split('.')
          let extType = info[info.length - 1]
          if (/\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/i.test(fileName)) {
            extType = 'media'
          } else if (/\.(png|jpe?g|gif|svg|ico|webp)(\?.*)?$/i.test(fileName)) {
            extType = 'img'
          } else if (/\.(woff2?|eot|ttf|otf)(\?.*)?$/i.test(fileName)) {
            extType = 'fonts'
          }
          return `${assetsDir}/${extType}/[name].[hash].[ext]`
        }
      }
    },
    assetsInlineLimit: 12288, // 增加内联限制以减少HTTP请求 (12KB)
    cssCodeSplit: false, // 禁用CSS代码分割，合并所有CSS到一个文件
    sourcemap: false,
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: false,
        drop_debugger: false,
        // pure_funcs: ['console.log', 'console.info', 'console.debug'],
        passes: 2 // 多次压缩以获得更好的结果
      },
      mangle: {
        safari10: true
      },
      format: {
        comments: false
      }
    },
    chunkSizeWarningLimit: 1000, // 增加警告限制
    reportCompressedSize: false // 禁用压缩大小报告以加快构建
  },
  resolve: {
    alias: [
      {
        find: '@',
        replacement: resolve(__dirname, '../src')
      },
      {
        find: 'assets',
        replacement: resolve(__dirname, '../src/assets')
      },
      {
        find: 'vue-i18n',
        replacement: 'vue-i18n/dist/vue-i18n.cjs.js'
      },
      {
        find: 'vue',
        replacement: 'vue/dist/vue.esm-bundler.js'
      }
    ],
    extensions: ['.ts', '.js']
  },
  define: {
    'process.env': { version },
    // 定义开发环境标志，用于 Tree Shaking
    __DEV__: JSON.stringify(process.env.NODE_ENV === 'development'),
    __TEST__: JSON.stringify(process.env.NODE_ENV === 'test')
  },
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true
      }
    },
    // CSS压缩配置
    postcss: {
      plugins: [
        autoprefixer,
        cssnano({
          preset: [
            'default',
            {
              // 保留重要的注释
              discardComments: {
                removeAll: false
              },
              // 压缩CSS
              normalizeWhitespace: true,
              // 合并相同的规则
              mergeRules: true,
              // 压缩颜色值
              colormin: true,
              // 移除未使用的CSS
              reduceIdents: false, // 保持类名不变，避免影响动态类名
              // 压缩字体权重
              minifyFontValues: true,
              // 压缩选择器
              minifySelectors: true
            }
          ]
        })
      ]
    }
  }
}

export default baseConfig
