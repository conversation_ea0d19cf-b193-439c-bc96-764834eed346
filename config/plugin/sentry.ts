import { sentryVitePlugin } from '@sentry/vite-plugin'
import type { Plugin } from 'vite'

/**
 * 创建 Sentry Vite 插件
 * @param options Sentry 配置选项
 */
export function createSentryPlugin(options: {
  org?: string
  project?: string
  authToken?: string
  release?: string
  environment?: string
  enabled?: boolean
}): Plugin | null {
  // 如果没有启用或缺少必要配置，返回 null
  if (!options.enabled || !options.authToken || !options.org || !options.project) {
    console.warn('Sentry plugin 未启用或配置不完整，跳过 Source Maps 上传')
    return null
  }

  try {
    return sentryVitePlugin({
      org: options.org,
      project: options.project,
      authToken: options.authToken,

      // 发布配置
      release: {
        name: options.release || '1.0.0',
        setCommits: {
          auto: true,
          ignoreMissing: true,
          ignoreEmpty: true
        }
      },

      // Source Maps 配置
      sourcemaps: {
        assets: ['./dist/**'],
        ignore: ['node_modules/**', 'dist/**/*.map'],
        filesToDeleteAfterUpload: ['./dist/**/*.map']
      },

      // 调试选项
      debug: false,
      silent: false
    })
  } catch (error) {
    console.warn('Sentry plugin 初始化失败:', error)
    return null
  }
}

/**
 * 获取 Sentry 环境配置
 */
export function getSentryConfig(env: Record<string, string>) {
  // 尝试从 package.json 读取版本号
  let packageVersion = '1.0.0'
  try {
    const packageJson = require('../../package.json')
    packageVersion = packageJson.version
  } catch (error) {
    console.warn('无法读取 package.json 版本号，使用默认值')
  }

  return {
    org: env.VITE_SENTRY_ORG || process.env.SENTRY_ORG,
    project: env.VITE_SENTRY_PROJECT || process.env.SENTRY_PROJECT,
    authToken: env.VITE_SENTRY_AUTH_TOKEN || process.env.SENTRY_AUTH_TOKEN,
    release: packageVersion,
    environment: env.VITE_SENTRY_ENVIRONMENT || 'production',
    enabled: Boolean(
      env.VITE_SENTRY_UPLOAD_SOURCEMAPS === 'true' ||
        process.env.SENTRY_UPLOAD_SOURCEMAPS === 'true'
    )
  }
}
