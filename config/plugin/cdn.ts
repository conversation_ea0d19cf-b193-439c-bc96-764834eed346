/**
 * CDN外部化插件
 * 将大型第三方库通过CDN加载，减少bundle大小
 */
import type { Plugin } from 'vite'

export interface CDNConfig {
  name: string
  var: string
  path: string
  css?: string
}

const cdnConfigs: CDNConfig[] = [
  // Vue生态系统 - 使用unpkg CDN
  {
    name: 'vue',
    var: 'Vue',
    path: 'https://unpkg.com/vue@3.5.4/dist/vue.global.prod.js'
  },
  {
    name: 'vue-router',
    var: 'VueRouter',
    path: 'https://unpkg.com/vue-router@4.5.0/dist/vue-router.global.prod.js'
  },
  {
    name: 'pinia',
    var: 'Pinia',
    path: 'https://unpkg.com/pinia@2.1.6/dist/pinia.iife.prod.js'
  },
  // UI库 - 使用jsDelivr CDN (更快的国内访问)
  {
    name: '@arco-design/web-vue',
    var: 'ArcoVue',
    path: 'https://cdn.jsdelivr.net/npm/@arco-design/web-vue@2.44.7/dist/arco-vue.min.js',
    css: 'https://cdn.jsdelivr.net/npm/@arco-design/web-vue@2.44.7/dist/arco.min.css'
  },
  // 工具库
  {
    name: 'lodash-es',
    var: '_',
    path: 'https://cdn.jsdelivr.net/npm/lodash@4.17.21/lodash.min.js'
  },
  {
    name: 'dayjs',
    var: 'dayjs',
    path: 'https://cdn.jsdelivr.net/npm/dayjs@1.11.5/dayjs.min.js'
  },
  // 动画库
  {
    name: 'animejs',
    var: 'anime',
    path: 'https://cdn.jsdelivr.net/npm/animejs@4.0.1/lib/anime.min.js'
  },
  {
    name: 'canvas-confetti',
    var: 'confetti',
    path: 'https://cdn.jsdelivr.net/npm/canvas-confetti@1.9.3/dist/confetti.browser.min.js'
  }
]

export function createCdnPlugin(): Plugin {
  return {
    name: 'vite-plugin-cdn-externals',
    config(config) {
      // 配置外部依赖
      config.build = config.build || {}
      config.build.rollupOptions = config.build.rollupOptions || {}
      config.build.rollupOptions.external = config.build.rollupOptions.external || []

      const external = Array.isArray(config.build.rollupOptions.external)
        ? config.build.rollupOptions.external
        : []

      // 添加CDN库到外部依赖
      cdnConfigs.forEach((cdn) => {
        if (!external.includes(cdn.name)) {
          external.push(cdn.name)
        }
      })

      config.build.rollupOptions.external = external

      // 配置全局变量映射
      config.build.rollupOptions.output = config.build.rollupOptions.output || {}
      const output = config.build.rollupOptions.output
      output.globals = output.globals || {}

      cdnConfigs.forEach((cdn) => {
        output.globals[cdn.name] = cdn.var
      })
    },
    transformIndexHtml(html) {
      // 注入CDN链接
      const cdnLinks = cdnConfigs
        .map((cdn) => {
          const jsLink = `<script src="${cdn.path}" crossorigin="anonymous"></script>`
          const cssLink = cdn.css
            ? `<link rel="stylesheet" href="${cdn.css}" crossorigin="anonymous">`
            : ''
          return cssLink + jsLink
        })
        .join('\n')

      // 在head标签末尾插入CDN链接
      return html.replace('</head>', `${cdnLinks}\n</head>`)
    }
  }
}

/**
 * 创建轻量级CDN插件 - 只外部化最大的库
 */
export function createLightCdnPlugin(): Plugin {
  const lightCdnConfigs: CDNConfig[] = [
    // 只外部化最大的库以获得最佳性能收益
    {
      name: '@arco-design/web-vue',
      var: 'ArcoVue',
      path: 'https://cdn.jsdelivr.net/npm/@arco-design/web-vue@2.44.7/dist/arco-vue.min.js',
      css: 'https://cdn.jsdelivr.net/npm/@arco-design/web-vue@2.44.7/dist/arco.min.css'
    },
    {
      name: 'lodash-es',
      var: '_',
      path: 'https://cdn.jsdelivr.net/npm/lodash@4.17.21/lodash.min.js'
    },
    {
      name: 'canvas-confetti',
      var: 'confetti',
      path: 'https://cdn.jsdelivr.net/npm/canvas-confetti@1.9.3/dist/confetti.browser.min.js'
    }
  ]

  return {
    name: 'vite-plugin-light-cdn-externals',
    config(config) {
      config.build = config.build || {}
      config.build.rollupOptions = config.build.rollupOptions || {}
      config.build.rollupOptions.external = config.build.rollupOptions.external || []

      const external = Array.isArray(config.build.rollupOptions.external)
        ? config.build.rollupOptions.external
        : []

      lightCdnConfigs.forEach((cdn) => {
        if (!external.includes(cdn.name)) {
          external.push(cdn.name)
        }
      })

      config.build.rollupOptions.external = external

      config.build.rollupOptions.output = config.build.rollupOptions.output || {}
      const output = config.build.rollupOptions.output
      output.globals = output.globals || {}

      lightCdnConfigs.forEach((cdn) => {
        output.globals[cdn.name] = cdn.var
      })
    },
    transformIndexHtml(html) {
      const cdnLinks = lightCdnConfigs
        .map((cdn) => {
          const jsLink = `<script src="${cdn.path}" crossorigin="anonymous"></script>`
          const cssLink = cdn.css
            ? `<link rel="stylesheet" href="${cdn.css}" crossorigin="anonymous">`
            : ''
          return cssLink + jsLink
        })
        .join('\n')

      return html.replace('</head>', `${cdnLinks}\n</head>`)
    }
  }
}
