/**
 * 高级压缩插件配置
 * 支持 Gzip 和 Brotli 压缩，优化传输性能
 */
import type { Plugin } from 'vite'
import compressPlugin from 'vite-plugin-compression'

export interface CompressOptions {
  // 压缩类型
  type: 'gzip' | 'brotli' | 'both'
  // 是否删除原文件
  deleteOriginFile?: boolean
  // 压缩阈值（字节）
  threshold?: number
  // 包含的文件类型
  include?: RegExp | string[]
  // 排除的文件类型
  exclude?: RegExp | string[]
}

// 检查文件是否匹配模式
function matchesPattern(fileName: string, pattern: RegExp | string[] | undefined): boolean {
  if (!pattern) return true

  if (pattern instanceof RegExp) {
    return pattern.test(fileName)
  }

  if (Array.isArray(pattern)) {
    return pattern.some((ext) => fileName.endsWith(ext))
  }

  return true
}

export default function configCompressPlugin(options: CompressOptions): Plugin | Plugin[] {
  const {
    type = 'both',
    deleteOriginFile = false,
    threshold = 1024,
    include = /\.(js|css|html|svg|json|txt|xml)$/,
    exclude
  } = options

  const plugins: Plugin[] = []

  // Gzip 压缩配置
  if (type === 'gzip' || type === 'both') {
    plugins.push(
      compressPlugin({
        ext: '.gz',
        algorithm: 'gzip',
        deleteOriginFile,
        threshold,
        compressionOptions: {
          level: 9, // 最高压缩级别
          memLevel: 8
        },
        filter: (fileName) => {
          if (exclude && matchesPattern(fileName, exclude)) {
            return false
          }
          return matchesPattern(fileName, include)
        }
      })
    )
  }

  // Brotli 压缩配置
  if (type === 'brotli' || type === 'both') {
    plugins.push(
      compressPlugin({
        ext: '.br',
        algorithm: 'brotliCompress',
        deleteOriginFile,
        threshold,
        compressionOptions: {
          level: 11 // 最高压缩级别
        },
        filter: (fileName) => {
          if (exclude && matchesPattern(fileName, exclude)) {
            return false
          }
          return matchesPattern(fileName, include)
        }
      })
    )
  }

  return plugins
}

/**
 * 创建生产环境压缩配置
 */
export function createProductionCompressPlugin(): Plugin[] {
  return configCompressPlugin({
    type: 'both',
    deleteOriginFile: false,
    threshold: 1024,
    include: /\.(js|css|html|svg|json|txt|xml|woff2?)$/,
    exclude: /\.(png|jpg|jpeg|gif|webp|ico)$/
  }) as Plugin[]
}
