/**
 * 高级性能优化插件配置
 * 包含 Critical CSS、Smart Preload、Tree Shaking 等优化
 */
import type { Plugin } from 'vite'
import { createCSSOptimizationPlugin, createCSSSplitPlugin } from './css-optimization'

/**
 * Critical CSS 提取插件
 */
export function createCriticalCSSPlugin(): Plugin {
  return {
    name: 'vite-plugin-critical-css',
    generateBundle(options, _bundle) {
      // 在生产环境提取关键CSS
      if (options.format === 'es') {
        // 提取首屏关键CSS
        const criticalCSS = `
          /* Critical CSS for above-the-fold content */
          body { margin: 0; padding: 0; font-family: var(--font-family); }
          #app { min-height: 100vh; }
          .loading-spinner { 
            width: 40px; height: 40px; 
            border: 3px solid #f3f3f3; 
            border-top: 3px solid #007bff; 
            border-radius: 50%; 
            animation: spin 1s linear infinite; 
          }
          @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        `

        this.emitFile({
          type: 'asset',
          fileName: 'critical.css',
          source: criticalCSS.trim()
        })
      }
    }
  }
}

/**
 * Smart Preload 插件
 */
export function createSmartPreloadPlugin(): Plugin {
  return {
    name: 'vite-plugin-smart-preload',
    generateBundle(_options, _bundle) {
      // 生成智能预加载脚本
      const preloadScript = `
        (function() {
          // 智能预加载关键资源 (字体已通过 Google Fonts 优化)
          const criticalResources = [
            '/assets/css/critical.css'
          ];
          
          // 预加载关键资源
          criticalResources.forEach(function(href) {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.href = href;
            link.as = href.endsWith('.css') ? 'style' : 'font';
            if (link.as === 'font') {
              link.type = 'font/woff2';
              link.crossOrigin = 'anonymous';
            }
            document.head.appendChild(link);
          });
          
          // 预连接到外部域名
          const preconnectDomains = [
            'https://cdn.jsdelivr.net',
            'https://cdn.magiclight.ai',
            'https://static.playshot.ai'
          ];
          
          preconnectDomains.forEach(function(domain) {
            const link = document.createElement('link');
            link.rel = 'preconnect';
            link.href = domain;
            document.head.appendChild(link);
          });
        })();
      `

      this.emitFile({
        type: 'asset',
        fileName: 'preload.js',
        source: preloadScript.trim()
      })
    }
  }
}

/**
 * Tree Shaking 增强插件
 */
export function createTreeShakingPlugin(): Plugin {
  return {
    name: 'vite-plugin-enhanced-tree-shaking',
    config(config) {
      // 增强 Tree Shaking 配置
      config.build = config.build || {}
      config.build.rollupOptions = config.build.rollupOptions || {}
      config.build.rollupOptions.treeshake = {
        // 保留模块副作用，避免移除重要的初始化代码（如axios配置）
        moduleSideEffects: (id: string) => {
          // 保留这些模块的副作用
          if (id.includes('inject') || id.includes('axios') || id.includes('main.ts')) {
            return true
          }
          // 其他模块可以安全地进行tree shaking
          return false
        }
      }

      // 配置外部依赖
      config.build.rollupOptions.external = (_id: string) => {
        // 将大型库标记为外部依赖（如果通过CDN加载）
        return false // 暂时不外部化任何依赖
      }
    }
  }
}

/**
 * Bundle 分析插件
 */
export function createBundleAnalyzerPlugin(): Plugin {
  return {
    name: 'vite-plugin-bundle-analyzer',
    generateBundle(_options, bundle) {
      const analysis = {
        chunks: {} as Record<string, any>,
        assets: {} as Record<string, any>,
        totalSize: 0,
        timestamp: new Date().toISOString()
      }

      // 分析 chunks
      Object.entries(bundle).forEach(([fileName, chunk]) => {
        if (chunk.type === 'chunk') {
          analysis.chunks[fileName] = {
            size: chunk.code.length,
            modules: Object.keys(chunk.modules || {}),
            imports: chunk.imports,
            exports: chunk.exports,
            isDynamicEntry: chunk.isDynamicEntry,
            isEntry: chunk.isEntry
          }
          analysis.totalSize += chunk.code.length
        } else {
          analysis.assets[fileName] = {
            size: chunk.source.toString().length,
            type: chunk.type
          }
          analysis.totalSize += chunk.source.toString().length
        }
      })

      // 生成分析报告
      this.emitFile({
        type: 'asset',
        fileName: 'bundle-analysis.json',
        source: JSON.stringify(analysis, null, 2)
      })

      // 控制台输出摘要
      console.log('\n📊 Bundle Analysis Summary:')
      console.log(`Total Size: ${(analysis.totalSize / 1024).toFixed(2)} KB`)
      console.log(`Chunks: ${Object.keys(analysis.chunks).length}`)
      console.log(`Assets: ${Object.keys(analysis.assets).length}`)

      // 显示最大的chunks
      const sortedChunks = Object.entries(analysis.chunks)
        .sort(([, a], [, b]) => b.size - a.size)
        .slice(0, 5)

      console.log('\n🔍 Largest Chunks:')
      sortedChunks.forEach(([name, chunk]) => {
        console.log(`  ${name}: ${(chunk.size / 1024).toFixed(2)} KB`)
      })
    }
  }
}

/**
 * 资源优化插件
 */
export function createResourceOptimizationPlugin(): Plugin {
  return {
    name: 'vite-plugin-resource-optimization',
    generateBundle(_options, _bundle) {
      // 生成资源优化提示
      const hints = {
        performance: {
          suggestions: [
            'Consider using WebP format for images',
            'Enable Brotli compression on server',
            'Implement Service Worker for caching',
            'Use HTTP/2 Server Push for critical resources'
          ]
        },
        seo: {
          suggestions: [
            'Add structured data markup',
            'Optimize meta descriptions',
            'Implement Open Graph tags',
            'Add canonical URLs'
          ]
        }
      }

      this.emitFile({
        type: 'asset',
        fileName: 'optimization-hints.json',
        source: JSON.stringify(hints, null, 2)
      })
    }
  }
}

/**
 * 创建所有性能优化插件
 */
export function createPerformancePlugins(): Plugin[] {
  return [
    createCriticalCSSPlugin(),
    createSmartPreloadPlugin(),
    createTreeShakingPlugin(),
    createCSSOptimizationPlugin({
      criticalCSS: {
        enabled: true,
        inlineThreshold: 14336,
        extractPath: 'critical.css'
      },
      purgeCSS: {
        enabled: true,
        safelist: ['/^v-/', '/^vue-/', '/^arco-/', '/^theme-/', '/^animate-/'],
        blocklist: []
      },
      minification: {
        enabled: true,
        removeComments: true,
        removeUnusedRules: true,
        mergeRules: true
      }
    }),
    createCSSSplitPlugin()
    // createBundleAnalyzerPlugin(),
    // createResourceOptimizationPlugin()
  ]
}

/**
 * 创建开发环境性能插件
 */
export function createDevPerformancePlugins(): Plugin[] {
  // return [createTreeShakingPlugin(), createBundleAnalyzerPlugin()]
  return [createTreeShakingPlugin()]
}

/**
 * 创建生产环境性能插件
 */
export function createProdPerformancePlugins(): Plugin[] {
  return createPerformancePlugins()
}
