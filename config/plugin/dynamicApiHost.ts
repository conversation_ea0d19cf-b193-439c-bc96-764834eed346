/**
 * 动态API地址构造插件
 * 根据当前域名动态构造API地址，支持多域名部署
 */

export interface DynamicApiHostOptions {
  /** 环境变量中的API地址作为回退 */
  fallbackApiHost: string
  /** 自定义域名映射规则 */
  domainMappings?: Record<string, string>
  /** 是否为开发环境 */
  isDevelopment?: boolean
}

/**
 * 创建动态API地址插件
 */
export function createDynamicApiHostPlugin(options: DynamicApiHostOptions) {
  const { fallbackApiHost, domainMappings = {}, isDevelopment = false } = options

  return {
    name: 'dynamic-api-host',
    config(config: any, { command }: { command: string }) {
      if (command === 'build') {
        // 构建时使用环境变量中的API地址
        return
      }
    },
    configureServer(server: any) {
      // 开发时可以保持原有逻辑
    },
    transformIndexHtml(html: string) {
      // 构建默认域名映射规则
      const defaultMappings = {
        // 开发环境特殊处理
        'dev.playshot.ai': 'https://api-test.zhijianyuzhou.com',
        // 生产环境映射
        'playshot.ai': 'https://api.playshot.ai',
        'reelplay.ai': 'https://api.reelplay.ai',
        'playshort.ai': 'https://api.playshort.ai',
        ...domainMappings
      }

      // 生成域名判断逻辑 - 精确匹配优先
      const domainChecks = Object.entries(defaultMappings)
        .sort(([a], [b]) => b.length - a.length) // 按域名长度降序排列，确保精确匹配优先
        .map(([domain, apiHost]) => {
          return `if (currentHost === '${domain}' || currentHost.endsWith('.${domain}')) {
              apiHost = '${apiHost}';
            }`
        })
        .join(' else ')

      // 注入动态API地址构造脚本
      const script = `
        <script>
          (function() {
            // 根据当前域名动态构造API地址
            const currentHost = window.location.hostname;
            let apiHost;

            // 添加调试信息
            console.log('[DynamicApiHost] Current hostname:', currentHost);
            console.log('[DynamicApiHost] isDevelopment:', ${isDevelopment});

            // 开发环境优先使用环境变量配置的API地址
            ${
              isDevelopment
                ? `
            // 开发环境：直接使用环境变量中的API地址
            apiHost = '${fallbackApiHost}';
            console.log('[DynamicApiHost] Development mode, using fallback:', apiHost);
            `
                : `
            // 生产环境：根据域名动态构造API地址
            ${domainChecks} else {
              // 默认使用环境变量中的API地址
              apiHost = '${fallbackApiHost}';
            }
            console.log('[DynamicApiHost] Production mode, resolved API host:', apiHost);
            `
            }

            // 将动态构造的API地址设置为全局变量
            window.__DYNAMIC_API_HOST__ = apiHost;
            console.log('[DynamicApiHost] Set window.__DYNAMIC_API_HOST__ to:', apiHost);

            // 也可以通过meta标签的方式提供给应用使用
            const metaTag = document.createElement('meta');
            metaTag.name = 'api-host';
            metaTag.content = apiHost;
            document.head.appendChild(metaTag);
            console.log('[DynamicApiHost] Added meta tag with content:', apiHost);
          })();
        </script>
      `

      return html.replace('<head>', '<head>' + script)
    }
  }
}

/**
 * 默认的动态API地址插件配置
 */
export function createDefaultDynamicApiHostPlugin(fallbackApiHost: string) {
  return createDynamicApiHostPlugin({
    fallbackApiHost,
    domainMappings: {
      // 可以在这里添加更多的域名映射
    }
  })
}
