import { mergeConfig, loadEnv } from 'vite'
import eslint from 'vite-plugin-eslint'
import { createHtmlPlugin } from 'vite-plugin-html'
import baseConfig from './vite.config.base'
import configVisualizerPlugin from './plugin/visualizer'
import configArcoResolverPlugin from './plugin/arcoResolver'
import createManifestPlugin from './plugin/manifest'
import { createProductionCompressPlugin } from './plugin/compress'
import { optimizeFontLoading } from './plugins/optimizeFontLoading'
import { createDevPerformancePlugins } from './plugin/performance'
// import { createDynamicApiHostPlugin } from './plugin/dynamicApiHost'
import { createPWAPlugin } from './plugin/pwa'
import { resolve } from 'path'
// import basicSsl from '@vitejs/plugin-basic-ssl' // 需要HTTPS时启用

// Load environment variables
const env = loadEnv('development', resolve(process.cwd()))

export default mergeConfig(
  {
    mode: 'development',
    define: {
      // 开发环境变量定义
      __DEV__: true,
      __TEST__: false
    },
    server: {
      open: true,
      fs: {
        strict: true
      },
      // 开发服务器优化
      hmr: {
        overlay: true
      }
    },
    plugins: [
      // 开发环境专用插件
      eslint({
        cache: false,
        include: ['src/**/*.ts', 'src/**/*.tsx', 'src/**/*.vue'],
        exclude: ['node_modules']
      }),
      optimizeFontLoading(),
      configVisualizerPlugin(),
      configArcoResolverPlugin(),
      createManifestPlugin(),
      // PWA插件
      createPWAPlugin(env),
      // 移除动态API地址插件，直接使用环境变量
      // createDynamicApiHostPlugin({
      //   fallbackApiHost: env.VITE_API_HOST,
      //   isDevelopment: true
      // }),
      // HTML 插件配置
      createHtmlPlugin({
        inject: {
          data: {
            title: 'PlayShot.AI - NSFW Character AI CrushOn Chat - Spicy AI',
            description:
              'PlayShot AI is an adult 18+ virtual roleplay platform where you interact with uncensored, lifelike AI characters. Inspired by leading names like CrushOn AI, Spicy AI, and crush No Filter AI, PlayShot AI takes immersive storytelling to the next level with bold Character AI NSFW experiences and customizable sexting chat pron scenarios. Explore your fantasies with intelligent AI designed for deep, unfiltered connections.',
            iconUrl: 'https://cdn.magiclight.ai/assets/playshot/playshot-icon.png'
          }
        }
      }),

      ...createDevPerformancePlugins(),
      // 压缩插件（开发环境启用以测试压缩效果）
      ...createProductionCompressPlugin()

      // 可选插件
      // basicSsl(), // 开发环境需要HTTPS时启用
    ],

    // 开发环境构建优化
    build: {
      // 开发环境也启用代码分割以测试效果
      rollupOptions: {
        output: {
          manualChunks: {
            // 核心库分包
            vue: ['vue', 'vue-router', 'pinia', '@vueuse/core', 'vue-i18n'],
            arco: ['@arco-design/web-vue'],
            // 工具库分包
            utils: ['lodash-es', 'dayjs', 'nanoid'],
            // 第三方服务分包
            services: ['axios', '@stripe/stripe-js', 'howler']
          }
        }
      },
      // 开发环境启用 sourcemap
      sourcemap: true,
      // 较小的 chunk 警告限制
      chunkSizeWarningLimit: 1500
    },

    // 开发环境优化
    optimizeDeps: {
      include: [
        'vue',
        'vue-router',
        'pinia',
        '@vueuse/core',
        'vue-i18n',
        '@arco-design/web-vue',
        'lodash-es',
        'dayjs'
      ]
    }
  },
  baseConfig
)
