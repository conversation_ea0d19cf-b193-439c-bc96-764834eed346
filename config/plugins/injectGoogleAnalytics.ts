import type { Plugin } from 'vite'

export function injectGoogleAnalytics(): Plugin {
  const gtagCode = `
    <!-- Google tag (gtag.js) -->
    <script>
      // 优化 Google Analytics 加载策略，减少对LCP的影响
      (function() {
        var loaded = false;
        var loadGoogleAnalytics = function() {
          if (loaded) return;
          loaded = true;

          var loadScript = function() {
            // 动态加载 gtag.js
            var script = document.createElement('script');
            script.async = true;
            script.src = 'https://www.googletagmanager.com/gtag/js?id=G-BCD38QPPKH';
            document.head.appendChild(script);

            // 初始化 gtag
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-BCD38QPPKH');
          };

          if (window.requestIdleCallback) {
            requestIdleCallback(loadScript, { timeout: 6000 });
          } else {
            setTimeout(loadScript, 3000);
          }
        };

        // 监听用户交互，延迟加载广告脚本
        var events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
        var onUserInteraction = function() {
          setTimeout(loadGoogleAnalytics, 1500); // 交互后1.5秒再加载
          events.forEach(function(event) {
            document.removeEventListener(event, onUserInteraction, true);
          });
        };

        events.forEach(function(event) {
          document.addEventListener(event, onUserInteraction, true);
        });

        // 备用方案：6秒后自动加载
        setTimeout(loadGoogleAnalytics, 6000);
      })();
    </script>
    <!-- End Google tag -->
  `.trim()

  return {
    name: 'vite-plugin-inject-google-analytics',
    transformIndexHtml(html) {
      return html.replace('</head>', `${gtagCode}\n</head>`)
    }
  }
}
