import type { Plugin } from 'vite'

/**
 * 简化字体加载优化插件 - 优雅、高性能、无重复
 */
export function optimizeFontLoading(): Plugin {
  const fontOptimizationCode = `
    <script>
      // 统一字体加载管理器 - 确保字体稳定应用
      (function() {
        'use strict';

        // 字体加载状态管理
        var fontManager = {
          isLoaded: false,
          loadTimeout: null,

          // 初始化字体加载状态
          init: function() {
            document.documentElement.classList.add('fonts-loading');
            this.setupFontLoadingMonitor();
            this.setupFallbackTimer();
          },

          // 监控字体加载状态
          setupFontLoadingMonitor: function() {
            if ('fonts' in document) {
              document.fonts.ready.then(function() {
                fontManager.markFontsLoaded();
              }).catch(function() {
                console.warn('⚠️ 字体加载失败，使用系统字体');
                fontManager.markFontsLoaded();
              });
            } else {
              // 不支持 Font Loading API 的浏览器
              fontManager.markFontsLoaded();
            }
          },

          // 设置回退计时器
          setupFallbackTimer: function() {
            this.loadTimeout = setTimeout(function() {
              if (!fontManager.isLoaded) {
                console.warn('⚠️ 字体加载超时，使用系统字体');
                fontManager.markFontsLoaded();
              }
            }, 3000);
          },

          // 标记字体加载完成
          markFontsLoaded: function() {
            if (this.isLoaded) return;

            this.isLoaded = true;
            if (this.loadTimeout) {
              clearTimeout(this.loadTimeout);
            }

            document.documentElement.classList.remove('fonts-loading');
            document.documentElement.classList.add('fonts-loaded');
            console.log('✅ 字体加载完成');
          }
        };
        
        // 启动字体管理器
        fontManager.init();

        // 添加字体预加载提示元素（帮助浏览器优化）
        function addPreloadHint() {
          if (document.body) {
            var preloadHint = document.createElement('div');
            preloadHint.className = 'font-preload-hint';
            preloadHint.textContent = 'Font preload hint';
            preloadHint.style.display = 'none'; // 隐藏提示元素
            document.body.appendChild(preloadHint);
          } else {
            // 如果 body 还未加载，等待 DOM 准备就绪
            if (document.readyState === 'loading') {
              document.addEventListener('DOMContentLoaded', addPreloadHint);
            } else {
              // DOM 已经加载完成，但 body 仍然为 null（异常情况）
              setTimeout(addPreloadHint, 10);
            }
          }
        }

        addPreloadHint();
      })();
    </script>
  `.trim()

  return {
    name: 'vite-plugin-optimize-font-loading',
    transformIndexHtml(html) {
      return html.replace('</head>', `${fontOptimizationCode}\n</head>`)
    }
  }
}
