import type { Plugin } from 'vite'

export function injectByteDanceAnalytics(): Plugin {
  const byteDanceCode = `
    <script>
    // Cache API calls in array q before SDK main file is loaded
    // After SDK loads, these cached calls will be executed
    (function(win, export_obj) {
        win['LogAnalyticsObject'] = export_obj;
        if (!win[export_obj]) {
            var _collect = function() {
                _collect.q.push(arguments);
            }
            _collect.q = _collect.q || [];
            win[export_obj] = _collect;
        }
        win[export_obj].l = +new Date();
    })(window, 'collectEvent');

    // 优化加载策略：用户首次交互后加载，减少对LCP的影响
    (function() {
        var loaded = false;
        var loadByteDanceSDK = function() {
            if (loaded) return;
            loaded = true;

            // 使用 requestIdleCallback 在浏览器空闲时加载
            var loadScript = function() {
                var script = document.createElement('script');
                script.async = true;
                script.src = 'https://lf3-data.volccdn.com/obj/data-static/log-sdk/collect/5.0/collect-rangers-v5.2.1.js';
                document.head.appendChild(script);
            };

            if (window.requestIdleCallback) {
                requestIdleCallback(loadScript, { timeout: 3000 });
            } else {
                setTimeout(loadScript, 100);
            }
        };

        // 监听用户首次交互
        var events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
        var onFirstInteraction = function() {
            loadByteDanceSDK();
            events.forEach(function(event) {
                document.removeEventListener(event, onFirstInteraction, true);
            });
        };

        events.forEach(function(event) {
            document.addEventListener(event, onFirstInteraction, true);
        });

        // 备用方案：3秒后自动加载
        setTimeout(loadByteDanceSDK, 3000);
    })();
    </script>
  `.trim()

  return {
    name: 'vite-plugin-inject-bytedance-analytics',
    transformIndexHtml(html) {
      return html.replace('</head>', `${byteDanceCode}\n</head>`)
    }
  }
}
