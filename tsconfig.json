{"compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "bundler", "strict": false, "noImplicitAny": false, "noEmitOnError": false, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "lib": ["es2020", "dom"], "skipLibCheck": true}, "include": ["src/**/*", "src/**/*.vue", "src/**/*.ts"], "exclude": ["node_modules", "dist"]}