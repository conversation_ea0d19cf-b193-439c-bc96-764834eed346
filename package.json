{"name": "playshot", "description": "playshot", "version": "1.3.1", "private": true, "author": "magiclight", "license": "MIT", "type": "module", "scripts": {"dev": "vite --config ./config/vite.config.dev.ts --host", "build": "npx vite build --config ./config/vite.config.dev.ts", "build:pre": "npx vite build --config ./config/vite.config.pre.ts", "build:prod": "npx vite build --config ./config/vite.config.prod.ts", "build:prod.southeastAsia": "npx vite build --config ./config/vite.config.prod.southeastAsia.ts", "build:prerender": "node scripts/build-prerender.js", "build:optimized": "node scripts/build-optimized.js", "build:analyze": "cross-env ANALYZE=true npm run build:prod", "report": "cross-env REPORT=true npm run build", "preview": "npm run build:prod.southeastAsia && vite preview --host", "preview:optimized": "npm run build:optimized && vite preview --host", "preview:prerender": "npm run build:prerender && vite preview --host", "verify:build": "node scripts/verify-build.js", "verify:cache": "node scripts/verify-cache-config.js", "build:verify": "npm run build:prod && npm run verify:build", "perf:test": "npm run build:optimized && echo '🚀 优化构建完成，请查看 dist/performance-report.html'", "prerender:test": "npm run build:prerender && echo '🎨 预渲染构建完成，请查看 dist/prerender-report.html'", "type:check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "lint-staged": "npx lint-staged", "prepare": "husky install"}, "lint-staged": {"*.{js,ts,jsx,tsx}": ["prettier --write", "eslint --fix"], "*.vue": ["stylelint --fix", "prettier --write", "eslint --fix"], "*.{scss,sass,less,css}": ["stylelint --fix --custom-syntax postcss", "prettier --write"]}, "dependencies": {"@antv/x6": "^2.18.1", "@antv/x6-vue-shape": "^2.1.2", "@arco-design/web-vue": "^2.44.7", "@monaco-editor/loader": "^1.5.0", "@openreplay/tracker": "^15.0.3", "@sentry/vite-plugin": "^3.5.0", "@sentry/vue": "^9.26.0", "@stripe/stripe-js": "^5.4.0", "@types/js-cookie": "^3.0.6", "@unhead/vue": "^1.11.20", "@vitejs/plugin-basic-ssl": "^1.2.0", "@vue-stripe/vue-stripe": "^4.5.0", "@vueuse/core": "^13.3.0", "animejs": "^4.0.1", "axios": "^1.9.0", "canvas-confetti": "^1.9.3", "danmaku": "^2.0.8", "dayjs": "^1.11.5", "fake-progress": "^1.0.4", "file-saver": "^2.0.5", "howler": "^2.2.4", "idb": "^8.0.3", "ismobilejs": "^1.1.1", "js-cookie": "^3.0.5", "js-yaml": "^4.1.0", "lodash-es": "^4.17.21", "mitt": "^3.0.0", "monaco-editor": "^0.52.2", "motion": "^11.16.4", "nanoid": "^5.1.2", "pinia": "^2.1.6", "pinia-plugin-persistedstate": "^4.1.3", "shepherd.js": "^14.5.0", "splitpanes": "^3.1.8", "swiper": "^11.1.15", "ua-parser-js": "2.0.0-rc.1", "uuid": "^10.0.0", "vue": "^3.5.4", "vue-gtag-next": "^1.14.0", "vue-i18n": "^9.2.2", "vue-router": "^4.5.0", "vuedraggable": "^4.1.0"}, "devDependencies": {"@arco-plugins/vite-vue": "^1.4.5", "@commitlint/cli": "^17.1.2", "@commitlint/config-conventional": "^17.1.0", "@types/canvas-confetti": "^1.9.0", "@types/howler": "^2.2.11", "@types/lodash-es": "^4.17.12", "@types/node": "^20.10.6", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^6.9.0", "@typescript-eslint/parser": "^6.9.0", "@vitejs/plugin-vue": "^5.0.5", "@vitejs/plugin-vue-jsx": "^4.0.0", "@vue/babel-plugin-jsx": "^1.1.1", "@vue/test-utils": "^2.4.1", "autoprefixer": "^10.4.16", "consola": "^2.15.3", "cross-env": "^7.0.3", "cssnano": "^7.0.7", "eslint": "^8.25.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.5.0", "eslint-import-resolver-typescript": "^3.5.1", "eslint-plugin-import": "^2.26.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-vue": "^9.18.0", "husky": "^8.0.1", "less": "^4.1.3", "lint-staged": "^13.0.3", "postcss": "^8.4.32", "postcss-html": "^1.5.0", "prettier": "3.0.2", "rollup": "^4.18.0", "rollup-plugin-visualizer": "^5.12.0", "stylelint": "^15.3.0", "stylelint-config-prettier": "^9.0.3", "stylelint-config-rational-order": "^0.1.2", "stylelint-config-recommended-vue": "^1.4.0", "stylelint-config-standard": "^29.0.0", "stylelint-order": "^5.0.0", "svgo": "^4.0.0", "terser": "^5.30.3", "typescript": "5.3.3", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^0.24.1", "vite": "6.3.5", "vite-plugin-compression": "^0.5.1", "vite-plugin-eslint": "^1.8.1", "vite-plugin-html": "^3.2.2", "vite-plugin-imagemin": "^0.6.1", "vite-plugin-pwa": "^0.20.5", "vite-svg-loader": "^3.6.0", "vitest": "^3.2.1", "vue-tsc": "^2.1.6", "workbox-window": "^7.3.0"}, "engines": {"node": ">=14.0.0"}, "resolutions": {"bin-wrapper": "npm:bin-wrapper-china", "gifsicle": "5.2.0"}}