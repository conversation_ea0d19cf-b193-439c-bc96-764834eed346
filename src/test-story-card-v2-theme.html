<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StoryCardV2 主题测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        
        /* 暗色主题 */
        body:not(.light-theme) {
            background: #180430;
            color: #ffffff;
        }
        
        /* 亮色主题 */
        body.light-theme {
            background: #f8f9fa;
            color: #333333;
        }
        
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            background: #ca93f2;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            z-index: 1000;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 60px 20px 20px;
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .card-wrapper {
            width: 200px;
            height: 350px;
        }
        
        h1 {
            text-align: center;
            margin-bottom: 10px;
        }
        
        .theme-info {
            text-align: center;
            margin-bottom: 30px;
            opacity: 0.7;
        }
    </style>
</head>
<body>
    <button class="theme-toggle" onclick="toggleTheme()">切换主题</button>
    
    <div class="container">
        <h1>StoryCardV2 主题适配测试</h1>
        <div class="theme-info">
            当前主题: <span id="current-theme">暗色主题</span>
        </div>
        
        <div class="grid">
            <!-- 这里可以放置 StoryCardV2 组件的示例 -->
            <div class="card-wrapper">
                <div style="
                    width: 100%;
                    height: 100%;
                    background: var(--bg-card, rgba(255, 255, 255, 0.08));
                    border-radius: 16px;
                    display: flex;
                    flex-direction: column;
                    overflow: hidden;
                    transition: background-color 0.3s ease;
                ">
                    <!-- 图片区域 -->
                    <div style="
                        flex: 1;
                        background: linear-gradient(45deg, #ca93f2, #ba9eff);
                        position: relative;
                    ">
                        <!-- 徽章 -->
                        <div style="
                            position: absolute;
                            top: 0;
                            left: 0;
                            background: var(--accent-color, #ca93f2);
                            color: white;
                            padding: 2px 10px;
                            font-size: 11px;
                            font-weight: 600;
                            border-radius: 0 0 10px 0;
                        ">HOT</div>
                        
                        <!-- 收藏按钮 -->
                        <div style="
                            position: absolute;
                            top: 4px;
                            right: 4px;
                            width: 36px;
                            height: 36px;
                            background: rgba(0, 0, 0, 0.3);
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            color: white;
                            backdrop-filter: blur(8px);
                        ">♥</div>
                    </div>
                    
                    <!-- 描述区域 -->
                    <div style="
                        background: var(--bg-secondary, #ffffff);
                        padding: 12px 16px 16px;
                        min-height: 120px;
                        display: flex;
                        flex-direction: column;
                        gap: 8px;
                        transition: background-color 0.3s ease;
                    ">
                        <div style="
                            font-size: 14px;
                            font-weight: 600;
                            color: var(--text-primary, #333);
                            transition: color 0.3s ease;
                        ">示例故事标题</div>
                        
                        <div style="
                            font-size: 12px;
                            color: var(--text-secondary, #666);
                            transition: color 0.3s ease;
                        ">这是一个示例描述，展示StoryCardV2在不同主题下的显示效果。</div>
                        
                        <div style="display: flex; gap: 4px; flex-wrap: wrap;">
                            <span style="
                                background: var(--accent-bg, rgba(202, 147, 242, 0.1));
                                color: var(--accent-color, #ca93f2);
                                padding: 2px 6px;
                                border-radius: 12px;
                                font-size: 10px;
                                transition: background-color 0.3s ease, color 0.3s ease;
                            ">标签1</span>
                            <span style="
                                background: var(--bg-tertiary, rgba(0, 0, 0, 0.05));
                                color: var(--text-secondary, #666);
                                padding: 2px 6px;
                                border-radius: 12px;
                                font-size: 10px;
                                transition: background-color 0.3s ease, color 0.3s ease;
                            ">分类</span>
                        </div>
                        
                        <div style="
                            font-size: 11px;
                            color: var(--text-tertiary, #999);
                            margin-top: 4px;
                            transition: color 0.3s ease;
                        ">作者名称</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 设置CSS变量
        function setCSSVariables() {
            const root = document.documentElement;
            const isLight = document.body.classList.contains('light-theme');
            
            if (isLight) {
                // 亮色主题变量
                root.style.setProperty('--bg-primary', '#f8f9fa');
                root.style.setProperty('--bg-secondary', '#ffffff');
                root.style.setProperty('--bg-tertiary', 'rgba(0, 0, 0, 0.05)');
                root.style.setProperty('--bg-card', '#ffffff');
                root.style.setProperty('--text-primary', '#333333');
                root.style.setProperty('--text-secondary', '#666666');
                root.style.setProperty('--text-tertiary', '#999999');
                root.style.setProperty('--accent-color', '#ca93f2');
                root.style.setProperty('--accent-bg', 'rgba(202, 147, 242, 0.1)');
            } else {
                // 暗色主题变量
                root.style.setProperty('--bg-primary', '#180430');
                root.style.setProperty('--bg-secondary', '#290e40');
                root.style.setProperty('--bg-tertiary', 'rgba(255, 255, 255, 0.05)');
                root.style.setProperty('--bg-card', 'rgba(255, 255, 255, 0.08)');
                root.style.setProperty('--text-primary', '#ffffff');
                root.style.setProperty('--text-secondary', 'rgba(255, 255, 255, 0.7)');
                root.style.setProperty('--text-tertiary', 'rgba(255, 255, 255, 0.5)');
                root.style.setProperty('--accent-color', '#ca93f2');
                root.style.setProperty('--accent-bg', 'rgba(202, 147, 242, 0.1)');
            }
        }
        
        function toggleTheme() {
            document.body.classList.toggle('light-theme');
            const isLight = document.body.classList.contains('light-theme');
            document.getElementById('current-theme').textContent = isLight ? '亮色主题' : '暗色主题';
            setCSSVariables();
        }
        
        // 初始化
        setCSSVariables();
    </script>
</body>
</html>
