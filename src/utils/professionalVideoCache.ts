/**
 * 专业视频缓存管理器
 * 使用 idb + workbox 策略解决视频缓存问题
 */

import { openDB, type DBSchema, type IDBPDatabase } from 'idb'

interface VideoDBSchema extends DBSchema {
  videos: {
    key: string
    value: {
      url: string
      blob: Blob
      timestamp: number
      size: number
      contentType: string
    }
    indexes: {
      timestamp: number
      size: number
    }
  }
  metadata: {
    key: string
    value: {
      key: string
      totalSize: number
      lastCleanup: number
    }
  }
}

class ProfessionalVideoCache {
  private db: IDBPDatabase<VideoDBSchema> | null = null
  private readonly DB_NAME = 'professional-video-cache'
  private readonly DB_VERSION = 1
  private readonly MAX_CACHE_SIZE = 500 * 1024 * 1024 // 500MB
  private readonly CACHE_EXPIRY = 7 * 24 * 60 * 60 * 1000 // 7天

  async init(): Promise<void> {
    try {
      this.db = await openDB<VideoDBSchema>(this.DB_NAME, this.DB_VERSION, {
        upgrade(db) {
          // 创建视频存储
          if (!db.objectStoreNames.contains('videos')) {
            const videoStore = db.createObjectStore('videos', { keyPath: 'url' })
            videoStore.createIndex('timestamp', 'timestamp')
            videoStore.createIndex('size', 'size')
          }

          // 创建元数据存储
          if (!db.objectStoreNames.contains('metadata')) {
            db.createObjectStore('metadata', { keyPath: 'key' })
          }
        }
      })

      console.log('🎯 Professional Video Cache initialized')

      // 启动时清理过期缓存
      await this.cleanupExpiredCache()
    } catch (error) {
      console.error('❌ Failed to initialize Professional Video Cache:', error)
    }
  }

  /**
   * 智能缓存视频 - 检查空间并自动清理
   */
  async cacheVideo(url: string, blob: Blob): Promise<boolean> {
    if (!this.db) {
      console.warn('❌ Database not initialized')
      return false
    }

    try {
      const videoSize = blob.size
      const contentType = blob.type || 'video/mp4'

      // 检查是否需要清理空间
      await this.ensureSpace(videoSize)

      // 存储视频
      await this.db.put('videos', {
        url,
        blob,
        timestamp: Date.now(),
        size: videoSize,
        contentType
      })

      // 更新总大小
      await this.updateTotalSize(videoSize)

      console.log(`✅ Video cached: ${url} (${(videoSize / 1024 / 1024).toFixed(2)}MB)`)
      return true
    } catch (error) {
      console.error('❌ Failed to cache video:', error)
      return false
    }
  }

  /**
   * 获取缓存的视频
   */
  async getCachedVideo(url: string): Promise<Blob | null> {
    if (!this.db) {
      await this.init()
      if (!this.db) return null
    }

    try {
      const cached = await this.db.get('videos', url)

      if (!cached) {
        console.log('❌ Video not in cache:', url)
        return null
      }

      // 检查是否过期
      if (Date.now() - cached.timestamp > this.CACHE_EXPIRY) {
        console.log('⏰ Video cache expired, removing:', url)
        await this.removeVideo(url)
        return null
      }

      console.log('✅ Video cache hit:', url)
      return cached.blob
    } catch (error) {
      console.error('❌ Failed to get cached video:', error)
      return null
    }
  }

  /**
   * 预加载视频并缓存
   */
  async preloadAndCache(url: string): Promise<string | null> {
    try {
      // 首先检查缓存
      const cachedBlob = await this.getCachedVideo(url)
      if (cachedBlob) {
        return URL.createObjectURL(cachedBlob)
      }

      console.log('📥 Preloading video:', url)

      // 下载完整视频
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Cache-Control': 'no-cache'
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const blob = await response.blob()

      // 缓存视频
      await this.cacheVideo(url, blob)

      // 返回ObjectURL
      return URL.createObjectURL(blob)
    } catch (error) {
      console.error('❌ Failed to preload and cache video:', error)
      return null
    }
  }

  /**
   * 确保有足够空间
   */
  private async ensureSpace(requiredSize: number): Promise<void> {
    if (!this.db) return

    const metadata = await this.db.get('metadata', 'cache-info')
    const currentSize = metadata?.totalSize || 0

    if (currentSize + requiredSize > this.MAX_CACHE_SIZE) {
      console.log('🧹 Cache size limit reached, cleaning up...')
      await this.cleanupOldestVideos(requiredSize)
    }
  }

  /**
   * 清理最旧的视频
   */
  private async cleanupOldestVideos(requiredSize: number): Promise<void> {
    if (!this.db) return

    try {
      // 获取所有视频，按时间戳排序
      const tx = this.db.transaction('videos', 'readwrite')
      const index = tx.store.index('timestamp')
      const videos = await index.getAll()

      // 按时间戳升序排序（最旧的在前）
      videos.sort((a, b) => a.timestamp - b.timestamp)

      let freedSpace = 0
      const toDelete: string[] = []

      for (const video of videos) {
        toDelete.push(video.url)
        freedSpace += video.size

        if (freedSpace >= requiredSize) {
          break
        }
      }

      // 删除选中的视频
      for (const url of toDelete) {
        await this.removeVideo(url)
      }

      console.log(
        `🗑️ Cleaned up ${toDelete.length} videos, freed ${(freedSpace / 1024 / 1024).toFixed(2)}MB`
      )
    } catch (error) {
      console.error('❌ Failed to cleanup old videos:', error)
    }
  }

  /**
   * 清理过期缓存
   */
  private async cleanupExpiredCache(): Promise<void> {
    if (!this.db) return

    try {
      const tx = this.db.transaction('videos', 'readwrite')
      const videos = await tx.store.getAll()
      const now = Date.now()
      let cleanedCount = 0

      for (const video of videos) {
        if (now - video.timestamp > this.CACHE_EXPIRY) {
          await this.removeVideo(video.url)
          cleanedCount++
        }
      }

      if (cleanedCount > 0) {
        console.log(`🧹 Cleaned up ${cleanedCount} expired videos`)
      }
    } catch (error) {
      console.error('❌ Failed to cleanup expired cache:', error)
    }
  }

  /**
   * 删除视频
   */
  private async removeVideo(url: string): Promise<void> {
    if (!this.db) return

    try {
      const video = await this.db.get('videos', url)
      if (video) {
        await this.db.delete('videos', url)
        await this.updateTotalSize(-video.size)
      }
    } catch (error) {
      console.error('❌ Failed to remove video:', error)
    }
  }

  /**
   * 更新总大小
   */
  private async updateTotalSize(sizeChange: number): Promise<void> {
    if (!this.db) return

    try {
      const metadata = await this.db.get('metadata', 'cache-info')
      const currentSize = metadata?.totalSize || 0
      const newSize = Math.max(0, currentSize + sizeChange)

      await this.db.put('metadata', {
        key: 'cache-info',
        totalSize: newSize,
        lastCleanup: Date.now()
      })
    } catch (error) {
      console.error('❌ Failed to update total size:', error)
    }
  }

  /**
   * 获取缓存统计信息
   */
  async getCacheStats(): Promise<{
    totalSize: number
    videoCount: number
    sizeMB: string
  }> {
    if (!this.db) {
      return { totalSize: 0, videoCount: 0, sizeMB: '0.00' }
    }

    try {
      const videos = await this.db.getAll('videos')
      const totalSize = videos.reduce((sum, video) => sum + video.size, 0)

      return {
        totalSize,
        videoCount: videos.length,
        sizeMB: (totalSize / 1024 / 1024).toFixed(2)
      }
    } catch (error) {
      console.error('❌ Failed to get cache stats:', error)
      return { totalSize: 0, videoCount: 0, sizeMB: '0.00' }
    }
  }

  /**
   * 清空所有缓存
   */
  async clearAll(): Promise<void> {
    if (!this.db) return

    try {
      await this.db.clear('videos')
      await this.db.clear('metadata')
      console.log('🗑️ All video cache cleared')
    } catch (error) {
      console.error('❌ Failed to clear cache:', error)
    }
  }
}

// 创建单例实例
export const professionalVideoCache = new ProfessionalVideoCache()

// 自动初始化
professionalVideoCache.init()
