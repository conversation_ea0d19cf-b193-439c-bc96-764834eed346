/**
 * 版本管理器 - 实现静默更新机制
 * 解决部署后资源404问题，避免强制清缓存
 */

interface VersionInfo {
  version: string
  buildTime: number
  timestamp: number
}

interface VersionManagerOptions {
  /** 检查更新的间隔时间（毫秒），默认5分钟 */
  checkInterval?: number
  /** 是否在页面可见时立即检查 */
  checkOnVisible?: boolean
  /** 是否在路由变化时检查 */
  checkOnRouteChange?: boolean
  /** 更新回调函数 */
  onUpdateAvailable?: (newVersion: VersionInfo, currentVersion: VersionInfo) => void
  /** 更新失败回调 */
  onUpdateError?: (error: Error) => void
  /** 是否显示更新通知UI */
  showUpdateNotification?: boolean
  /** 是否自动更新（不显示通知直接刷新） */
  autoUpdate?: boolean
}

class VersionManager {
  private currentVersion: VersionInfo | null = null
  private checkTimer: number | null = null
  private isChecking = false
  private options: Required<VersionManagerOptions>

  constructor(options: VersionManagerOptions = {}) {
    this.options = {
      checkInterval: 5 * 60 * 1000, // 5分钟
      checkOnVisible: true,
      checkOnRouteChange: true,
      showUpdateNotification: false, // 默认不显示通知，直接静默更新
      autoUpdate: true, // 默认自动更新
      onUpdateAvailable: this.defaultUpdateHandler.bind(this),
      onUpdateError: this.defaultErrorHandler.bind(this),
      ...options
    }

    this.init()
  }

  /**
   * 初始化版本管理器
   */
  private async init(): Promise<void> {
    try {
      // 获取当前版本信息
      this.currentVersion = await this.getCurrentVersion()
      console.log('📦 当前版本:', this.currentVersion)

      // 开始定期检查
      this.startPeriodicCheck()

      // 监听页面可见性变化
      if (this.options.checkOnVisible) {
        this.setupVisibilityListener()
      }

      // 监听路由变化
      if (this.options.checkOnRouteChange) {
        this.setupRouteListener()
      }

      console.log('✅ 版本管理器初始化完成')
    } catch (error) {
      console.error('❌ 版本管理器初始化失败:', error)
      this.options.onUpdateError(error as Error)
    }
  }

  /**
   * 获取当前版本信息
   */
  private async getCurrentVersion(): Promise<VersionInfo> {
    try {
      // 首先尝试从本地存储获取
      const stored = localStorage.getItem('app_version_info')
      if (stored) {
        const parsed = JSON.parse(stored)
        // 验证存储的版本信息是否有效
        if (parsed.version && parsed.buildTime && parsed.timestamp) {
          return parsed
        }
      }

      // 如果本地没有或无效，从服务器获取
      const response = await fetch('/version.json?t=' + Date.now(), {
        cache: 'no-cache'
      })

      if (!response.ok) {
        throw new Error(`获取版本信息失败: ${response.status}`)
      }

      const responseText = await response.text()

      // 检查响应是否为JSON格式
      if (!responseText.trim().startsWith('{')) {
        throw new Error('版本文件格式错误，可能返回了HTML页面')
      }

      const versionInfo = JSON.parse(responseText)

      // 存储到本地
      localStorage.setItem('app_version_info', JSON.stringify(versionInfo))

      return versionInfo
    } catch (error) {
      console.warn('获取版本信息失败，使用默认版本:', error)
      // 返回默认版本信息
      return {
        version: '1.0.0',
        buildTime: Date.now(),
        timestamp: Date.now()
      }
    }
  }

  /**
   * 检查是否有新版本
   */
  private async checkForUpdates(): Promise<void> {
    if (this.isChecking || !this.currentVersion) {
      return
    }

    this.isChecking = true

    try {
      console.log('🔍 检查版本更新...')

      const response = await fetch('/version.json?t=' + Date.now(), {
        cache: 'no-cache',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          Pragma: 'no-cache',
          Expires: '0'
        }
      })

      if (!response.ok) {
        throw new Error(`检查更新失败: ${response.status}`)
      }

      const responseText = await response.text()

      // 检查响应是否为JSON格式
      if (!responseText.trim().startsWith('{')) {
        throw new Error('版本文件格式错误，可能返回了HTML页面')
      }

      const latestVersion: VersionInfo = JSON.parse(responseText)

      // 比较版本
      if (this.isNewVersionAvailable(latestVersion, this.currentVersion)) {
        console.log('🆕 发现新版本:', latestVersion)
        this.options.onUpdateAvailable(latestVersion, this.currentVersion)

        // 更新当前版本信息
        this.currentVersion = latestVersion
        localStorage.setItem('app_version_info', JSON.stringify(latestVersion))
      } else {
        console.log('✅ 当前已是最新版本')
      }
    } catch (error) {
      console.error('❌ 检查更新失败:', error)
      this.options.onUpdateError(error as Error)
    } finally {
      this.isChecking = false
    }
  }

  /**
   * 判断是否有新版本
   */
  private isNewVersionAvailable(latest: VersionInfo, current: VersionInfo): boolean {
    // 比较构建时间戳
    return latest.buildTime > current.buildTime
  }

  /**
   * 开始定期检查
   */
  private startPeriodicCheck(): void {
    if (this.checkTimer) {
      clearInterval(this.checkTimer)
    }

    this.checkTimer = window.setInterval(() => {
      this.checkForUpdates()
    }, this.options.checkInterval)
  }

  /**
   * 设置页面可见性监听
   */
  private setupVisibilityListener(): void {
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'visible') {
        console.log('👀 页面重新可见，检查版本更新')
        this.checkForUpdates()
      }
    })
  }

  /**
   * 设置路由变化监听
   */
  private setupRouteListener(): void {
    // 监听路由变化
    window.addEventListener('popstate', () => {
      this.checkForUpdates()
    })

    // 监听 pushState 和 replaceState
    const originalPushState = history.pushState
    const originalReplaceState = history.replaceState

    history.pushState = function (...args) {
      originalPushState.apply(history, args)
      setTimeout(() => versionManager.checkForUpdates(), 1000)
    }

    history.replaceState = function (...args) {
      originalReplaceState.apply(history, args)
      setTimeout(() => versionManager.checkForUpdates(), 1000)
    }
  }

  /**
   * 默认更新处理器 - 根据配置决定是否显示通知或静默更新
   */
  private defaultUpdateHandler(newVersion: VersionInfo, currentVersion: VersionInfo): void {
    console.log('🔄 检测到新版本，准备更新...')

    if (this.options.showUpdateNotification) {
      // 显示更新通知
      this.showUpdateNotificationUI(newVersion, currentVersion)
    } else if (this.options.autoUpdate) {
      // 静默自动更新
      this.performSilentUpdate()
    }
  }

  /**
   * 显示更新通知UI
   */
  private showUpdateNotificationUI(newVersion: VersionInfo, currentVersion: VersionInfo): void {
    // 动态创建更新通知组件
    import('@/shared/components/UpdateNotification.vue')
      .then(() => {
        // 组件加载成功，使用事件的方式通知应用层
        const event = new CustomEvent('version-update-available', {
          detail: {
            newVersion,
            currentVersion,
            onUpdate: () => this.performSilentUpdate(),
            onLater: () => this.postponeUpdate()
          }
        })

        window.dispatchEvent(event)
      })
      .catch(() => {
        // 如果组件加载失败，回退到静默更新
        console.warn('更新通知组件加载失败，执行静默更新')
        this.performSilentUpdate()
      })
  }

  /**
   * 执行静默更新
   */
  private async performSilentUpdate(): Promise<void> {
    console.log('🔄 执行静默更新...')

    try {
      // 先清除相关缓存
      await this.clearAppCache()

      // 延迟一段时间后刷新，确保缓存清理完成
      setTimeout(() => {
        console.log('🔄 刷新页面以应用更新...')
        window.location.reload()
      }, 1000) // 1秒延迟，给缓存清理足够时间
    } catch (error) {
      console.warn('⚠️ 静默更新过程中出现错误:', error)
      // 即使清理缓存失败，也要刷新页面
      setTimeout(() => {
        window.location.reload()
      }, 1000)
    }
  }

  /**
   * 推迟更新
   */
  private postponeUpdate(): void {
    console.log('⏰ 用户选择推迟更新')
    // 可以在这里设置推迟更新的逻辑，比如30分钟后再检查
    setTimeout(
      () => {
        this.checkForUpdates()
      },
      30 * 60 * 1000
    ) // 30分钟后再检查
  }

  /**
   * 默认错误处理器
   */
  private defaultErrorHandler(error: Error): void {
    console.error('版本检查错误:', error)
    // 可以在这里添加错误上报逻辑
  }

  /**
   * 清除应用缓存 - 增强版本
   */
  private async clearAppCache(): Promise<void> {
    try {
      console.log('🧹 开始清理应用缓存...')

      // 清除 localStorage 中的版本相关信息
      localStorage.removeItem('app_version_info')

      // 清除其他可能的缓存数据
      const keysToRemove = Object.keys(localStorage).filter(
        (key) => key.includes('cache') || key.includes('version') || key.includes('build')
      )
      keysToRemove.forEach((key) => localStorage.removeItem(key))

      // 如果支持 Service Worker，通知 SW 清除缓存
      if ('serviceWorker' in navigator) {
        const registration = await navigator.serviceWorker.ready

        if (registration.active) {
          // 创建消息通道
          const messageChannel = new MessageChannel()

          // 监听 SW 的响应
          const clearCachePromise = new Promise<void>((resolve) => {
            messageChannel.port1.onmessage = (event) => {
              if (event.data.success) {
                console.log('✅ Service Worker 缓存清理完成')
                resolve()
              }
            }

            // 设置超时，避免无限等待
            setTimeout(() => {
              console.log('⚠️ Service Worker 缓存清理超时')
              resolve()
            }, 5000)
          })

          // 发送清理缓存消息
          registration.active.postMessage({ type: 'CLEAR_CACHE' }, [messageChannel.port2])

          await clearCachePromise
        }

        // 手动清理浏览器缓存
        if ('caches' in window) {
          const cacheNames = await caches.keys()
          await Promise.all(
            cacheNames.map(async (cacheName) => {
              if (
                cacheName.includes('magic-partner') ||
                cacheName.includes('static') ||
                cacheName.includes('dynamic') ||
                cacheName.includes('images') ||
                cacheName.includes('api')
              ) {
                await caches.delete(cacheName)
                console.log('🗑️ 清除缓存:', cacheName)
              }
            })
          )
        }
      }

      console.log('✅ 应用缓存清理完成')
    } catch (error) {
      console.warn('⚠️ 清除缓存失败:', error)
    }
  }

  /**
   * 手动检查更新
   */
  public async manualCheck(): Promise<void> {
    await this.checkForUpdates()
  }

  /**
   * 获取当前版本信息
   */
  public getCurrentVersionInfo(): VersionInfo | null {
    return this.currentVersion
  }

  /**
   * 停止版本检查
   */
  public stop(): void {
    if (this.checkTimer) {
      clearInterval(this.checkTimer)
      this.checkTimer = null
    }
    console.log('⏹️ 版本检查已停止')
  }

  /**
   * 销毁版本管理器
   */
  public destroy(): void {
    this.stop()
    this.currentVersion = null
    console.log('🗑️ 版本管理器已销毁')
  }
}

// 创建全局实例（延迟初始化）
let _versionManager: VersionManager | null = null

export const versionManager = {
  getInstance(): VersionManager {
    if (!_versionManager) {
      _versionManager = new VersionManager()
    }
    return _versionManager
  },

  getCurrentVersionInfo() {
    return this.getInstance().getCurrentVersionInfo()
  },

  async manualCheck() {
    return this.getInstance().manualCheck()
  },

  async checkForUpdates() {
    return this.getInstance().manualCheck()
  },

  stop() {
    return this.getInstance().stop()
  },

  destroy() {
    if (_versionManager) {
      _versionManager.destroy()
      _versionManager = null
    }
  }
}

// 导出类型和工具函数
export type { VersionInfo, VersionManagerOptions }

/**
 * 初始化版本管理器
 */
export function initVersionManager(options?: VersionManagerOptions): VersionManager {
  return new VersionManager(options)
}

/**
 * 手动检查更新
 */
export async function checkForUpdates(): Promise<void> {
  await versionManager.manualCheck()
}

/**
 * 获取当前版本信息
 */
export function getCurrentVersion(): VersionInfo | null {
  return versionManager.getCurrentVersionInfo()
}

// 开发环境下的调试工具
// if (import.meta.env.DEV) {
//   const windowAny = window as any
//   windowAny.__versionManager = {
//     checkForUpdates,
//     getCurrentVersion,
//     manager: versionManager
//   }
//   console.log('🔧 版本管理器调试工具已挂载到 window.__versionManager')
// }
