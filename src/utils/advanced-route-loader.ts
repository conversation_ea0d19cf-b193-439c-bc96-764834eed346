import { defineAsyncComponent, AsyncComponentLoader, Component } from 'vue'
import { RouteComponent } from 'vue-router'

// 注入样式到页面
const injectStyles = () => {
  if (typeof document !== 'undefined' && !document.getElementById('route-loader-styles')) {
    const style = document.createElement('style')
    style.id = 'route-loader-styles'
    style.textContent = `
      .route-error {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 400px;
        padding: 2rem;
        background: var(--bg-primary, #180430);
        color: var(--text-primary, #ffffff);
      }

      .route-error .error-content {
        text-align: center;
        max-width: 400px;
        padding: 2rem;
        background: var(--bg-card, rgba(255, 255, 255, 0.08));
        border-radius: 16px;
        border: 1px solid var(--border-color, rgba(255, 255, 255, 0.1));
        backdrop-filter: blur(10px);
        animation: fadeInUp 0.5s ease-out;
      }

      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .route-error .error-icon {
        color: var(--accent-color, #ca93f2);
        margin-bottom: 1.5rem;
        opacity: 0.8;
      }

      .route-error .error-title {
        font-size: 1.25rem;
        font-weight: 600;
        margin: 0 0 1rem 0;
        color: var(--text-primary, #ffffff);
      }

      .route-error .error-message {
        font-size: 0.875rem;
        margin: 0 0 2rem 0;
        color: var(--text-secondary, rgba(255, 255, 255, 0.7));
        line-height: 1.5;
      }

      .route-error .retry-btn {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1.5rem;
        background: var(--accent-color, #ca93f2);
        color: var(--bg-primary, #180430);
        border: none;
        border-radius: 8px;
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
      }

      .route-error .retry-btn:hover {
        background: var(--accent-hover, #b87de0);
        transform: translateY(-1px);
      }

      .route-error .retry-btn:active {
        transform: translateY(0);
      }

      .route-loading {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 300px;
        padding: 2rem;
        background: var(--bg-primary, #180430);
        color: var(--text-primary, #ffffff);
      }

      .route-loading .loading-content {
        text-align: center;
        animation: fadeIn 0.3s ease-out;
      }

      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }

      .route-loading .loading-spinner {
        position: relative;
        width: 40px;
        height: 40px;
        margin: 0 auto 1rem auto;
      }

      .route-loading .spinner-ring {
        position: absolute;
        width: 100%;
        height: 100%;
        border: 2px solid transparent;
        border-top: 2px solid var(--accent-color, #ca93f2);
        border-radius: 50%;
        animation: spin 1.2s linear infinite;
      }

      .route-loading .spinner-ring:nth-child(1) {
        animation-delay: 0s;
        opacity: 1;
      }

      .route-loading .spinner-ring:nth-child(2) {
        animation-delay: -0.4s;
        opacity: 0.7;
        transform: scale(0.8);
      }

      .route-loading .spinner-ring:nth-child(3) {
        animation-delay: -0.8s;
        opacity: 0.4;
        transform: scale(0.6);
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      .route-loading .loading-text {
        font-size: 0.875rem;
        margin: 0;
        color: var(--text-secondary, rgba(255, 255, 255, 0.7));
        font-weight: 500;
      }

      /* 响应式适配 */
      @media (max-width: 768px) {
        .route-error {
          min-height: 300px;
          padding: 1rem;
        }

        .route-error .error-content {
          padding: 1.5rem;
          border-radius: 12px;
        }

        .route-error .error-title {
          font-size: 1.125rem;
        }

        .route-loading {
          min-height: 200px;
          padding: 1rem;
        }
      }

      /* 亮色主题适配 */
      body.light-theme .route-error {
        background: var(--bg-primary, #f8f9fa);
        color: var(--text-primary, #333333);
      }

      body.light-theme .route-error .error-content {
        background: var(--bg-card, #ffffff);
        border: 1px solid var(--border-color, rgba(0, 0, 0, 0.1));
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      body.light-theme .route-error .retry-btn {
        color: #ffffff;
      }

      body.light-theme .route-loading {
        background: var(--bg-primary, #f8f9fa);
        color: var(--text-primary, #333333);
      }
    `
    document.head.appendChild(style)
  }
}

// 立即注入样式
injectStyles()

// 错误组件
const ErrorComponent = {
  template: `
    <div class="route-error">
      <div class="error-content">
        <div class="error-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
            <line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" stroke-width="2"/>
            <line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" stroke-width="2"/>
          </svg>
        </div>
        <h3 class="error-title">Failed to Load Page</h3>
        <p class="error-message">Network connection error. Please check your connection and try again.</p>
        <button @click="retry" class="retry-btn">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M1 4v6h6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          Retry
        </button>
      </div>
    </div>
  `,
  methods: {
    retry() {
      window.location.reload()
    }
  }
}

// 加载组件
const LoadingComponent = {
  template: `
    <div class="route-loading">
      <div class="loading-content">
        <div class="loading-spinner">
          <div class="spinner-ring"></div>
          <div class="spinner-ring"></div>
          <div class="spinner-ring"></div>
        </div>
        <p class="loading-text">Loading...</p>
      </div>
    </div>
  `
}

// 路由组件缓存
const componentCache = new Map<string, Promise<Component>>()

// 预加载队列
const preloadQueue = new Set<string>()

// 加载统计
const loadStats = {
  total: 0,
  success: 0,
  failed: 0,
  cached: 0
}

/**
 * 路由组件加载器配置
 */
export interface RouteLoaderOptions {
  delay?: number
  timeout?: number
  loadingComponent?: Component
  errorComponent?: Component
  retryDelay?: number
  maxRetries?: number
  enableCache?: boolean
  preload?: boolean
  priority?: 'high' | 'normal' | 'low'
}

/**
 * 路由组件分组配置
 */
export interface RouteGroup {
  name: string
  routes: string[]
  priority: 'high' | 'normal' | 'low'
  preload?: boolean
}

/**
 * 创建智能路由组件加载器
 * @param loader 组件加载函数
 * @param cacheKey 缓存键名
 * @param options 配置选项
 */
export function createRouteLoader(
  loader: AsyncComponentLoader,
  cacheKey: string,
  options: RouteLoaderOptions = {}
): RouteComponent {
  const {
    delay = 200,
    timeout = 15000,
    loadingComponent = LoadingComponent,
    errorComponent = ErrorComponent,
    retryDelay = 1000,
    maxRetries = 3,
    enableCache = true,
    preload = false,
    priority = 'normal'
  } = options

  // 如果启用预加载，添加到预加载队列
  if (preload && !preloadQueue.has(cacheKey)) {
    preloadQueue.add(cacheKey)
    // 根据优先级决定预加载延迟
    const preloadDelay = priority === 'high' ? 50 : priority === 'normal' ? 200 : 500
    setTimeout(() => preloadComponent(loader, cacheKey), preloadDelay)
  }

  let retryCount = 0

  const cachedLoader = (): Promise<Component> => {
    loadStats.total++

    // 如果启用缓存且已缓存，直接返回
    if (enableCache && componentCache.has(cacheKey)) {
      loadStats.cached++
      return componentCache.get(cacheKey)!
    }

    const loadPromise = loader()
      .then((component) => {
        loadStats.success++
        console.log(`✅ Route component loaded successfully [${cacheKey}]`)
        return component
      })
      .catch((error) => {
        loadStats.failed++
        console.error(`❌ Route component failed to load [${cacheKey}]:`, error)

        if (retryCount < maxRetries) {
          retryCount++
          console.log(`🔄 Retrying route component [${cacheKey}] (${retryCount}/${maxRetries})`)

          return new Promise((resolve, reject) => {
            setTimeout(() => {
              // 清除失败的缓存
              if (enableCache) {
                componentCache.delete(cacheKey)
              }
              cachedLoader().then(resolve).catch(reject)
            }, retryDelay * retryCount) // 递增延迟
          })
        }

        // 清除失败的缓存
        if (enableCache) {
          componentCache.delete(cacheKey)
        }
        throw error
      })

    // 缓存加载Promise
    if (enableCache) {
      componentCache.set(cacheKey, loadPromise)
    }

    return loadPromise
  }

  return defineAsyncComponent({
    loader: cachedLoader,
    delay,
    timeout,
    loadingComponent,
    errorComponent
  })
}

/**
 * 预加载组件
 * @param loader 组件加载函数
 * @param cacheKey 缓存键名
 */
export function preloadComponent(
  loader: AsyncComponentLoader,
  cacheKey?: string
): Promise<Component> {
  if (cacheKey && componentCache.has(cacheKey)) {
    return componentCache.get(cacheKey)!
  }

  const loadPromise = loader().catch((error) => {
    console.error(`Failed to preload component [${cacheKey}]:`, error)
    if (cacheKey) {
      componentCache.delete(cacheKey)
    }
    throw error
  })

  if (cacheKey) {
    componentCache.set(cacheKey, loadPromise)
  }

  return loadPromise
}

/**
 * 批量预加载组件
 * @param loaders 组件加载配置数组
 */
export async function preloadComponents(
  loaders: Array<{
    loader: AsyncComponentLoader
    cacheKey: string
    priority?: 'high' | 'normal' | 'low'
  }>
): Promise<Component[]> {
  // 按优先级排序
  const sortedLoaders = loaders.sort((a, b) => {
    const priorityOrder = { high: 0, normal: 1, low: 2 }
    return priorityOrder[a.priority || 'normal'] - priorityOrder[b.priority || 'normal']
  })

  try {
    const promises = sortedLoaders.map(({ loader, cacheKey }) => preloadComponent(loader, cacheKey))
    return await Promise.all(promises)
  } catch (error) {
    console.error('Failed to batch preload components:', error)
    throw error
  }
}

/**
 * 预加载路由组
 * @param group 路由组配置
 * @param routeLoaders 路由加载器映射
 */
export async function preloadRouteGroup(
  group: RouteGroup,
  routeLoaders: Map<string, AsyncComponentLoader>
): Promise<void> {
  console.log(`🚀 Starting to preload route group: ${group.name}`)

  const loaders = group.routes
    .map((route) => {
      const loader = routeLoaders.get(route)
      return loader ? { loader, cacheKey: route, priority: group.priority } : null
    })
    .filter(Boolean) as Array<{
    loader: AsyncComponentLoader
    cacheKey: string
    priority: 'high' | 'normal' | 'low'
  }>

  try {
    await preloadComponents(loaders)
    console.log(`✅ Route group preload completed: ${group.name}`)
  } catch (error) {
    console.error(`❌ Route group preload failed: ${group.name}`, error)
  }
}

/**
 * 清除组件缓存
 * @param cacheKey 缓存键名，不传则清除所有
 */
export function clearComponentCache(cacheKey?: string): void {
  if (cacheKey) {
    componentCache.delete(cacheKey)
  } else {
    componentCache.clear()
  }
}

/**
 * 获取缓存状态
 */
export function getCacheInfo(): { size: number; keys: string[]; stats: typeof loadStats } {
  return {
    size: componentCache.size,
    keys: Array.from(componentCache.keys()),
    stats: { ...loadStats }
  }
}

/**
 * 重置加载统计
 */
export function resetLoadStats(): void {
  loadStats.total = 0
  loadStats.success = 0
  loadStats.failed = 0
  loadStats.cached = 0
}
