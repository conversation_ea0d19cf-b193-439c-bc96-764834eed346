import { v4 as uuid } from 'uuid'
import { getAppVersion, isAppEnv, isMobileEnv } from './util'
import { ReportEvent } from '@/interface'
import { UAParser } from 'ua-parser-js'

// 环境变量
const APPLICATION_VERSION = getAppVersion()
const parser = new UAParser()
let env = isMobileEnv() ? 'mobile' : 'pc'
env = isAppEnv() ? 'app' : env

// 事件队列配置
const pendingEvents: Array<{ eventType: ReportEvent; eventData: any }> = []
const MAX_PENDING_EVENTS = 50
let hasTriedReInit = false

// 关键事件类型
const CRITICAL_EVENTS = [
  ReportEvent.ApiError,
  ReportEvent.ApiUnknownError,
  ReportEvent.PaymentSuccess
]

/**
 * 初始化火山引擎 DataFinder
 * @returns {boolean} 初始化是否成功
 */
function initVolcDataFinder(): boolean {
  if (!window.collectEvent) {
    console.warn('初始化火山引擎 DataFinder 失败: window.collectEvent 不存在')
    return false
  }

  // 使用 window.onerror 捕获全局错误，避免 try-catch
  const originalWindowOnError = window.onerror
  window.onerror = function (message, _source, _lineno, _colno, error) {
    console.error('火山引擎 DataFinder 初始化失败:', error || message)
    window.onerror = originalWindowOnError
    return false
  }

  // 字节跳动埋点
  window.collectEvent('init', {
    app_id: Number(import.meta.env.VITE_VOLC_APP_ID),
    channel_domain: 'https://gator.volces.com',
    log: true,
    auto_track: true,
    disable_sdk_monitor: true,
    enable_stay_duration: true,
    spa: true
  })

  window.collectEvent('start')
  console.log('火山引擎 DataFinder 初始化成功')

  // 恢复原始的 onerror 处理
  window.onerror = originalWindowOnError
  return true
}

/**
 * 尝试重新加载火山引擎 DataFinder 脚本
 */
function tryReInitVolcDataFinder(): void {
  if (hasTriedReInit || window.collectEvent) return

  console.warn('尝试重新初始化火山引擎 DataFinder')
  hasTriedReInit = true

  const script = document.createElement('script')
  script.src =
    'https://lf3-data.volccdn.com/obj/data-static/log-sdk/collect/5.0/collect-rangers-v5.2.1.js'
  script.async = true

  script.onload = () => {
    console.log('火山引擎 DataFinder 脚本重新加载成功')
    if (initVolcDataFinder() && window.collectEvent) {
      console.log(`处理 ${pendingEvents.length} 个待上报事件`)
      processPendingEvents()
    }
  }

  script.onerror = () => console.error('火山引擎 DataFinder 脚本重新加载失败')
  document.head.appendChild(script)
}

/**
 * 处理队列中的待上报事件
 */
function processPendingEvents(): void {
  if (!window.collectEvent || pendingEvents.length === 0) return

  // 复制队列并清空原队列
  const eventsToProcess = [...pendingEvents]
  pendingEvents.length = 0

  for (const { eventType, eventData } of eventsToProcess) {
    // 使用 window.onerror 捕获错误，避免 try-catch
    const originalWindowOnError = window.onerror
    window.onerror = function (message, _source, _lineno, _colno, error) {
      console.error('处理队列事件失败:', error || message)
      window.onerror = originalWindowOnError
      return true // 阻止错误继续传播
    }

    window.collectEvent(eventType, eventData)
    window.onerror = originalWindowOnError
  }
}

/**
 * 添加事件到队列
 * @param {ReportEvent} eventType 事件类型
 * @param {object} eventData 事件数据
 */
function addToEventQueue(eventType: ReportEvent, eventData: object): void {
  if (pendingEvents.length >= MAX_PENDING_EVENTS) return

  pendingEvents.push({ eventType, eventData })

  // 记录关键事件
  if (CRITICAL_EVENTS.includes(eventType)) {
    console.group('关键事件未能上报')
    console.log('事件类型:', eventType)
    console.log('事件数据:', eventData)
    console.groupEnd()
  }
}

/**
 * 上报事件
 * @param {ReportEvent} eventType 事件类型
 * @param {object} eventData 事件数据
 * @returns {boolean} 上报是否成功
 */
export function reportEvent(eventType: ReportEvent, eventData: object = {}): boolean {
  // 准备事件数据
  const fullEventData = {
    ...eventData,
    eventTime: Date.now(),
    app_host: location.host,
    app_env: env,
    app_version: APPLICATION_VERSION,
    os: parser.getOS(),
    ua: parser.getUA(),
    userId: JSON.parse(localStorage.getItem('user') || '{}')?.uuid || '',
    route: window.location.pathname
  }

  // 如果 collectEvent 不存在，加入队列等待后续处理
  if (!window.collectEvent) {
    console.warn(`事件上报失败: window.collectEvent 不存在, 事件类型: ${eventType}`)
    addToEventQueue(eventType, fullEventData)
    return false
  }

  const originalWindowOnError = window.onerror
  let reportSuccess = true

  window.onerror = function (message, _source, _lineno, _colno, error) {
    console.error('事件上报失败:', error || message)
    reportSuccess = false
    window.onerror = originalWindowOnError
    return true // 阻止错误继续传播
  }

  // 上报事件
  window.collectEvent(eventType, fullEventData)
  window.onerror = originalWindowOnError

  // 如果上报失败，加入队列
  if (!reportSuccess) {
    addToEventQueue(eventType, fullEventData)
  }

  return reportSuccess
}

// 初始化逻辑
if (typeof window !== 'undefined') {
  // 首次初始化
  const initSuccess = initVolcDataFinder()

  // 初始化失败时，在页面加载完成后重试
  if (!initSuccess) {
    console.log('首次初始化失败，将在页面加载完成后重试')
    // 只在页面完全加载后再尝试重新加载脚本
    window.addEventListener('load', () => {
      // 延迟2秒，确保其他资源已加载
      setTimeout(() => {
        if (!window.collectEvent) {
          console.log('页面加载完成，开始重新初始化火山引擎 DataFinder')
          tryReInitVolcDataFinder()
        }
      }, 2000)
    })
  }
}
