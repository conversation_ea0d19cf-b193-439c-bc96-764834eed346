/**
 * Stripe 延迟加载工具 - 减少对 LCP 的影响
 * 统一管理 Stripe 的加载，避免重复导入
 */

let stripePromise: Promise<any> | null = null
let stripeLoading = false

/**
 * 延迟加载 Stripe SDK
 * 只在用户真正需要付费时才加载
 */
export async function loadStripeSDK(): Promise<any> {
  // 如果已经有 Promise，直接返回
  if (stripePromise) {
    return stripePromise
  }

  // 如果正在加载，等待加载完成
  if (stripeLoading) {
    return new Promise((resolve, reject) => {
      const checkLoading = () => {
        if (stripePromise) {
          resolve(stripePromise)
        } else if (!stripeLoading) {
          reject(new Error('Stripe loading failed'))
        } else {
          setTimeout(checkLoading, 100)
        }
      }
      checkLoading()
    })
  }

  try {
    stripeLoading = true
    console.log('💳 开始加载 Stripe SDK...')

    // 动态导入 Stripe
    const { loadStripe } = await import('@stripe/stripe-js')
    const publishableKey = import.meta.env.VITE_STRIPE_PUBLIC_KEY

    if (!publishableKey) {
      throw new Error('Stripe public key not found')
    }

    // 创建 Stripe 实例
    stripePromise = loadStripe(publishableKey)
    
    console.log('✅ Stripe SDK 加载完成')
    return stripePromise
  } catch (error) {
    console.error('❌ Stripe SDK 加载失败:', error)
    stripePromise = null
    throw error
  } finally {
    stripeLoading = false
  }
}

/**
 * 获取 Stripe 实例
 * 如果还没加载，会自动加载
 */
export async function getStripeInstance(): Promise<any> {
  const stripe = await loadStripeSDK()
  return stripe
}

/**
 * 预加载 Stripe SDK
 * 在用户可能需要付费时提前加载
 */
export function preloadStripe(): void {
  // 使用 requestIdleCallback 在浏览器空闲时预加载
  if ('requestIdleCallback' in window) {
    requestIdleCallback(() => {
      loadStripeSDK().catch(() => {
        // 预加载失败不影响主流程
        console.warn('⚠️ Stripe 预加载失败')
      })
    }, { timeout: 3000 })
  } else {
    setTimeout(() => {
      loadStripeSDK().catch(() => {
        console.warn('⚠️ Stripe 预加载失败')
      })
    }, 2000)
  }
}

/**
 * 检查是否已经加载了 Stripe
 */
export function isStripeLoaded(): boolean {
  return stripePromise !== null
}

/**
 * 清理 Stripe 实例（用于测试或重置）
 */
export function clearStripeInstance(): void {
  stripePromise = null
  stripeLoading = false
}

/**
 * 创建支付会话并重定向到 Stripe
 */
export async function createStripeCheckout(options: {
  priceId: string
  successUrl: string
  cancelUrl: string
}): Promise<void> {
  try {
    // 动态导入支付 API
    const { createCheckoutSession } = await import('@/api/payment')
    
    // 创建结账会话
    const result = await createCheckoutSession({
      price_id: options.priceId,
      success_url: options.successUrl,
      cancel_url: options.cancelUrl
    })

    if (result.code !== '0' || !result.data?.session_id) {
      throw new Error(result.message || 'Failed to create payment session')
    }

    // 获取 Stripe 实例
    const stripe = await getStripeInstance()
    
    if (!stripe) {
      throw new Error('Failed to load Stripe')
    }

    // 重定向到 Stripe 结账页面
    const { error } = await stripe.redirectToCheckout({
      sessionId: result.data.session_id
    })

    if (error) {
      throw error
    }
  } catch (error) {
    console.error('Stripe checkout failed:', error)
    throw error
  }
}

// 导出类型定义
export interface StripeCheckoutOptions {
  priceId: string
  successUrl: string
  cancelUrl: string
}
