/**
 * 滚动性能监控工具
 * 用于检测和分析虚拟滚动的性能问题
 */

interface PerformanceMetrics {
  fps: number
  frameTime: number
  scrollEvents: number
  renderTime: number
  memoryUsage: number
  isSmooth: boolean
}

interface ScrollEvent {
  timestamp: number
  scrollTop: number
  deltaY: number
  fps: number
}

class ScrollPerformanceMonitor {
  private isMonitoring = false
  private metrics: PerformanceMetrics = {
    fps: 0,
    frameTime: 0,
    scrollEvents: 0,
    renderTime: 0,
    memoryUsage: 0,
    isSmooth: true
  }

  private scrollEvents: ScrollEvent[] = []
  private lastFrameTime = 0
  private frameCount = 0
  private animationFrameId: number | null = null
  private startTime = 0
  private lastScrollTime = 0
  private scrollEventCount = 0

  /**
   * 开始监控滚动性能
   */
  startMonitoring(container?: HTMLElement): void {
    if (this.isMonitoring) return

    this.isMonitoring = true
    this.startTime = performance.now()
    this.frameCount = 0
    this.scrollEventCount = 0
    this.scrollEvents = []

    // 监控帧率
    this.monitorFrameRate()

    // 监控滚动事件
    if (container) {
      this.monitorScrollEvents(container)
    }

    console.log('🚀 滚动性能监控已启动')
  }

  /**
   * 停止监控
   */
  stopMonitoring(): PerformanceMetrics {
    if (!this.isMonitoring) return this.metrics

    this.isMonitoring = false

    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId)
      this.animationFrameId = null
    }

    // 计算最终指标
    const totalTime = performance.now() - this.startTime
    this.metrics.scrollEvents = this.scrollEventCount
    this.metrics.renderTime = totalTime

    // 获取内存使用情况
    if ('memory' in performance) {
      // @ts-ignore
      this.metrics.memoryUsage = performance.memory.usedJSHeapSize / 1024 / 1024 // MB
    }

    console.log('📊 滚动性能监控结果:', this.metrics)
    return this.metrics
  }

  /**
   * 获取当前性能指标
   */
  getCurrentMetrics(): PerformanceMetrics {
    return { ...this.metrics }
  }

  /**
   * 获取滚动事件历史
   */
  getScrollHistory(): ScrollEvent[] {
    return [...this.scrollEvents]
  }

  /**
   * 分析性能问题
   */
  analyzePerformance(): string[] {
    const issues: string[] = []

    if (this.metrics.fps < 30) {
      issues.push('严重性能问题：FPS 低于 30，滚动非常卡顿')
    } else if (this.metrics.fps < 45) {
      issues.push('性能问题：FPS 低于 45，滚动有些卡顿')
    } else if (this.metrics.fps < 55) {
      issues.push('轻微性能问题：FPS 低于 55，可能偶尔卡顿')
    }

    if (this.metrics.frameTime > 33) {
      issues.push('帧时间过长：单帧渲染时间超过 33ms')
    }

    if (this.metrics.scrollEvents > 100) {
      issues.push('滚动事件过多：可能需要节流优化')
    }

    if (this.metrics.memoryUsage > 100) {
      issues.push('内存使用过高：超过 100MB，可能存在内存泄漏')
    }

    if (issues.length === 0) {
      issues.push('性能良好：未发现明显问题')
    }

    return issues
  }

  /**
   * 获取优化建议
   */
  getOptimizationSuggestions(): string[] {
    const suggestions: string[] = []

    if (this.metrics.fps < 45) {
      suggestions.push('减少 overscan 值，降低预渲染数量')
      suggestions.push('禁用复杂的 hover 动画效果')
      suggestions.push('使用 CSS transform 代替改变 position')
    }

    if (this.metrics.frameTime > 25) {
      suggestions.push('优化组件渲染逻辑，减少重排重绘')
      suggestions.push('使用 CSS containment 属性')
      suggestions.push('启用硬件加速 (transform: translateZ(0))')
    }

    if (this.metrics.scrollEvents > 50) {
      suggestions.push('添加滚动事件节流')
      suggestions.push('使用 passive 事件监听器')
    }

    if (this.metrics.memoryUsage > 50) {
      suggestions.push('检查是否存在内存泄漏')
      suggestions.push('及时清理不需要的事件监听器')
      suggestions.push('优化图片加载和缓存策略')
    }

    return suggestions
  }

  /**
   * 监控帧率
   */
  private monitorFrameRate(): void {
    const measureFrame = (currentTime: number) => {
      if (this.lastFrameTime > 0) {
        const deltaTime = currentTime - this.lastFrameTime
        this.metrics.frameTime = deltaTime
        this.metrics.fps = 1000 / deltaTime
        this.metrics.isSmooth = this.metrics.fps > 50
        this.frameCount++
      }

      this.lastFrameTime = currentTime

      if (this.isMonitoring) {
        this.animationFrameId = requestAnimationFrame(measureFrame)
      }
    }

    this.animationFrameId = requestAnimationFrame(measureFrame)
  }

  /**
   * 监控滚动事件
   */
  private monitorScrollEvents(container: HTMLElement): void {
    let lastScrollTop = 0

    const handleScroll = (event: Event) => {
      const currentTime = performance.now()
      const target = event.target as HTMLElement
      const scrollTop = target.scrollTop
      const deltaY = scrollTop - lastScrollTop

      this.scrollEventCount++

      // 记录滚动事件（最多保留最近100个）
      if (this.scrollEvents.length >= 100) {
        this.scrollEvents.shift()
      }

      this.scrollEvents.push({
        timestamp: currentTime,
        scrollTop,
        deltaY,
        fps: this.metrics.fps
      })

      lastScrollTop = scrollTop
      this.lastScrollTime = currentTime
    }

    container.addEventListener('scroll', handleScroll, { passive: true })

    // 清理函数
    const cleanup = () => {
      container.removeEventListener('scroll', handleScroll)
    }

    // 存储清理函数以便后续调用
    ;(this as any).cleanup = cleanup
  }

  /**
   * 生成性能报告
   */
  generateReport(): string {
    const metrics = this.getCurrentMetrics()
    const issues = this.analyzePerformance()
    const suggestions = this.getOptimizationSuggestions()

    return `
📊 滚动性能报告
================

📈 性能指标:
- FPS: ${metrics.fps.toFixed(1)}
- 帧时间: ${metrics.frameTime.toFixed(1)}ms
- 滚动事件数: ${metrics.scrollEvents}
- 渲染时间: ${metrics.renderTime.toFixed(1)}ms
- 内存使用: ${metrics.memoryUsage.toFixed(1)}MB
- 是否流畅: ${metrics.isSmooth ? '是' : '否'}

⚠️ 发现的问题:
${issues.map((issue) => `- ${issue}`).join('\n')}

💡 优化建议:
${suggestions.map((suggestion) => `- ${suggestion}`).join('\n')}
    `.trim()
  }
}

// 创建全局实例
export const scrollPerformanceMonitor = new ScrollPerformanceMonitor()

// 开发环境下自动启用性能监控
// if (import.meta.env.DEV) {
//   // 延迟启动，避免影响初始加载
//   setTimeout(() => {
//     const virtualScrollContainer = document.querySelector('.virtual-story-grid-pc, .virtual-story-grid-mobile') as HTMLElement
//     if (virtualScrollContainer) {
//       scrollPerformanceMonitor.startMonitoring(virtualScrollContainer)

//       // 10秒后输出报告
//       setTimeout(() => {
//         console.log(scrollPerformanceMonitor.generateReport())
//       }, 10000)
//     }
//   }, 2000)
// }

export default scrollPerformanceMonitor
