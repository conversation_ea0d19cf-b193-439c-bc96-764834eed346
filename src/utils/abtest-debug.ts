/**
 * AB测试调试工具
 * 用于开发环境下测试AB测试功能
 */

import { useABTestStore } from '@/store/abtest'
import { getExperimentConfig } from '@/config/abtest'

/**
 * 调试AB测试配置
 */
export function debugABTest() {
  if (!import.meta.env.DEV) {
    console.warn('AB测试调试工具只能在开发环境下使用')
    return
  }

  const abtestStore = useABTestStore()
  
  console.group('🧪 AB测试调试信息')
  
  // 显示所有实验配置
  console.log('📋 实验配置:')
  console.table({
    story_tags_display: getExperimentConfig('story_tags_display'),
    mobile_default_theme: getExperimentConfig('mobile_default_theme'),
    story_card_design: getExperimentConfig('story_card_design')
  })
  
  // 显示当前用户的分配结果
  console.log('🎯 当前用户分配结果:')
  console.table({
    story_tags_display: abtestStore.storyTagsDisplay,
    mobile_default_theme: abtestStore.mobileDefaultTheme,
    story_card_design: abtestStore.storyCardDesign
  })
  
  // 显示实验变体缓存
  console.log('💾 实验变体缓存:')
  console.log(abtestStore.experimentVariants)
  
  console.groupEnd()
}

/**
 * 强制设置实验变体（仅开发环境）
 */
export function forceABTestVariant(experimentName: string, variant: 'A' | 'B' | 'C' | 'D') {
  if (!import.meta.env.DEV) {
    console.warn('强制设置AB测试变体只能在开发环境下使用')
    return
  }

  const abtestStore = useABTestStore()
  
  // 直接修改缓存
  abtestStore.experimentVariants[experimentName] = variant
  
  console.log(`🔧 已强制设置实验 "${experimentName}" 为变体 "${variant}"`)
  
  // 刷新页面以应用更改
  if (confirm('是否刷新页面以应用更改？')) {
    window.location.reload()
  }
}

/**
 * 重置所有AB测试分配
 */
export function resetAllABTests() {
  if (!import.meta.env.DEV) {
    console.warn('重置AB测试只能在开发环境下使用')
    return
  }

  // 清除localStorage
  localStorage.removeItem('abtest_assignments')
  
  console.log('🔄 已重置所有AB测试分配')
  
  // 刷新页面
  if (confirm('是否刷新页面以重新分配？')) {
    window.location.reload()
  }
}

// 在开发环境下挂载到window对象
if (import.meta.env.DEV) {
  const windowAny = window as any
  windowAny.__abtestDebug = {
    debug: debugABTest,
    forceVariant: forceABTestVariant,
    resetAll: resetAllABTests
  }
  
  console.log('🧪 AB测试调试工具已挂载到 window.__abtestDebug')
  console.log('使用方法:')
  console.log('- window.__abtestDebug.debug() - 查看调试信息')
  console.log('- window.__abtestDebug.forceVariant("story_card_design", "B") - 强制设置变体')
  console.log('- window.__abtestDebug.resetAll() - 重置所有分配')
}
