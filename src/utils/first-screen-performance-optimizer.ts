/**
 * 首屏性能优化工具
 * 专注于提升首屏渲染速度和用户体验
 * 与 useFirstScreenLoading 配合使用，提供额外的性能优化
 */

class FirstScreenPerformanceOptimizer {
  private startTime = performance.now()
  private imageObserver?: MutationObserver
  private lazyObserver?: IntersectionObserver

  constructor() {
    this.optimizeImages()
    this.preloadCriticalResources()
    this.optimizeThirdPartyScripts()
  }

  /**
   * 优化图片加载
   */
  private optimizeImages(): void {
    // 为所有图片添加 loading="lazy" 和 decoding="async"
    const images = document.querySelectorAll('img:not([loading])')
    images.forEach((img) => {
      img.setAttribute('loading', 'lazy')
      img.setAttribute('decoding', 'async')
    })

    // 监听新添加的图片
    if ('MutationObserver' in window) {
      this.imageObserver = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element
              const newImages = element.querySelectorAll('img:not([loading])')
              newImages.forEach((img) => {
                img.setAttribute('loading', 'lazy')
                img.setAttribute('decoding', 'async')
              })
            }
          })
        })
      })
      this.imageObserver.observe(document.body, { childList: true, subtree: true })
    }
  }

  /**
   * 预加载关键资源
   */
  private preloadCriticalResources(): void {
    const criticalResources = [
      // 关键图片（根据实际需要调整）
      'https://cdn.magiclight.ai/assets/playshot/logo-v2.png'
    ]

    criticalResources.forEach((url) => {
      const link = document.createElement('link')
      link.rel = 'prefetch'
      link.href = url
      if (url.match(/\.(png|jpg|jpeg|webp)$/)) {
        link.as = 'image'
      }
      document.head.appendChild(link)
    })

    // 字体已经通过 Google Fonts 优化，不需要额外预加载
    console.log('✅ 关键资源预加载完成 (字体已通过 Google Fonts 优化)')
  }

  /**
   * 优化关键渲染路径
   */
  optimizeCriticalRenderingPath(): void {
    // 移除阻塞渲染的资源
    const blockingScripts = document.querySelectorAll(
      'script:not([async]):not([defer])'
    ) as NodeListOf<HTMLScriptElement>
    blockingScripts.forEach((script) => {
      if (!script.src.includes('main.ts')) {
        script.setAttribute('defer', '')
      }
    })

    // 优化 CSS 加载 - 减少首屏CSS文件数量
    const stylesheets = document.querySelectorAll('link[rel="stylesheet"]')
    stylesheets.forEach((link, index) => {
      if (index > 1) {
        // 只保留前2个关键样式表同步加载，其他延迟加载
        link.setAttribute('media', 'print')
        link.addEventListener('load', () => {
          link.setAttribute('media', 'all')
        })
      }
    })

    // 优化modulepreload - 减少首屏预加载的JS文件数量
    this.optimizeModulePreloads()
  }

  /**
   * 优化模块预加载，减少首屏HTTP请求数量
   */
  private optimizeModulePreloads(): void {
    const modulePreloads = document.querySelectorAll('link[rel="modulepreload"]')
    const criticalModules = ['critical-vendor', 'shared-components', 'page-stories']

    modulePreloads.forEach((link) => {
      const href = link.getAttribute('href') || ''
      const isCritical = criticalModules.some((module) => href.includes(module))

      if (!isCritical && modulePreloads.length > 6) {
        // 如果预加载模块过多，移除非关键模块的预加载
        link.remove()
        console.log('🔧 移除非关键模块预加载:', href)
      }
    })

    console.log(
      `✅ 模块预加载优化完成，保留 ${
        document.querySelectorAll('link[rel="modulepreload"]').length
      } 个关键模块`
    )
  }

  /**
   * 启用首屏渐进式加载
   */
  enableProgressiveLoading(): void {
    // 检查是否为移动端
    const isMobile = window.innerWidth <= 768

    // 延迟一点时间确保 DOM 完全渲染
    setTimeout(
      () => {
        // 为首屏元素添加渐入动画
        const firstScreenElements = document.querySelectorAll('.stories-page, .pc-layout')
        firstScreenElements.forEach((element) => {
          element.classList.add('loaded')
          console.log('✅ 添加 loaded 类到:', element.className)
        })

        // 移动端立即显示内容，不等待加载完成
        if (isMobile) {
          const mobileContent = document.querySelector('.app-mobile') as HTMLElement
          if (mobileContent) {
            mobileContent.style.opacity = '1'
            console.log('📱 移动端内容立即显示')
          }
        }
      },
      isMobile ? 0 : 100
    ) // 移动端立即执行，PC端延迟100ms

    // 延迟加载非首屏内容
    this.setupLazyLoading()
  }

  /**
   * 设置延迟加载
   */
  private setupLazyLoading(): void {
    if ('IntersectionObserver' in window) {
      this.lazyObserver = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const element = entry.target as HTMLElement
            element.style.opacity = '1'
            element.style.transform = 'translateY(0)'
            this.lazyObserver?.unobserve(element)
          }
        })
      })

      // 观察非首屏元素
      const belowFoldElements = document.querySelectorAll('[data-lazy-load]')
      belowFoldElements.forEach((element) => {
        this.lazyObserver?.observe(element)
      })
    }
  }

  /**
   * 分析分包情况，检测是否存在分包过细的问题
   */
  analyzeChunkStrategy(): void {
    if ('performance' in window && performance.getEntriesByType) {
      const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[]

      // 分析JS文件
      const jsResources = resources.filter(
        (r) => r.name.includes('.js') && !r.name.includes('legacy')
      )
      const cssResources = resources.filter((r) => r.name.includes('.css'))

      console.log('\n📊 分包分析报告:')
      console.log(`JS文件数量: ${jsResources.length}`)
      console.log(`CSS文件数量: ${cssResources.length}`)

      // 检查小文件数量
      const smallJsFiles = jsResources.filter((r) => r.transferSize < 5120) // 小于5KB
      const smallCssFiles = cssResources.filter((r) => r.transferSize < 2048) // 小于2KB

      if (smallJsFiles.length > 5) {
        console.log(`⚠️  发现 ${smallJsFiles.length} 个小于5KB的JS文件，建议合并`)
        smallJsFiles.forEach((file) => {
          console.log(`   - ${file.name}: ${(file.transferSize / 1024).toFixed(2)}KB`)
        })
      }

      if (smallCssFiles.length > 3) {
        console.log(`⚠️  发现 ${smallCssFiles.length} 个小于2KB的CSS文件，建议合并`)
      }

      // 计算首屏关键资源大小
      const criticalResources = jsResources.filter(
        (r) =>
          r.name.includes('critical-vendor') ||
          r.name.includes('shared-components') ||
          r.name.includes('page-stories')
      )

      const criticalSize = criticalResources.reduce((sum, r) => sum + r.transferSize, 0)
      console.log(`🚀 首屏关键资源总大小: ${(criticalSize / 1024).toFixed(2)}KB`)

      if (criticalSize > 200 * 1024) {
        console.log('⚠️  首屏关键资源过大，建议进一步优化')
      } else {
        console.log('✅ 首屏关键资源大小合理')
      }
    }
  }

  /**
   * 获取性能建议
   */
  getPerformanceRecommendations(): string[] {
    const recommendations: string[] = []
    const loadTime = performance.now() - this.startTime

    if (loadTime > 3000) {
      recommendations.push('首屏加载时间过长，建议优化关键资源')
    }

    // 检查图片优化
    const unoptimizedImages = document.querySelectorAll('img:not([loading])')
    if (unoptimizedImages.length > 0) {
      recommendations.push(`发现 ${unoptimizedImages.length} 个未优化的图片`)
    }

    // 检查字体加载
    if (!document.fonts) {
      recommendations.push('浏览器不支持字体 API，建议使用字体预加载')
    }

    // 检查阻塞脚本
    const blockingScripts = document.querySelectorAll('script:not([async]):not([defer])')
    if (blockingScripts.length > 3) {
      recommendations.push(`发现 ${blockingScripts.length} 个阻塞脚本，建议添加 async 或 defer`)
    }

    // 检查分包策略
    const modulePreloads = document.querySelectorAll('link[rel="modulepreload"]')
    const stylesheets = document.querySelectorAll('link[rel="stylesheet"]')

    if (modulePreloads.length > 8) {
      recommendations.push(`发现 ${modulePreloads.length} 个预加载模块，建议减少分包数量`)
    }

    if (stylesheets.length > 5) {
      recommendations.push(`发现 ${stylesheets.length} 个CSS文件，建议合并CSS以减少HTTP请求`)
    }

    // 检查大型资源
    if ('performance' in window && performance.getEntriesByType) {
      const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[]
      const largeResources = resources.filter((r) => r.transferSize > 500000) // 500KB
      if (largeResources.length > 0) {
        recommendations.push(`发现 ${largeResources.length} 个大型资源 (>500KB)，建议压缩或分割`)
      }
    }

    return recommendations
  }

  /**
   * 优化第三方脚本
   */
  optimizeThirdPartyScripts(): void {
    // 延迟加载非关键的第三方脚本
    const thirdPartyScripts = document.querySelectorAll(
      'script[src*="google"], script[src*="facebook"], script[src*="analytics"]'
    )
    thirdPartyScripts.forEach((script) => {
      if (!script.hasAttribute('defer') && !script.hasAttribute('async')) {
        script.setAttribute('defer', '')
      }
    })
  }

  /**
   * 清理资源
   */
  destroy(): void {
    this.imageObserver?.disconnect()
    this.lazyObserver?.disconnect()
  }
}

// 创建全局实例
export const firstScreenPerformanceOptimizer = new FirstScreenPerformanceOptimizer()

// 将实例挂载到全局对象上
if (typeof window !== 'undefined') {
  // @ts-ignore
  window.firstScreenPerformanceOptimizer = firstScreenPerformanceOptimizer
}

// 在页面加载完成后启用优化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    firstScreenPerformanceOptimizer.optimizeCriticalRenderingPath()
    firstScreenPerformanceOptimizer.enableProgressiveLoading()
  })
} else {
  firstScreenPerformanceOptimizer.optimizeCriticalRenderingPath()
  firstScreenPerformanceOptimizer.enableProgressiveLoading()
}

// 在页面完全加载后输出性能建议和分包分析
// window.addEventListener('load', () => {
//   setTimeout(() => {
//     // 分析分包策略
//     firstScreenPerformanceOptimizer.analyzeChunkStrategy()

//     // 获取性能建议
//     const recommendations = firstScreenPerformanceOptimizer.getPerformanceRecommendations()
//     if (recommendations.length > 0) {
//       console.log('\n💡 性能优化建议:', recommendations)
//     } else {
//       console.log('\n✅ 当前性能表现良好，无需额外优化')
//     }
//   }, 1000)
// })

export default firstScreenPerformanceOptimizer
