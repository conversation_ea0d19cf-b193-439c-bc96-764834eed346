/**
 * PWA 相关工具函数
 */

/**
 * 检查是否为PWA模式
 */
export function isPWAMode(): boolean {
  return window.matchMedia && window.matchMedia('(display-mode: standalone)').matches
}

/**
 * 检查是否为iOS设备
 */
export function isIOSDevice(): boolean {
  return /iPad|iPhone|iPod/.test(navigator.userAgent)
}

/**
 * 检查是否为Android设备
 */
export function isAndroidDevice(): boolean {
  return /Android/.test(navigator.userAgent)
}

/**
 * 检查是否支持PWA安装
 */
export function supportsPWAInstall(): boolean {
  return 'serviceWorker' in navigator && 'BeforeInstallPromptEvent' in window
}

/**
 * 检查是否应该显示PWA安装提示
 */
export function shouldShowPWAPrompt(): boolean {
  // 如果已经是PWA模式，不显示
  if (isPWAMode()) {
    return false
  }

  // 检查用户是否最近拒绝过
  const dismissed = localStorage.getItem('pwa-install-dismissed')
  if (dismissed) {
    const dismissedTime = parseInt(dismissed)
    const daysSinceDismissed = (Date.now() - dismissedTime) / (1000 * 60 * 60 * 24)
    if (daysSinceDismissed < 7) { // 7天内不再显示
      return false
    }
  }

  // 检查安装次数限制
  const installPromptCount = parseInt(localStorage.getItem('pwa-install-prompt-count') || '0')
  if (installPromptCount >= 3) { // 最多显示3次
    return false
  }

  return true
}

/**
 * 记录PWA安装提示显示次数
 */
export function incrementPWAPromptCount(): void {
  const count = parseInt(localStorage.getItem('pwa-install-prompt-count') || '0')
  localStorage.setItem('pwa-install-prompt-count', (count + 1).toString())
}

/**
 * 记录用户拒绝PWA安装
 */
export function recordPWADismissal(): void {
  localStorage.setItem('pwa-install-dismissed', Date.now().toString())
  incrementPWAPromptCount()
}

/**
 * 获取PWA安装指导文本（针对不同设备）
 */
export function getPWAInstallInstructions(): string {
  if (isIOSDevice()) {
    return 'Tap the share button and select "Add to Home Screen"'
  } else if (isAndroidDevice()) {
    return 'Tap the menu button and select "Add to Home Screen" or "Install App"'
  } else {
    return 'Click the install button in your browser\'s address bar'
  }
}

/**
 * 检查PWA更新
 */
export function checkPWAUpdate(): Promise<boolean> {
  return new Promise((resolve) => {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.getRegistration().then((registration) => {
        if (registration) {
          registration.update().then(() => {
            resolve(true)
          }).catch(() => {
            resolve(false)
          })
        } else {
          resolve(false)
        }
      }).catch(() => {
        resolve(false)
      })
    } else {
      resolve(false)
    }
  })
}

/**
 * PWA性能监控
 */
export function trackPWAMetrics(): void {
  if (isPWAMode()) {
    // 记录PWA启动时间
    const startTime = performance.now()
    
    window.addEventListener('load', () => {
      const loadTime = performance.now() - startTime
      console.log(`PWA Load Time: ${loadTime}ms`)
      
      // 可以发送到分析服务
      if (window.gtag) {
        window.gtag('event', 'pwa_load_time', {
          event_category: 'PWA',
          value: Math.round(loadTime)
        })
      }
    })

    // 记录PWA使用情况
    if (window.gtag) {
      window.gtag('event', 'pwa_usage', {
        event_category: 'PWA',
        event_label: 'standalone_mode'
      })
    }
  }
}
