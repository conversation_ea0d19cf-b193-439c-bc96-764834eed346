/**
 * 缓存测试工具 - 用于验证缓存清理和版本更新机制
 */

export class CacheTestUtils {
  /**
   * 测试缓存清理功能
   */
  static async testCacheClear(): Promise<void> {
    console.log('🧪 开始测试缓存清理功能...')

    try {
      // 检查 Service Worker 状态
      if ('serviceWorker' in navigator) {
        const registration = await navigator.serviceWorker.ready
        console.log('✅ Service Worker 已就绪:', registration.scope)

        // 列出当前所有缓存
        if ('caches' in window) {
          const cacheNames = await caches.keys()
          console.log('📦 当前缓存列表:', cacheNames)

          // 测试创建一个临时缓存
          const testCache = await caches.open('test-cache-' + Date.now())
          await testCache.put('/test', new Response('test data'))
          console.log('✅ 创建测试缓存成功')

          // 测试清理功能
          const messageChannel = new MessageChannel()
          const clearPromise = new Promise<boolean>((resolve) => {
            messageChannel.port1.onmessage = (event) => {
              resolve(event.data.success)
            }
            setTimeout(() => resolve(false), 5000)
          })

          registration.active?.postMessage({ type: 'CLEAR_CACHE' }, [messageChannel.port2])

          const success = await clearPromise
          console.log(success ? '✅ 缓存清理测试成功' : '❌ 缓存清理测试失败')

          // 验证缓存是否被清理
          const newCacheNames = await caches.keys()
          console.log('📦 清理后缓存列表:', newCacheNames)
        }
      } else {
        console.log('❌ Service Worker 不支持')
      }
    } catch (error) {
      console.error('❌ 缓存测试失败:', error)
    }
  }

  /**
   * 测试版本检查功能
   */
  static async testVersionCheck(): Promise<void> {
    console.log('🧪 开始测试版本检查功能...')

    try {
      // 模拟获取版本信息
      const response = await fetch('/version.json?t=' + Date.now(), {
        cache: 'no-cache'
      })

      if (response.ok) {
        const versionInfo = await response.json()
        console.log('✅ 版本信息获取成功:', versionInfo)

        // 检查本地存储的版本
        const storedVersion = localStorage.getItem('app_version_info')
        if (storedVersion) {
          const parsed = JSON.parse(storedVersion)
          console.log('📱 本地存储版本:', parsed)

          // 比较版本
          if (
            parsed.version !== versionInfo.version ||
            parsed.buildTime !== versionInfo.buildTime
          ) {
            console.log('🆕 检测到版本差异，需要更新')
          } else {
            console.log('✅ 版本一致，无需更新')
          }
        } else {
          console.log('📱 本地无版本信息，首次访问')
        }
      } else {
        console.log('❌ 版本信息获取失败:', response.status)
      }
    } catch (error) {
      console.error('❌ 版本检查测试失败:', error)
    }
  }

  /**
   * 模拟版本更新流程
   */
  static async simulateVersionUpdate(): Promise<void> {
    console.log('🧪 开始模拟版本更新流程...')

    try {
      // 1. 清理当前缓存
      console.log('1️⃣ 清理当前缓存...')
      await this.testCacheClear()

      // 2. 更新版本信息
      console.log('2️⃣ 更新版本信息...')
      const newVersionInfo = {
        version: '1.3.1-test',
        buildTime: Date.now(),
        timestamp: Date.now()
      }
      localStorage.setItem('app_version_info', JSON.stringify(newVersionInfo))

      // 3. 验证更新
      console.log('3️⃣ 验证更新...')
      await this.testVersionCheck()

      console.log('✅ 版本更新流程模拟完成')
    } catch (error) {
      console.error('❌ 版本更新流程模拟失败:', error)
    }
  }

  /**
   * 获取缓存统计信息
   */
  static async getCacheStats(): Promise<void> {
    console.log('📊 获取缓存统计信息...')

    try {
      if ('caches' in window) {
        const cacheNames = await caches.keys()
        console.log(`📦 总缓存数量: ${cacheNames.length}`)

        for (const cacheName of cacheNames) {
          const cache = await caches.open(cacheName)
          const keys = await cache.keys()
          console.log(`📦 ${cacheName}: ${keys.length} 个条目`)

          // 显示前5个条目
          const sampleKeys = keys.slice(0, 5).map((req) => req.url)
          if (sampleKeys.length > 0) {
            console.log(`   示例: ${sampleKeys.join(', ')}`)
          }
        }
      }

      // 显示 localStorage 信息
      const versionInfo = localStorage.getItem('app_version_info')
      if (versionInfo) {
        console.log('📱 本地版本信息:', JSON.parse(versionInfo))
      }
    } catch (error) {
      console.error('❌ 获取缓存统计失败:', error)
    }
  }
}

// 在开发环境下暴露到全局，方便调试
// if (import.meta.env.DEV) {
//   (window as any).CacheTestUtils = CacheTestUtils
//   console.log('🛠️ CacheTestUtils 已暴露到全局，可在控制台使用')
//   console.log('使用方法:')
//   console.log('- CacheTestUtils.testCacheClear() // 测试缓存清理')
//   console.log('- CacheTestUtils.testVersionCheck() // 测试版本检查')
//   console.log('- CacheTestUtils.simulateVersionUpdate() // 模拟版本更新')
//   console.log('- CacheTestUtils.getCacheStats() // 获取缓存统计')
// }
