import { 
  captureSentryException, 
  captureSentryMessage, 
  addSentryBreadcrumb, 
  setSentryContext,
  isSentryEnabled 
} from './sentry'
import { reportEvent } from './report'
import { ReportEvent } from '@/interface'

/**
 * 错误类型枚举
 */
export enum ErrorType {
  API_ERROR = 'api_error',
  NETWORK_ERROR = 'network_error',
  JAVASCRIPT_ERROR = 'javascript_error',
  RESOURCE_ERROR = 'resource_error',
  PROMISE_REJECTION = 'promise_rejection',
  CHAT_ERROR = 'chat_error',
  PAYMENT_ERROR = 'payment_error',
  AUTH_ERROR = 'auth_error'
}

/**
 * 错误上下文接口
 */
interface ErrorContext {
  url?: string
  method?: string
  status?: number
  duration?: number
  route?: string
  userId?: string
  userAgent?: string
  timestamp?: number
  [key: string]: any
}

/**
 * 统一错误处理类
 */
export class ErrorHandler {
  /**
   * 处理 API 错误
   */
  static handleApiError(error: Error, context: ErrorContext = {}) {
    const errorContext = {
      ...context,
      errorType: ErrorType.API_ERROR,
      timestamp: Date.now(),
      route: window.location.pathname,
      userAgent: navigator.userAgent
    }

    // 添加面包屑
    if (isSentryEnabled()) {
      addSentryBreadcrumb(
        `API Error: ${error.message}`,
        'api',
        'error',
        errorContext
      )
      
      // 设置错误上下文
      setSentryContext('api_error', errorContext)
      
      // 捕获异常
      captureSentryException(error, errorContext)
    }

    // 上报到火山引擎
    reportEvent(ReportEvent.ApiError, errorContext)

    console.error('API Error:', error, errorContext)
  }

  /**
   * 处理网络错误
   */
  static handleNetworkError(error: Error, context: ErrorContext = {}) {
    const errorContext = {
      ...context,
      errorType: ErrorType.NETWORK_ERROR,
      timestamp: Date.now(),
      route: window.location.pathname
    }

    if (isSentryEnabled()) {
      addSentryBreadcrumb(
        `Network Error: ${error.message}`,
        'network',
        'error',
        errorContext
      )
      captureSentryException(error, errorContext)
    }

    reportEvent(ReportEvent.ApiUnknownError, errorContext)
    console.error('Network Error:', error, errorContext)
  }

  /**
   * 处理 JavaScript 运行时错误
   */
  static handleJavaScriptError(error: Error, context: ErrorContext = {}) {
    const errorContext = {
      ...context,
      errorType: ErrorType.JAVASCRIPT_ERROR,
      timestamp: Date.now(),
      route: window.location.pathname,
      stack: error.stack
    }

    if (isSentryEnabled()) {
      addSentryBreadcrumb(
        `JavaScript Error: ${error.message}`,
        'javascript',
        'error',
        errorContext
      )
      captureSentryException(error, errorContext)
    }

    // 对于严重的 JS 错误，也上报到火山引擎
    reportEvent(ReportEvent.ApiUnknownError, {
      ...errorContext,
      message: error.message,
      stack: error.stack
    })

    console.error('JavaScript Error:', error, errorContext)
  }

  /**
   * 处理聊天相关错误
   */
  static handleChatError(error: Error, context: ErrorContext = {}) {
    const errorContext = {
      ...context,
      errorType: ErrorType.CHAT_ERROR,
      timestamp: Date.now(),
      route: window.location.pathname
    }

    if (isSentryEnabled()) {
      addSentryBreadcrumb(
        `Chat Error: ${error.message}`,
        'chat',
        'error',
        errorContext
      )
      setSentryContext('chat_error', errorContext)
      captureSentryException(error, errorContext)
    }

    console.error('Chat Error:', error, errorContext)
  }

  /**
   * 处理支付相关错误
   */
  static handlePaymentError(error: Error, context: ErrorContext = {}) {
    const errorContext = {
      ...context,
      errorType: ErrorType.PAYMENT_ERROR,
      timestamp: Date.now(),
      route: window.location.pathname
    }

    if (isSentryEnabled()) {
      addSentryBreadcrumb(
        `Payment Error: ${error.message}`,
        'payment',
        'error',
        errorContext
      )
      setSentryContext('payment_error', errorContext)
      captureSentryException(error, errorContext)
    }

    // 支付错误比较重要，也上报到火山引擎
    reportEvent(ReportEvent.ApiError, errorContext)
    console.error('Payment Error:', error, errorContext)
  }

  /**
   * 处理认证相关错误
   */
  static handleAuthError(error: Error, context: ErrorContext = {}) {
    const errorContext = {
      ...context,
      errorType: ErrorType.AUTH_ERROR,
      timestamp: Date.now(),
      route: window.location.pathname
    }

    if (isSentryEnabled()) {
      addSentryBreadcrumb(
        `Auth Error: ${error.message}`,
        'auth',
        'error',
        errorContext
      )
      setSentryContext('auth_error', errorContext)
      captureSentryException(error, errorContext)
    }

    console.error('Auth Error:', error, errorContext)
  }

  /**
   * 处理 Promise 拒绝
   */
  static handlePromiseRejection(reason: any, context: ErrorContext = {}) {
    const error = reason instanceof Error ? reason : new Error(String(reason))
    const errorContext = {
      ...context,
      errorType: ErrorType.PROMISE_REJECTION,
      timestamp: Date.now(),
      route: window.location.pathname,
      reason: String(reason)
    }

    if (isSentryEnabled()) {
      addSentryBreadcrumb(
        `Promise Rejection: ${error.message}`,
        'promise',
        'error',
        errorContext
      )
      captureSentryException(error, errorContext)
    }

    console.error('Promise Rejection:', reason, errorContext)
  }

  /**
   * 记录信息性消息
   */
  static logInfo(message: string, context: Record<string, any> = {}) {
    if (isSentryEnabled()) {
      addSentryBreadcrumb(message, 'info', 'info', context)
    }
    console.log(message, context)
  }

  /**
   * 记录警告消息
   */
  static logWarning(message: string, context: Record<string, any> = {}) {
    if (isSentryEnabled()) {
      addSentryBreadcrumb(message, 'warning', 'warning', context)
      captureSentryMessage(message, 'warning', context)
    }
    console.warn(message, context)
  }

  /**
   * 记录调试信息
   */
  static logDebug(message: string, context: Record<string, any> = {}) {
    if (isSentryEnabled()) {
      addSentryBreadcrumb(message, 'debug', 'debug', context)
    }
    console.debug(message, context)
  }
}

/**
 * 全局错误处理器设置
 */
export function setupGlobalErrorHandlers() {
  // 捕获未处理的 JavaScript 错误
  window.addEventListener('error', (event) => {
    ErrorHandler.handleJavaScriptError(
      new Error(event.message),
      {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        source: 'window.error'
      }
    )
  })

  // 捕获未处理的 Promise 拒绝
  window.addEventListener('unhandledrejection', (event) => {
    ErrorHandler.handlePromiseRejection(event.reason, {
      source: 'unhandledrejection'
    })
  })

  // 捕获资源加载错误
  window.addEventListener('error', (event) => {
    if (event.target && event.target !== window) {
      const target = event.target as HTMLElement
      ErrorHandler.logWarning('Resource loading failed', {
        errorType: ErrorType.RESOURCE_ERROR,
        tagName: target.tagName,
        src: (target as any).src || (target as any).href,
        route: window.location.pathname
      })
    }
  }, true)
}
