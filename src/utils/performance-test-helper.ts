/**
 * 性能测试辅助工具
 * 用于验证首屏加载优化效果
 */

export class PerformanceTestHelper {
  private startTime = performance.now()
  private checkpoints: { name: string; time: number }[] = []

  /**
   * 添加检查点
   */
  checkpoint(name: string): void {
    const time = performance.now() - this.startTime
    this.checkpoints.push({ name, time })
    console.log(`⏱️ ${name}: ${time.toFixed(2)}ms`)
  }

  /**
   * 获取性能报告
   */
  getReport(): {
    totalTime: number
    checkpoints: { name: string; time: number }[]
    metrics: any
  } {
    const totalTime = performance.now() - this.startTime
    
    // 获取 Web Vitals
    const metrics = {
      fcp: this.getFCP(),
      lcp: this.getLCP(),
      cls: this.getCLS(),
      fid: this.getFID()
    }

    return {
      totalTime,
      checkpoints: this.checkpoints,
      metrics
    }
  }

  /**
   * 获取 FCP
   */
  private getFCP(): number | null {
    const fcpEntries = performance.getEntriesByName('first-contentful-paint')
    return fcpEntries.length > 0 ? fcpEntries[0].startTime : null
  }

  /**
   * 获取 LCP
   */
  private getLCP(): number | null {
    const lcpEntries = performance.getEntriesByType('largest-contentful-paint')
    return lcpEntries.length > 0 ? lcpEntries[lcpEntries.length - 1].startTime : null
  }

  /**
   * 获取 CLS
   */
  private getCLS(): number {
    const clsEntries = performance.getEntriesByType('layout-shift')
    let clsValue = 0
    clsEntries.forEach((entry: any) => {
      if (!entry.hadRecentInput) {
        clsValue += entry.value
      }
    })
    return clsValue
  }

  /**
   * 获取 FID
   */
  private getFID(): number | null {
    const fidEntries = performance.getEntriesByType('first-input')
    if (fidEntries.length > 0) {
      const entry = fidEntries[0] as PerformanceEventTiming
      return entry.processingStart - entry.startTime
    }
    return null
  }

  /**
   * 输出详细报告
   */
  printDetailedReport(): void {
    const report = this.getReport()
    
    console.group('📊 性能测试报告')
    console.log(`总耗时: ${report.totalTime.toFixed(2)}ms`)
    
    console.group('⏱️ 检查点')
    report.checkpoints.forEach(checkpoint => {
      console.log(`${checkpoint.name}: ${checkpoint.time.toFixed(2)}ms`)
    })
    console.groupEnd()
    
    console.group('🎯 Web Vitals')
    console.log(`FCP: ${report.metrics.fcp?.toFixed(2) || 'N/A'}ms`)
    console.log(`LCP: ${report.metrics.lcp?.toFixed(2) || 'N/A'}ms`)
    console.log(`CLS: ${report.metrics.cls?.toFixed(4) || 'N/A'}`)
    console.log(`FID: ${report.metrics.fid?.toFixed(2) || 'N/A'}ms`)
    console.groupEnd()
    
    // 性能评级
    this.printPerformanceGrade(report.metrics)
    
    console.groupEnd()
  }

  /**
   * 性能评级
   */
  private printPerformanceGrade(metrics: any): void {
    console.group('🏆 性能评级')
    
    // FCP 评级
    if (metrics.fcp) {
      if (metrics.fcp <= 1800) {
        console.log('FCP: 🟢 优秀')
      } else if (metrics.fcp <= 3000) {
        console.log('FCP: 🟡 需要改进')
      } else {
        console.log('FCP: 🔴 较差')
      }
    }
    
    // LCP 评级
    if (metrics.lcp) {
      if (metrics.lcp <= 2500) {
        console.log('LCP: 🟢 优秀')
      } else if (metrics.lcp <= 4000) {
        console.log('LCP: 🟡 需要改进')
      } else {
        console.log('LCP: 🔴 较差')
      }
    }
    
    // CLS 评级
    if (metrics.cls !== undefined) {
      if (metrics.cls <= 0.1) {
        console.log('CLS: 🟢 优秀')
      } else if (metrics.cls <= 0.25) {
        console.log('CLS: 🟡 需要改进')
      } else {
        console.log('CLS: 🔴 较差')
      }
    }
    
    console.groupEnd()
  }
}

// 创建全局实例（仅开发环境）
export const performanceTestHelper = import.meta.env.DEV ? new PerformanceTestHelper() : null

// 自动记录关键检查点
if (performanceTestHelper) {
  // DOM 加载完成
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      performanceTestHelper.checkpoint('DOM Content Loaded')
    })
  } else {
    performanceTestHelper.checkpoint('DOM Already Loaded')
  }
  
  // 页面完全加载
  window.addEventListener('load', () => {
    performanceTestHelper.checkpoint('Window Load Complete')
    
    // 延迟输出报告
    setTimeout(() => {
      performanceTestHelper.printDetailedReport()
    }, 2000)
  })
}
