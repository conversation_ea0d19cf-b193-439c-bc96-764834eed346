/**
 * 地理位置检测工具
 * 用于检测用户所在地区，实现地区访问限制
 */

export interface GeoLocationInfo {
  country: string
  countryCode: string
  region: string
  city: string
  ip: string
  timezone: string
}

export interface GeoLocationResponse {
  success: boolean
  data?: GeoLocationInfo
  error?: string
}

/**
 * 中国大陆相关的国家/地区代码
 */
const CHINA_MAINLAND_CODES = [
  'CN', // 中国大陆
  'CHN' // 中国大陆（ISO 3166-1 alpha-3）
]

/**
 * 检测是否为中国大陆用户
 * @param countryCode 国家代码
 * @returns 是否为中国大陆用户
 */
export function isChinaMainlandUser(countryCode: string): boolean {
  if (!countryCode) return false
  return CHINA_MAINLAND_CODES.includes(countryCode.toUpperCase())
}

/**
 * 通过多个IP地理位置API获取用户位置信息
 * 使用备用方案确保检测的可靠性
 */
export class GeoLocationDetector {
  private static instance: GeoLocationDetector
  private cachedLocation: GeoLocationInfo | null = null
  private cacheExpiry: number = 0
  private readonly CACHE_DURATION = 30 * 60 * 1000 // 30分钟缓存

  static getInstance(): GeoLocationDetector {
    if (!GeoLocationDetector.instance) {
      GeoLocationDetector.instance = new GeoLocationDetector()
    }
    return GeoLocationDetector.instance
  }

  /**
   * 获取用户地理位置信息
   * @returns Promise<GeoLocationResponse>
   */
  async getUserLocation(): Promise<GeoLocationResponse> {
    // 检查缓存
    if (this.cachedLocation && Date.now() < this.cacheExpiry) {
      return {
        success: true,
        data: this.cachedLocation
      }
    }

    // 尝试多个API服务
    const apis = [
      this.getLocationFromIpapi,
      this.getLocationFromIpinfo,
      this.getLocationFromCloudflare
    ]

    for (const api of apis) {
      try {
        const result = await api.call(this)
        if (result.success && result.data) {
          // 缓存结果
          this.cachedLocation = result.data
          this.cacheExpiry = Date.now() + this.CACHE_DURATION
          return result
        }
      } catch (error) {
        console.warn('地理位置API调用失败:', error)
        continue
      }
    }

    return {
      success: false,
      error: '无法获取地理位置信息'
    }
  }

  /**
   * 使用 ipapi.co 获取位置信息
   */
  private async getLocationFromIpapi(): Promise<GeoLocationResponse> {
    try {
      const response = await fetch('https://ipapi.co/json/', {
        method: 'GET',
        headers: {
          'Accept': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }

      const data = await response.json()
      
      if (data.error) {
        throw new Error(data.reason || 'API错误')
      }

      return {
        success: true,
        data: {
          country: data.country_name || '',
          countryCode: data.country_code || '',
          region: data.region || '',
          city: data.city || '',
          ip: data.ip || '',
          timezone: data.timezone || ''
        }
      }
    } catch (error) {
      return {
        success: false,
        error: `ipapi.co 调用失败: ${error.message}`
      }
    }
  }

  /**
   * 使用 ipinfo.io 获取位置信息
   */
  private async getLocationFromIpinfo(): Promise<GeoLocationResponse> {
    try {
      const response = await fetch('https://ipinfo.io/json', {
        method: 'GET',
        headers: {
          'Accept': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }

      const data = await response.json()

      return {
        success: true,
        data: {
          country: data.country || '',
          countryCode: data.country || '',
          region: data.region || '',
          city: data.city || '',
          ip: data.ip || '',
          timezone: data.timezone || ''
        }
      }
    } catch (error) {
      return {
        success: false,
        error: `ipinfo.io 调用失败: ${error.message}`
      }
    }
  }

  /**
   * 使用 Cloudflare 的 trace 服务获取位置信息
   */
  private async getLocationFromCloudflare(): Promise<GeoLocationResponse> {
    try {
      const response = await fetch('https://www.cloudflare.com/cdn-cgi/trace')
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }

      const text = await response.text()
      const data: Record<string, string> = {}
      
      text.split('\n').forEach(line => {
        const [key, value] = line.split('=')
        if (key && value) {
          data[key] = value
        }
      })

      return {
        success: true,
        data: {
          country: data.loc || '',
          countryCode: data.loc || '',
          region: '',
          city: '',
          ip: data.ip || '',
          timezone: ''
        }
      }
    } catch (error) {
      return {
        success: false,
        error: `Cloudflare trace 调用失败: ${error.message}`
      }
    }
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cachedLocation = null
    this.cacheExpiry = 0
  }
}

/**
 * 便捷方法：检测当前用户是否为中国大陆用户
 * @returns Promise<boolean>
 */
export async function isCurrentUserFromChinaMainland(): Promise<boolean> {
  try {
    const detector = GeoLocationDetector.getInstance()
    const result = await detector.getUserLocation()
    
    if (result.success && result.data) {
      return isChinaMainlandUser(result.data.countryCode)
    }
    
    return false
  } catch (error) {
    console.error('检测用户地区失败:', error)
    return false
  }
}

/**
 * 获取地理位置检测器实例
 */
export function getGeoLocationDetector(): GeoLocationDetector {
  return GeoLocationDetector.getInstance()
}
