export function loadScript(url: string, tag: string) {
  return new Promise<void>((resolve, reject) => {
    // 检查是否已经加载过这个脚本
    if (document.getElementById(tag)) {
      resolve()
      return
    }

    const script = document.createElement('script')
    script.id = tag
    script.type = 'text/javascript'
    script.src = url

    // 设置超时时间
    const timer = setTimeout(() => {
      reject()
    }, 30000)
    script.onload = (e) => {
      clearTimeout(timer)
      resolve()
      script.onload = null
      script.onerror = null
    }

    script.onerror = (error) => {
      clearTimeout(timer)
      reject()
      script.onload = null
      script.onerror = null
    }
    document.body.appendChild(script)
  })
}
