// 小于999展示999，否则展示（千位数k+）例: 1999展示19k+
export const formatCount = (count: number | string): string => {
  const numCount: number = typeof count === 'string' ? parseInt(count) : count
  if (isNaN(numCount) || numCount < 0) {
    return '0'
  }
  if (numCount < 1000) {
    return numCount.toString()
  } else {
    const kCount = Math.floor(numCount / 1000)
    return `${kCount}k+`
  }
}

export const formatIndex = (num: number | string) => {
  const number = Number(num)
  if (isNaN(number)) {
    return '0'
  }
  return number <= 9 ? `0${number}` : number.toString()
}

export const formatAmount = (num: number | string) => {
  return Math.floor(Number(num)).toLocaleString('en-US')
}
