/**
 * Stripe 智能预加载策略 - 减少对 LCP 的影响
 * 在用户可能需要付费时提前加载 Stripe SDK
 */

import { preloadStripe } from './stripe-loader'

let preloadInitialized = false
let userInteractionDetected = false

/**
 * 初始化 Stripe 预加载策略
 */
export function initStripePreloader(): void {
  if (preloadInitialized) return
  preloadInitialized = true

  console.log('💳 初始化 Stripe 预加载策略...')

  // 策略1: 用户访问特定页面时预加载
  monitorRouteChanges()

  // 策略2: 用户交互一定时间后预加载
  monitorUserInteraction()

  // 策略3: 检测到低余额时预加载
  monitorUserBalance()

  // 策略4: 用户在聊天页面停留一定时间后预加载
  monitorChatPageActivity()
}

/**
 * 监控路由变化，在特定页面预加载 Stripe
 */
function monitorRouteChanges(): void {
  // 监听路由变化
  const checkRoute = () => {
    const currentPath = window.location.pathname

    // 在这些页面预加载 Stripe
    const preloadRoutes = [
      '/chat', // 聊天页面 - 可能触发付费
      '/user', // 用户页面 - 可能查看余额
      '/stories' // 故事列表 - 可能开始付费对话
    ]

    if (preloadRoutes.some((route) => currentPath.includes(route))) {
      console.log('💳 检测到付费相关页面，预加载 Stripe')
      delayedPreload(2000) // 延迟2秒预加载
    }
  }

  // 立即检查当前路由
  checkRoute()

  // 监听路由变化（适用于 SPA）
  window.addEventListener('popstate', checkRoute)

  // 监听 pushState 和 replaceState
  const originalPushState = history.pushState
  const originalReplaceState = history.replaceState

  history.pushState = function (...args) {
    originalPushState.apply(history, args)
    setTimeout(checkRoute, 100)
  }

  history.replaceState = function (...args) {
    originalReplaceState.apply(history, args)
    setTimeout(checkRoute, 100)
  }
}

/**
 * 监控用户交互，活跃用户更可能付费
 */
function monitorUserInteraction(): void {
  let interactionCount = 0
  const interactionThreshold = 5 // 5次交互后预加载

  const trackInteraction = () => {
    if (userInteractionDetected) return

    interactionCount++
    console.log(`💳 用户交互次数: ${interactionCount}`)

    if (interactionCount >= interactionThreshold) {
      userInteractionDetected = true
      console.log('💳 检测到活跃用户，预加载 Stripe')
      delayedPreload(1000)
    }
  }

  // 监听各种用户交互
  const events = ['click', 'scroll', 'keypress', 'touchstart']
  events.forEach((event) => {
    document.addEventListener(event, trackInteraction, { passive: true })
  })
}

/**
 * 监控用户余额，低余额时预加载
 */
function monitorUserBalance(): void {
  // 这里可以集成用户余额检查逻辑
  // 当余额低于某个阈值时预加载 Stripe

  const checkBalance = () => {
    try {
      // 从 localStorage 或 store 获取用户余额
      const userDataStr = localStorage.getItem('user-store')
      if (userDataStr) {
        const userData = JSON.parse(userDataStr)
        const balance = userData.user?.coins || 0

        // 余额低于10时预加载
        if (balance < 10) {
          console.log('💳 检测到低余额，预加载 Stripe')
          delayedPreload(500)
          return true
        }
      }
    } catch (error) {
      console.warn('检查用户余额失败:', error)
    }
    return false
  }

  // 立即检查
  if (checkBalance()) return

  // 定期检查余额变化
  setInterval(checkBalance, 30000) // 每30秒检查一次
}

/**
 * 监控聊天页面活动
 */
function monitorChatPageActivity(): void {
  let chatPageTimer: number | null = null

  const checkChatPage = () => {
    const pathname = window.location.pathname
    const isChatPage = pathname.includes('/chat') || pathname.startsWith('/chat4/')

    if (isChatPage && !chatPageTimer) {
      console.log('💳 用户进入聊天页面，准备预加载 Stripe')

      // 在聊天页面停留10秒后预加载
      chatPageTimer = window.setTimeout(() => {
        console.log('💳 用户在聊天页面停留较久，预加载 Stripe')
        preloadStripe()
      }, 10000)
    } else if (!isChatPage && chatPageTimer) {
      // 离开聊天页面，清除定时器
      clearTimeout(chatPageTimer)
      chatPageTimer = null
    }
  }

  // 立即检查
  checkChatPage()

  // 监听路由变化
  window.addEventListener('popstate', checkChatPage)
}

/**
 * 延迟预加载 Stripe
 */
function delayedPreload(delay: number): void {
  setTimeout(() => {
    preloadStripe()
  }, delay)
}

/**
 * 手动触发 Stripe 预加载
 * 可以在特定业务逻辑中调用
 */
export function triggerStripePreload(): void {
  console.log('💳 手动触发 Stripe 预加载')
  preloadStripe()
}

/**
 * 检查是否应该预加载 Stripe
 */
export function shouldPreloadStripe(): boolean {
  // 检查各种条件
  const pathname = window.location.pathname
  const isChatPage = pathname.includes('/chat') || pathname.startsWith('/chat4/')
  const isUserPage = pathname.includes('/user')
  const hasUserInteraction = userInteractionDetected

  return isChatPage || isUserPage || hasUserInteraction
}

// 自动初始化预加载策略
if (typeof window !== 'undefined') {
  // 延迟初始化，避免影响首屏加载
  setTimeout(initStripePreloader, 3000)
}
