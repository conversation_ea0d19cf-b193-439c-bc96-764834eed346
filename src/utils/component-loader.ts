// import { GlobalLoading, ErrorTip } from '@/components';
import { h, markRaw } from 'vue'

export function asyncComponentLoader(loader, name = 'index') {
  return {
    name,
    data() {
      return {
        isLoading: true,
        isError: false,
        Component: null
      }
    },
    async created() {
      try {
        const module = await loader()
        this.Component = markRaw(module.default || module)
        this.isLoading = false
        this.isError = false
      } catch (e) {
        this.isError = true
        this.isLoading = false
        console.error('组件资源加载错误', e)
      }
    },
    render() {
      // if (this.isLoading) return h(GlobalLoading, { visible: true })
      // if (this.isError) return h(ErrorTip)

      return h(this.Component)
    }
  }
}
