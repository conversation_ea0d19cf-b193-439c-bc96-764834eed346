// 递归过滤对象中的 null 值
export const removeNullValues = (obj: any): any => {
  // 处理数组
  if (Array.isArray(obj)) {
    const filteredArray = obj.map((item) => removeNullValues(item)).filter((item) => item !== null)
    return filteredArray.length > 0 ? filteredArray : null
  }

  // 处理对象
  if (obj !== null && typeof obj === 'object') {
    const filteredEntries = Object.entries(obj)
      .map(([key, value]) => [key, removeNullValues(value)])
      .filter(([_, value]) => value !== null)

    // 如果过滤后没有任何属性，返回 null
    if (filteredEntries.length === 0) {
      return null
    }

    return Object.fromEntries(filteredEntries)
  }

  return obj
}
