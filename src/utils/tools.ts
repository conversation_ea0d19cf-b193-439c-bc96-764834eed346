export function JSONParse<T = any>(str: string, defaultValue = null): T | null {
  try {
    return JSON.parse(str) as T
  } catch (e) {
    console.error('JSONParse error: ', e, str)
    return defaultValue
  }
}

export enum VersionCompareResult {
  Greater = 1,
  Equal = 0,
  Less = -1,
  Unknown = 2
}

/**
 * 版本比较
 * @returns 1: version1 > version2, -1: version1 < version2, 0: version1 === version2
 */
export function compareVersions(version1: string, version2: string) {
  if (!version1 || !version2) return VersionCompareResult.Unknown

  const v1 = version1.split('.').map(Number)
  const v2 = version2.split('.').map(Number)

  for (let i = 0; i < Math.max(v1.length, v2.length); i++) {
    if ((v1[i] || 0) === (v2[i] || 0)) continue
    return (v1[i] || 0) > (v2[i] || 0) ? VersionCompareResult.Greater : VersionCompareResult.Less
  }

  return VersionCompareResult.Equal
}
