import { getDeviceId } from '@/utils/util'
import { reportEvent } from '@/utils/report'
import { ReportEvent } from '@/interface/report'

/**
 * AB测试变体类型
 */
export type ABTestVariant = 'A' | 'B' | 'C' | 'D'

/**
 * AB测试配置
 */
export interface ABTestConfig {
  [experimentName: string]: {
    variants: ABTestVariant[]
    weights?: number[] // 权重分配，如果不提供则平均分配
    description?: string
  }
}

/**
 * AB测试结果
 */
export interface ABTestResult {
  experiment: string
  variant: ABTestVariant
  deviceId: string
  assignedAt: number
}

/**
 * 简单的AB测试管理器
 */
class SimpleABTestManager {
  private static instance: SimpleABTestManager | null = null
  private readonly STORAGE_KEY = 'abtest_assignments'
  private assignments: Record<string, ABTestResult> = {}

  constructor() {
    this.loadAssignments()
  }

  static getInstance(): SimpleABTestManager {
    if (!SimpleABTestManager.instance) {
      SimpleABTestManager.instance = new SimpleABTestManager()
    }
    return SimpleABTestManager.instance
  }

  /**
   * 从localStorage加载分配记录
   */
  private loadAssignments(): void {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY)
      if (stored) {
        this.assignments = JSON.parse(stored)
      }
    } catch (error) {
      console.warn('加载AB测试分配记录失败:', error)
      this.assignments = {}
    }
  }

  /**
   * 保存分配记录到localStorage
   */
  private saveAssignments(): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.assignments))
    } catch (error) {
      console.warn('保存AB测试分配记录失败:', error)
    }
  }

  /**
   * 生成随机数
   */
  private getStableRandom(): number {
    return Math.random()
  }

  /**
   * 获取实验变体
   */
  getVariant(experimentName: string, config: ABTestConfig[string]): ABTestVariant {
    const deviceId = getDeviceId()
    const assignmentKey = `${experimentName}_${deviceId}`

    // 检查是否已有分配记录
    if (this.assignments[assignmentKey]) {
      return this.assignments[assignmentKey].variant
    }

    // 使用随机数进行分配
    const stableRandom = this.getStableRandom()
    const variants = config.variants
    const weights = config.weights

    let selectedVariant: ABTestVariant

    if (weights && weights.length === variants.length) {
      // 使用权重分配
      const totalWeight = weights.reduce((sum, weight) => sum + weight, 0)
      let cumulative = 0

      for (let i = 0; i < variants.length; i++) {
        cumulative += weights[i] / totalWeight
        if (stableRandom <= cumulative) {
          selectedVariant = variants[i]
          break
        }
      }
      selectedVariant = selectedVariant! || variants[0]
    } else {
      // 平均分配
      const index = Math.floor(stableRandom * variants.length)
      selectedVariant = variants[index]
    }

    // 保存分配记录
    const result: ABTestResult = {
      experiment: experimentName,
      variant: selectedVariant,
      deviceId,
      assignedAt: Date.now()
    }

    this.assignments[assignmentKey] = result
    this.saveAssignments()

    // 上报分配事件
    reportEvent(ReportEvent.ABTestAssigned, {
      experiment: experimentName,
      variant: selectedVariant,
      deviceId,
      config: config.description || ''
    })

    console.log(`🧪 AB测试分配: ${experimentName} -> ${selectedVariant}`)

    return selectedVariant
  }

  /**
   * 检查是否在指定变体中
   */
  isVariant(experimentName: string, variant: ABTestVariant, config: ABTestConfig[string]): boolean {
    return this.getVariant(experimentName, config) === variant
  }

  /**
   * 上报AB测试曝光
   */
  trackExposure(experimentName: string, context?: Record<string, any>): void {
    const deviceId = getDeviceId()
    const assignmentKey = `${experimentName}_${deviceId}`
    const assignment = this.assignments[assignmentKey]

    if (assignment) {
      reportEvent(ReportEvent.ABTestExposure, {
        experiment: experimentName,
        variant: assignment.variant,
        deviceId,
        page: window.location.pathname,
        ...context
      })
    }
  }

  /**
   * 上报AB测试转化
   */
  trackConversion(experimentName: string, conversionType: string, value?: number): void {
    const deviceId = getDeviceId()
    const assignmentKey = `${experimentName}_${deviceId}`
    const assignment = this.assignments[assignmentKey]

    if (assignment) {
      reportEvent(ReportEvent.ABTestConversion, {
        experiment: experimentName,
        variant: assignment.variant,
        deviceId,
        conversionType,
        value,
        page: window.location.pathname
      })
    }
  }

  /**
   * 获取所有分配记录
   */
  getAllAssignments(): ABTestResult[] {
    return Object.values(this.assignments)
  }

  /**
   * 清除指定实验的分配记录
   */
  clearExperiment(experimentName: string): void {
    const deviceId = getDeviceId()
    const assignmentKey = `${experimentName}_${deviceId}`
    delete this.assignments[assignmentKey]
    this.saveAssignments()
  }

  /**
   * 清除所有分配记录
   */
  clearAll(): void {
    this.assignments = {}
    localStorage.removeItem(this.STORAGE_KEY)
  }
}

// 导出单例实例
export const abtest = SimpleABTestManager.getInstance()

// 便捷函数
export function getABTestVariant(
  experimentName: string,
  config: ABTestConfig[string]
): ABTestVariant {
  return abtest.getVariant(experimentName, config)
}

export function isABTestVariant(
  experimentName: string,
  variant: ABTestVariant,
  config: ABTestConfig[string]
): boolean {
  return abtest.isVariant(experimentName, variant, config)
}

export function trackABTestExposure(experimentName: string, context?: Record<string, any>): void {
  abtest.trackExposure(experimentName, context)
}

export function trackABTestConversion(
  experimentName: string,
  conversionType: string,
  value?: number
): void {
  abtest.trackConversion(experimentName, conversionType, value)
}
