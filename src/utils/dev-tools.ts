/**
 * 开发工具加载器
 * 只在开发环境中加载，生产环境会被 Tree Shaking 移除
 */

/**
 * 加载开发环境测试工具
 * 这个函数在生产环境中会被完全移除
 */
export function loadDevTools(): void {
  // 使用 __DEV__ 标志进行 Tree Shaking
  if (__DEV__) {
    console.log('🔧 加载开发环境测试工具...')

    // 动态导入测试工具，避免在生产环境中包含
    Promise.all([import('./route-preload-test'), import('./version-manager-test')])
      .then(() => {
        console.log('✅ 开发环境测试工具加载完成')
        console.log('💡 可用的测试工具:')
        console.log('  - window.__routePreloadDebug (路由预加载调试)')
        console.log('  - window.__versionManagerTest (版本管理器测试)')
        console.log('  - window.__versionManager (版本管理器调试)')
      })
      .catch((error) => {
        console.warn('⚠️ 开发环境测试工具加载失败:', error)
      })
  }
}

/**
 * 初始化开发环境
 * 在生产环境中这个函数体会被完全移除
 */
export function initDevEnvironment(): void {
  // if (__DEV__) {
  //   // 设置开发环境特有的配置
  //   console.log('🚀 初始化开发环境...')
  //   // 加载测试工具
  //   loadDevTools()
  //   // 设置开发环境的全局变量
  //   const windowAny = window as any
  //   windowAny.__DEV_MODE__ = true
  //   windowAny.__BUILD_TIME__ = new Date().toISOString()
  //   // 开发环境性能监控
  //   if (typeof performance !== 'undefined') {
  //     const observer = new PerformanceObserver((list) => {
  //       for (const entry of list.getEntries()) {
  //         if (entry.entryType === 'navigation') {
  //           // 类型断言为 PerformanceNavigationTiming
  //           const navEntry = entry as PerformanceNavigationTiming
  //           console.log('📊 页面加载性能:', {
  //             domContentLoaded:
  //               navEntry.domContentLoadedEventEnd - navEntry.domContentLoadedEventStart,
  //             loadComplete: navEntry.loadEventEnd - navEntry.loadEventStart,
  //             totalTime: navEntry.loadEventEnd - navEntry.fetchStart
  //           })
  //         }
  //       }
  //     })
  //     try {
  //       observer.observe({ entryTypes: ['navigation'] })
  //     } catch (error) {
  //       console.warn('性能监控初始化失败:', error)
  //     }
  //   }
  //   console.log('✅ 开发环境初始化完成')
  // }
}

/**
 * 生产环境初始化
 * 这个函数在开发环境中不会执行
 */
export function initProductionEnvironment(): void {
  if (!__DEV__) {
    console.log('🚀 生产环境模式')

    // 生产环境特有的配置
    const windowAny = window as any
    windowAny.__PROD_MODE__ = true

    // 禁用一些开发环境的功能
    if (typeof console !== 'undefined') {
      // 在生产环境中可以选择性地禁用某些 console 方法
      // console.debug = () => {}
      // console.log = () => {}
    }
  }
}

/**
 * 通用初始化函数
 * 在生产环境中，这个函数会被 Tree Shaking 优化掉
 */
export function initEnvironment(): void {
  if (__DEV__) {
    initDevEnvironment()
  } else {
    initProductionEnvironment()
  }
}

/**
 * 生产环境安全的初始化函数
 * 这个函数总是存在，但在生产环境中只执行必要的操作
 */
export function safeInitEnvironment(): void {
  // 使用条件编译，生产环境中这个函数体会被完全移除
  if (__DEV__) {
    initDevEnvironment()
  } else {
    // 生产环境的轻量级初始化
    const windowAny = window as any
    windowAny.__PROD_MODE__ = true

    // 可选：在生产环境中禁用某些 console 方法
    // if (typeof console !== 'undefined') {
    //   console.debug = () => {}
    // }
  }
}

// 类型声明，确保 TypeScript 编译通过
export type DevToolsLoader = typeof loadDevTools
export type EnvironmentInitializer = typeof initEnvironment
