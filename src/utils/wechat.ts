// import { getSignature } from '@/api'
// import wx from 'weixin-js-sdk'

// export interface WXShareConfig {
//   link: string
//   title: string
//   desc: string
//   imgUrl: string
// }

// // 微信分享信息
// export const updateWechatShare = async (shareConfig: WXShareConfig) => {
//   const signRes = await getSignature(shareConfig.link)
//   if (!signRes.data?.data) return

//   const { appId, timestamp, nonceStr, signature } = signRes.data.data
//   wx.config({
//     debug: false,
//     appId, // 必填，公众号的唯一标识
//     timestamp, // 必填，生成签名的时间戳
//     nonceStr, // 必填，生成签名的随机串
//     signature, // 必填，签名
//     jsApiList: [
//       'onMenuShareAppMessage',
//       'onMenuShareTimeline',
//       'updateAppMessageShareData',
//       'updateTimelineShareData'
//     ] // 必填，需要使用的JS接口列表
//   })
//   wx.ready(() => {
//     const shareData = {
//       ...shareConfig
//     }
//     //需在用户可能点击分享按钮前就先调用
//     wx.updateAppMessageShareData({
//       ...shareData,
//       success: () => {
//         console.log('updateAppMessage success')
//       }
//     })
//     wx.updateTimelineShareData({
//       ...shareData,
//       success: () => {
//         console.log('updateAppMessage success')
//       }
//     })
//   })
// }
