import axios from 'axios'

export interface LoginParams {
  username: string
  password: string
  remember?: boolean
}

export interface RegisterParams {
  username: string
  password: string
}

export interface TokenResponse {
  access_token: string
  refresh_token: string
  expires_in: number
}

export interface UserInfo {
  id: string
  username: string
  avatar: string
}

export const login = (data: LoginParams) => {
  return axios.post<TokenResponse>('/api/auth/login', data)
}

export const register = (data: RegisterParams) => {
  return axios.post<void>('/api/auth/register', data)
}

export const refreshToken = (refresh_token: string) => {
  return axios.post<TokenResponse>('/api/auth/refresh', { refresh_token })
}

export const getUserInfo = () => {
  return axios.get<UserInfo>('/api/user/info')
}
