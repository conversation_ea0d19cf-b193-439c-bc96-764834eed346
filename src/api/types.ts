interface BaseEvent {
  event_type: string
  timestamp: number
  data: unknown
}

interface PlayVideoEvent extends BaseEvent {
  event_type: 'play_video'
  data: {
    url: string
  }
}

interface ShowOverlayEvent extends BaseEvent {
  event_type: 'show_overlay'
  data: {
    overlay: {
      text: string
      position: 'bottom' | 'top' | 'center'
      display_time: 'after' | 'before'
    }
    button?: {
      icon: string
      text: string
      action: string
    }
  }
}

interface MessageEvent extends BaseEvent {
  event_type: 'message'
  data: {
    id: string
    sender_type: 'actor' | 'user'
    msg_type: 'text'
    content: {
      text: string
    }
    create_time: string
    sender: {
      avatar_url: string
      name: string
      is_me: boolean
    }
  }
}

interface WaitEvent extends BaseEvent {
  event_type: 'wait'
  data: {
    seconds: number
  }
}

interface ShowTipsEvent extends BaseEvent {
  event_type: 'show_tips'
  data: {
    content: {
      html: string
    }
    create_time: string
  }
}

interface ShowChatOptionsEvent extends BaseEvent {
  event_type: 'show_chat_options'
  data: {
    options: Array<{
      option_id: string
      text: string
      paid_required: boolean
      coins: number
      is_purchased: boolean
    }>
  }
}

export type ChatEvent =
  | PlayVideoEvent
  | MessageEvent
  | WaitEvent
  | ShowTipsEvent
  | ShowChatOptionsEvent
  | ShowOverlayEvent
