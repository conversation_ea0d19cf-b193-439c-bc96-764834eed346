import axios from 'axios'

interface CheckoutCreateParams {
  price_id: string
  success_url: string
  cancel_url: string
}

interface CheckoutCreateResponse {
  code: string
  message: string
  data: {
    session_id: string
  }
}

interface PriceListResponse {
  code: string
  message: string
  data: PriceItem[]
}

export interface PriceExtra {
  background_url: string
  discount_percent: number
}

export interface PriceItem {
  id: string
  name: string
  amount: number
  coins: number
  period: string
  extra: PriceExtra
}

export async function createCheckoutSession(
  params: CheckoutCreateParams
): Promise<CheckoutCreateResponse> {
  const { data } = await axios.post<CheckoutCreateResponse>('/api/v1/checkout.create', params)
  return data
}

export async function getPriceList(): Promise<PriceListResponse> {
  const { data } = await axios.get<PriceListResponse>('/api/v1/price.list')
  return data
}
