import axios from 'axios'

export type AgeType = 'child' | 'teenager' | 'youth' | 'middle-age' | 'senior' | 'unknown'
export type GenderType = 'male' | 'female'
export type ClothesStyleType = 'suit' | 'agent' | 'gym' | 'yamato'
export type StatusType = 'submitted' | 'start' | 'finish' | 'error' | 'failed'

export interface UserAvatar {
  id: string
  user_id: string
  gender: GenderType
  age: AgeType
  style: string
  clothes_style: ClothesStyleType
  status: StatusType
  progress?: number
  image_urls: string[]
  submitted_at: string
  ended_at: string
  params: {
    seed: number
    image_urls: string[] | null
  }
}

export interface CreateAvatarParams {
  age: AgeType
  style: string
  gender: GenderType
  clothes_style: ClothesStyleType
  image_urls?: string[]
}

export interface CreateAvatarResponse {
  code: string
  message: string
  data: {
    user_avatar: UserAvatar
  }
}

export interface AvatarListResponse {
  code: string
  message: string
  data: {
    user_avatars: UserAvatar[]
    total: number
  }
}

export interface AvatarDetailResponse {
  code: string
  message: string
  data: {
    user_avatar: UserAvatar
  }
}

// Interface for predefined avatars response
export interface PredefinedAvatarsResponse {
  code: string
  message: string
  data: {
    preview_url: string[]
  }
}

// Get avatar list
export const getAvatarList = () => {
  return axios.get<AvatarListResponse>('/api/v1/user-avatar.list')
}

// Get predefined avatars
export const getPredefinedAvatars = () => {
  return axios.get<PredefinedAvatarsResponse>('/api/v1/user-avatar.preview')
}

// Create avatar
export const createAvatar = (params: CreateAvatarParams) => {
  return axios.post<CreateAvatarResponse>('/api/v1/user-avatar.create', params)
}

// Delete avatar
export const deleteAvatar = (id: string) => {
  return axios.post<{ code: string; message: string }>('/api/v1/user-avatar.delete', { id })
}

// Get avatar detail
export const getAvatarDetail = (id: string) => {
  return axios.get<{ code: string; message: string; data: { user_avatar: UserAvatar } }>(
    `/api/v1/user-avatar.get?id=${id}`
  )
}
