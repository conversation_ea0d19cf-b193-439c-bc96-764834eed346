import axios from 'axios'
import type { ResponseData } from '@/interface'
import type {
  ExperimentConfig,
  ExperimentListResponse,
  UserExperimentAssignment,
  AssignmentResponse,
  BatchAssignmentRequest,
  BatchAssignmentResponse,
  ExperimentExposureEvent,
  ExperimentConversionEvent,
  ExperimentFilter,
  ExperimentStats
} from '@/types/abtest'

/**
 * 获取实验列表
 */
export const getExperimentList = (filter?: ExperimentFilter) => {
  return axios.get<ResponseData<ExperimentListResponse>>('/api/v1/abtest/experiments', {
    params: filter
  })
}

/**
 * 获取单个实验配置
 */
export const getExperiment = (experimentId: string) => {
  return axios.get<ResponseData<ExperimentConfig>>(`/api/v1/abtest/experiments/${experimentId}`)
}

/**
 * 为用户分配实验变体
 */
export const assignExperiment = (experimentId: string, userId?: string, deviceId?: string) => {
  return axios.post<ResponseData<AssignmentResponse>>(`/api/v1/abtest/experiments/${experimentId}/assign`, {
    userId,
    deviceId
  })
}

/**
 * 批量分配实验变体
 */
export const batchAssignExperiments = (request: BatchAssignmentRequest) => {
  return axios.post<ResponseData<BatchAssignmentResponse>>('/api/v1/abtest/experiments/batch-assign', request)
}

/**
 * 获取用户的实验分配记录
 */
export const getUserAssignments = (userId?: string, deviceId?: string) => {
  return axios.get<ResponseData<UserExperimentAssignment[]>>('/api/v1/abtest/assignments', {
    params: { userId, deviceId }
  })
}

/**
 * 上报实验曝光事件
 */
export const reportExposure = (event: ExperimentExposureEvent) => {
  return axios.post<ResponseData<void>>('/api/v1/abtest/events/exposure', event)
}

/**
 * 上报实验转化事件
 */
export const reportConversion = (event: ExperimentConversionEvent) => {
  return axios.post<ResponseData<void>>('/api/v1/abtest/events/conversion', event)
}

/**
 * 批量上报事件
 */
export const batchReportEvents = (events: {
  exposures?: ExperimentExposureEvent[]
  conversions?: ExperimentConversionEvent[]
}) => {
  return axios.post<ResponseData<void>>('/api/v1/abtest/events/batch', events)
}

/**
 * 获取实验统计数据
 */
export const getExperimentStats = (experimentId: string, variantId?: string) => {
  return axios.get<ResponseData<ExperimentStats[]>>(`/api/v1/abtest/experiments/${experimentId}/stats`, {
    params: { variantId }
  })
}

/**
 * 强制刷新实验配置缓存
 */
export const refreshExperimentCache = () => {
  return axios.post<ResponseData<void>>('/api/v1/abtest/cache/refresh')
}

/**
 * 获取实验健康状态
 */
export const getExperimentHealth = () => {
  return axios.get<ResponseData<{
    status: 'healthy' | 'degraded' | 'unhealthy'
    activeExperiments: number
    totalAssignments: number
    lastSyncTime: number
  }>>('/api/v1/abtest/health')
}
