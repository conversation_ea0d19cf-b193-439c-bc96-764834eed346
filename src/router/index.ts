import { createRouter, createWebHistory } from 'vue-router'
import mobileRoutes from '@/mobile/routes'
import pcRoutes from '@/pc/routes'
import createRouteGuard from './guard'
import { setupSEOGuard } from './seo-guard'

// 合并移动端和PC端路由
const routes = [...mobileRoutes, ...pcRoutes]

const router = createRouter({
  history: createWebHistory(),
  // @ts-ignore
  routes
})

createRouteGuard(router)

// 设置SEO守卫
setupSEOGuard(router)

export * from './constants'
export default router
