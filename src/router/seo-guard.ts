import type { Router } from 'vue-router'
import { useRouteSEO } from '@/composables/useSEO'

/**
 * 路由SEO守卫
 * 自动根据路由meta配置应用SEO设置
 */
export function setupSEOGuard(router: Router) {
  const { setRouteSEO } = useRouteSEO()

  router.afterEach((to) => {
    // 在路由切换完成后设置SEO
    // 使用 nextTick 确保组件已经挂载
    setTimeout(() => {
      setRouteSEO(to)
    }, 100)
  })
}

/**
 * 为动态路由设置SEO
 * 在组件中调用此函数来设置动态SEO数据
 */
export function setDynamicSEO(routeName: string, data: any) {
  const { setRouteSEO } = useRouteSEO()
  
  // 创建一个模拟的路由对象
  const mockRoute = {
    name: routeName,
    meta: { seo: { dynamic: true } },
    fullPath: window.location.pathname
  } as any

  setRouteSEO(mockRoute, data)
}
