import 'vue-router'

declare module 'vue-router' {
  interface RouteMeta {
    roles?: string[] // Controls roles that have access to the page
    requiresAuth: boolean // Whether login is required to access the current page (every route must declare)
    icon?: string // The icon show in the side menu
    locale?: string // The locale name show in side menu and breadcrumb
    hideInMenu?: boolean // If true, it is not displayed in the side menu
    hideChildrenInMenu?: boolean // if set true, the children are not displayed in the side menu
    activeMenu?: string // if set name, the menu will be highlighted according to the name you set
    order?: number // Sort routing menu items. If set key, the higher the value, the more forward it is
    noAffix?: boolean // if set true, the tag will not affix in the tab-bar

    // SEO 配置
    seo?: {
      title?: string // 页面标题
      description?: string // 页面描述
      keywords?: string // 页面关键词
      image?: string // 页面图片
      type?: 'website' | 'article' // 页面类型
      dynamic?: boolean // 是否为动态SEO（如故事详情页）
      pageType?: string // 页面类型标识，用于自动应用预设的SEO配置
    }
    ignoreCache?: boolean // if set true, the page will not be cached
  }
}
