import type { Router } from 'vue-router'
import { useUserStore } from '@/store/user'
import { useGenderStore } from '@/store/gender'
import { Message } from '@/mobile/components/Message'

const paymentFlow = {
  isValidFlow: false,
  setValid() {
    this.isValidFlow = true
  },
  clearValid() {
    this.isValidFlow = false
  }
}

// 不需要年龄验证的路由白名单
const ageVerificationWhitelist = ['/landingpage']

// 不需要登录的路由白名单
const authWhitelist = ['/landingpage', '/user/social-callback', '/user/login', '/user/register']

// 游客注册重试管理
const GUEST_SIGNUP_MAX_RETRIES = 3
const GUEST_SIGNUP_RETRY_KEY = 'guest_signup_retry_count'
const GUEST_SIGNUP_LAST_ATTEMPT_KEY = 'guest_signup_last_attempt'
const GUEST_SIGNUP_COOLDOWN = 5 * 60 * 1000 // 5分钟冷却时间

function getGuestSignupRetryCount(): number {
  const count = localStorage.getItem(GUEST_SIGNUP_RETRY_KEY)
  return count ? parseInt(count, 10) : 0
}

function incrementGuestSignupRetryCount(): void {
  const count = getGuestSignupRetryCount() + 1
  localStorage.setItem(GUEST_SIGNUP_RETRY_KEY, count.toString())
  localStorage.setItem(GUEST_SIGNUP_LAST_ATTEMPT_KEY, Date.now().toString())
}

function resetGuestSignupRetryCount(): void {
  localStorage.removeItem(GUEST_SIGNUP_RETRY_KEY)
  localStorage.removeItem(GUEST_SIGNUP_LAST_ATTEMPT_KEY)
}

function canAttemptGuestSignup(): boolean {
  const retryCount = getGuestSignupRetryCount()
  if (retryCount < GUEST_SIGNUP_MAX_RETRIES) {
    return true
  }

  // 检查是否已过冷却时间
  const lastAttempt = localStorage.getItem(GUEST_SIGNUP_LAST_ATTEMPT_KEY)
  if (lastAttempt) {
    const timeSinceLastAttempt = Date.now() - parseInt(lastAttempt, 10)
    if (timeSinceLastAttempt > GUEST_SIGNUP_COOLDOWN) {
      resetGuestSignupRetryCount()
      return true
    }
  }

  return false
}

export function setupRouterGuards(router: Router) {
  router.beforeEach(async (to, from, next) => {
    const userStore = useUserStore()
    const genderStore = useGenderStore()

    try {
      // 如果是白名单路由，直接通过
      if (authWhitelist.includes(to.path)) {
        next()
        return
      }

      const token = localStorage.getItem('token')
      const userInfoStr = localStorage.getItem('userInfo')

      // 如果 token 和 userId 存在，尝试恢复用户状态
      if (token && userInfoStr) {
        try {
          const userInfo = JSON.parse(userInfoStr)
          userStore.setUserInfo(userInfo)
          userStore.isAuthenticated = true
        } catch (e) {
          console.error('Failed to parse stored userInfo:', e)
          // 如果解析失败，清除无效数据并继续访客注册流程
          localStorage.removeItem('userInfo')
        }
      }

      // 如果没有认证信息，尝试访客注册
      if (!userStore.isAuthenticated) {
        if (!localStorage.getItem('user_gender')) {
          localStorage.setItem('user_gender', 'male')
        }

        // 检查是否可以尝试游客注册
        if (!canAttemptGuestSignup()) {
          console.warn('Guest signup retry limit exceeded, redirecting to login')
          next({ path: '/user/login' })
          return
        }

        try {
          const isLogin = await userStore.signUpAsGuest()
          if (!isLogin) {
            incrementGuestSignupRetryCount()
            console.warn('Guest signup failed, retry count:', getGuestSignupRetryCount())
            next({ path: '/user/login' })
            return
          }
          // 注册成功，重置重试计数
          resetGuestSignupRetryCount()
        } catch (error) {
          console.error('Guest signup error:', error)
          incrementGuestSignupRetryCount()
          next({ path: '/user/login' })
          return
        }
      }

      // 现在检查路由是否需要特定的认证级别
      const requiresAuth = to.matched.some((record) => record.meta.requiresAuth)
      const requiresLogin = to.matched.some((record) => record.meta.requiresLogin)

      if (requiresAuth && !userStore.userInfo?.uuid) {
        // 保存用户想要访问的页面路径到 sessionStorage
        sessionStorage.setItem('login_redirect', to.fullPath)
        next({ path: '/user/login' })
        return
      }

      if (requiresLogin && userStore.isGuest) {
        next({ path: '/user/login' })
        return
      }

      // 访客模式下的特殊处理
      if (userStore.isGuest) {
        // 支付成功后需要完善信息
        if (to.path === '/payment/success') {
          next('/user/complete')
          return
        }
      }

      next()
    } catch (error) {
      console.error('Route guard error:', error)
      next('/user/login')
    }
  })
}

// 导出支付流程控制
export const PaymentFlow = {
  start() {
    paymentFlow.setValid()
  }
}
