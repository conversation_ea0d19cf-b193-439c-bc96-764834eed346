import axios, { InternalAxiosRequestConfig, AxiosResponse } from 'axios'
import { getAccessToken, reportEvent, isMobileEnv, getAppVersion } from '@/utils'
import { getDynamicApiHost } from '@/utils/dynamicApiHost'
import { ReportEvent } from '@/interface'
import { useUserStore } from '@/store'
import { Message } from '@/mobile/components/Message'
import { ErrorHandler } from '@/utils/errorHandler'

// 设置axios默认配置
axios.defaults.baseURL = getDynamicApiHost()
axios.defaults.timeout = 30000 // 默认 30 s 超时

// 扩展 InternalAxiosRequestConfig 以支持 startTime
interface AxiosRequestConfigWithStartTime extends InternalAxiosRequestConfig {
  startTime?: number
}

// 性能优化：请求去重缓存
const pendingRequests = new Map<string, Promise<AxiosResponse>>()

// 生成请求唯一标识
function generateRequestKey(config: InternalAxiosRequestConfig): string {
  const { method = 'get', url = '', params, data } = config
  return `${method.toUpperCase()}:${url}:${JSON.stringify(params)}:${JSON.stringify(data)}`
}

// 清理过期的请求缓存
function cleanupPendingRequests() {
  // 每5分钟清理一次缓存，避免内存泄漏
  setTimeout(
    () => {
      pendingRequests.clear()
      cleanupPendingRequests()
    },
    5 * 60 * 1000
  )
}

// 启动清理任务
cleanupPendingRequests()

// 性能优化：创建带有取消功能的 axios 实例
export function createCancelableAxios() {
  const controller = new AbortController()
  const instance = axios.create({
    signal: controller.signal,
    timeout: 30000
  })

  return {
    instance,
    cancel: () => controller.abort(),
    controller
  }
}

// 请求拦截器
axios.interceptors.request.use(async (config: AxiosRequestConfigWithStartTime) => {
  // 性能优化：对于GET请求进行去重处理
  if (config.method?.toLowerCase() === 'get' && !config.url?.startsWith('http')) {
    const requestKey = generateRequestKey(config)
    const existingRequest = pendingRequests.get(requestKey)
    if (existingRequest) {
      // 如果相同请求正在进行中，返回现有请求的Promise
      return existingRequest.then((response) => response.config as AxiosRequestConfigWithStartTime)
    }
  }

  if (!config.url?.startsWith('http')) {
    // node api 请求增加 header
    config.headers.set('Custom-Client', isMobileEnv() ? 'mobile' : 'pc') // 设置设备请求头
    config.headers.set('Custom-Version', getAppVersion()) // 设置设备版本号
    config.timeout = 30000 // 默认 30 s 超时
    const token = getAccessToken()
    if (token) {
      config.headers.setAuthorization(`Bearer ${token}`)
    }
  }
  config.startTime = Date.now()
  return config
})

let refreshTokenPromise: Promise<boolean> | null = null

// 响应拦截器
axios.interceptors.response.use(
  (response: AxiosResponse) => {
    // 性能优化：清理已完成的请求缓存
    if (
      response.config.method?.toLowerCase() === 'get' &&
      !response.config.url?.startsWith('http')
    ) {
      const requestKey = generateRequestKey(response.config)
      pendingRequests.delete(requestKey)
    }

    if (response.config.url?.startsWith('http') || response.config.responseType === 'blob')
      return response

    const isSuccessStatus = response.status >= 200 && response.status < 300
    const config = response.config as AxiosRequestConfigWithStartTime
    const currentTime = Date.now()
    const requestDuration = config.startTime ? currentTime - config.startTime : 0

    // 性能优化：只在开发环境或慢请求时记录详细信息
    if (import.meta.env.DEV || requestDuration > 1000) {
      reportEvent(ReportEvent.ApiRequest, {
        url: response.config.url,
        method: response.config.method,
        status: response.status,
        duration: requestDuration,
        route: window.location.pathname
      })
    }

    if (response.headers?.['content-type']?.includes('application/json')) {
      response.data.isOk = isSuccessStatus
    }

    if (!isSuccessStatus) {
      const errorContext = {
        url: response.config.url,
        method: response.config.method,
        params: response.config.params,
        status: response.status,
        body: response.config.data,
        data: response.data,
        duration: requestDuration,
        route: window.location.pathname
      }

      // 使用新的错误处理器
      ErrorHandler.handleApiError(
        new Error(`API Error: ${response.status} ${response.statusText}`),
        errorContext
      )
    }

    return response
  },
  async (error) => {
    const { response, code, config } = error
    const currentTime = Date.now()
    const requestDuration = config?.startTime ? currentTime - config.startTime : 0

    const errorContext = {
      url: config?.url,
      method: config?.method,
      code,
      message: error.message,
      duration: requestDuration,
      route: window.location.pathname
    }

    // 使用新的错误处理器
    if (code === 'ECONNABORTED') {
      ErrorHandler.handleNetworkError(error, { ...errorContext, type: 'timeout' })
      Message.error('Sorry, the network is unstable, please try again later')
      return Promise.reject(error)
    } else {
      ErrorHandler.handleNetworkError(error, errorContext)
    }
    if (response && response.status === 401 && window.location.pathname !== '/user/login') {
      // 处理token过期逻辑
      const userStore = useUserStore()
      const refreshToken = userStore.storedRefreshToken
      if (!refreshToken) {
        userStore.logout()
        // 只有在非首页时才跳转到登录页
        if (window.location.pathname !== '/') {
          window.location.href = '/user/login'
        }
        return Promise.resolve(response)
      }

      if (!refreshTokenPromise) {
        refreshTokenPromise = userStore.handleRefreshToken(refreshToken)
      }
      const refreshSuccess = await refreshTokenPromise
      if (!refreshSuccess) {
        userStore.logout()
        // 只有在非首页时才跳转到登录页
        if (window.location.pathname !== '/') {
          Message.error('Failed to refresh token')
          window.location.href = '/user/login'
        }
        return Promise.resolve(response)
      }

      return axios.request(error.config)
    }
    if (!response || typeof response.data !== 'object') {
      Message.error('Sorry, the network is unstable, please try again later')
      return Promise.reject(error)
    } else {
      response.data.isOk = false
    }
    return Promise.resolve(response)
  }
)

// 性能优化：清理 refresh token promise 的工具函数
export function clearRefreshTokenPromise() {
  refreshTokenPromise = null
}

// 性能优化：获取当前待处理请求数量（用于调试）
export function getPendingRequestsCount() {
  return pendingRequests.size
}

// 性能优化：手动清理所有待处理请求
export function clearAllPendingRequests() {
  pendingRequests.clear()
}
