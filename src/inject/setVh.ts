// 设置 rem 函数
const isMobile = import.meta.env.VITE_DEVICE === 'mobile'

function setVh() {
  // 使用 requestAnimationFrame 确保在下一帧计算 vh
  requestAnimationFrame(() => {
    // 在PC端（1366px以上）时使用固定的手机高度
    const isPC = window.innerWidth >= 1366
    const vh = window.innerHeight * 0.01
    document.documentElement.style.setProperty('--vh', `${vh}px`)
  })
}

// 使用防抖优化 resize 事件处理
function debounce<T extends (...args: any[]) => void>(fn: T, delay: number) {
  let timer: number | null = null
  return function (this: any, ...args: Parameters<T>) {
    if (timer) window.clearTimeout(timer)
    timer = window.setTimeout(() => {
      fn.apply(this, args)
    }, delay)
  }
}

// 初始化
setVh()

// 改变窗口大小时重新设置 rem & vh，使用防抖优化
window.addEventListener('resize', debounce(setVh, 100))

// 添加页面可见性变化监听，确保在页面切换回来时重新计算
document.addEventListener('visibilitychange', () => {
  if (document.visibilityState === 'visible') {
    setVh()
  }
})

// 确保在所有资源加载完成后重新计算一次
window.addEventListener('load', () => {
  setVh()
})
