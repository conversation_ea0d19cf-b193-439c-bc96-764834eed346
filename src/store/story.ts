import { defineStore } from 'pinia'
import type { Story, Actor, Category } from '@/api/stories'
import { fetchStoryList, getStoryCategories, getStoryListByCategory } from '@/api/stories'
import { getPreloadResources } from '@/api/stories'
import { getStoryDetail } from '@/api/stories'
import { PreloadResource } from '@/api/stories'
import {
  addFavorite,
  removeFavorite,
  getFavoriteStatus,
  getHobbyCollectionStatus,
  createHobbyCollection
} from '@/api/stories'
import { Message } from '@/mobile/components/Message'
import { ref } from 'vue'
import { RatingHistory } from '@/types/chat'
import { getRatingHistory } from '@/api/chat'
import { reportEvent } from '@/utils'
import { ReportEvent } from '@/interface'
import { useUserStore } from '@/store/user'
interface PreloadedBlob {
  url: string
  blob: Blob
}

interface StoryState {
  currentStory: Story | null
  currentActor: Actor | null
  stories: Story[]
  loading: boolean
  error: string | null
  preloadResourceList: PreloadResource[]
  preloadedUrls: string[]
  isPreloading: boolean
  preloadedBlobs: Map<string, Blob>
  currentActors: Actor[]
  storyCategories: Category[]
  currentCategory: Category | { id: 'all'; name: 'All'; level: 0 }
  isFavoriteLoading: boolean
  isShowRatingModal: boolean
  isShowTagRatingModal: boolean
  isCollectedHobby: boolean
  isDiscount: boolean
  isNeedRecharge: boolean
  lastFetchParams?: {
    categoryIds: string[]
    sort: string
  }
}

// 定义预加载资源的接口

export const useStoryStore = defineStore('story', {
  state: (): StoryState => ({
    currentStory: null,
    currentActor: null,
    stories: [],
    loading: false,
    error: null,
    preloadResourceList: [],
    preloadedUrls: [],
    isPreloading: false,
    preloadedBlobs: new Map<string, Blob>(),
    currentActors: [],
    storyCategories: [],
    currentCategory: {
      id: 'all',
      name: 'All',
      level: 0
    },
    isFavoriteLoading: false,
    isShowRatingModal: false,
    isShowTagRatingModal: false,
    isCollectedHobby: false,
    isDiscount: false,
    isNeedRecharge: false,
    lastFetchParams: undefined
  }),

  getters: {
    hotStories: (state) => state.stories,
    unpreloadedResources: (state) =>
      state.preloadResourceList.filter((resource) => !resource.isPreloaded)
  },

  actions: {
    // 初始化store
    initializeStore() {
      // 确保 preloadedBlobs 是 Map 实例
      if (!(this.preloadedBlobs instanceof Map)) {
        // 如果是普通对象，尝试转换为Map
        if (this.preloadedBlobs && typeof this.preloadedBlobs === 'object') {
          this.preloadedBlobs = new Map(Object.entries(this.preloadedBlobs))
        } else {
          this.preloadedBlobs = new Map()
        }
      }
    },

    getPreloadedBlob(url: string): Blob | null {
      this.initializeStore() // 确保Map已初始化
      return this.preloadedBlobs.get(url) || null
    },

    setPreloadedBlob(url: string, blob: Blob) {
      this.initializeStore() // 确保Map已初始化
      this.preloadedBlobs.set(url, blob)
      this.markUrlAsPreloaded(url)
    },

    isUrlPreloaded(url: string): boolean {
      return this.preloadedUrls.includes(url)
    },

    // 标记 URL 为已预加载
    markUrlAsPreloaded(url: string) {
      if (!this.preloadedUrls.includes(url)) {
        this.preloadedUrls.push(url)
      }
    },
    // 重置预加载状态
    resetPreloadState() {
      this.preloadResourceList = []
      this.preloadedUrls = []
      this.isPreloading = false
      this.initializeStore() // 确保Map已初始化
      this.preloadedBlobs.clear() // 清空Map
    },
    async fetchStories(categoryIds?: string[], sort?: string) {
      this.loading = true
      this.error = null
      const filterCategoryIds = categoryIds?.filter((id) => id !== '')

      // 保存请求参数用于缓存比较
      this.lastFetchParams = {
        categoryIds: filterCategoryIds || [],
        sort: sort || 'popular'
      }

      try {
        const result = await fetchStoryList(filterCategoryIds, sort)
        if (result.data.code === '0') {
          this.stories = result.data.data.stories || []
        } else {
          this.error = result.data.message
          this.stories = [] // 确保在错误时清空故事列表
        }
      } catch (err) {
        this.error = 'Failed to load stories'
        this.stories = [] // 确保在错误时清空故事列表
        console.error('Error fetching stories:', err)
      } finally {
        this.loading = false
      }
    },

    setCurrentStory(story: Story, isDiscount?: boolean) {
      this.currentStory = story
      if (isDiscount === undefined) return
      this.isDiscount = isDiscount
    },

    setCurrentActors(actors: Actor[]) {
      this.currentActors = actors
    },

    setCurrentActor(actor: Actor) {
      this.currentActor = actor
    },

    setCurrentCategory(category: Category) {
      this.currentCategory = category
    },

    clearCurrent() {
      this.currentStory = null
      this.currentActor = null
      this.currentCategory = null
      this.currentActors = []
    },
    async getPreloadResources(actorId: string, storyId: string) {
      const result = await getPreloadResources(actorId, storyId)
      this.handlePreloadResources(result?.data?.data?.preloads)
    },
    async handlePreloadResources(resources: PreloadResource[]) {
      if (this.isPreloading || !resources?.length) return
      this.isPreloading = true

      try {
        // 过滤掉已经预加载的资源
        const newResources = resources.filter(
          (resource) => !this.preloadedUrls.includes(resource.url)
        )
        this.preloadResourceList = newResources

        // 创建预加载函数
        const preloadImage = (url: string): Promise<void> => {
          return new Promise((resolve, reject) => {
            const img = new Image()
            img.onload = () => {
              this.markUrlAsPreloaded(url) // 加载成功时标记
              resolve()
            }
            img.onerror = () => reject()
            img.src = url
          })
        }

        const preloadVideo = (url: string): Promise<void> => {
          return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest()
            xhr.open('GET', url, true)
            xhr.responseType = 'blob'

            xhr.onload = () => {
              if (xhr.status === 200) {
                const blob = xhr.response
                this.setPreloadedBlob(url, blob) // 存储 Blob
                resolve()
              } else {
                reject(new Error(`Failed to load video: ${xhr.status}`))
              }
            }
            xhr.onerror = () => reject()
            xhr.send()
          })
        }
        // 顺序预加载
        for (const resource of newResources) {
          try {
            if (resource.type === 'video') {
              await preloadVideo(resource.url)
            } else {
              await preloadImage(resource.url)
            }
          } catch (error) {
            console.error(`Failed to preload ${resource.type}:`, resource.url, error)
          }
        }
      } catch (error) {
        console.error('Error in preloading resources:', error)
      } finally {
        this.isPreloading = false
      }
    },
    async toggleFavorite() {
      if (!this.currentStory || this.isFavoriteLoading) return false

      try {
        const currentStatus = this.currentStory.is_fav
        this.isFavoriteLoading = true
        const response = currentStatus
          ? await removeFavorite(this.currentStory.id)
          : await addFavorite(this.currentStory.id)

        if (response.data.code === '0') {
          this.currentStory.is_fav = !currentStatus
          return true
        }
        return false
      } catch (error) {
        Message.error('Failed to toggle favorite')
        return false
      } finally {
        this.isFavoriteLoading = false
      }
    },

    async getStoreDetail(storyId?: string, shouldSetCurrent: boolean = true) {
      if (!storyId && !this.currentStory) return
      const result = await getStoryDetail(storyId || this.currentStory?.id)
      if (result.data.code === '0') {
        if (shouldSetCurrent) {
          this.setCurrentStory(result.data.data.story, result.data.data.is_discount_user)
          this.setCurrentActors(result.data.data.story.actors)
        }
        return result.data.data
      } else {
        this.error = result.data.message
        return null
      }
    },
    async getStoryCategories() {
      const result = await getStoryCategories()
      if (result.data.code === '0') {
        const all = {
          id: 'all',
          name: 'All',
          level: 0
        }
        this.storyCategories = [all, ...(result?.data?.data?.category ?? [])]
      } else {
        this.error = result.data.message
        this.storyCategories = []
      }
    },
    async getStoryListByCategory(categoryId: string) {
      const result = await getStoryListByCategory(categoryId)
      if (result.data.code === '0') {
        this.stories = result.data.data.stories
      } else {
        this.error = result.data.message
        this.stories = []
      }
    },
    // 获取评分历史
    async getRatingHistory(): Promise<RatingHistory | null> {
      const { data } = await getRatingHistory(this.currentStory?.id, this.currentActor?.id)
      this.isShowRatingModal = data.isOk && data.data.is_commentable === true
      return data.isOk ? data.data : null
    },
    async checkHobbyCollectionStatus() {
      this.loading = true
      this.error = null

      try {
        const { data } = await getHobbyCollectionStatus()
        this.isCollectedHobby = data.data.is_collected_hobby
        if (this.isCollectedHobby) {
          await this.getRatingHistory()
        } else {
          this.isShowRatingModal = false
          this.isShowTagRatingModal = true
          reportEvent(ReportEvent.TagRatingModalExpose, {
            storyId: this.currentStory?.id,
            actorId: this.currentActor?.id,
            storyName: this.currentStory?.title,
            actorName: this.currentActor?.name
          })
        }
      } catch (err) {
        this.error = err instanceof Error ? err.message : 'An unexpected error occurred'
        throw err
      } finally {
        this.loading = false
      }
    },
    async submitHobbyCollection(selectedTags: Record<string, string[]>) {
      const userStore = useUserStore()
      this.loading = true
      this.error = null

      try {
        const { data } = await createHobbyCollection(selectedTags)
        if (data.code === '0') {
          this.isCollectedHobby = true
          this.isShowTagRatingModal = false
          reportEvent(ReportEvent.TagRatingModalSubmit, {
            storyId: this.currentStory?.id,
            actorId: this.currentActor?.id,
            storyName: this.currentStory?.title,
            actorName: this.currentActor?.name
          })
          userStore.setUserInfo(data.data.user)
        } else {
          this.error = data.message
        }
      } catch (err) {
        this.error = err instanceof Error ? err.message : 'An unexpected error occurred'
        throw err
      } finally {
        this.loading = false
      }
    }
  },
  persist: {
    omit: ['stories', 'storyCategories']
  }
})
