import { defineStore } from 'pinia'
import { ref } from 'vue'
import { PriceItem, getPriceList, PriceExtra } from '@/api/payment'

export const useRechargeStore = defineStore('recharge', () => {
  const visible = ref(false)
  const priceList = ref<PriceItem[]>([])
  const loading = ref(false)

  // 获取价格列表
  const fetchPriceList = async () => {
    try {
      loading.value = true
      const response = await getPriceList()
      if (response.code === '0') {
        priceList.value = response.data
      } else {
        throw new Error(response.message)
      }
    } catch (error) {
      console.error('Failed to fetch price list:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  // 显示充值弹窗
  const showRechargeModal = () => {
    visible.value = true
  }

  // 隐藏充值弹窗
  const hideRechargeModal = () => {
    visible.value = false
  }

  // 切换充值弹窗显示状态
  const toggleRechargeModal = () => {
    visible.value = !visible.value
  }

  return {
    visible,
    priceList,
    loading,
    fetchPriceList,
    showRechargeModal,
    hideRechargeModal,
    toggleRechargeModal
  }
})
