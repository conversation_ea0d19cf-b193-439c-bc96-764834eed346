// 基础 SSE 数据接口
export interface BaseSSEData {
  event_type: string
  timestamp: number
  data: Record<string, any>
}

// 任务接口
export interface Task {
  task_id: number
  percent: number
  description: string
}

// 预加载项接口
export interface PreloadItem {
  url: string
  type: 'video' | 'image'
}

// 覆盖层接口
export interface Overlay {
  text: string
  position: 'bottom' | 'top' | 'center'
  display_time: 'after' | 'before'
  button?: {
    icon: string
    text: string
    action: string
  }
}

// 用户头像接口
export interface UserAvatar {
  id?: string
  url: string
  type: 'predefined' | 'custom'
}

// 结束内容接口
export interface Ending {
  html: string
}

// 语音配置接口
export interface VoiceConfig {
  voice_id: string | null
  provider: string | null
}
