import { defineStore } from 'pinia'
import { ref } from 'vue'
import { useDeviceDetection } from '@/composables/useDeviceDetection'
import { getABTestVariant } from '@/utils/abtest'
import { getExperimentConfig } from '@/config/abtest'

export type ThemeMode = 'light' | 'dark'

export const useThemeStore = defineStore('theme', () => {
  const { isMobileDevice } = useDeviceDetection()
  const storedTheme = localStorage.getItem('theme') as ThemeMode | null

  // 获取默认主题
  const getDefaultTheme = (): ThemeMode => {
    if (!isMobileDevice) {
      // PC端默认亮色主题
      return 'light'
    } else {
      // 移动端根据AB测试决定默认主题
      const config = getExperimentConfig('mobile_default_theme')
      if (config) {
        const variant = getABTestVariant('mobile_default_theme', config)
        // A组默认亮色主题，B组默认暗色主题
        return variant === 'A' ? 'light' : 'dark'
      }
      // 如果AB测试配置不存在，回退到暗色主题
      return 'dark'
    }
  }

  const theme = ref<ThemeMode>(storedTheme || getDefaultTheme())

  // 是否为暗色主题
  const isDarkTheme = ref(theme.value === 'dark')

  // 切换主题
  const toggleTheme = () => {
    theme.value = theme.value === 'dark' ? 'light' : 'dark'
    isDarkTheme.value = theme.value === 'dark'
    applyTheme()
  }

  // 设置特定主题
  const setTheme = (newTheme: ThemeMode) => {
    theme.value = newTheme
    isDarkTheme.value = newTheme === 'dark'
    applyTheme()
  }

  // 更新浏览器主题色
  const updateThemeColor = () => {
    const themeColorMeta = document.getElementById('theme-color-meta') as HTMLMetaElement
    if (themeColorMeta) {
      // 根据当前主题设置对应的主题色
      const themeColor = theme.value === 'dark' ? '#180430' : '#f8f9fa'
      themeColorMeta.setAttribute('content', themeColor)
    }
  }

  // 应用主题到DOM
  const applyTheme = () => {
    // 保存到localStorage
    localStorage.setItem('theme', theme.value)

    // 应用到body
    if (theme.value === 'dark') {
      document.body.classList.add('dark-theme')
      document.body.classList.remove('light-theme')
    } else {
      document.body.classList.add('light-theme')
      document.body.classList.remove('dark-theme')
    }

    // 更新浏览器主题色
    updateThemeColor()
  }

  // 初始化主题
  const initTheme = () => {
    applyTheme()

    // 在空闲时执行事件收集
    if (window.collectEvent) {
      const collectThemeEvent = () => {
        // @ts-ignore
        window.collectEvent('config', {
          theme: theme.value
        })
      }

      if (window.requestIdleCallback) {
        window.requestIdleCallback(collectThemeEvent)
      } else {
        // 降级方案：使用 setTimeout
        setTimeout(collectThemeEvent, 0)
      }
    }
  }

  // 监听系统主题变化
  const listenForSystemThemeChanges = () => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')

    const handleChange = (e: MediaQueryListEvent) => {
      const newTheme: ThemeMode = e.matches ? 'dark' : 'light'
      setTheme(newTheme)
    }

    mediaQuery.addEventListener('change', handleChange)

    // 如果没有存储的主题，则使用系统主题
    if (!storedTheme) {
      const systemTheme: ThemeMode = mediaQuery.matches ? 'dark' : 'light'
      setTheme(systemTheme)
    }

    // 返回清理函数
    return () => {
      mediaQuery.removeEventListener('change', handleChange)
    }
  }

  return {
    theme,
    isDarkTheme,
    toggleTheme,
    setTheme,
    initTheme,
    listenForSystemThemeChanges
  }
})
