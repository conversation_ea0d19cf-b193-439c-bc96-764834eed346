import { defineStore } from 'pinia'
import type { Actor } from '@/types/actor'
import { fetchActorList, formatNumber } from '@/api/characters'

interface ActorState {
  currentActor: Actor | null
  actors: Actor[]
  loading: boolean
  error: string | null
}

export const useActorStore = defineStore('actor', {
  state: (): ActorState => ({
    currentActor: null,
    actors: [],
    loading: false,
    error: null
  }),

  getters: {
    hotActors: (state) => state.actors.sort((a, b) => b.hot - a.hot)
    // animeActors: (state) =>
    //   state.actors.filter((actor) =>
    //     actor.tags.some((tag) => tag.name.toLowerCase().includes('anime'))
    //   ),
  },

  actions: {
    async fetchActors() {
      this.loading = true
      this.error = null

      try {
        const result = await fetchActorList()
        if (result.code === '0') {
          this.actors = result.data.actors
        } else {
          this.error = result.message
        }
      } catch (err) {
        this.error = 'Failed to load characters'
        console.error('Error fetching actors:', err)
      } finally {
        this.loading = false
      }
    },

    formatNumber(num: number): string {
      return formatNumber(num)
    },

    setCurrentActor(actor: Actor) {
      this.currentActor = actor
    },

    clearCurrentActor() {
      this.currentActor = null
    }
  }
})
