import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { SkillInfo } from '@/interface/skill'
import {
  getSkillInfo,
  fetchSkillDetail,
  createSkill,
  updateSkill,
  getAllSkillInfo
} from '@/api/skill'

export const useSkillStore = defineStore(
  'skill',
  () => {
    const skillList = ref<SkillInfo[]>([])
    const selectedSkills = ref<SkillInfo[]>([])
    const initialAttributes = ref({
      happiness: 0,
      intelligence: 0,
      strength: 0,
      wealth: 0
    })

    // 用户可分配的总点数
    const totalAssignablePoints = ref(20)

    // 用户分配的点数
    const userAllocatedPoints = ref({
      happiness: 0,
      intelligence: 0,
      strength: 0,
      wealth: 0
    })

    // 计算总属性值
    const totalAttributes = computed(() => ({
      happiness: initialAttributes.value.happiness + userAllocatedPoints.value.happiness,
      intelligence: initialAttributes.value.intelligence + userAllocatedPoints.value.intelligence,
      strength: initialAttributes.value.strength + userAllocatedPoints.value.strength,
      wealth: initialAttributes.value.wealth + userAllocatedPoints.value.wealth
    }))

    // 计算已使用的用户点数
    const usedUserPoints = computed(() =>
      Object.values(userAllocatedPoints.value).reduce((acc, val) => acc + val, 0)
    )

    const loading = ref(false)

    const fetchAllSkillInfo = async () => {
      try {
        loading.value = true
        const { data } = await getAllSkillInfo()
        skillList.value = data.data.skills
      } catch (error) {
        console.error('Failed to fetch skill list:', error)
      } finally {
        loading.value = false
      }
    }

    const fetchSkillList = async (storyId: string) => {
      try {
        loading.value = true
        const { data } = await getSkillInfo(storyId)
        skillList.value = data.data.skills
      } catch (error) {
        console.error('Failed to fetch skill list:', error)
      } finally {
        loading.value = false
      }
    }

    const calculateInitialAttributes = (skill: SkillInfo) => {
      // 重置用户分配的点数
      Object.keys(userAllocatedPoints.value).forEach((key) => {
        userAllocatedPoints.value[key] = 0
      })

      // 重置初始属性
      Object.keys(initialAttributes.value).forEach((key) => {
        initialAttributes.value[key] = 0
      })

      // 重置可分配点数为默认值20
      totalAssignablePoints.value = 20

      skill?.skill_configs?.effect?.forEach((effect) => {
        if (Object.prototype.hasOwnProperty.call(initialAttributes.value, effect.attr)) {
          initialAttributes.value[effect.attr] = parseInt(effect.value, 10)
        }

        // 处理可分配点数的修改
        if (effect.attr === 'assignablePoints') {
          totalAssignablePoints.value += parseInt(effect.value, 10)
          // 确保可分配点数不会小于0
          totalAssignablePoints.value = Math.max(0, totalAssignablePoints.value)
        }
      })
    }

    // 增加属性点
    const increaseAttribute = (attr: string) => {
      if (
        usedUserPoints.value < totalAssignablePoints.value && // 未超过可分配点数
        totalAttributes.value[attr] < 10 // 未超过属性最大值
      ) {
        userAllocatedPoints.value[attr]++
      }
    }

    // 减少属性点
    const decreaseAttribute = (attr: string) => {
      if (userAllocatedPoints.value[attr] > 0) {
        userAllocatedPoints.value[attr]--
      }
    }

    const selectSkill = (skill: SkillInfo) => {
      const isCurrentlySelected = selectedSkills.value.some((s) => s.id === skill.id)
      if (isCurrentlySelected) {
        selectedSkills.value = []
      } else {
        selectedSkills.value = [skill]
        calculateInitialAttributes(skill)
      }
    }

    const removeSkill = (skillId: string) => {
      selectedSkills.value = selectedSkills.value.filter((s) => s.id !== skillId)
    }

    const getSkillDetail = async (storyId: string, actorId: string) => {
      const { data } = await fetchSkillDetail(storyId, actorId)
      selectedSkills.value = [data.data.skill]
      return data.data
    }

    const addSkill = (skill: SkillInfo) => {
      skillList.value = [...skillList.value, skill]
    }

    const createOrUpdateSkill = async (skill: SkillInfo) => {
      try {
        let response
        if (skill.id) {
          response = await updateSkill(skill)
        } else {
          response = await createSkill(skill)
          console.log('response', response.data)
        }
        if (response.data.code === '0') {
          // 刷新技能列表
          await fetchAllSkillInfo()
          return true
        } else {
          return false
        }
      } catch (error) {
        console.error('Failed to create/update skill:', error)
        return false
      }
    }

    // Add computed property for skills
    const skills = computed(() => skillList.value)

    const reset = () => {
      selectedSkills.value = []
      initialAttributes.value = {
        happiness: 0,
        intelligence: 0,
        strength: 0,
        wealth: 0
      }
      // Reset user allocated points as well
      userAllocatedPoints.value = {
        happiness: 0,
        intelligence: 0,
        strength: 0,
        wealth: 0
      }
      // Reset to default assignable points
      totalAssignablePoints.value = 20
    }

    return {
      skillList,
      selectedSkills,
      skills,
      initialAttributes,
      userAllocatedPoints,
      totalAttributes,
      usedUserPoints,
      totalAssignablePoints,
      loading,
      fetchSkillList,
      fetchAllSkillInfo,
      selectSkill,
      removeSkill,
      getSkillDetail,
      increaseAttribute,
      decreaseAttribute,
      addSkill,
      createOrUpdateSkill,
      reset
    }
  },
  {
    persist: {
      storage: localStorage,
      pick: ['selectedSkills']
    }
  }
)
