import { defineStore } from 'pinia'
import { ref } from 'vue'
import {
  getSysConfigList,
  type SysConfigListResponse,
  type CheckInCoinsPerDay
} from '@/api/sysconfig'

export const useSysConfigStore = defineStore(
  'sysconfig',
  () => {
    const checkInCoinsPerDay = ref<CheckInCoinsPerDay | null>(null)
    const userHobbyCollection = ref<Record<string, object> | null>(null)
    const loading = ref(false)
    const error = ref<string | null>(null)

    const fetchConfigs = async () => {
      loading.value = true
      error.value = null

      try {
        const { data } = await getSysConfigList()
        checkInCoinsPerDay.value = data.data.checkin.check_in_coins_per_day
        userHobbyCollection.value = data.data.user_hobby_collection
      } catch (err) {
        error.value = err instanceof Error ? err.message : 'An unexpected error occurred'
        throw err
      } finally {
        loading.value = false
      }
    }

    const getCheckInCoins = (day: number) => {
      if (!checkInCoinsPerDay.value) return 0

      const dayMap: Record<number, keyof CheckInCoinsPerDay> = {
        1: 'day_one',
        2: 'day_two',
        3: 'day_three',
        4: 'day_four',
        5: 'day_five',
        6: 'day_six',
        7: 'day_seven'
      }

      const key = dayMap[day]
      return key ? checkInCoinsPerDay.value[key] : 0
    }

    return {
      // State
      checkInCoinsPerDay,
      userHobbyCollection,
      loading,
      error,

      // Actions
      fetchConfigs,
      getCheckInCoins
    }
  },
  {
    persist: false
  }
)
