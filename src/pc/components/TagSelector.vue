<template>
  <div class="tag-selector">
    <div class="tags-grid">
      <div
        v-for="tag in tags"
        :key="tag.id"
        class="tag-item"
        :class="{ active: selectedTags.includes(tag.id) }"
        @click="toggleTag(tag.id)"
      >
        {{ tag.name }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

const props = defineProps<{
  tags: { id: string; name: string }[]
  modelValue: string[]
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: string[]): void
}>()

const selectedTags = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const toggleTag = (id: string) => {
  const newSelectedTags = [...selectedTags.value]
  const index = newSelectedTags.indexOf(id)
  
  if (index === -1) {
    newSelectedTags.push(id)
  } else {
    newSelectedTags.splice(index, 1)
  }
  
  selectedTags.value = newSelectedTags
}
</script>

<style lang="less" scoped>
@import '@/assets/style/theme.less';

.tag-selector {
  padding: 16px;
  max-height: 300px;
  overflow-y: auto;

  .tags-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    .tag-item {
      padding: 6px 12px;
      border-radius: 16px;
      background-color: var(--bg-secondary);
      color: var(--text-secondary);
      font-size: 13px;
      cursor: pointer;
      transition: all 0.2s ease;
      white-space: nowrap;

      &:hover {
        background-color: var(--bg-hover);
      }

      &.active {
        background-color: var(--accent-bg);
        color: var(--accent-color);
      }
    }
  }
}
</style>
