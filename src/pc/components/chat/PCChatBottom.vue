<template>
  <div class="pc-chat-bottom">
    <!-- 历史记录按钮 -->
    <button class="history-button" @click="handleToggleHistory">
      <svg viewBox="0 0 32 32" fill="none">
        <path
          d="M16 0C7.16344 0 0 7.16344 0 16C0 24.8366 7.16344 32 16 32C24.8366 32 32 24.8366 32 16C32 7.16344 24.8366 0 16 0ZM16 29.6296C8.47656 29.6296 2.37037 23.5234 2.37037 16C2.37037 8.47656 8.47656 2.37037 16 2.37037C23.5234 2.37037 29.6296 8.47656 29.6296 16C29.6296 23.5234 23.5234 29.6296 16 29.6296Z"
          fill="currentColor"
        />
        <path
          d="M16 5.92593C15.3456 5.92593 14.8148 6.45674 14.8148 7.11111V16C14.8148 16.3148 14.9407 16.6148 15.1630 16.8370L20.6815 22.3556C21.1481 22.8222 21.9037 22.8222 22.3704 22.3556C22.8370 21.8889 22.8370 21.1333 22.3704 20.6667L17.1852 15.4815V7.11111C17.1852 6.45674 16.6544 5.92593 16 5.92593Z"
          fill="currentColor"
        />
      </svg>
    </button>

    <!-- 主输入区域 -->
    <div class="input-area">
      <!-- 输入框容器 -->
      <div class="input-container">
        <!-- 麦克风图标 -->
        <!-- <div class="mic-icon">
          <svg viewBox="0 0 28 37" fill="none">
            <path
              d="M14 0C10.6863 0 8 2.68629 8 6V16C8 19.3137 10.6863 22 14 22C17.3137 22 20 19.3137 20 16V6C20 2.68629 17.3137 0 14 0Z"
              stroke="currentColor"
              stroke-width="2.5"
              fill="none"
            />
            <path
              d="M8.857 34.498H19.805"
              stroke="currentColor"
              stroke-width="2.5"
              stroke-linecap="round"
            />
            <path
              d="M10.291 7.885C10.291 7.885 12.5 14 14.5 14C16.5 14 18.795 7.885 18.795 7.885"
              stroke="currentColor"
              stroke-width="2.5"
              stroke-linecap="round"
            />
          </svg>
        </div> -->

        <!-- 分隔线 -->
        <!-- <div class="separator"></div> -->

        <!-- 输入框 -->
        <input
          ref="inputRef"
          v-model="inputText"
          type="text"
          :placeholder="placeholder"
          :disabled="isDisabled"
          @keydown.enter="handleSend"
          @input="handleInput"
          class="message-input"
        />
      </div>

      <!-- 发送按钮 -->
      <button
        class="send-button"
        :class="{ disabled: !canSend }"
        :disabled="!canSend"
        @click="handleSend"
      >
        <svg viewBox="0 0 25 25" fill="none">
          <path
            d="M2.477 2.477L22.523 12L2.477 21.523V14.286L16.071 12L2.477 9.714V2.477Z"
            fill="currentColor"
          />
        </svg>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue'
import { storeToRefs } from 'pinia'
import { useChatMessagesStore } from '@/store/chat-messages'
import { useChatResourcesStore } from '@/store/chat-resources'
import { useChatUIStore } from '@/store/chat-ui'

// Emits
const emit = defineEmits<{
  (e: 'send-message', message: string): void
  (e: 'toggle-history'): void
}>()

// Stores
const chatMessagesStore = useChatMessagesStore()
const chatResourcesStore = useChatResourcesStore()
const chatUIStore = useChatUIStore()

// Refs
const inputRef = ref<HTMLInputElement>()
const inputText = ref('')

// Computed
const placeholder = computed(() => {
  if (isDisabled.value) {
    return 'Please wait...'
  }
  return 'Type a message ...'
})

const isDisabled = computed(() => {
  const isTyping = chatMessagesStore.messageTypingPromise !== null
  const isThinking = chatMessagesStore.isActorThinking
  const hasOptions = chatResourcesStore.actionOptions?.length > 0

  return isTyping || isThinking || hasOptions
})

const canSend = computed(() => {
  return (
    !isDisabled.value && inputText.value.trim().length > 0 && inputText.value.trim().length <= 500
  )
})

// Methods
const handleInput = () => {
  // 自动调整输入框高度等逻辑可以在这里添加
}

const handleSend = () => {
  if (!canSend.value) return

  const message = inputText.value.trim()
  if (message) {
    emit('send-message', message)
    inputText.value = ''

    // 重新聚焦输入框
    nextTick(() => {
      inputRef.value?.focus()
    })
  }
}

const handleToggleHistory = () => {
  emit('toggle-history')
}

// 暴露方法给父组件
defineExpose({
  focus: () => {
    inputRef.value?.focus()
  },
  clear: () => {
    inputText.value = ''
  }
})
</script>

<style lang="less" scoped>
.pc-chat-bottom {
  display: flex;
  align-items: center;
  gap: 24px;
  width: 100%;
  max-width: 736px;
  margin: 0 auto;

  .history-button {
    width: 64px;
    height: 64px;
    border-radius: 44.44px;
    background: #ca93f2;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #1f0038;
    flex-shrink: 0;
    padding: 17.78px;

    &:hover {
      background: #d4a3f7;
      transform: scale(1.02);
    }

    &:active {
      transform: scale(0.98);
    }

    svg {
      width: 29.63px;
      height: 29.63px;
    }
  }

  .input-area {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(20px);
    border-radius: 43px;
    padding: 0 8px;
    height: 64px;

    .input-container {
      flex: 1;
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 0 16px;

      .mic-icon {
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0.7;
        color: #1f0038;
        flex-shrink: 0;

        svg {
          width: 28px;
          height: 37px;
        }
      }

      .separator {
        width: 0.5px;
        height: 30px;
        background: rgba(31, 0, 56, 0.15);
        flex-shrink: 0;
      }

      .message-input {
        flex: 1;
        background: transparent;
        border: none;
        outline: none;
        color: #1f0038;
        font-family: 'Work Sans', sans-serif;
        font-size: 14px;
        font-weight: 400;
        line-height: 1.17;
        padding: 0;
        min-width: 129px;

        &::placeholder {
          color: rgba(31, 0, 56, 0.5);
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }
    }

    .send-button {
      width: 48px;
      height: 48px;
      border-radius: 25.14px;
      background: #ca93f2;
      border: none;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;
      color: #ffffff;
      flex-shrink: 0;

      &:hover:not(.disabled) {
        background: #d4a3f7;
        transform: scale(1.02);
      }

      &:active:not(.disabled) {
        transform: scale(0.98);
      }

      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
        background: #ca93f2;
      }

      svg {
        width: 24.76px;
        height: 24.76px;
      }
    }
  }
}
</style>
