<template>
  <div
    ref="containerRef"
    class="virtual-story-grid-pc"
    v-bind="containerProps"
    :style="{ height: containerHeight + 'px', overflow: 'auto' }"
  >
    <div v-bind="wrapperProps">
      <div
        v-for="{ data: rowItem, index } in list"
        :key="index"
        class="story-row-pc"
        :style="{
          gridTemplateColumns: `repeat(${currentColumns}, 1fr)`,
          gap: `${currentGap}px`,
          padding: `10px ${currentHorizontalPadding}px ${
            index === list.length - 1 ? getLastRowPadding() : '30px'
          } ${currentHorizontalPadding}px`
        }"
      >
        <StoryCard
          v-for="(story, index) in rowItem.stories"
          :key="story.id || `skeleton-${index}`"
          :story="story"
          :is-pc="true"
          :loading="loading"
          class="story-card-item"
          @click="$emit('story-click', story)"
          @image-loaded="$emit('image-loaded', story)"
          @subscription-change="$emit('subscription-change', $event)"
          @need-login="$emit('need-login', $event)"
          @need-email="$emit('need-email', $event)"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineAsyncComponent, onMounted, computed, ref, onUnmounted } from 'vue'
import { useVirtualList } from '@vueuse/core'
import type { Story } from '@/api/stories'
import { performanceMonitor } from '@/utils/performance'
import {
  useVirtualScrollOptimization,
  useOptimalContainerHeight
} from '@/composables/useVirtualScrollOptimization'
import { scrollPerformanceMonitor } from '@/utils/scroll-performance-monitor'

const StoryCard = defineAsyncComponent(() => import('@/shared/components/StoryCard.vue'))

interface Props {
  stories: Story[]
  itemHeight?: number
  columnsCount?: number
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  columnsCount: 6, // PC端默认6列，会根据屏幕宽度自适应
  loading: false
})

defineEmits<{
  'story-click': [story: Story]
  'image-loaded': [story: Story]
  'subscription-change': [story: Story]
  'need-login': [message?: string]
  'need-email': [message?: string]
}>()

// 响应式列数计算
const currentColumns = ref(props.columnsCount)
const currentGap = ref(20)
const currentPadding = ref(12)
const currentHorizontalPadding = ref(24)

// 动态计算行高
const currentItemHeight = ref(props.itemHeight)

// 容器引用和高度
const containerRef = ref<HTMLElement>()
const containerHeight = ref(600)

// 使用性能优化 composables
const { getRecommendedConfig } = useVirtualScrollOptimization()
const { getOptimalHeight, getOptimalItemHeight } = useOptimalContainerHeight()

// 获取推荐配置
const recommendedConfig = getRecommendedConfig()

// 动态计算最后一行的底部 padding
const getLastRowPadding = () => {
  const width = window.innerWidth
  if (width >= 1800) {
    return '50px' // 大屏幕适中的底部间距
  } else if (width >= 1400) {
    return '40px'
  } else if (width >= 1200) {
    return '35px'
  } else if (width >= 900) {
    return '30px'
  } else {
    return '25px' // 小屏幕较小的底部间距
  }
}

// 根据屏幕宽度计算列数和行高
const calculateColumns = () => {
  const width = window.innerWidth
  let columns = 6 // 默认6列，减少列数让卡片更大
  let gap = 20

  if (width >= 2200) {
    columns = 7
    gap = 20
  } else if (width >= 1800) {
    columns = 6 // 大屏幕最大6列，避免卡片过小
    gap = 24
  } else if (width >= 1600) {
    columns = 6
    gap = 20
  } else if (width >= 1400) {
    columns = 6
    gap = 18
  } else if (width >= 1200) {
    columns = 6
    gap = 16
  } else if (width >= 900) {
    columns = 5
    gap = 16
  } else if (width >= 600) {
    columns = 4
    gap = 14
  } else {
    columns = 2
    gap = 12
  }

  currentColumns.value = columns
  currentGap.value = gap

  // 根据屏幕大小调整padding
  // 上下padding包含行间距，下padding更大来创建行间距
  if (width >= 1800) {
    currentPadding.value = 10 // 上padding
    currentHorizontalPadding.value = 24
  } else if (width >= 1400) {
    currentPadding.value = 8
    currentHorizontalPadding.value = 20
  } else if (width >= 1200) {
    currentPadding.value = 6
    currentHorizontalPadding.value = 18
  } else if (width >= 900) {
    currentPadding.value = 4
    currentHorizontalPadding.value = 16
  } else {
    currentPadding.value = 2
    currentHorizontalPadding.value = 14
  }

  // 使用更大的固定行高，确保不会重叠
  if (width >= 1800) {
    currentItemHeight.value = 480 // 8列时的固定行高，增加空间
  } else if (width >= 1600) {
    currentItemHeight.value = 460 // 7列时的固定行高
  } else if (width >= 1400) {
    currentItemHeight.value = 440 // 6列时的固定行高
  } else if (width >= 1200) {
    currentItemHeight.value = 420 // 5列时的固定行高
  } else if (width >= 900) {
    currentItemHeight.value = 400 // 4列时的固定行高
  } else {
    currentItemHeight.value = 380 // 小屏幕时的固定行高
  }
}

// 将故事数据按行分组
const rowData = computed(() => {
  const rows: { rowIndex: number; stories: Story[] }[] = []
  for (let i = 0; i < props.stories.length; i += currentColumns.value) {
    rows.push({
      rowIndex: i,
      stories: props.stories.slice(i, i + currentColumns.value)
    })
  }
  return rows
})

// VueUse 虚拟列表 - 动态优化配置
const { list, containerProps, wrapperProps } = useVirtualList(rowData, {
  itemHeight: () => currentItemHeight.value, // 使用响应式高度，确保动态组件加载后能正确计算
  overscan: props.loading ? 2 : recommendedConfig.overscan // 根据设备性能动态调整
})

// 监听窗口大小变化
let resizeObserver: ResizeObserver | null = null

// 性能监控
onMounted(() => {
  performanceMonitor.mark('virtual-scroll-pc-mount')

  // 初始化列数
  calculateColumns()

  // 计算容器高度 - 使用优化的高度计算
  const updateContainerHeight = () => {
    containerHeight.value = getOptimalHeight()
  }
  updateContainerHeight()

  // 监听窗口大小变化 - 添加防抖优化
  let resizeTimer: number
  const handleResize = () => {
    if (resizeTimer) {
      clearTimeout(resizeTimer)
    }
    resizeTimer = window.setTimeout(() => {
      calculateColumns()
      updateContainerHeight()
    }, 150) // 防抖延迟150ms
  }

  window.addEventListener('resize', handleResize, { passive: true })

  // 清理函数
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
    if (resizeObserver) {
      resizeObserver.disconnect()
    }
    if (resizeTimer) {
      clearTimeout(resizeTimer)
    }
  })

  // 开发环境下启用性能监控
  // if (import.meta.env.DEV) {
  //   console.log(`📊 PC VueUse Virtual List initialized:`, {
  //     totalItems: props.stories.length,
  //     totalRows: rowData.value.length,
  //     columns: currentColumns.value,
  //     itemHeight: getOptimalItemHeight(),
  //     gap: currentGap.value,
  //     padding: `10px ${currentHorizontalPadding.value}px 30px ${currentHorizontalPadding.value}px`,
  //     containerHeight: containerHeight.value,
  //     screenWidth: window.innerWidth,
  //     virtualListItems: list.value.length,
  //     recommendedConfig
  //   })

  //   // 启动性能监控
  //   setTimeout(() => {
  //     if (containerRef.value) {
  //       scrollPerformanceMonitor.startMonitoring(containerRef.value)
  //     }
  //   }, 1000)
  // }
})
</script>

<style lang="less" scoped>
.virtual-story-grid-pc {
  width: 100%;
  // height 通过动态样式设置

  // 滚动性能优化
  scroll-behavior: auto; // 禁用平滑滚动，提升性能
  -webkit-overflow-scrolling: touch; // iOS 滚动优化
  contain: layout style paint; // CSS containment 优化

  // 强制硬件加速
  transform: translateZ(0);
  will-change: scroll-position;
}

.story-row-pc {
  display: grid;
  width: 100%;
  box-sizing: border-box;
  // position, transform, grid-template-columns, gap, padding 通过动态样式设置

  // 性能优化
  contain: layout style; // 限制重排重绘范围
  transform: translateZ(0); // 强制硬件加速
}

.story-card-item {
  width: 100%;
  height: auto;
}
</style>
