<template>
  <!-- 资源加载器 -->
  <!-- <ResourceLoader
    :visible="showLoader"
    :progress="loadingProgress.percentage"
    :loading-text="loadingProgress.currentResource"
    :logo-url="logoUrl"
  /> -->

  <Teleport to="body">
    <!--<Transition name="fade">
      <div v-if="visible" class="login-modal-overlay" @click.self="handleClose">
        <div class="login-modal">
          <!~~ Logo ~~>
          <div class="modal-logo">
            <img :src="logoUrl" alt="ReelPlay" />
          </div>

          <div class="modal-content">
            <!~~ Left Preview Section ~~>
            <div class="preview-section">
              <!~~ Main Image Display ~~>
              <div class="main-image-container">
                <!~~ 角色滑动容器 ~~>
                <div
                  class="character-slider"
                  :style="{ transform: `translateX(-${currentCharacterIndex * 33.333}%)` }"
                >
                  <div
                    v-for="(character, charIndex) in characters"
                    :key="character.id"
                    class="character-slide"
                  >
                    <!~~ 图片容器，支持溶解过渡效果（仅用于demo按钮切换） ~~>
                    <div class="media-container">
                      <!~~ 背景层 - 当前显示的媒体 ~~>
                      <div class="media-layer background-layer">
                        <video
                          v-if="
                            charIndex === currentCharacterIndex && currentMedia?.type === 'video'
                          "
                          :src="currentMedia.src"
                          class="main-media"
                          autoplay
                          loop
                          muted
                          playsinline
                          @loadeddata="handleMediaLoaded"
                          @canplay="handleMediaLoaded"
                        />
                        <img
                          v-else-if="charIndex === currentCharacterIndex && currentMedia"
                          :src="currentMedia.src"
                          alt="Preview"
                          class="main-media"
                          @load="handleMediaLoaded"
                          @error="handleMediaError"
                        />
                        <!~~ 非当前角色显示默认图片 ~~>
                        <img
                          v-else
                          :src="
                            character.mediaResources.find(
                              (m) =>
                                m.scene === 'living-room' &&
                                m.outfit === 'sailor' &&
                                m.type === 'image'
                            )?.src || character.thumbnail
                          "
                          alt="Preview"
                          class="main-media"
                        />
                      </div>

                      <!~~ 前景层 - 过渡中的媒体（仅用于demo按钮切换） ~~>
                      <div
                        v-if="charIndex === currentCharacterIndex"
                        class="media-layer foreground-layer"
                        :class="{
                          dissolving: isTransitioning && isNewMediaReady,
                          'pre-dissolve': isTransitioning && !isNewMediaReady
                        }"
                        :style="{ visibility: transitionMedia ? 'visible' : 'hidden' }"
                      >
                        <video
                          v-if="transitionMedia?.type === 'video'"
                          :src="transitionMedia.src"
                          class="main-media"
                          autoplay
                          loop
                          muted
                          playsinline
                        />
                        <img
                          v-else-if="transitionMedia"
                          :src="transitionMedia.src"
                          alt="Preview"
                          class="main-media"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!~~ Character Selection Thumbnails ~~>
              <div class="character-thumbnails">
                <div
                  v-for="(character, index) in characters"
                  :key="index"
                  class="thumbnail-item"
                  :class="{ active: currentCharacterIndex === index }"
                  @click="selectCharacter(index)"
                >
                  <img :src="character.thumbnail" :alt="`Character ${index + 1}`" />
                </div>
              </div>
            </div>

            <!~~ Right Login Section ~~>
            <div class="login-section">
              <div class="login-header">
                <h1 v-text-stroke class="login-title">
                  Next Generationed Ai<br /><span class="nsfw-gradient">Nsfw</span> Game Is Here !
                </h1>
              </div>

              <!~~ Demo Buttons ~~>
              <div class="demo-buttons">
                <button
                  class="demo-button"
                  :class="{ disabled: isTransitioning }"
                  :disabled="isTransitioning"
                  @click="handleDemoClick('remove')"
                >
                  <div class="demo-icon">
                    <template v-if="isClothesOff">
                      <TShirtIcon></TShirtIcon>
                    </template>
                    <template v-else>
                      <ClothesIcon></ClothesIcon>
                    </template>
                  </div>
                  <span>{{ removeButtonText }}</span>
                </button>

                <button
                  class="demo-button"
                  :class="{ disabled: isTransitioning }"
                  :disabled="isTransitioning"
                  @click="handleDemoClick('beach')"
                >
                  <div class="demo-icon">
                    <template v-if="currentScene === 'living-room'">
                      <BeachIcon></BeachIcon>
                    </template>
                    <template v-else>
                      <BedIcon></BedIcon>
                    </template>
                  </div>
                  <span>{{ beachButtonText }}</span>
                </button>

                <button
                  class="demo-button"
                  :class="{ disabled: isTransitioning }"
                  :disabled="isTransitioning"
                  @click="handleDemoClick('shake')"
                >
                  <div class="demo-icon">
                    <!~~ Shake icon ~~>
                    <BreastsIcon></BreastsIcon>
                  </div>
                  <span>Click to shake her breasts</span>
                </button>
              </div>

              <!~~ Divider ~~>
              <div class="signup-divider">
                <div class="divider-line"></div>
                <span class="divider-text">Sign Up</span>
                <div class="divider-line"></div>
              </div>

              <!~~ Login Form ~~>
              <div class="login-form">
                <button class="login-button" @click="handleLogin" :disabled="loading">
                  <span v-if="!loading">Log in</span>
                  <a-spin v-else />
                </button>

                <button class="signup-later-button" @click="handleSignupLater">
                  sign up later
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Transition>-->

    <LoginFormModal
      :visible="showLoginForm"
      :title="props.title"
      @update:visible="handleLoginFormVisibleChange"
      @login="handleFormLogin"
    />
  </Teleport>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, watch } from 'vue'
import LoginFormModal from './LoginFormModal.vue'
import ResourceLoader from '@/shared/components/ResourceLoader.vue'
import {
  preloadWithMinimumTime,
  type PreloadProgress,
  type Character
} from '@/shared/utils/resourcePreloader'
import ClothesIcon from '@/assets/icon/clothes.svg'
import BeachIcon from '@/assets/icon/beach.svg'
import BreastsIcon from '@/assets/icon/breasts.svg'
import TShirtIcon from '@/assets/icon/tshirt.svg'
import BedIcon from '@/assets/icon/bed.svg'
import { reportEvent } from '@/utils'
import { ReportEvent } from '@/interface'
import { useUserStore } from '@/store/user'

const props = defineProps<{
  visible: boolean
  title?: string
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'login'): void
}>()

const userStore = useUserStore()

const logoUrl = computed(
  () => import.meta.env.VITE_LOGO_URL || 'https://cdn.magiclight.ai/assets/playshot/logo-v2.png'
)

// Character data with media resources
const characters = ref<Character[]>([
  {
    id: 1,
    thumbnail: 'https://static.playshot.ai/static/images/login-modal/2-suit-beach.png',
    name: 'Character 2',
    mediaResources: [
      // Beach场景 - 水手服
      {
        type: 'image',
        src: 'https://static.playshot.ai/static/images/login-modal/2-suit-beach.png',
        scene: 'beach',
        outfit: 'sailor'
      },
      {
        type: 'video',
        src: 'https://static.playshot.ai/static/images/login-modal/2-suit-beach.mp4',
        scene: 'beach',
        outfit: 'sailor'
      },
      // Beach场景 - 比基尼
      {
        type: 'image',
        src: 'https://static.playshot.ai/static/images/login-modal/2-bikini-beach.png',
        scene: 'beach',
        outfit: 'bikini'
      },
      {
        type: 'video',
        src: 'https://static.playshot.ai/static/images/login-modal/2-bikini-beach.mp4',
        scene: 'beach',
        outfit: 'bikini'
      },
      // 客厅场景 - 水手服
      {
        type: 'image',
        src: 'https://static.playshot.ai/static/images/login-modal/2-suit-parlor.png',
        scene: 'living-room',
        outfit: 'sailor'
      },
      {
        type: 'video',
        src: 'https://static.playshot.ai/static/images/login-modal/2-suit-parlor.mp4',
        scene: 'living-room',
        outfit: 'sailor'
      },
      // 客厅场景 - 比基尼
      {
        type: 'image',
        src: 'https://static.playshot.ai/static/images/login-modal/2-bikini-parlor.png',
        scene: 'living-room',
        outfit: 'bikini'
      },
      {
        type: 'video',
        src: 'https://static.playshot.ai/static/images/login-modal/2-bikini-parlor.mp4',
        scene: 'living-room',
        outfit: 'bikini'
      }
    ]
  },
  {
    id: 2,
    thumbnail: 'https://static.playshot.ai/static/images/login-modal/1-sailorsuit-beach.png',
    name: 'Character 1',
    mediaResources: [
      // Beach场景 - 水手服
      {
        type: 'image' as const,
        src: 'https://static.playshot.ai/static/images/login-modal/1-sailorsuit-beach.png',
        scene: 'beach',
        outfit: 'sailor'
      },
      {
        type: 'video' as const,
        src: 'https://static.playshot.ai/static/images/login-modal/1-sailorsuit-beach.mp4',
        scene: 'beach',
        outfit: 'sailor'
      },
      // Beach场景 - 比基尼
      {
        type: 'image' as const,
        src: 'https://static.playshot.ai/static/images/login-modal/1-bikini-beach.png',
        scene: 'beach',
        outfit: 'bikini'
      },
      {
        type: 'video' as const,
        src: 'https://static.playshot.ai/static/images/login-modal/1-bikini-beach.mp4',
        scene: 'beach',
        outfit: 'bikini'
      },
      // 客厅场景 - 水手服
      {
        type: 'image' as const,
        src: 'https://static.playshot.ai/static/images/login-modal/1-suit-parlor.png',
        scene: 'living-room',
        outfit: 'sailor'
      },
      {
        type: 'video' as const,
        src: 'https://static.playshot.ai/static/images/login-modal/1-suit-parlor.mp4',
        scene: 'living-room',
        outfit: 'sailor'
      },
      // 客厅场景 - 比基尼
      {
        type: 'image' as const,
        src: 'https://static.playshot.ai/static/images/login-modal/1-bikini-parlor.png',
        scene: 'living-room',
        outfit: 'bikini'
      },
      {
        type: 'video' as const,
        src: 'https://static.playshot.ai/static/images/login-modal/1-bikini-parlor.mp4',
        scene: 'living-room',
        outfit: 'bikini'
      }
    ]
  },

  {
    id: 3,
    thumbnail: 'https://static.playshot.ai/static/images/login-modal/3-suit-beach.png',
    name: 'Character 3',
    mediaResources: [
      // Beach场景 - 水手服
      {
        type: 'image',
        src: 'https://static.playshot.ai/static/images/login-modal/3-suit-beach.png',
        scene: 'beach',
        outfit: 'sailor'
      },
      {
        type: 'video',
        src: 'https://static.playshot.ai/static/images/login-modal/3-suit-beach.mp4',
        scene: 'beach',
        outfit: 'sailor'
      },
      // Beach场景 - 比基尼
      {
        type: 'image',
        src: 'https://static.playshot.ai/static/images/login-modal/3-bikini-beach.png',
        scene: 'beach',
        outfit: 'bikini'
      },
      {
        type: 'video',
        src: 'https://static.playshot.ai/static/images/login-modal/3-bikini-beach.mp4',
        scene: 'beach',
        outfit: 'bikini'
      },
      // 客厅场景 - 水手服
      {
        type: 'image',
        src: 'https://static.playshot.ai/static/images/login-modal/3-suit-parlor.png',
        scene: 'living-room',
        outfit: 'sailor'
      },
      {
        type: 'video',
        src: 'https://static.playshot.ai/static/images/login-modal/3-suit-parlor.mp4',
        scene: 'living-room',
        outfit: 'sailor'
      },
      // 客厅场景 - 比基尼
      {
        type: 'image',
        src: 'https://static.playshot.ai/static/images/login-modal/3-bikini-parlor.png',
        scene: 'living-room',
        outfit: 'bikini'
      },
      {
        type: 'video',
        src: 'https://static.playshot.ai/static/images/login-modal/3-bikini-parlor.mp4',
        scene: 'living-room',
        outfit: 'bikini'
      }
    ]
  }
])

// Current state
const currentCharacterIndex = ref(0)
const currentScene = ref<'living-room' | 'beach'>('living-room') // 场景状态
const currentOutfit = ref<'sailor' | 'bikini'>('sailor') // 服装状态
const currentMediaType = ref<'image' | 'video'>('image') // 媒体类型
const isClothesOff = ref(false) // 脱衣服状态
const activeAction = ref<string>('')
const showLoginForm = ref(false)

// 过渡状态管理
const isTransitioning = ref(false)
const transitionMedia = ref<any>(null)
const isNewMediaReady = ref(false)

// 资源预加载状态
const showLoader = ref(false)
const loadingProgress = ref<PreloadProgress>({
  loaded: 0,
  total: 0,
  percentage: 0,
  currentResource: ''
})

// Computed properties
const currentMedia = computed(() => {
  const character = characters.value[currentCharacterIndex.value]
  if (!character) return null

  const outfit = currentOutfit.value
  const scene = currentScene.value
  const type = currentMediaType.value

  return (
    character.mediaResources.find(
      (media) => media.scene === scene && media.outfit === outfit && media.type === type
    ) || character.mediaResources[0]
  )
})

// 动态按钮文案
const removeButtonText = computed(() => {
  return isClothesOff.value ? 'Click to put on her clothes' : 'Click to take off her clothes'
})

const beachButtonText = computed(() => {
  return currentScene.value === 'living-room'
    ? 'Click to take her to the beach'
    : 'Click to take her to living room'
})

const loading = ref(false)

// 资源预加载函数
const startResourcePreload = async () => {
  try {
    showLoader.value = true

    const shouldShowLoader = await preloadWithMinimumTime(
      characters.value,
      (progress) => {
        loadingProgress.value = progress
      },
      800 // 最小显示时间800ms
    )

    // 如果加载时间过短，不显示加载器
    if (!shouldShowLoader) {
      showLoader.value = false
    } else {
      // 延迟隐藏加载器，确保用户看到100%
      setTimeout(() => {
        showLoader.value = false
      }, 300)
    }
  } catch (error) {
    console.error('资源预加载失败:', error)
    showLoader.value = false
  }
}

// 监听模态框显示状态，开始预加载
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      // 上报弹窗显示事件
      reportEvent(ReportEvent.LoginModalView, {
        userId: userStore.userInfo?.uuid
      })

      // if (!showLoader.value && loadingProgress.value.total === 0) {
      //   // 只在第一次显示时预加载
      //   startResourcePreload()
      // }
      handleLogin()
    } else {
      // 当外层弹窗关闭时，确保内层登录表单也关闭
      showLoginForm.value = false
    }
  },
  { immediate: true }
)

// 溶解过渡函数
const triggerDissolveTransition = (oldMedia: any) => {
  if (!oldMedia || isTransitioning.value) return

  // 设置过渡媒体为旧媒体（即将被溶解的）
  transitionMedia.value = oldMedia

  // 开始过渡
  isTransitioning.value = true
  isNewMediaReady.value = false

  // 延迟一帧确保DOM更新
  nextTick(() => {
    // 预加载新媒体以减少闪烁
    const newMedia = currentMedia.value
    if (newMedia?.type === 'image') {
      const img = new Image()
      img.onload = () => {
        console.log('新图片预加载完成')
        // 图片加载完成后延迟确保渲染完成
        setTimeout(() => {
          isNewMediaReady.value = true
        }, 100)
      }
      img.onerror = () => {
        console.error('图片预加载失败')
        isNewMediaReady.value = true // 即使失败也继续过渡
      }
      img.src = newMedia.src
    } else if (newMedia?.type === 'video') {
      // 视频类型，等待视频元素加载事件
      // 如果300ms内没有收到加载事件，强制开始过渡
      const fallbackTimer = setTimeout(() => {
        console.log('视频加载超时，强制开始过渡')
        isNewMediaReady.value = true
      }, 300)

      // 清除定时器的函数
      const clearFallback = () => {
        clearTimeout(fallbackTimer)
      }

      // 监听一次性事件
      const checkVideoReady = () => {
        clearFallback()
        setTimeout(() => {
          isNewMediaReady.value = true
        }, 50)
      }

      // 存储清理函数，以便在组件卸载时清理
      window.addEventListener('video-ready', checkVideoReady, { once: true })
    }

    // 2秒后强制结束过渡（防止卡住）
    setTimeout(() => {
      isTransitioning.value = false
      transitionMedia.value = null
      // 不重置 isNewMediaReady，保持背景层显示
    }, 2000)
  })
}

// 媒体加载处理
const handleMediaLoaded = () => {
  console.log('媒体加载完成')
  if (isTransitioning.value && !isNewMediaReady.value) {
    // 只有在过渡期间且新媒体还未准备好时才设置为准备好
    setTimeout(() => {
      isNewMediaReady.value = true
    }, 50)
  } else if (!isTransitioning.value) {
    // 非过渡期间，直接设置为准备好
    isNewMediaReady.value = true
  }
}

const handleMediaError = (error: Event) => {
  console.error('媒体加载失败:', error)
  if (isTransitioning.value) {
    // 即使加载失败，也要继续过渡避免卡住
    isNewMediaReady.value = true
  }
}

// Event handlers
const handleClose = () => {
  // 上报弹窗关闭事件
  reportEvent(ReportEvent.LoginModalClose, {
    userId: userStore.userInfo?.uuid
  })
  emit('update:visible', false)
}

const selectCharacter = (index: number) => {
  if (index === currentCharacterIndex.value) return

  // 上报角色选择事件
  reportEvent(ReportEvent.LoginModalCharacterSwitch, {
    userId: userStore.userInfo?.uuid,
    fromCharacter: currentCharacterIndex.value,
    toCharacter: index,
    direction: 'select'
  })

  // 角色切换使用滑动效果，不使用溶解效果
  // 切换到指定角色并重置状态
  currentCharacterIndex.value = index
  currentScene.value = 'living-room'
  currentOutfit.value = 'sailor'
  currentMediaType.value = 'image'
  isClothesOff.value = false
  activeAction.value = ''
  // 重置过渡状态
  isTransitioning.value = false
  transitionMedia.value = null
  isNewMediaReady.value = true
}

const handleDemoClick = (action: string) => {
  // 防止在过渡期间重复点击
  if (isTransitioning.value) {
    console.log('过渡中，忽略点击')
    return
  }

  // 上报Demo按钮点击事件
  reportEvent(ReportEvent.LoginModalButtonClick, {
    userId: userStore.userInfo?.uuid,
    action: action,
    currentCharacter: currentCharacterIndex.value,
    currentScene: currentScene.value,
    currentOutfit: currentOutfit.value,
    currentMediaType: currentMediaType.value
  })

  // 获取当前媒体作为过渡的起点
  const oldMedia = currentMedia.value

  // 预先计算新状态，检查是否真的需要切换
  let newScene = currentScene.value
  let newOutfit = currentOutfit.value
  let newMediaType = currentMediaType.value
  let newIsClothesOff = isClothesOff.value

  switch (action) {
    case 'remove':
      // 切换服装状态
      if (currentOutfit.value === 'sailor') {
        newOutfit = 'bikini'
        newIsClothesOff = true
      } else {
        newOutfit = 'sailor'
        newIsClothesOff = false
      }
      newMediaType = 'image' // 重置为图片
      break

    case 'beach':
      // 切换场景，保持当前服装状态
      newScene = currentScene.value === 'living-room' ? 'beach' : 'living-room'
      newMediaType = 'image' // 重置为图片
      break

    case 'shake':
      // 切换到视频
      newMediaType = 'video'
      break
  }

  // 计算新媒体
  const character = characters.value[currentCharacterIndex.value]
  const newMedia = character?.mediaResources.find(
    (media) => media.scene === newScene && media.outfit === newOutfit && media.type === newMediaType
  )

  // 如果新媒体和当前媒体相同，则不需要过渡
  if (oldMedia && newMedia && oldMedia.src === newMedia.src) {
    console.log('媒体相同，无需过渡')
    return
  }

  // 先触发溶解过渡效果（在状态改变前）
  if (oldMedia) {
    triggerDissolveTransition(oldMedia)
  }

  // 然后应用新状态（这会更新背景层的媒体）
  currentScene.value = newScene
  currentOutfit.value = newOutfit
  currentMediaType.value = newMediaType
  isClothesOff.value = newIsClothesOff

  console.log(
    `Demo clicked: ${action}, character: ${currentCharacterIndex.value}, scene: ${currentScene.value}, outfit: ${currentOutfit.value}, type: ${currentMediaType.value}`
  )
}

const handleLogin = () => {
  // 上报登录按钮点击事件
  reportEvent(ReportEvent.LoginModalLoginClick, {
    userId: userStore.userInfo?.uuid
  })
  // 显示原来的登录表单
  showLoginForm.value = true
}

const handleSignupLater = () => {
  // Handle "sign up later" - could close modal or navigate to guest mode
  console.log('Sign up later clicked')
  emit('update:visible', false)
}

const handleLoginFormVisibleChange = (visible: boolean) => {
  showLoginForm.value = visible
  // 当登录表单关闭时，也关闭外层模态框
  if (!visible) {
    emit('update:visible', false)
  }
}

const handleFormLogin = () => {
  // 当登录表单成功登录后，关闭所有模态框
  showLoginForm.value = false
  emit('login')
  emit('update:visible', false)
}
</script>

<style lang="less" scoped>
@import '@/assets/style/theme.less';

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.login-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.login-modal {
  width: 800px; /* 进一步增加宽度确保所有元素显示 */
  height: 650px; /* 进一步增加高度确保图片完整显示 */
  background: var(--bg-secondary);
  border-radius: 20px;
  position: relative;
  padding: 0;
  animation: zoomIn 0.3s ease;
  box-shadow: 0 10px 30px var(--shadow-color);
  overflow: hidden;
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.close-button {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--text-secondary);
  transition: color 0.2s ease;
  z-index: 10;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  backdrop-filter: blur(8px);

  &:hover {
    color: var(--text-primary);
    background: rgba(255, 255, 255, 0.2);
  }

  svg {
    width: 20px;
    height: 20px;
  }
}

.modal-logo {
  position: absolute;
  top: 12px;
  left: 12px;
  z-index: 10;

  img {
    height: 23px;
  }
}

.modal-content {
  display: flex;
  width: 100%;
  height: 650px; /* 匹配模态框高度 */
  border-radius: 20px;
  overflow: hidden;
}

.preview-section {
  flex: 1;
  background: #f5f5f5;
  position: relative;
  display: flex;
  flex-direction: column;
  height: 650px;
}

.main-image-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.character-slider {
  display: flex;
  width: 300%; /* 3个角色 */
  height: 100%;
  transition: transform 0.5s ease-in-out;
}

.character-slide {
  width: 33.333%; /* 每个角色占1/3宽度 */
  height: 100%;
  flex-shrink: 0;
}

.media-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.media-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.background-layer {
  z-index: 1;
  opacity: 1; // 默认显示
  transition: opacity 0.3s ease-in-out;
}

.foreground-layer {
  z-index: 2;
  opacity: 1;
  transition: opacity 1.2s ease-in-out;

  &.pre-dissolve {
    opacity: 1; // 保持完全不透明，等待新媒体准备好
  }

  &.dissolving {
    opacity: 0; // 开始溶解
  }
}

.main-media {
  width: 100%;
  height: 100%;
  object-fit: cover;
  background: #f5f5f5;
  display: block;

  // 确保媒体元素在加载时不会闪烁
  &[src=''] {
    opacity: 0;
  }

  // 图片加载完成前的占位
  img& {
    background-color: #f5f5f5;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Cg fill='%23ddd'%3E%3Cpath d='M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z'/%3E%3C/g%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center;
    background-size: 40px 40px;
  }

  // 视频加载时的处理
  video& {
    background-color: #f5f5f5;
  }
}

.image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 20px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.action-button {
  background: #ca93f2;
  border: 1px solid #1f0038;
  border-radius: 20px;
  padding: 12px 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 80px;

  &:hover {
    background: linear-gradient(180deg, #b857ff 0%, #cc95f5 100%);
    transform: translateY(-2px);
  }

  &.active {
    background: #b857ff;
    color: white;
  }

  .action-icon {
    width: 24px;
    height: 24px;
    background: white;
    border: 1px solid #1f0038;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;

    svg {
      width: 16px;
      height: 16px;
    }
  }

  span {
    font-family: 'Rammetto One', sans-serif;
    font-size: 10px;
    font-weight: 400;
    line-height: 1.69;
    text-transform: uppercase;
    text-align: center;
    color: white;
    -webkit-text-stroke: 1px #1f0038;
    text-shadow: 0px 1px 0px rgba(31, 0, 56, 1);
  }
}

.character-thumbnails {
  display: flex;
  gap: 16px; /* 增加间距 */
  padding: 18px; /* 增加内边距 */
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  position: absolute;
  z-index: 10;
  bottom: 0;
  left: 0;
  right: 0;
}

.thumbnail-item {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  border: 3px solid transparent;
  transition: all 0.3s ease;

  &:hover {
    border-color: rgba(255, 255, 255, 0.5);
  }

  &.active {
    border-color: #ca93f2;
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: top;
  }
}

.login-section {
  width: 320px;
  background: var(--bg-secondary);
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  height: 650px;
  box-sizing: border-box;
  justify-content: center;
  position: relative;
  left: -1px;
}

.login-header {
  text-align: center;
  font-family: 'Rammetto One', sans-serif;
}

.login-title {
  font-size: 16px;
  font-weight: 400;
  line-height: 1.69;
  // text-transform: uppercase;
  color: #fff;
  margin: 0;
  margin-bottom: 4px;

  .nsfw-gradient {
    -webkit-text-fill-color: #b48ced;
  }
}

.demo-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
  font-family: 'Rammetto One', sans-serif;
}

.demo-button {
  background: #ca93f2;
  border-radius: 20px;
  padding: 12px 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  height: 68px;
  box-sizing: border-box;
  border: 1px solid #1f0038;
  // &:hover:not(:disabled) {
  //   background: linear-gradient(180deg, #b857ff 0%, #cc95f5 100%);
  //   transform: translateY(-1px);
  // }

  &:active:not(:disabled) {
    transform: translateY(1px);
    box-shadow: none;
  }

  &:disabled,
  &.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    background: #ca93f2 !important;
  }

  .demo-icon {
    width: 36px;
    height: 36px;
    background: white;
    border: 1px solid #1f0038;
    border-radius: 9px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }

  span {
    font-family: 'Rammetto One', sans-serif;
    font-size: 12px;
    font-weight: 400;
    line-height: 1.69;
    text-transform: uppercase;
    color: white;
    -webkit-text-stroke: 1px #1f0038;
    text-shadow: 0px 1px 0px rgba(31, 0, 56, 1);
    flex: 1;
    text-align: center;
  }
}

.signup-divider {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  margin-top: 120px;
}

.divider-line {
  flex: 1;
  height: 0.5px;
  background: var(--divider-color);
}

.divider-text {
  font-family: 'Work Sans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  text-transform: uppercase;
  color: var(--text-tertiary);
  white-space: nowrap; /* 防止文字换行 */
}

.login-form {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  width: 100%;
  flex-shrink: 0; /* 防止压缩 */
}

.login-button {
  width: 100%; /* 使用全宽确保按钮完整显示 */
  height: 42px;
  border: none;
  border-radius: 54px;
  background: #ca93f2;
  color: rgba(0, 0, 0, 0.85);
  font-family: 'Work Sans', sans-serif;
  font-size: 16px;
  font-weight: 600;
  text-transform: uppercase;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 11px 24px;
  box-sizing: border-box;

  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }

  &:not(:disabled):hover {
    opacity: 0.9;
  }

  &:not(:disabled):active {
    transform: scale(0.98);
  }
}

.signup-later-button {
  background: transparent;
  border: none;
  font-family: 'Work Sans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: var(--text-secondary);
  cursor: pointer;
  transition: color 0.3s ease;

  &:hover {
    color: var(--text-primary);
  }
}
</style>
