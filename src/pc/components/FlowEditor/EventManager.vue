<template>
  <div class="event-manager">
    <div class="manager-header">
      <h3>{{ node ? node.name : '' }} 事件管理</h3>
      <button class="add-button" @click="showAddEventPanel" v-if="!showEventConfig">
        <svg viewBox="0 0 24 24" width="16" height="16">
          <path fill="currentColor" d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z" />
        </svg>
        添加事件
      </button>
    </div>

    <div class="event-list" v-if="events.length > 0 && !showEventConfig">
      <div
        v-for="(event, index) in events"
        :key="event.id"
        class="event-item"
        :class="{ selected: event.id === selectedEventId }"
      >
        <div class="event-info" @click="selectEvent(event.id)">
          <div class="event-icon" :style="{ backgroundColor: getEventColor(event.type) }">
            <svg viewBox="0 0 24 24" width="16" height="16">
              <path fill="currentColor" :d="getEventIcon(event.type)" />
            </svg>
          </div>
          <div class="event-details">
            <div class="event-title">{{ getEventLabel(event.type) }}</div>
            <div class="event-description">{{ getEventDescription(event) }}</div>
          </div>
        </div>
        <div class="event-actions">
          <button class="action-button edit" @click="editEvent(event)">
            <svg viewBox="0 0 24 24" width="14" height="14">
              <path
                fill="currentColor"
                d="M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z"
              />
            </svg>
          </button>
          <button
            class="action-button move-up"
            @click="moveEvent(index, -1)"
            :disabled="index === 0"
          >
            <svg viewBox="0 0 24 24" width="14" height="14">
              <path
                fill="currentColor"
                d="M7.41,15.41L12,10.83L16.59,15.41L18,14L12,8L6,14L7.41,15.41Z"
              />
            </svg>
          </button>
          <button
            class="action-button move-down"
            @click="moveEvent(index, 1)"
            :disabled="index === events.length - 1"
          >
            <svg viewBox="0 0 24 24" width="14" height="14">
              <path
                fill="currentColor"
                d="M7.41,8.58L12,13.17L16.59,8.58L18,10L12,16L6,10L7.41,8.58Z"
              />
            </svg>
          </button>
          <button class="action-button delete" @click="deleteEvent(event.id)">
            <svg viewBox="0 0 24 24" width="14" height="14">
              <path
                fill="currentColor"
                d="M19,4H15.5L14.5,3H9.5L8.5,4H5V6H19M6,19A2,2 0 0,0 8,21H16A2,2 0 0,0 18,19V7H6V19Z"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>

    <div class="empty-state" v-if="events.length === 0 && !showEventConfig">
      <svg viewBox="0 0 24 24" width="48" height="48" class="empty-icon">
        <path
          fill="currentColor"
          d="M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,5V19H5V5H19Z"
        />
      </svg>
      <p>该节点暂无事件</p>
      <button class="add-button" @click="showAddEventPanel">
        <svg viewBox="0 0 24 24" width="16" height="16">
          <path fill="currentColor" d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z" />
        </svg>
        添加第一个事件
      </button>
    </div>

    <EventConfigPanel
      v-if="showEventConfig"
      :event="currentEvent"
      :node="node"
      :is-new="isNewEvent"
      :available-nodes="availableNodes"
      @save="saveEvent"
      @close="cancelEdit"
      @delete="deleteEvent"
    />

    <transition name="fade">
      <div class="confirm-dialog" v-if="showDeleteConfirm">
        <div class="dialog-content">
          <h4>删除事件</h4>
          <p>确定要删除这个事件吗？此操作无法撤销。</p>
          <div class="dialog-actions">
            <button class="cancel-btn" @click="cancelDelete">取消</button>
            <button class="confirm-btn" @click="confirmDelete">删除</button>
          </div>
        </div>
      </div>
    </transition>

    <div class="overlay" v-if="showDeleteConfirm" @click="cancelDelete"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { nanoid } from 'nanoid'
import EventConfigPanel from './EventConfigPanel.vue'
import { FlowEvent, FlowEventType, FlowNode, TreeNode } from '@/pc/components/FlowEditor/types'
import { getEventTypeName, getEventTypeColor, getEventTypeIcon } from './utils/eventUtils'

// 属性定义
const props = defineProps<{
  node?: FlowNode
  availableNodes?: FlowNode[]
}>()

// 事件定义
const emit = defineEmits<{
  update: [nodeId: string, events: FlowEvent[]]
}>()

// 当前事件列表
const events = ref<FlowEvent[]>([])

// 事件配置相关
const showEventConfig = ref(false)
const isNewEvent = ref(false)
const currentEvent = ref<FlowEvent | null>(null)
const selectedEventId = ref<string>('')

// 删除确认对话框
const showDeleteConfirm = ref(false)
const eventToDelete = ref<string>('')

// 图标和颜色映射
const eventIcons: Record<FlowEventType, string> = {
  message:
    'M20,2H4A2,2 0 0,0 2,4V22L6,18H20A2,2 0 0,0 22,16V4A2,2 0 0,0 20,2M6,9H18V11H6M14,14H6V12H14M18,8H6V6H18',
  wait: 'M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z',
  show_tips:
    'M11,9H13V7H11M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M11,13H13V17H11V13Z',
  play_audio:
    'M14,3.23V5.29C16.89,6.15 19,8.83 19,12C19,15.17 16.89,17.84 14,18.7V20.77C18,19.86 21,16.28 21,12C21,7.72 18,4.14 14,3.23M16.5,12C16.5,10.23 15.5,8.71 14,7.97V16C15.5,15.29 16.5,13.76 16.5,12M3,9V15H7L12,20V4L7,9H3Z',
  show_image:
    'M20,5A2,2 0 0,1 22,7V17A2,2 0 0,1 20,19H4C2.89,19 2,18.1 2,17V7C2,5.89 2.89,5 4,5H20M5,16H19L14.5,10L11,14.5L8.5,11.5L5,16Z',
  play_video:
    'M4,4H7L9,2H15L17,4H20A2,2 0 0,1 22,6V18A2,2 0 0,1 20,20H4A2,2 0 0,1 2,18V6A2,2 0 0,1 4,4M12,7A5,5 0 0,0 7,12A5,5 0 0,0 12,17A5,5 0 0,0 17,12A5,5 0 0,0 12,7M12,9A3,3 0 0,1 15,12A3,3 0 0,1 12,15A3,3 0 0,1 9,12A3,3 0 0,1 12,9Z',
  show_chat_options:
    'M3,9H17V7H3V9M3,13H17V11H3V13M3,17H17V15H3V17M19,17H21V15H19V17M19,7V9H21V7H19M19,13H21V11H19V13Z',
  scene_transition: 'M8,5.14V19.14L19,12.14L8,5.14Z',
  heart_value:
    'M12,21.35L10.55,20.03C5.4,15.36 2,12.28 2,8.5C2,5.42 4.42,3 7.5,3C9.24,3 10.91,3.81 12,5.09C13.09,3.81 14.76,3 16.5,3C19.58,3 22,5.42 22,8.5C22,12.28 18.6,15.36 13.45,20.03L12,21.35Z',
  show_ending:
    'M5,3H19A2,2 0 0,1 21,5V19A2,2 0 0,1 19,21H5A2,2 0 0,1 3,19V5A2,2 0 0,1 5,3M12,10A2,2 0 0,0 10,12A2,2 0 0,0 12,14A2,2 0 0,0 14,12A2,2 0 0,0 12,10Z',
  show_overlay: 'M3,3H21V5H3V3M3,7H21V9H3V7M3,11H21V13H3V11M3,15H21V17H3V15M3,19H21V21H3V19Z',
  animated_images:
    'M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z',
  update_task_progress:
    'M19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3M19,19H5V5H19V19M10,17L6,13L7.41,11.59L10,14.17L16.59,7.58L18,9',
  interactive:
    'M7.5,5.6L5,7L6.4,4.5L5,2L7.5,3.4L10,2L8.6,4.5L10,7L7.5,5.6M19.5,15.4L22,14L20.6,16.5L22,19L19.5,17.6L17,19L18.4,16.5L17,14L19.5,15.4M22,2L20.6,4.5L22,7L19.5,5.6L17,7L18.4,4.5L17,2L19.5,3.4L22,2M13.34,12.78L15.78,10.34L13.66,8.22L11.22,10.66L13.34,12.78M14.37,7.29L16.71,9.63C17.1,10 17.1,10.65 16.71,11.04L5.04,22.71C4.65,23.1 4,23.1 3.63,22.71L1.29,20.37C0.9,20 0.9,19.35 1.29,18.96L12.96,7.29C13.35,6.9 14,6.9 14.37,7.29Z',
  voice_config:  'M7.5,5.6L5,7L6.4,4.5L5,2L7.5,3.4L10,2L8.6,4.5L10,7L7.5,5.6M19.5,15.4L22,14L20.6,16.5L22,19L19.5,17.6L17,19L18.4,16.5L17,14L19.5,15.4M22,2L20.6,4.5L22,7L19.5,5.6L17,7L18.4,4.5L17,2L19.5,3.4L22,2M13.34,12.78L15.78,10.34L13.66,8.22L11.22,10.66L13.34,12.78M14.37,7.29L16.71,9.63C17.1,10 17.1,10.65 16.71,11.04L5.04,22.71C4.65,23.1 4,23.1 3.63,22.71L1.29,20.37C0.9,20 0.9,19.35 1.29,18.96L12.96,7.29C13.35,6.9 14,6.9 14.37,7.29Z',
}

const eventColors: Record<FlowEventType, string> = {
  message: '#3498db',
  wait: '#9b59b6',
  show_tips: '#f39c12',
  play_audio: '#27ae60',
  show_image: '#e74c3c',
  play_video: '#e74c3c',
  show_chat_options: '#2980b9',
  scene_transition: '#16a085',
  heart_value: '#e74c3c',
  show_ending: '#d35400',
  show_overlay: '#f1c40f',
  animated_images: '#9b59b6',
  update_task_progress: '#27ae60',
  interactive: '#3498db',
  voice_config: '#3498db'
}

const eventLabels: Record<FlowEventType, string> = {
  message: '对话',
  wait: '等待',
  show_tips: '提示',
  play_audio: '音频',
  show_image: '图片',
  play_video: '视频',
  show_chat_options: '选项',
  scene_transition: '跳转',
  heart_value: '好感度',
  show_ending: '结局',
  show_overlay: '覆盖层',
  animated_images: '动画图片',
  update_task_progress: '任务进度',
  interactive: '互动事件',
  voice_config: '语音配置'
}

// 初始化事件列表
const initEvents = () => {
  if (props.node && props.node.events) {
    events.value = [...props.node.events]
  } else {
    events.value = []
  }
}

// 获取事件图标
const getEventIcon = (type: FlowEventType) => {
  return (
    eventIcons[type] ||
    'M3,3H21V21H3V3M7,7V9H9V7H7M11,7V9H13V7H11M15,7V9H17V7H15M7,11V13H9V11H7M11,11V13H13V11H11M15,11V13H17V11H15M7,15V17H9V15H7M11,15V17H13V15H11M15,15V17H17V15H15Z'
  )
}

// 获取事件颜色
const getEventColor = (type: FlowEventType) => {
  return eventColors[type] || '#95a5a6'
}

// 获取事件标签
const getEventLabel = (type: FlowEventType) => {
  return eventLabels[type] || '未知事件'
}

// 获取事件描述
const getEventDescription = (event: FlowEvent) => {
  let targetNode: FlowNode | undefined
  let sign: string | undefined

  switch (event.type) {
    case 'message':
      return (
        event.params.content?.substring(0, 30) + (event.params.content?.length > 30 ? '...' : '')
      )
    case 'wait':
      return `等待 ${event.params.seconds || event.params.duration / 1000} 秒`
    case 'scene_transition':
      targetNode = props.availableNodes?.find((node) => node.id === event.params.targetNodeId)
      return `跳转到: ${targetNode?.name || event.params.targetNodeId || '未指定'}`
    case 'heart_value':
      sign = event.params.value >= 0 ? '+' : ''
      return `${sign}${event.params.value} 点好感度`
    case 'show_tips':
      return `提示: ${event.params.content?.substring(0, 20) || ''}${
        event.params.content?.length > 20 ? '...' : ''
      }`
    case 'play_audio':
      return `音频: ${event.params.url ? '已设置' : '未设置'}`
    case 'show_image':
      return `图片: ${event.params.url ? '已设置' : '未设置'}`
    case 'play_video':
      return `视频: ${event.params.url ? '已设置' : '未设置'}`
    case 'show_chat_options':
      return `选项数量: ${event.params.options?.length || 0}`
    case 'show_ending':
      return `${event.params.content?.html?.substring(0, 20) || ''}${
        event.params.content?.html?.length > 20 ? '...' : '未设置'
      }`
    case 'show_overlay':
      return `覆盖层: ${event.params.content?.substring(0, 20) || ''}${
        event.params.content?.length > 20 ? '...' : ''
      }`
    case 'animated_images':
      return `动画图片: ${event.params.images?.length || 0} 张`
    case 'update_task_progress':
      return `任务: ${event.params.task_id || '未指定'} - 进度 ${event.params.progress || 0}%`
    case 'interactive':
      return `互动类型: ${event.params.type || '未指定'}`
    default:
      return '点击编辑查看详情'
  }
}

// 显示添加事件面板
const showAddEventPanel = () => {
  isNewEvent.value = true
  currentEvent.value = {
    id: nanoid(),
    type: '' as FlowEventType,
    params: {}
  }
  showEventConfig.value = true
}

// 选择事件
const selectEvent = (eventId: string) => {
  selectedEventId.value = eventId === selectedEventId.value ? '' : eventId
}

// 编辑事件
const editEvent = (event: FlowEvent) => {
  isNewEvent.value = false
  currentEvent.value = { ...event }
  showEventConfig.value = true
}

// 保存事件
const saveEvent = (event: FlowEvent) => {
  if (isNewEvent.value) {
    // 添加新事件
    events.value.push(event)
  } else {
    // 更新现有事件
    const index = events.value.findIndex((e) => e.id === event.id)
    if (index !== -1) {
      events.value[index] = event
    }
  }

  // 通知父组件更新
  emit('update', props.node?.id || '', [...events.value])

  // 关闭配置面板
  showEventConfig.value = false
}

// 取消编辑
const cancelEdit = () => {
  showEventConfig.value = false
  currentEvent.value = null
}

// 移动事件
const moveEvent = (index: number, direction: number) => {
  const newIndex = index + direction

  // 确保新位置有效
  if (newIndex < 0 || newIndex >= events.value.length) return

  // 交换位置
  const temp = events.value[index]
  events.value[index] = events.value[newIndex]
  events.value[newIndex] = temp

  // 通知父组件更新
  emit('update', props.node?.id || '', [...events.value])
}

// 删除事件
const deleteEvent = (eventId: string) => {
  eventToDelete.value = eventId
  showDeleteConfirm.value = true
}

// 确认删除
const confirmDelete = () => {
  events.value = events.value.filter((event) => event.id !== eventToDelete.value)

  // 通知父组件更新
  emit('update', props.node?.id || '', [...events.value])

  // 关闭对话框
  showDeleteConfirm.value = false
  eventToDelete.value = ''

  // 如果正在编辑的事件被删除，关闭编辑面板
  if (currentEvent.value?.id === eventToDelete.value) {
    showEventConfig.value = false
    currentEvent.value = null
  }
}

// 取消删除
const cancelDelete = () => {
  showDeleteConfirm.value = false
  eventToDelete.value = ''
}

// 监听节点变化
watch(
  () => props.node,
  () => {
    initEvents()
  },
  { deep: true, immediate: true }
)
</script>

<style lang="less" scoped>
.event-manager {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #1a1a1c;
  position: relative;

  .manager-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background-color: rgba(0, 0, 0, 0.2);

    h3 {
      margin: 0;
      color: #fff;
      font-size: 16px;
      font-weight: 600;
    }

    .add-button {
      display: flex;
      align-items: center;
      gap: 4px;
      background: rgba(255, 255, 255, 0.08);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 4px;
      color: #fff;
      padding: 6px 12px;
      font-size: 13px;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        background: rgba(202, 147, 242, 0.2);
      }

      svg {
        color: #ca93f2;
      }
    }
  }

  .event-list {
    flex: 1;
    overflow-y: auto;
    padding: 12px;

    .event-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 12px;
      margin-bottom: 8px;
      background: rgba(255, 255, 255, 0.03);
      border: 1px solid rgba(255, 255, 255, 0.05);
      border-radius: 6px;
      transition: all 0.2s;

      &:hover {
        background: rgba(255, 255, 255, 0.05);
      }

      &.selected {
        background: rgba(202, 147, 242, 0.1);
        border-color: rgba(202, 147, 242, 0.3);
      }

      .event-info {
        display: flex;
        align-items: center;
        gap: 12px;
        cursor: pointer;
        flex: 1;

        .event-icon {
          width: 32px;
          height: 32px;
          border-radius: 6px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;
        }

        .event-details {
          .event-title {
            font-size: 14px;
            font-weight: 500;
            color: #fff;
            margin-bottom: 2px;
          }

          .event-description {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.5);
          }
        }
      }

      .event-actions {
        display: flex;
        gap: 4px;

        .action-button {
          width: 28px;
          height: 28px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 4px;
          color: rgba(255, 255, 255, 0.7);
          cursor: pointer;
          transition: all 0.2s;

          &:hover {
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
          }

          &:disabled {
            opacity: 0.3;
            cursor: not-allowed;

            &:hover {
              background: rgba(255, 255, 255, 0.05);
              color: rgba(255, 255, 255, 0.7);
            }
          }

          &.edit:hover {
            color: #ca93f2;
          }

          &.delete:hover {
            color: #e74c3c;
          }
        }
      }
    }
  }

  .empty-state {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 24px;
    color: rgba(255, 255, 255, 0.5);

    .empty-icon {
      margin-bottom: 16px;
      opacity: 0.4;
    }

    p {
      margin: 0 0 20px 0;
      font-size: 14px;
    }

    .add-button {
      display: flex;
      align-items: center;
      gap: 4px;
      background: rgba(255, 255, 255, 0.08);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 4px;
      color: #fff;
      padding: 8px 16px;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        background: rgba(202, 147, 242, 0.2);
      }

      svg {
        color: #ca93f2;
      }
    }
  }

  .confirm-dialog {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 300px;
    background: #222226;
    border-radius: 8px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
    z-index: 20;

    .dialog-content {
      padding: 16px;

      h4 {
        margin: 0 0 12px 0;
        color: #fff;
        font-size: 16px;
      }

      p {
        margin: 0 0 16px 0;
        color: rgba(255, 255, 255, 0.7);
        font-size: 14px;
      }

      .dialog-actions {
        display: flex;
        justify-content: flex-end;
        gap: 8px;

        button {
          padding: 8px 16px;
          border-radius: 4px;
          font-size: 13px;
          cursor: pointer;
          transition: all 0.2s;

          &.cancel-btn {
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: #fff;

            &:hover {
              background: rgba(255, 255, 255, 0.12);
            }
          }

          &.confirm-btn {
            background: #e74c3c;
            border: 1px solid #e74c3c;
            color: #fff;

            &:hover {
              background: #c0392b;
            }
          }
        }
      }
    }
  }

  .overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10;
  }
}

// 淡入淡出动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
