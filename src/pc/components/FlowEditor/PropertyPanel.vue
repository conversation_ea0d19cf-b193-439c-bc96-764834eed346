<template>
  <div class="property-panel-content">
    <!-- 如果是节点属性配置 -->
    <template v-if="scene && !event">
      <ChapterConfig
        :scene="scene"
        :availableNodes="availableNodes"
        :selected-event-id="selectedEventId"
        @update-scene="handleUpdateScene"
        @add-event="handleAddEvent"
        @select-event="handleSelectEvent"
        @delete-event="handleDeleteEvent"
      />
    </template>

    <template v-else>
      <div class="panel-section">
        <div class="empty-tip">请选择一个节点或事件进行配置</div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import type { Scene, GameEvent, GameEventType } from '@/types/editor'
import { shallowRef } from 'vue'
import ChapterConfig from '@/pc/components/FlowEditor/ChapterConfig.vue'

// 定义消息内容接口
interface MessageContent {
  text?: string
  [key: string]: any
}

// 接收的属性
const props = defineProps<{
  scene?: Scene | null
  event?: GameEvent | null
  availableNodes?: { id: string; name: string; type: string }[]
  selectedEventId?: string // 新增：选中的事件ID
}>()

// 需要发出的事件
const emit = defineEmits<{
  update: [eventData: Partial<GameEvent>]
  'update-scene': [sceneData: Partial<Scene>]
  'save-event': [eventData: Partial<GameEvent>]
  'add-event': [] // 新增：添加事件的事件
}>()

// 本地状态
const localScene = ref<Partial<Scene>>({})
const localEvent = ref<Partial<GameEvent>>({})
const jsonError = ref<string>('')

// 添加更新状态标记
const isUpdating = ref(false)

// 监听props变化，更新本地状态
watch(
  () => props.scene,
  (newScene) => {
    if (newScene) {
      // 比较新旧值，只在真正变化时更新
      const newValue = JSON.stringify(newScene)
      const currentValue = JSON.stringify(localScene.value)
      if (newValue !== currentValue && !isUpdating.value) {
        // 创建深拷贝断开引用关系
        localScene.value = JSON.parse(JSON.stringify(newScene))
      }
    } else {
      localScene.value = {}
    }
  },
  { immediate: true, deep: true }
)

watch(
  () => props.event,
  (newEvent) => {
    if (newEvent) {
      // 比较新旧值，只在真正变化时更新
      const newValue = JSON.stringify(newEvent)
      const currentValue = JSON.stringify(localEvent.value)
      if (newValue !== currentValue) {
        // 创建深拷贝断开引用关系
        localEvent.value = JSON.parse(JSON.stringify(newEvent))
      }
    } else {
      localEvent.value = {}
    }
  },
  { immediate: true }
)

// 本地编辑时，防抖处理更新
const debouncedUpdateScene = (() => {
  let timer: ReturnType<typeof setTimeout> | null = null
  let lastValue: string | null = null

  return (value: Partial<Scene>) => {
    if (timer) clearTimeout(timer)
    if (isUpdating.value) return

    // 比较新旧值，只在真正变化时触发更新
    const currentValue = JSON.stringify(value)
    if (currentValue === lastValue) {
      return
    }

    timer = setTimeout(() => {
      isUpdating.value = true
      lastValue = currentValue
      emit('update-scene', value)
      setTimeout(() => {
        isUpdating.value = false
      }, 100)
    }, 300)
  }
})()

// 监听本地场景变化，触发更新
watch(
  localScene,
  (value) => {
    if (!isUpdating.value) {
      debouncedUpdateScene(value)
    }
  },
  { deep: true }
)

// 用于存储JSON字符串，方便编辑
const eventConfigJson = computed({
  get: () => {
    try {
      return JSON.stringify(localEvent.value.plot || {}, null, 2)
    } catch (e) {
      return '{}'
    }
  },
  set: (value: string) => {
    try {
      const parsed = JSON.parse(value)
      localEvent.value.plot = parsed
      jsonError.value = ''

      // 使用防抖处理更新
      debouncedUpdateEvent(localEvent.value)
    } catch (e) {
      jsonError.value = '无效的JSON格式'
    }
  }
})

// 防抖处理事件更新
const debouncedUpdateEvent = (() => {
  let timer: ReturnType<typeof setTimeout> | null = null
  return (value: Partial<GameEvent>) => {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      emit('update', value)
    }, 300)
  }
})()

// 更新事件配置
const updateEventConfig = (updatedParams: any) => {
  console.log('更新事件配置:', JSON.stringify(updatedParams))
  if (localEvent.value) {
    localEvent.value.plot = updatedParams
    debouncedUpdateEvent(localEvent.value)
  }
}

// 处理章节更新
const handleUpdateScene = (updatedScene: Partial<Scene>) => {
  emit('update-scene', updatedScene)
}

// 处理添加事件
const handleAddEvent = () => {
  emit('add-event')
}

// 处理选择事件
const handleSelectEvent = (selectedEvent: GameEvent) => {
  localEvent.value = JSON.parse(JSON.stringify(selectedEvent))
  emit('update', selectedEvent)
}

// 处理删除事件
const handleDeleteEvent = (event: GameEvent, index: number) => {
  // 如果当前场景存在
  if (props.scene && props.scene.events) {
    // 创建场景的深拷贝
    const updatedScene = JSON.parse(JSON.stringify(props.scene))
    // 删除指定索引的事件
    updatedScene.events.splice(index, 1)
    // 更新场景
    emit('update-scene', updatedScene)
  }
}
</script>

<style lang="less" scoped>
.property-panel-content {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  padding: 0;
  color: rgba(255, 255, 255, 0.85);
  font-size: 14px;

  .panel-section {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 15px;
    font-weight: 500;
    margin: 0 0 12px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    color: #ca93f2;
  }

  .form-item {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .form-label {
    margin-bottom: 6px;
    font-size: 13px;
    color: rgba(255, 255, 255, 0.7);
  }

  .form-sublabel {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.5);
    margin-top: 2px;
  }

  .custom-input,
  .custom-textarea,
  .custom-select {
    width: 100%;
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    color: rgba(255, 255, 255, 0.9);
    padding: 8px 10px;
    font-size: 13px;
    transition: all 0.2s;

    &:focus {
      border-color: #ca93f2;
      outline: none;
      box-shadow: 0 0 0 2px rgba(202, 147, 242, 0.2);
    }

    &::placeholder {
      color: rgba(255, 255, 255, 0.3);
    }
  }

  .custom-textarea {
    resize: vertical;
    min-height: 80px;
  }

  .custom-checkbox {
    display: flex;
    align-items: center;
    cursor: pointer;
    user-select: none;

    input[type='checkbox'] {
      width: 16px;
      height: 16px;
      margin-right: 8px;
      accent-color: #ca93f2;
    }

    span {
      font-size: 13px;
    }
  }

  .save-button {
    display: block;
    width: 100%;
    padding: 10px;
    background: rgba(46, 204, 113, 0.2);
    border: 1px solid rgba(46, 204, 113, 0.4);
    border-radius: 4px;
    color: #2ecc71;
    font-size: 14px;
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
      background: rgba(46, 204, 113, 0.3);
      transform: translateY(-1px);
    }

    &:active {
      transform: translateY(0);
    }
  }

  .back-button {
    display: flex;
    align-items: center;
    gap: 4px;
    background: rgba(52, 152, 219, 0.15);
    border: 1px solid rgba(52, 152, 219, 0.3);
    color: #3498db;
    border-radius: 4px;
    padding: 5px 10px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
      background: rgba(52, 152, 219, 0.25);
    }

    .icon-back {
      width: 16px;
      height: 16px;
    }
  }

  .buttons-group {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }

  .add-button {
    display: flex;
    align-items: center;
    gap: 4px;
    background: rgba(46, 204, 113, 0.15);
    border: 1px solid rgba(46, 204, 113, 0.3);
    color: #2ecc71;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
      background: rgba(46, 204, 113, 0.25);
      transform: translateY(-1px);
    }

    .icon-add {
      width: 16px;
      height: 16px;
    }
  }

  .delete-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 4px;
    background: rgba(231, 76, 60, 0.15);
    border: 1px solid rgba(231, 76, 60, 0.3);
    color: #e74c3c;
    padding: 0;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
      background: rgba(231, 76, 60, 0.25);
    }

    .icon-delete {
      width: 16px;
      height: 16px;
    }
  }

  .url-item {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    margin-bottom: 12px;

    .form-label {
      margin-bottom: 0;
      margin-top: 5px;
      min-width: 100px;
    }
  }

  .error-message {
    color: #ff4d4f;
    font-size: 12px;
    margin-top: 4px;
  }

  .empty-tip {
    text-align: center;
    padding: 20px;
    color: rgba(255, 255, 255, 0.5);
  }

  :deep(.select-container) {
    width: 100%;
  }

  .form-actions {
    margin-top: 20px;
  }
}
</style>
