<template>
  <div
    class="node-container"
    :class="{
      'begin-node': node.type === 'begin',
      'outline-node': node.type === 'outline',
      'group-node': node.type === 'group',
      selected: isSelected
    }"
  >
    <div class="node-icon" :class="getNodeIconClass()">
      <svg v-if="node.type === 'begin'" viewBox="0 0 24 24" width="18" height="18">
        <path
          fill="currentColor"
          d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z"
        />
      </svg>
      <svg v-else-if="node.type === 'outline'" viewBox="0 0 24 24" width="18" height="18">
        <path fill="currentColor" d="M3,6H21V8H3V6M3,11H21V13H3V11M3,16H21V18H3V16Z" />
      </svg>
      <svg v-else-if="node.type === 'group'" viewBox="0 0 24 24" width="18" height="18">
        <path
          fill="currentColor"
          d="M12,5.5A3.5,3.5 0 0,1 15.5,9A3.5,3.5 0 0,1 12,12.5A3.5,3.5 0 0,1 8.5,9A3.5,3.5 0 0,1 12,5.5M5,8C5.56,8 6.08,8.15 6.53,8.42C6.38,9.85 6.8,11.27 7.66,12.38C7.16,13.34 6.16,14 5,14A3,3 0 0,1 2,11A3,3 0 0,1 5,8M19,8A3,3 0 0,1 22,11A3,3 0 0,1 19,14C17.84,14 16.84,13.34 16.34,12.38C17.2,11.27 17.62,9.85 17.47,8.42C17.92,8.15 18.44,8 19,8M5.5,18.25C5.5,16.18 8.41,14.5 12,14.5C15.59,14.5 18.5,16.18 18.5,18.25V20H5.5V18.25M0,20V18.5C0,17.11 1.89,15.94 4.45,15.6C3.86,16.28 3.5,17.22 3.5,18.25V20H0M24,20H20.5V18.25C20.5,17.22 20.14,16.28 19.55,15.6C22.11,15.94 24,17.11 24,18.5V20Z"
        />
      </svg>
      <svg v-else viewBox="0 0 24 24" width="18" height="18">
        <path
          fill="currentColor"
          d="M10 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2h-8L10 4z"
        />
      </svg>
    </div>
    <div class="node-content">
      <div class="node-title">{{ nodeData.name || '未命名章节' }}</div>
      <div class="node-id">
        {{
          node.type === 'outline'
            ? '故事结构总览'
            : node.type === 'group'
            ? '场景组'
            : `ID: ${nodeData.id}`
        }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, onMounted } from 'vue'
import { TreeNode } from './types'

const props = defineProps<{
  node: any // 使用 any 类型以允许访问 X6 节点的方法
}>()

// 节点数据状态
const nodeState = ref<any>({})

// 在组件挂载后设置监听器
onMounted(() => {
  try {
    // 获取初始数据
    const initialData = props.node.getData() || {}
    nodeState.value = { ...initialData }
    console.log('节点初始数据:', initialData)

    // 监听数据变化事件
    props.node.on('change:data', ({ current }: { current: any }) => {
      console.log('节点数据变化:', current)
      nodeState.value = { ...current }
    })
  } catch (error) {
    console.error('设置节点数据监听器时出错:', error)
  }
})

// 使用防抖处理节点数据变化
const updateKey = ref(Date.now())

// 作为后备的监听方案，如果直接监听不生效
watch(
  () => props.node,
  (newNode, oldNode) => {
    // 只在关键属性变化时更新
    if (
      !oldNode ||
      newNode.id !== oldNode.id ||
      JSON.stringify(newNode.getData()) !== JSON.stringify(oldNode.getData())
    ) {
      updateKey.value = Date.now()
      // 更新节点状态
      nodeState.value = { ...newNode.getData() }
    }
  },
  { deep: false }
)

// 优化计算属性，减少不必要的计算
const nodeData = computed(() => {
  // 优先使用 nodeState 中的数据
  const data = nodeState.value || {}

  // 如果 nodeState 中没有数据，则尝试从 props.node 中获取
  if (Object.keys(data).length === 0) {
    try {
      const nodeData = props.node.getData() || {}
      return {
        name: nodeData.name || props.node.name || '',
        id: nodeData.id || props.node.id || '',
        type: nodeData.type || props.node.type || '',
        isSelected: nodeData.isSelected || props.node.isSelected || false,
        ...nodeData
      }
    } catch (error) {
      console.error('获取节点数据时出错:', error)
      return {
        name: props.node.name || '',
        id: props.node.id || '',
        type: props.node.type || '',
        isSelected: props.node.isSelected || false
      }
    }
  }

  return {
    name: data.name || '',
    id: data.id || '',
    type: data.type || '',
    isSelected: data.isSelected || false,
    ...data
  }
})

// 计算节点是否被选中
const isSelected = computed(() => {
  return nodeData.value.isSelected
})

const getNodeIconClass = () => {
  const type = nodeData.value.type || props.node.type
  if (type === 'begin') return 'begin-icon'
  if (type === 'outline') return 'outline-icon'
  if (type === 'group') return 'group-icon'
  return 'chapter-icon'
}
</script>

<style scoped>
.node-container {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  padding: 8px;
  border-radius: 6px;
  background: rgba(202, 147, 242, 0.1);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  color: #fff;
  position: relative;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.node-container:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.begin-node {
  background: rgba(138, 43, 226, 0.2);
  border-color: rgba(138, 43, 226, 0.3);
}

.outline-node {
  background: rgba(78, 42, 132, 0.3);
  border-color: rgba(78, 42, 132, 0.4);
}

.group-node {
  background: rgba(55, 72, 131, 0.3);
  border-color: rgba(55, 72, 131, 0.4);
}

.node-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 10px;
  flex-shrink: 0;
  background: rgba(255, 255, 255, 0.05);
}

.begin-icon {
  color: #8a2be2;
}

.outline-icon {
  color: #9b59b6;
}

.group-icon {
  color: #5d7ecb;
}

.chapter-icon {
  color: #ca93f2;
}

.node-content {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  overflow: hidden;
}

.node-title {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.node-id {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
