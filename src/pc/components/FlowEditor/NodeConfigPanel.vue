<template>
  <div class="node-config-panel" v-if="node">
    <div class="panel-header">
      <h3>{{ nodeTypeName }} 配置</h3>
      <button class="close-button" @click="closePanel">
        <svg viewBox="0 0 24 24" width="16" height="16">
          <path
            fill="currentColor"
            d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"
          />
        </svg>
      </button>
    </div>

    <div class="panel-content">
      <!-- 基本信息配置 -->
      <div class="config-section">
        <div class="section-title">基本信息</div>

        <div class="form-group">
          <label>级别名称</label>
          <input type="text" v-model="editedNode.name" />
        </div>

        <div class="form-group">
          <label>级别 ID</label>
          <input type="text" v-model="editedNode.id" :disabled="!isNewNode" />
        </div>

        <div class="form-group" v-if="showLevelTypeSelect">
          <label>级别类型</label>
          <div class="level-type-options">
            <div
              v-for="option in levelTypeOptions"
              :key="option.id"
              class="level-type-option"
              :class="{ selected: editedNode.type === option.id }"
              @click="editedNode.type = option.id"
            >
              <div class="option-icon" :style="{ backgroundColor: option.color }">
                <svg viewBox="0 0 24 24" width="24" height="24">
                  <path fill="currentColor" :d="option.icon" />
                </svg>
              </div>
              <div class="option-label">{{ option.label }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 节点特有配置 -->
      <div class="config-section" v-if="hasCustomConfig">
        <div class="section-title">{{ nodeTypeName }}配置</div>

        <!-- 根据节点类型显示不同配置 -->
        <template v-if="editedNode.type === 'opening'">
          <div class="form-group">
            <label>角色心情</label>
            <div class="tag-input">
              <div v-for="(mood, index) in characterMoods" :key="index" class="tag">
                {{ mood }}
                <span class="tag-delete" @click="removeMood(index)">×</span>
              </div>
              <div class="tag-input-wrapper">
                <input
                  type="text"
                  v-model="newMood"
                  @keyup.enter="addMood"
                  placeholder="输入心情按回车添加"
                />
                <button @click="addMood">添加</button>
              </div>
            </div>
          </div>

          <div class="form-group">
            <label>场景描述</label>
            <textarea
              v-model="editedNode.properties.sceneDescription"
              rows="5"
              placeholder="输入场景描述..."
            ></textarea>
          </div>
        </template>

        <template v-else-if="editedNode.type === 'dialogue'">
          <div class="form-group">
            <label>对话内容</label>
            <textarea
              v-model="editedNode.properties.dialogueContent"
              rows="5"
              placeholder="输入对话内容..."
            ></textarea>
          </div>

          <!-- <div class="form-group">
            <label>角色语气</label>
            <select v-model="editedNode.properties.characterTone">
              <option value="friendly">友好</option>
              <option value="neutral">中性</option>
              <option value="serious">严肃</option>
              <option value="angry">愤怒</option>
              <option value="sad">悲伤</option>
            </select>
          </div> -->
        </template>

        <template v-else-if="editedNode.type === 'video'">
          <div class="form-group">
            <label>视频 URL</label>
            <input type="text" v-model="editedNode.properties.videoUrl" />
          </div>

          <div class="form-group">
            <label>自动播放</label>
            <div class="toggle-switch">
              <input type="checkbox" id="autoplay" v-model="editedNode.properties.autoplay" />
              <label for="autoplay"></label>
            </div>
          </div>
        </template>

        <template v-else-if="editedNode.type === 'jump'">
          <div class="form-group">
            <label>跳转目标</label>
            <Select
              v-model="editedNode.properties.targetId"
              :options="jumpTargets.map((n) => ({ label: n.name, value: n.id }))"
            ></Select>
          </div>
        </template>
      </div>

      <!-- 成功条件配置 -->
      <div class="config-section" v-if="showSuccessConditions">
        <div class="section-title">成功条件</div>

        <div class="form-group">
          <label>条件列表</label>
          <div class="conditions-list">
            <div
              v-for="(condition, index) in successConditions"
              :key="index"
              class="condition-item"
            >
              <input type="text" v-model="condition.value" />
              <button class="delete-btn" @click="removeCondition(index)">×</button>
            </div>
            <button class="add-condition-btn" @click="addCondition">
              <svg viewBox="0 0 24 24" width="16" height="16">
                <path fill="currentColor" d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z" />
              </svg>
              添加条件
            </button>
          </div>
        </div>
      </div>

      <!-- 事件管理 -->
      <div class="config-section" v-if="!['begin', 'jump'].includes(editedNode.type)">
        <div class="section-title">事件管理</div>
        <EventManager
          :node="editedNode"
          :available-nodes="availableNodes || []"
          @update="handleEventsUpdate"
        />
      </div>
    </div>

    <div class="panel-footer">
      <button class="cancel-btn" @click="closePanel">取消</button>
      <button class="save-btn" @click="saveChanges">保存</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { TreeNode, NodeType, FlowNode, FlowEvent } from './types'
import EventManager from './EventManager.vue'
import Select from '@/mobile/components/Select.vue'

// 属性定义
const props = defineProps<{
  node: TreeNode | null
  isNewNode?: boolean
  availableNodes?: TreeNode[]
}>()

// 事件定义
const emit = defineEmits<{
  save: [node: TreeNode]
  close: []
}>()

// 编辑中的节点数据（深度克隆，避免直接修改原始数据）
const editedNode = ref<TreeNode>({
  id: '',
  name: '',
  type: '',
  properties: {}
})

// 节点特有属性
const newMood = ref('')
const characterMoods = ref<string[]>([])
const successConditions = ref<Array<{ value: string }>>([])

// 级别类型选项
const levelTypeOptions = [
  {
    id: 'opening',
    label: '开场级别',
    icon: 'M3.55,18.54L4.96,19.95L6.76,18.16L5.34,16.74M11,22.45C11.32,22.45 13,22.45 13,22.45V19.5H11M12,5.5A6,6 0 0,0 6,11.5A6,6 0 0,0 12,17.5A6,6 0 0,0 18,11.5C18,8.18 15.31,5.5 12,5.5M20,12.5H23V10.5H20M17.24,18.16L19.04,19.95L20.45,18.54L18.66,16.74M20.45,4.46L19.04,3.05L17.24,4.84L18.66,6.26M13,0.55H11V3.5H13M4,10.5H1V12.5H4M6.76,4.84L4.96,3.05L3.55,4.46L5.34,6.26L6.76,4.84Z',
    color: '#27ae60'
  },
  {
    id: 'dialogue',
    label: '对话级别',
    icon: 'M20,2H4A2,2 0 0,0 2,4V22L6,18H20A2,2 0 0,0 22,16V4A2,2 0 0,0 20,2M6,9H18V11H6M14,14H6V12H14M18,8H6V6H18',
    color: '#3498db'
  },
  {
    id: 'video',
    label: '视频级别',
    icon: 'M4,4H7L9,2H15L17,4H20A2,2 0 0,1 22,6V18A2,2 0 0,1 20,20H4A2,2 0 0,1 2,18V6A2,2 0 0,1 4,4M12,7A5,5 0 0,0 7,12A5,5 0 0,0 12,17A5,5 0 0,0 17,12A5,5 0 0,0 12,7M12,9A3,3 0 0,1 15,12A3,3 0 0,1 12,15A3,3 0 0,1 9,12A3,3 0 0,1 12,9Z',
    color: '#e74c3c'
  },
  {
    id: 'jump',
    label: '跳转级别',
    icon: 'M14,16.94V12.94H5.08L5.05,10.93H14V6.94L19,11.94Z',
    color: '#f39c12'
  }
]

// 跳转目标节点列表
const jumpTargets = computed(() => {
  return props.availableNodes?.filter((n) => n.id !== props.node?.id) || []
})

// 节点类型名称
const nodeTypeName = computed(() => {
  switch (editedNode.value.type) {
    case 'begin':
      return '起始节点'
    case 'opening':
      return '开场级别'
    case 'dialogue':
      return '对话级别'
    case 'video':
      return '视频级别'
    case 'jump':
      return '跳转级别'
    default:
      return '节点'
  }
})

// 是否显示级别类型选择
const showLevelTypeSelect = computed(() => {
  // 只有第二级节点（关卡节点）才能选择类型
  return props.node?.level === 1
})

// 是否有自定义配置
const hasCustomConfig = computed(() => {
  return ['opening', 'dialogue', 'video', 'jump'].includes(editedNode.value.type)
})

// 是否显示成功条件配置
const showSuccessConditions = computed(() => {
  return ['opening', 'dialogue', 'video'].includes(editedNode.value.type)
})

// 处理事件更新
const handleEventsUpdate = (nodeId: string, updatedEvents: FlowEvent[]) => {
  if (editedNode.value.id === nodeId) {
    editedNode.value.events = updatedEvents
  }
}

// 初始化编辑节点数据
const initNodeData = () => {
  if (!props.node) return

  editedNode.value = JSON.parse(JSON.stringify(props.node))

  // 确保properties对象存在
  if (!editedNode.value.properties) {
    editedNode.value.properties = {}
  }

  // 确保events数组存在
  if (!editedNode.value.events) {
    editedNode.value.events = []
  }

  // 初始化特定类型节点的默认属性
  switch (editedNode.value.type) {
    case 'opening':
      if (!editedNode.value.properties.characterMoods) {
        editedNode.value.properties.characterMoods = []
      }
      characterMoods.value = [...editedNode.value.properties.characterMoods]
      break
    case 'video':
      if (!editedNode.value.properties.autoplay) {
        editedNode.value.properties.autoplay = false
      }
      break
  }

  // 初始化成功条件
  successConditions.value = editedNode.value.properties.successConditions
    ? [...editedNode.value.properties.successConditions]
    : []
}

// 添加角色心情
const addMood = () => {
  if (newMood.value.trim()) {
    characterMoods.value.push(newMood.value.trim())
    newMood.value = ''
  }
}

// 移除角色心情
const removeMood = (index: number) => {
  characterMoods.value.splice(index, 1)
}

// 添加成功条件
const addCondition = () => {
  successConditions.value.push({ value: '' })
}

// 移除成功条件
const removeCondition = (index: number) => {
  successConditions.value.splice(index, 1)
}

// 保存更改
const saveChanges = () => {
  // 更新特定类型节点的属性
  switch (editedNode.value.type) {
    case 'opening':
      editedNode.value.properties.characterMoods = [...characterMoods.value]
      break
  }

  // 保存成功条件
  editedNode.value.properties.successConditions = [...successConditions.value]

  emit('save', editedNode.value)
}

// 关闭面板
const closePanel = () => {
  emit('close')
}

// 监听节点变化，重新初始化
watch(
  () => props.node,
  (newNode) => {
    if (newNode) {
      initNodeData()
    }
  },
  { immediate: true }
)
</script>

<style lang="less" scoped>
.node-config-panel {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #1a1a1c;
  border-left: 1px solid rgba(255, 255, 255, 0.1);

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    h3 {
      margin: 0;
      color: #fff;
      font-size: 16px;
      font-weight: 600;
    }

    .close-button {
      background: transparent;
      border: none;
      color: rgba(255, 255, 255, 0.6);
      cursor: pointer;
      transition: color 0.2s;

      &:hover {
        color: #fff;
      }
    }
  }

  .panel-content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;

    .config-section {
      margin-bottom: 24px;

      .section-title {
        font-size: 14px;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.7);
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      }

      .form-group {
        margin-bottom: 16px;

        label {
          display: block;
          font-size: 13px;
          color: rgba(255, 255, 255, 0.7);
          margin-bottom: 6px;
        }

        input[type='text'],
        textarea,
        select {
          width: 100%;
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 4px;
          color: #fff;
          padding: 8px 10px;
          transition: border-color 0.2s;
          font-size: 13px;

          &:focus {
            border-color: #ca93f2;
            outline: none;
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }
        }

        textarea {
          resize: vertical;
          min-height: 80px;
        }
      }

      .level-type-options {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;

        .level-type-option {
          width: calc(50% - 5px);
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 4px;
          padding: 10px;
          display: flex;
          align-items: center;
          cursor: pointer;
          transition: all 0.2s;

          &:hover {
            background: rgba(255, 255, 255, 0.08);
          }

          &.selected {
            border-color: #ca93f2;
            background: rgba(202, 147, 242, 0.1);
          }

          .option-icon {
            width: 36px;
            height: 36px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            color: #fff;
          }

          .option-label {
            color: #fff;
            font-size: 13px;
          }
        }
      }

      .tag-input {
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 4px;
        background: rgba(255, 255, 255, 0.05);
        padding: 6px;
        min-height: 40px;

        .tag {
          display: inline-flex;
          align-items: center;
          background: rgba(202, 147, 242, 0.2);
          color: #fff;
          font-size: 12px;
          padding: 2px 8px;
          border-radius: 3px;
          margin: 4px;

          .tag-delete {
            margin-left: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;

            &:hover {
              color: #ff5252;
            }
          }
        }

        .tag-input-wrapper {
          display: flex;
          margin-top: 8px;

          input {
            flex: 1;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 4px 0 0 4px;
            color: #fff;
            padding: 6px 10px;
            font-size: 12px;

            &:focus {
              outline: none;
              border-color: #ca93f2;
            }
          }

          button {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-left: none;
            border-radius: 0 4px 4px 0;
            color: #fff;
            padding: 6px 10px;
            cursor: pointer;

            &:hover {
              background: rgba(255, 255, 255, 0.15);
            }
          }
        }
      }

      .toggle-switch {
        position: relative;
        display: inline-block;
        width: 44px;
        height: 22px;

        input {
          opacity: 0;
          width: 0;
          height: 0;

          &:checked + label {
            background-color: #ca93f2;

            &:before {
              transform: translateX(22px);
            }
          }

          &:focus + label {
            box-shadow: 0 0 1px #ca93f2;
          }
        }

        label {
          position: absolute;
          cursor: pointer;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(255, 255, 255, 0.1);
          transition: 0.4s;
          border-radius: 34px;

          &:before {
            position: absolute;
            content: '';
            height: 18px;
            width: 18px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: 0.4s;
            border-radius: 50%;
          }
        }
      }

      .conditions-list {
        .condition-item {
          display: flex;
          align-items: center;
          margin-bottom: 8px;

          input {
            flex: 1;
            margin-right: 8px;
          }

          .delete-btn {
            width: 24px;
            height: 24px;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            color: rgba(255, 255, 255, 0.6);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;

            &:hover {
              background: rgba(255, 0, 0, 0.2);
              color: #ff5252;
            }
          }
        }

        .add-condition-btn {
          display: flex;
          align-items: center;
          gap: 6px;
          padding: 6px 12px;
          background: rgba(255, 255, 255, 0.05);
          border: 1px dashed rgba(255, 255, 255, 0.2);
          border-radius: 4px;
          color: rgba(255, 255, 255, 0.7);
          width: 100%;
          justify-content: center;
          cursor: pointer;
          transition: all 0.2s;

          &:hover {
            background: rgba(255, 255, 255, 0.08);
            border-color: rgba(255, 255, 255, 0.3);
          }
        }
      }
    }
  }

  .panel-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 12px 16px;
    background-color: rgba(0, 0, 0, 0.2);
    border-top: 1px solid rgba(255, 255, 255, 0.1);

    button {
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 13px;
      transition: all 0.2s;

      &.cancel-btn {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        color: rgba(255, 255, 255, 0.7);

        &:hover {
          background: rgba(255, 255, 255, 0.1);
          color: #fff;
        }
      }

      &.save-btn {
        background: #ca93f2;
        border: 1px solid #ca93f2;
        color: #000;

        &:hover {
          background: #d5a4fc;
        }
      }
    }
  }
}
</style>
