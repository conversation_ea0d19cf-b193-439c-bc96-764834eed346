<template>
  <div class="material-generator-modal" v-if="visible">
    <div class="modal-overlay" @click="handleClose"></div>
    <div class="modal-content">
      <div class="modal-header">
        <h2>素材生成</h2>
        <button class="close-btn" @click="handleClose">
          <svg viewBox="0 0 24 24" width="24" height="24">
            <path
              fill="currentColor"
              d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
            />
          </svg>
        </button>
      </div>

      <div class="modal-body">
        <div class="user-id-input">
          <div class="form-label">白日梦用户ID</div>
          <input
            type="text"
            class="custom-input"
            v-model="daydreamUserId"
            placeholder="请输入白日梦用户ID"
          />
        </div>

        <div class="material-list">
          <div class="material-content">
            <!-- 按场景组分组显示 -->
            <div class="group-item" v-for="group in sceneGroups" :key="group.groupName">
              <div class="group-header" @click="toggleGroup(group.groupName)">
                <div class="group-info">
                  <svg viewBox="0 0 24 24" width="20" height="20" class="icon-group">
                    <path
                      fill="currentColor"
                      d="M20,18H4V8H20M20,6H12L10,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V8C22,6.89 21.1,6 20,6Z"
                    />
                  </svg>
                  <span class="group-name">场景组：{{ group.groupName }}</span>
                </div>
                <svg
                  viewBox="0 0 24 24"
                  width="20"
                  height="20"
                  class="icon-expand"
                  :class="{ expanded: expandedGroups[group.groupName] }"
                >
                  <path fill="currentColor" d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z" />
                </svg>
              </div>

              <div class="group-content" v-show="expandedGroups[group.groupName]">
                <div class="material-item" v-for="scene in group.scenes" :key="scene.id">
                  <div class="material-item-header" @click="toggleMaterial(scene.id)">
                    <div class="material-info">
                      <svg viewBox="0 0 24 24" width="20" height="20" class="icon-folder">
                        <path
                          fill="currentColor"
                          d="M10 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2h-8l-2-2z"
                        />
                      </svg>
                      <span class="material-name">{{ scene.name }}</span>
                    </div>
                    <svg
                      viewBox="0 0 24 24"
                      width="20"
                      height="20"
                      class="icon-expand"
                      :class="{ expanded: expandedMaterials[scene.id] }"
                    >
                      <path fill="currentColor" d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z" />
                    </svg>
                  </div>

                  <div v-show="expandedMaterials[scene.id]" class="material-item-content">
                    <div class="plot-list">
                      <div
                        v-for="(plot, plotIndex) in getScenePlots(scene)"
                        :key="plotIndex"
                        class="plot-item"
                      >
                        <div class="plot-header">
                          <div class="plot-title">场景描述 {{ plotIndex + 1 }}</div>
                          <button
                            class="plot-delete-button"
                            @click="removePlot(scene.id, plotIndex)"
                            v-if="getScenePlots(scene).length > 1"
                          >
                            <svg viewBox="0 0 24 24" class="icon-delete" width="16" height="16">
                              <path
                                fill="currentColor"
                                d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
                              />
                            </svg>
                            <span>删除场景</span>
                          </button>
                        </div>

                        <div class="form-item">
                          <div class="form-label">场景描述</div>
                          <textarea
                            class="custom-textarea"
                            :value="plot.sentence"
                            @input="
                              updatePlotField(
                                scene.id,
                                plotIndex,
                                'sentence',
                                ($event.target as HTMLTextAreaElement).value
                              )
                            "
                            :rows="3"
                            placeholder="请输入场景描述"
                          ></textarea>
                        </div>

                        <div class="form-item">
                          <div class="form-label">场景位置</div>
                          <input
                            type="text"
                            class="custom-input"
                            :value="plot.location"
                            @input="
                              updatePlotField(
                                scene.id,
                                plotIndex,
                                'location',
                                ($event.target as HTMLInputElement).value
                              )
                            "
                            placeholder="请输入场景位置"
                          />
                        </div>

                        <div class="character-list">
                          <div class="section-title">角色配置</div>
                          <div
                            v-for="(character, charIndex) in plot.character"
                            :key="charIndex"
                            class="character-item"
                          >
                            <div class="form-item">
                              <div class="form-label">角色名称</div>
                              <input
                                type="text"
                                class="custom-input"
                                :value="character.name"
                                @input="
                                  updateCharacter(
                                    scene.id,
                                    plotIndex,
                                    charIndex,
                                    'name',
                                    ($event.target as HTMLInputElement).value
                                  )
                                "
                                placeholder="请输入角色名称"
                              />
                            </div>
                            <div class="form-item">
                              <div class="form-label">Lora ID</div>
                              <input
                                type="text"
                                class="custom-input"
                                :value="character.lora_id"
                                @input="
                                  updateCharacter(
                                    scene.id,
                                    plotIndex,
                                    charIndex,
                                    'lora_id',
                                    ($event.target as HTMLInputElement).value
                                  )
                                "
                                placeholder="请输入Lora ID"
                              />
                            </div>
                            <div class="form-item">
                              <div class="form-label">服装</div>
                              <input
                                type="text"
                                class="custom-input"
                                :value="character.clothes"
                                @input="
                                  updateCharacter(
                                    scene.id,
                                    plotIndex,
                                    charIndex,
                                    'clothes',
                                    ($event.target as HTMLInputElement).value
                                  )
                                "
                                placeholder="请输入服装描述"
                              />
                            </div>
                            <button
                              class="character-delete-button"
                              @click="removeCharacter(scene.id, plotIndex, charIndex)"
                              v-if="plot.character.length > 1"
                            >
                              <svg viewBox="0 0 24 24" class="icon-delete" width="16" height="16">
                                <path
                                  fill="currentColor"
                                  d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
                                />
                              </svg>
                              <span>删除角色</span>
                            </button>
                          </div>
                          <button
                            class="add-character-button"
                            @click="addCharacter(scene.id, plotIndex)"
                          >
                            <svg viewBox="0 0 24 24" width="16" height="16">
                              <path fill="currentColor" d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
                            </svg>
                            <span>添加角色</span>
                          </button>
                        </div>
                      </div>
                      <button class="add-plot-button" @click="addPlot(scene.id)">
                        <svg viewBox="0 0 24 24" width="16" height="16">
                          <path fill="currentColor" d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
                        </svg>
                        <span>添加场景描述</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <button class="cancel-btn" @click="handleClose">取消</button>
        <button
          class="generate-btn"
          @click="handleGenerate"
          :disabled="isGenerating || hasSubmitted"
        >
          <span v-if="isGenerating">生成中...</span>
          <span v-else-if="hasSubmitted">已提交</span>
          <span v-else>生成素材</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useEditorStore } from '@/store/editor'
import { cloneDeep } from 'lodash-es'
import type { Scene, Plot, Character } from '@/types/editor'
import { Message } from '@/mobile/components/Message'

const props = defineProps<{
  visible: boolean
}>()

const emit = defineEmits<{
  (e: 'close'): void
  (e: 'generate-success'): void
}>()

const editorStore = useEditorStore()
const expandedMaterials = ref<Record<string, boolean>>({})
const expandedGroups = ref<Record<string, boolean>>({})
const isGenerating = ref(false)
const hasSubmitted = ref(false)
const daydreamUserId = ref(editorStore.gameConfig.daydream_user_id || '')

// 按场景组分组的场景
// 先获取所有场景组
const sceneGroups = computed(() => {
  const allScenes = [...editorStore.gameConfig.scenes].filter((scene) => scene.id !== '~')
  const groups = new Map<string, Scene[]>()

  // 将场景按组分类
  allScenes.forEach((scene) => {
    const groupName = scene.scene_group || 'default'
    if (!groups.has(groupName)) {
      groups.set(groupName, [])
    }
    groups.get(groupName)?.push(scene)
  })

  // 将分组结果转换为数组并排序
  return Array.from(groups.entries())
    .sort(([groupNameA], [groupNameB]) => {
      // 默认组始终在最后
      if (groupNameA === 'default') return 1
      if (groupNameB === 'default') return -1
      // 其他组按名称字母顺序排序
      return groupNameA.localeCompare(groupNameB)
    })
    .map(([groupName, scenes]) => ({
      groupName,
      scenes: scenes.sort((a, b) => {
        // _BEGIN_ 场景始终在最前
        if (a.id === '_BEGIN_') return -1
        if (b.id === '_BEGIN_') return 1
        // _END_ 场景始终在最后
        if (a.id === '_END_') return 1
        if (b.id === '_END_') return -1
        // 其他场景保持原有顺序
        return 0
      })
    }))
})

// 保留原来的场景列表以兼容其他功能
const scenes = computed(() => {
  // 对场景进行排序，确保开始场景在最前，结束场景在最后
  return [...editorStore.gameConfig.scenes]
    .filter((scene) => scene.id !== '~')
    .sort((a, b) => {
      // _BEGIN_ 场景始终在最前
      if (a.id === '_BEGIN_') return -1
      if (b.id === '_BEGIN_') return 1
      // _END_ 场景始终在最后
      if (a.id === '_END_') return 1
      if (b.id === '_END_') return -1
      // 其他场景保持原有顺序
      return 0
    })
})

const toggleGroup = (groupName: string) => {
  expandedGroups.value[groupName] = !expandedGroups.value[groupName]
}

const toggleMaterial = (sceneId: string) => {
  expandedMaterials.value[sceneId] = !expandedMaterials.value[sceneId]
}

const getScenePlots = (scene: Scene): Plot[] => {
  if (!scene.story_generation_params?.plots) {
    return [
      {
        sentence: '',
        character: [],
        location: ''
      }
    ]
  }
  return scene.story_generation_params.plots
}

const updatePlotField = (
  sceneId: string,
  plotIndex: number,
  field: Exclude<keyof Plot, 'character'>,
  value: string
) => {
  const scene = scenes.value.find((s) => s.id === sceneId)
  if (!scene) return

  const updatedScene = cloneDeep(scene)
  if (!updatedScene.story_generation_params) {
    updatedScene.story_generation_params = {
      plots: [
        {
          sentence: '',
          character: [],
          location: ''
        }
      ]
    }
  }

  if (!updatedScene.story_generation_params.plots[plotIndex]) {
    updatedScene.story_generation_params.plots[plotIndex] = {
      sentence: '',
      character: [],
      location: ''
    }
  }

  updatedScene.story_generation_params.plots[plotIndex][field] = value
  editorStore.updateScene(updatedScene)
}

const updateCharacter = (
  sceneId: string,
  plotIndex: number,
  charIndex: number,
  key: keyof Character,
  value: string
) => {
  const scene = scenes.value.find((s) => s.id === sceneId)
  if (!scene) return

  const updatedScene = cloneDeep(scene)
  if (!updatedScene.story_generation_params) {
    updatedScene.story_generation_params = {
      plots: [
        {
          sentence: '',
          character: [],
          location: ''
        }
      ]
    }
  }

  if (!updatedScene.story_generation_params.plots[plotIndex]) {
    updatedScene.story_generation_params.plots[plotIndex] = {
      sentence: '',
      character: [],
      location: ''
    }
  }

  if (!updatedScene.story_generation_params.plots[plotIndex].character[charIndex]) {
    updatedScene.story_generation_params.plots[plotIndex].character[charIndex] = {
      name: '',
      lora_id: '',
      clothes: ''
    }
  }

  updatedScene.story_generation_params.plots[plotIndex].character[charIndex][key] = value
  editorStore.updateScene(updatedScene)
}

const addCharacter = (sceneId: string, plotIndex: number) => {
  const scene = scenes.value.find((s) => s.id === sceneId)
  if (!scene) return

  const updatedScene = cloneDeep(scene)
  if (!updatedScene.story_generation_params) {
    updatedScene.story_generation_params = {
      plots: [
        {
          sentence: '',
          character: [],
          location: ''
        }
      ]
    }
  }

  if (!updatedScene.story_generation_params.plots[plotIndex]) {
    updatedScene.story_generation_params.plots[plotIndex] = {
      sentence: '',
      character: [],
      location: ''
    }
  }

  updatedScene.story_generation_params.plots[plotIndex].character.push({
    name: '',
    lora_id: '',
    clothes: ''
  })

  editorStore.updateScene(updatedScene)
}

const removeCharacter = (sceneId: string, plotIndex: number, charIndex: number) => {
  const scene = scenes.value.find((s) => s.id === sceneId)
  if (!scene?.story_generation_params?.plots[plotIndex]?.character) return

  const updatedScene = cloneDeep(scene)
  updatedScene.story_generation_params.plots[plotIndex].character.splice(charIndex, 1)
  editorStore.updateScene(updatedScene)
}

const addPlot = (sceneId: string) => {
  const scene = scenes.value.find((s) => s.id === sceneId)
  if (!scene) return

  const updatedScene = cloneDeep(scene)
  if (!updatedScene.story_generation_params) {
    updatedScene.story_generation_params = {
      plots: []
    }
  }

  updatedScene.story_generation_params.plots.push({
    sentence: '',
    character: [],
    location: ''
  })

  editorStore.updateScene(updatedScene)
}

const removePlot = (sceneId: string, plotIndex: number) => {
  const scene = scenes.value.find((s) => s.id === sceneId)
  if (!scene?.story_generation_params?.plots) return

  const updatedScene = cloneDeep(scene)
  updatedScene.story_generation_params.plots.splice(plotIndex, 1)
  editorStore.updateScene(updatedScene)
}

const handleGenerate = async () => {
  if (!editorStore.currentStoryId) {
    Message.warning('请先保存故事配置后再生成素材')
    return
  }

  if (!daydreamUserId.value) {
    Message.warning('请输入白日梦用户ID')
    return
  }

  // 更新白日梦用户ID
  editorStore.gameConfig.daydream_user_id = daydreamUserId.value
  editorStore.isModified = true

  try {
    isGenerating.value = true
    // 先更新故事，确保故事配置id是最新的
    await editorStore.saveStory()
    const res = await editorStore.generateResources()
    if (res) {
      Message.success('素材生成任务提交成功')
      hasSubmitted.value = true
      emit('generate-success')
    } else {
      Message.error('素材生成任务提交失败')
    }
  } catch (error) {
    Message.error(error instanceof Error ? error.message : '素材生成任务提交失败')
  } finally {
    isGenerating.value = false
  }
}

const handleClose = () => {
  emit('close')
}

// 初始化时展开所有场景组和场景
onMounted(() => {
  // 展开所有场景组
  sceneGroups.value.forEach((group) => {
    expandedGroups.value[group.groupName] = true
    // 展开每个组中的所有场景
    group.scenes.forEach((scene) => {
      expandedMaterials.value[scene.id] = true
    })
  })
})
</script>

<style lang="less" scoped>
.material-generator-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: relative;
  width: 800px;
  max-width: 90%;
  max-height: 90vh;
  background-color: #1e1e2e;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.modal-header {
  padding: 16px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;

  h2 {
    margin: 0;
    color: #fff;
    font-size: 18px;
  }

  .close-btn {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.6);
    cursor: pointer;
    padding: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;

    &:hover {
      color: #fff;
      background-color: rgba(255, 255, 255, 0.1);
    }
  }
}

.modal-body {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.modal-footer {
  padding: 16px 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  button {
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
  }

  .cancel-btn {
    background-color: transparent;
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #fff;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }
  }

  .generate-btn {
    background-color: #7c3aed;
    border: none;
    color: #fff;

    &:hover {
      background-color: #6d28d9;
    }

    &:disabled {
      background-color: #4b5563;
      cursor: not-allowed;
    }
  }
}

.user-id-input {
  margin-bottom: 20px;

  .form-label {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 8px;
  }

  .custom-input {
    width: 100%;
    height: 42px;
    padding: 0 16px;
    border: 1px solid rgba(184, 196, 255, 0.1);
    background: rgba(204, 213, 255, 0.05);
    color: #fff;
    border-radius: 8px;
    font-size: 14px;
    outline: none;
    transition: all 0.3s ease;

    &:hover,
    &:focus {
      border-color: #7c3aed;
      background: rgba(204, 213, 255, 0.08);
    }
  }
}

.material-list {
  .group-item {
    margin-bottom: 20px;
    border-radius: 8px;
    overflow: hidden;
    background-color: rgba(255, 255, 255, 0.03);
  }

  .group-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: rgba(255, 255, 255, 0.1);
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background-color: rgba(255, 255, 255, 0.15);
    }

    .group-info {
      display: flex;
      align-items: center;
      gap: 8px;

      .icon-group {
        color: #f59e0b;
      }

      .group-name {
        font-size: 15px;
        font-weight: 600;
        color: #fff;
      }
    }

    .icon-expand {
      transition: transform 0.2s;

      &.expanded {
        transform: rotate(180deg);
      }
    }
  }

  .group-content {
    padding: 8px;
  }

  .material-item {
    margin-bottom: 12px;
    border-radius: 8px;
    overflow: hidden;
    background-color: rgba(255, 255, 255, 0.05);
  }

  .material-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: rgba(255, 255, 255, 0.08);
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover {
      background-color: rgba(255, 255, 255, 0.12);
    }

    .material-info {
      display: flex;
      align-items: center;
      gap: 8px;

      .icon-folder {
        color: #7c3aed;
      }

      .material-name {
        font-size: 14px;
        font-weight: 500;
        color: #fff;
      }
    }

    .icon-expand {
      transition: transform 0.2s;

      &.expanded {
        transform: rotate(180deg);
      }
    }
  }

  .material-item-content {
    padding: 16px;
  }

  .plot-list {
    .plot-item {
      margin-bottom: 20px;
      padding: 16px;
      border-radius: 8px;
      background-color: rgba(255, 255, 255, 0.03);
    }

    .plot-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .plot-title {
        font-size: 14px;
        font-weight: 500;
        color: #fff;
      }

      .plot-delete-button {
        display: flex;
        align-items: center;
        gap: 4px;
        padding: 4px 8px;
        background-color: rgba(255, 59, 48, 0.1);
        border: none;
        border-radius: 4px;
        color: #ff3b30;
        font-size: 12px;
        cursor: pointer;
        transition: background-color 0.2s;

        &:hover {
          background-color: rgba(255, 59, 48, 0.2);
        }
      }
    }
  }

  .form-item {
    margin-bottom: 16px;

    .form-label {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.8);
      margin-bottom: 8px;
    }

    .custom-input,
    .custom-textarea {
      width: 100%;
      padding: 10px 16px;
      border: 1px solid rgba(184, 196, 255, 0.1);
      background: rgba(204, 213, 255, 0.05);
      color: #fff;
      border-radius: 8px;
      font-size: 14px;
      outline: none;
      transition: all 0.3s ease;

      &:hover,
      &:focus {
        border-color: #7c3aed;
        background: rgba(204, 213, 255, 0.08);
      }
    }

    .custom-textarea {
      resize: vertical;
      min-height: 80px;
    }
  }

  .character-list {
    margin-top: 20px;

    .section-title {
      font-size: 14px;
      font-weight: 500;
      color: #fff;
      margin-bottom: 16px;
    }

    .character-item {
      margin-bottom: 16px;
      padding: 16px;
      border-radius: 8px;
      background-color: rgba(255, 255, 255, 0.05);
      position: relative;
    }

    .character-delete-button {
      position: absolute;
      top: 8px;
      right: 8px;
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 4px 8px;
      background-color: rgba(255, 59, 48, 0.1);
      border: none;
      border-radius: 4px;
      color: #ff3b30;
      font-size: 12px;
      cursor: pointer;
      transition: background-color 0.2s;

      &:hover {
        background-color: rgba(255, 59, 48, 0.2);
      }
    }
  }

  .add-character-button,
  .add-plot-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    width: 100%;
    padding: 10px;
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px dashed rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
      border-color: rgba(255, 255, 255, 0.3);
      color: #fff;
    }
  }
}
</style>
