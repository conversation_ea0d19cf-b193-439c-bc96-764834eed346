import { FlowEvent, FlowEventType } from '@/pc/components/FlowEditor/types'

// 使用缓存对象存储计算结果
const eventTypeNameCache: Record<string, string> = {}
const eventTypeColorCache: Record<string, string> = {}
const eventTypeIconCache: Record<string, string> = {}

// 获取事件类型的显示名称
export const getEventTypeName = (type: string): string => {
  // 如果缓存中已有结果，直接返回
  if (eventTypeNameCache[type]) {
    return eventTypeNameCache[type]
  }

  const eventTypeMap: Record<string, string> = {
    message: '对话事件',
    wait: '等待事件',
    show_tips: '提示事件',
    show_overlay: '覆盖层事件',
    show_image: '图片事件',
    animated_images: '动画事件',
    play_video: '视频事件',
    play_audio: '音频事件',
    show_chat_options: '对话选项',
    update_user_coins: '金币事件',
    update_task_progress: '任务事件',
    scene_transition: '场景转换',
    heart_value: '好感度事件',
    interactive: '互动事件',
    show_ending: '结局事件'
  }

  const result = eventTypeMap[type] || '未知事件'

  // 将结果存入缓存
  eventTypeNameCache[type] = result

  return result
}

// 获取事件类型的颜色
export const getEventTypeColor = (type: string): string => {
  // 如果缓存中已有结果，直接返回
  if (eventTypeColorCache[type]) {
    return eventTypeColorCache[type]
  }

  const colorMap: Record<string, string> = {
    message: '#4A90E2', // 蓝色
    wait: '#9B9B9B', // 灰色
    show_tips: '#F5A623', // 橙色
    show_overlay: '#7ED321', // 绿色
    show_image: '#50E3C2', // 青色
    animated_images: '#BD10E0', // 紫色
    play_video: '#D0021B', // 红色
    play_audio: '#8B572A', // 棕色
    show_chat_options: '#FF5722', // 深橙色
    update_user_coins: '#FFC107', // 琥珀色
    update_task_progress: '#4CAF50', // 草绿色
    scene_transition: '#9C27B0', // 紫色
    heart_value: '#E91E63', // 粉色
    interactive: '#00BCD4', // 蓝绿色
    show_ending: '#607D8B' // 蓝灰色
  }

  const result = colorMap[type] || '#9B9B9B'

  // 将结果存入缓存
  eventTypeColorCache[type] = result

  return result
}

// 获取事件类型的图标路径
export const getEventTypeIcon = (type: string): string => {
  // 如果缓存中已有结果，直接返回
  if (eventTypeIconCache[type]) {
    return eventTypeIconCache[type]
  }

  const iconMap: Record<string, string> = {
    message:
      'M20,2H4A2,2 0 0,0 2,4V22L6,18H20A2,2 0 0,0 22,16V4A2,2 0 0,0 20,2M6,9H18V11H6M14,14H6V12H14M18,8H6V6H18',
    wait: 'M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z',
    show_tips:
      'M11,9H13V7H11M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M11,17H13V11H11V17Z',
    show_overlay: 'M3,3H21V21H3V3M7,7V17H17V7H7Z',
    show_image:
      'M21,3H3C2,3 1,4 1,5V19A2,2 0 0,0 3,21H21C22,21 23,20 23,19V5C23,4 22,3 21,3M5,17L8.5,12.5L11,15.5L14.5,11L19,17H5Z',
    animated_images:
      'M4,2C2.89,2 2,2.89 2,4V14H4V4H14V2H4M8,6C6.89,6 6,6.89 6,8V18H8V8H18V6H8M12,10C10.89,10 10,10.89 10,12V20C10,21.11 10.89,22 12,22H20C21.11,22 22,21.11 22,20V12C22,10.89 21.11,10 20,10H12Z',
    play_video:
      'M17,10.5V7A1,1 0 0,0 16,6H4A1,1 0 0,0 3,7V17A1,1 0 0,0 4,18H16A1,1 0 0,0 17,17V13.5L21,17.5V6.5L17,10.5Z',
    play_audio:
      'M13,9H18.5L13,3.5V9M6,2H14L20,8V20A2,2 0 0,1 18,22H6C4.89,22 4,21.1 4,20V4C4,2.89 4.89,2 6,2M9,16A2,2 0 0,0 7,18A2,2 0 0,0 9,20A2,2 0 0,0 11,18V13H14V11H10V16.27C9.71,16.1 9.36,16 9,16Z',
    show_chat_options:
      'M12,3C17.5,3 22,6.58 22,11C22,15.42 17.5,19 12,19C10.76,19 9.57,18.82 8.47,18.5C5.55,21 2,21 2,21C4.33,18.67 4.7,17.1 4.75,16.5C3.05,15.07 2,13.13 2,11C2,6.58 6.5,3 12,3M17,12V10H15V12H17M13,12V10H11V12H13M9,12V10H7V12H9Z',
    update_user_coins:
      'M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M11,17V16H9V14H13V13H10A1,1 0 0,1 9,12V9A1,1 0 0,1 10,8H11V7H13V8H15V10H11V11H14A1,1 0 0,1 15,12V15A1,1 0 0,1 14,16H13V17H11Z',
    update_task_progress:
      'M16,10H8V8H10V4H14V8H16M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z',
    scene_transition:
      'M4,2C2.89,2 2,2.89 2,4V14H4V4H14V2H4M8,6C6.89,6 6,6.89 6,8V18H8V8H18V6H8M12,10C10.89,10 10,10.89 10,12V20C10,21.11 10.89,22 12,22H20C21.11,22 22,21.11 22,20V12C22,10.89 21.11,10 20,10H12Z',
    heart_value:
      'M12,21.35L10.55,20.03C5.4,15.36 2,12.27 2,8.5C2,5.41 4.42,3 7.5,3C9.24,3 10.91,3.81 12,5.08C13.09,3.81 14.76,3 16.5,3C19.58,3 22,5.41 22,8.5C22,12.27 18.6,15.36 13.45,20.03L12,21.35Z',
    interactive:
      'M10,2H14A2,2 0 0,1 16,4V6H20A2,2 0 0,1 22,8V19A2,2 0 0,1 20,21H4C2.89,21 2,20.1 2,19V8C2,6.89 2.89,6 4,6H8V4C8,2.89 8.89,2 10,2M14,6V4H10V6H14Z',
    show_ending:
      'M19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3M19,19H5V5H19V19M17,17H7V16H17V17M17,15H7V14H17V15M17,12H7V7H17V12Z'
  }

  const result = iconMap[type] || 'M3,3H21V21H3V3M7,7V17H17V7H7Z'

  // 将结果存入缓存
  eventTypeIconCache[type] = result

  return result
}

// 生成新的事件ID
export const generateEventId = (type: FlowEventType, sceneId: string): string => {
  return `${sceneId}_${type}_${Date.now().toString(36)}`
}

// 创建新的事件对象
export const createNewEvent = (type: FlowEventType, sceneId: string): FlowEvent => {
  const event: FlowEvent = {
    id: generateEventId(type, sceneId),
    type,
    params: {}
  }

  // 根据不同事件类型设置默认参数
  switch (type) {
    case 'message':
      event.params = {
        content: '',
        character: '',
        mood: 'normal',
        speed: 1,
        volume: 1,
        autoNext: false
      }
      break
    case 'wait':
      event.params = {
        seconds: 1,
        showIndicator: false
      }
      break
    case 'show_tips':
      event.params = {
        content: '',
        position: 'center',
        duration: 3,
        style: 'info',
        showIcon: true
      }
      break
    case 'play_audio':
      event.params = {
        url: '',
        volume: 1,
        loop: false,
        type: 'bgm'
      }
      break
    case 'scene_transition':
      event.params = {
        targetNodeId: '',
        seconds: 0,
        transitionEffect: 'fade'
      }
      break
  }

  return event
}
