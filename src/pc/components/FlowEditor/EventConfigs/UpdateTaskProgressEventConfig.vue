<template>
  <div class="update-task-progress-config">
    <div class="form-item">
      <div class="form-label">任务ID</div>
      <input
        type="text"
        class="custom-input"
        :value="params?.task_id"
        @change="updateParam('task_id', ($event.target as HTMLInputElement).value)"
        placeholder="请输入任务ID"
      />
      <div class="form-sublabel">任务的唯一标识符</div>
    </div>

    <div class="form-item">
      <div class="form-label">任务描述</div>
      <textarea
        class="custom-textarea"
        :value="params?.task_description"
        @change="updateParam('task_description', ($event.target as HTMLTextAreaElement).value)"
        rows="3"
        placeholder="请输入任务描述"
      ></textarea>
      <div class="form-sublabel">显示给用户的任务描述文本</div>
    </div>

    <!-- <div class="form-item">
      <div class="form-label">任务状态</div>
      <select
        class="custom-select"
        :value="params?.status || 'in_progress'"
        @change="updateParam('status', ($event.target as HTMLSelectElement).value)"
      >
        <option value="in_progress">进行中</option>
        <option value="completed">已完成</option>
        <option value="failed">失败</option>
      </select>
      <div class="form-sublabel">任务的当前状态</div>
    </div>
    
    <div class="form-item">
      <div class="form-label">进度值</div>
      <input
        type="number"
        class="custom-input"
        :value="params?.progress || 0"
        @input="updateParam('progress', Number(($event.target as HTMLInputElement).value))"
        min="0"
        max="100"
        step="1"
        placeholder="请输入进度值(0-100)"
      />
      <div class="form-sublabel">任务完成的百分比(0-100)</div>
    </div> -->
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

// 定义组件接收的属性
const props = defineProps<{
  params?: {
    task_id?: string
    task_description?: string
    status?: string
    progress?: number
    [key: string]: any
  }
  availableNodes?: { id: string; name: string; type: string }[]
}>()

// 定义组件发出的事件
const emit = defineEmits<{
  (e: 'update', params: any): void
}>()

// 更新参数
const updateParam = (key: string, value: any) => {
  if (!props.params) return

  // 创建一个新的对象，避免直接修改props
  const updatedParams = { ...props.params, [key]: value }

  // 发出更新事件
  emit('update', updatedParams)
}

// 监听props变化
watch(
  () => props.params,
  (newParams) => {
    if (!newParams) return

    // 确保必要的字段存在
    if (newParams.status === undefined) {
      updateParam('status', 'in_progress')
    }

    if (newParams.progress === undefined) {
      updateParam('progress', 0)
    }
  },
  { immediate: true, deep: true }
)
</script>

<style lang="less" scoped>
.update-task-progress-config {
  width: 100%;

  .form-item {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .form-label {
    margin-bottom: 6px;
    font-size: 13px;
    color: rgba(255, 255, 255, 0.7);
  }

  .form-sublabel {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.5);
    margin-top: 2px;
  }

  .custom-input,
  .custom-textarea,
  .custom-select {
    width: 100%;
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    color: rgba(255, 255, 255, 0.9);
    padding: 8px 10px;
    font-size: 13px;
    transition: all 0.2s;

    &:focus {
      border-color: #ca93f2;
      outline: none;
      box-shadow: 0 0 0 2px rgba(202, 147, 242, 0.2);
    }

    &::placeholder {
      color: rgba(255, 255, 255, 0.3);
    }
  }

  .custom-textarea {
    resize: vertical;
    min-height: 80px;
  }
}
</style>
