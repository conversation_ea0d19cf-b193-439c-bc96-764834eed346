<template>
  <div class="chat-options-config">
    <div class="config-section">
      <div class="section-title">对话选项配置</div>

      <div class="form-group">
        <div class="checkbox-control">
          <input type="checkbox" id="allowInput" v-model="params.allow_input" />
          <label for="allowInput">允许自由输入 (allow_input)</label>
        </div>
        <div class="field-hint">启用后，玩家可以输入自定义回复</div>
      </div>

      <div class="form-group" v-if="params.allow_input">
        <label>输入提示文本 (input_placeholder)</label>
        <input
          type="text"
          v-model="params.input_placeholder"
          class="custom-input"
          placeholder="请输入提示文本"
        />
        <div class="field-hint">输入框中显示的提示文字</div>
      </div>

      <div class="form-group">
        <label>选项类型（卡片/按钮）</label>
        <Select
          class="custom-select"
          v-model="params.style"
          :options="[
            { label: '卡片(card)', value: 'card' },
            { label: '按钮(button)', value: 'button' }
          ]"
        />
      </div>

      <div class="form-group" v-if="params.options && params.options.length > 0">
        <label>对话选项</label>
        <div class="options-list">
          <div
            v-for="(option, index) in params.options"
            :key="option.option_id"
            class="option-item"
          >
            <div class="option-header">
              <span class="option-number">选项 {{ index + 1 }}</span>
              <div class="option-actions">
                <button class="action-button delete" @click="removeOption(index)" title="删除选项">
                  <svg viewBox="0 0 24 24" class="icon-delete">
                    <path
                      fill="currentColor"
                      d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"
                    />
                  </svg>
                </button>
              </div>
            </div>

            <div class="form-group">
              <label class="required">选项文本 (options[{{ index }}].text)</label>
              <input
                type="text"
                v-model="option.text"
                class="custom-input"
                placeholder="请输入选项文本"
                required
              />
            </div>

            <div class="form-group">
              <label class="required">跳转场景 (options[{{ index }}].scene_id)</label>
              <Select
                v-model="option.scene_id"
                class="custom-select"
                required
                :options="handledAvailableNodes"
              >
              </Select>
            </div>

            <div class="form-group">
              <div class="checkbox-control">
                <input
                  :id="`paidRequired${index}`"
                  type="checkbox"
                  v-model="option.paid_required"
                />
                <label :for="`paidRequired${index}`"
                  >付费选项 (options[{{ index }}].paid_required)</label
                >
              </div>
            </div>

            <div class="form-group" v-if="option.paid_required">
              <label>付费金额 (options[{{ index }}].coins)</label>
              <input
                type="number"
                v-model.number="option.coins"
                min="0"
                step="1"
                class="custom-input"
                placeholder="请输入付费金额"
              />
            </div>

            <div class="form-group">
              <div class="checkbox-control">
                <input :id="`isHighlight${index}`" type="checkbox" v-model="option.is_highlight" />
                <label :for="`isHighlight${index}`"
                  >隐藏选项 (options[{{ index }}].is_highlight)</label
                >
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="form-group">
        <button class="add-option-button" @click="addOption">
          <svg viewBox="0 0 24 24" class="icon-add">
            <path fill="currentColor" d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
          </svg>
          添加对话选项
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, computed } from 'vue'
import { FlowNode } from '../types'
import Select from '@/mobile/components/Select.vue'

interface ChatOption {
  option_id: string
  text: string
  scene_id: string
  paid_required?: boolean
  coins?: number
  is_highlight?: boolean
  action?: string
}

interface Props {
  params: {
    allow_input?: boolean
    input_placeholder?: string
    style?: string
    options: ChatOption[]
  }
  availableNodes?: FlowNode[]
}

// 属性定义
const props = withDefaults(defineProps<Props>(), {
  params: () => ({
    allow_input: false,
    input_placeholder: '',
    style: 'card',
    options: []
  }),
  availableNodes: () => []
})

// 事件定义
const emit = defineEmits<{
  update: [updatedParams: any]
}>()

// 本地状态
const params = ref({ ...props.params })

// 跳转场景
const handledAvailableNodes = computed(() => {
  return (
    props.availableNodes.map((node) => ({
      label: `${node.id}`,
      value: node.id
    })) || []
  )
})

// 添加选项
const addOption = () => {
  // 使用时间戳作为唯一ID
  const newOption: ChatOption = {
    option_id: Date.now().toString(),
    text: '',
    scene_id: '',
    action: 'continue',
    paid_required: false,
    coins: 0,
    is_highlight: false
  }
  params.value.options.push(newOption)
  emit('update', params.value)
}

// 删除选项
const removeOption = (index: number) => {
  params.value.options.splice(index, 1)
  emit('update', params.value)
}

// 监听本地状态变化并向父组件发送更新
watch(
  params,
  (newParams) => {
    emit('update', newParams)
  },
  { deep: true }
)

// 监听props变化来更新本地状态
watch(
  () => props.params,
  (newParams) => {
    // 避免循环更新
    if (JSON.stringify(newParams) !== JSON.stringify(params.value)) {
      params.value = { ...newParams }
    }
  },
  { deep: true }
)

// 初始化默认值
onMounted(() => {
  // 确保所有需要的字段都存在
  if (params.value.allow_input === undefined) {
    params.value.allow_input = false
  }
  if (params.value.input_placeholder === undefined) {
    params.value.input_placeholder = ''
  }
  if (params.value.style === undefined) {
    params.value.style = 'card'
  }
  if (!params.value.options) {
    params.value.options = []
  }
})
</script>

<style lang="less" scoped>
.chat-options-config {
  .config-section {
    margin-bottom: 24px;

    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: rgba(255, 255, 255, 0.7);
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .form-group {
      margin-bottom: 16px;

      label {
        display: block;
        font-size: 13px;
        color: rgba(255, 255, 255, 0.7);
        margin-bottom: 6px;

        &.required::after {
          content: '*';
          color: #e74c3c;
          margin-left: 4px;
        }
      }

      .custom-input,
      .custom-textarea,
      .custom-select {
        width: 100%;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 4px;
        color: #fff;
        padding: 8px 10px;
        transition: border-color 0.2s;
        font-size: 13px;

        &:focus {
          border-color: #ca93f2;
          outline: none;
        }
      }

      .custom-textarea {
        resize: vertical;
        min-height: 60px;
      }

      .checkbox-control {
        display: flex;
        align-items: center;
        gap: 8px;

        input[type='checkbox'] {
          -webkit-appearance: none;
          appearance: none;
          width: 18px;
          height: 18px;
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 4px;
          background: rgba(255, 255, 255, 0.05);
          display: inline-block;
          position: relative;
          margin: 0;

          &:checked {
            background: #ca93f2;
            border-color: #ca93f2;

            &::after {
              content: '';
              position: absolute;
              width: 4px;
              height: 8px;
              border: solid white;
              border-width: 0 2px 2px 0;
              top: 3px;
              left: 6px;
              transform: rotate(45deg);
            }
          }
        }

        label {
          display: inline;
          margin: 0;
        }
      }

      .field-hint {
        margin-top: 4px;
        font-size: 12px;
        color: rgba(255, 255, 255, 0.4);
      }
    }

    .options-list {
      margin-top: 8px;

      .option-item {
        padding: 12px;
        margin-bottom: 12px;
        background: rgba(255, 255, 255, 0.03);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        position: relative;

        .option-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;
          padding-bottom: 8px;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);

          .option-number {
            font-weight: 500;
            color: rgba(255, 255, 255, 0.7);
          }

          .option-actions {
            display: flex;
            gap: 4px;

            .action-button {
              width: 28px;
              height: 28px;
              display: flex;
              align-items: center;
              justify-content: center;
              border-radius: 4px;
              border: none;
              cursor: pointer;
              background: rgba(255, 255, 255, 0.05);
              color: rgba(255, 255, 255, 0.7);
              transition: all 0.2s;

              &:hover {
                background: rgba(255, 255, 255, 0.1);
              }

              &:disabled {
                opacity: 0.3;
                cursor: not-allowed;
              }

              &.delete {
                color: #ff4d4f;
                background: rgba(255, 77, 79, 0.1);

                &:hover {
                  background: rgba(255, 77, 79, 0.2);
                }
              }

              svg {
                width: 18px;
                height: 18px;
              }
            }
          }
        }

        &:last-child {
          margin-bottom: 0;
        }

        .form-group:last-child {
          margin-bottom: 0;
        }
      }
    }

    .add-option-button {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      width: 100%;
      height: 40px;
      border: 1px dashed rgba(255, 255, 255, 0.2);
      border-radius: 8px;
      background: none;
      color: rgba(255, 255, 255, 0.6);
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        border-color: #ca93f2;
        color: #ca93f2;
      }

      svg {
        width: 18px;
        height: 18px;
      }
    }
  }
}
</style>
