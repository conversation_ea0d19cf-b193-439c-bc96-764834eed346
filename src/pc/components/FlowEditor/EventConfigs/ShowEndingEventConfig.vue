<template>
  <div class="show-ending-config">
    <div class="config-section">
      <div class="section-title">结局配置</div>

      <div class="form-group">
        <label class="required">结局内容 (content.html)</label>
        <textarea
          v-model="content.html"
          class="custom-textarea"
          rows="5"
          placeholder="请输入结局内容（支持HTML）"
          required
          @input="updateContent"
        ></textarea>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'

interface MessageContent {
  text?: string
  html?: string
  media_url?: string
}

interface Props {
  params: {
    content?: MessageContent
  }
}

// 属性定义
const props = withDefaults(defineProps<Props>(), {
  params: () => ({
    content: { text: '', html: '', media_url: '' }
  })
})

// 事件定义
const emit = defineEmits<{
  update: [updatedParams: any]
}>()

// 本地状态
const params = ref({ ...props.params })
const content = ref<MessageContent>(props.params.content || { text: '', html: '', media_url: '' })

// 更新内容
const updateContent = (event: Event) => {
  const value = (event.target as HTMLTextAreaElement).value
  content.value.html = value
  params.value.content = content.value
  emit('update', params.value)
}

// 监听本地状态变化并向父组件发送更新
watch(
  params,
  (newParams) => {
    emit('update', newParams)
  },
  { deep: true }
)

// 监听props变化来更新本地状态
watch(
  () => props.params,
  (newParams) => {
    // 避免循环更新
    if (JSON.stringify(newParams) !== JSON.stringify(params.value)) {
      params.value = { ...newParams }
      content.value = newParams.content || { text: '', html: '', media_url: '' }
    }
  },
  { deep: true }
)

// 初始化默认值
onMounted(() => {
  // 确保content存在
  if (!params.value.content) {
    params.value.content = { text: '', html: '', media_url: '' }
    content.value = params.value.content
  }
})
</script>

<style lang="less" scoped>
.show-ending-config {
  .config-section {
    margin-bottom: 24px;

    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: rgba(255, 255, 255, 0.7);
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .form-group {
      margin-bottom: 16px;

      label {
        display: block;
        font-size: 13px;
        color: rgba(255, 255, 255, 0.7);
        margin-bottom: 6px;

        &.required::after {
          content: '*';
          color: #e74c3c;
          margin-left: 4px;
        }
      }

      .custom-textarea {
        width: 100%;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 4px;
        color: #fff;
        padding: 8px 10px;
        transition: border-color 0.2s;
        font-size: 13px;
        resize: vertical;
        min-height: 120px;

        &:focus {
          border-color: #ca93f2;
          outline: none;
        }

        &::placeholder {
          color: rgba(255, 255, 255, 0.3);
        }
      }

      .field-hint {
        margin-top: 4px;
        font-size: 12px;
        color: rgba(255, 255, 255, 0.4);
      }
    }
  }
}
</style>
