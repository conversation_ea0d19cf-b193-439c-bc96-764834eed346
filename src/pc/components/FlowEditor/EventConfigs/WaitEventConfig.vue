<template>
  <div class="wait-event-config">
    <div class="config-section">
      <div class="section-title">等待事件配置</div>

      <div class="form-group">
        <label class="required">等待时间 (seconds)</label>
        <input
          type="number"
          v-model.number="params.seconds"
          min="0.1"
          step="0.1"
          class="custom-input"
          required
          placeholder="设置等待时间..."
        />
        <div class="field-hint">游戏流程将暂停指定的秒数后继续</div>
      </div>

      <div class="form-group">
        <div class="checkbox-control">
          <input type="checkbox" id="showLoadingIndicator" v-model="params.showIndicator" />
          <label for="showLoadingIndicator">显示加载指示器</label>
        </div>
        <div class="field-hint">启用后，等待过程中将显示加载动画</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'

interface Props {
  params: {
    seconds: number
    showIndicator?: boolean
  }
}

// 属性定义
const props = withDefaults(defineProps<Props>(), {
  params: () => ({
    seconds: 1,
    showIndicator: false
  })
})

// 事件定义
const emit = defineEmits<{
  update: [updatedParams: any]
}>()

// 本地状态
const params = ref({ ...props.params })

// 监听本地状态变化并向父组件发送更新
watch(
  params,
  (newParams) => {
    emit('update', newParams)
  },
  { deep: true }
)

// 监听props变化来更新本地状态
watch(
  () => props.params,
  (newParams) => {
    // 避免循环更新
    if (JSON.stringify(newParams) !== JSON.stringify(params.value)) {
      params.value = { ...newParams }
    }
  },
  { deep: true }
)

// 初始化默认值
onMounted(() => {
  // 确保所有需要的字段都存在
  if (params.value.seconds === undefined) {
    params.value.seconds = 1
  }
  if (params.value.showIndicator === undefined) {
    params.value.showIndicator = false
  }
})
</script>

<style lang="less" scoped>
.wait-event-config {
  .config-section {
    margin-bottom: 24px;

    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: rgba(255, 255, 255, 0.7);
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .form-group {
      margin-bottom: 16px;

      label {
        display: block;
        font-size: 13px;
        color: rgba(255, 255, 255, 0.7);
        margin-bottom: 6px;

        &.required::after {
          content: '*';
          color: #e74c3c;
          margin-left: 4px;
        }
      }

      .custom-input {
        width: 100%;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 4px;
        color: #fff;
        padding: 8px 10px;
        transition: border-color 0.2s;
        font-size: 13px;

        &:focus {
          border-color: #ca93f2;
          outline: none;
        }
      }

      .checkbox-control {
        display: flex;
        align-items: center;
        gap: 8px;

        input[type='checkbox'] {
          -webkit-appearance: none;
          appearance: none;
          width: 18px;
          height: 18px;
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 4px;
          background: rgba(255, 255, 255, 0.05);
          display: inline-block;
          position: relative;
          margin: 0;

          &:checked {
            background: #ca93f2;
            border-color: #ca93f2;

            &::after {
              content: '';
              position: absolute;
              width: 4px;
              height: 8px;
              border: solid white;
              border-width: 0 2px 2px 0;
              top: 3px;
              left: 6px;
              transform: rotate(45deg);
            }
          }
        }

        label {
          display: inline;
          margin: 0;
        }
      }

      .field-hint {
        margin-top: 4px;
        font-size: 12px;
        color: rgba(255, 255, 255, 0.4);
      }
    }
  }
}
</style>
