<template>
  <div class="show-overlay-config">
    <div class="config-section">
      <div class="section-title">浮层配置</div>

      <div class="form-group">
        <label class="required">按钮文本 (button.text)</label>
        <input
          type="text"
          v-model="button.text"
          class="custom-input"
          placeholder="请输入按钮文本"
          required
          @input="updateButton"
        />
      </div>

      <div class="form-group">
        <label class="required">浮层文本 (overlay.text)</label>
        <textarea
          v-model="overlay.text"
          class="custom-textarea"
          rows="3"
          placeholder="请输入浮层文本"
          required
          @input="updateOverlay"
        ></textarea>
      </div>

      <div class="form-group">
        <label class="required">按钮动作 (button.action)</label>
        <input
          type="text"
          v-model="button.action"
          class="custom-input"
          placeholder="请输入按钮动作"
          required
          @input="updateButton"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'

interface Button {
  text: string
  action: string
}

interface Overlay {
  text: string
}

interface Props {
  params: {
    button?: Button
    overlay?: Overlay
  }
}

// 属性定义
const props = withDefaults(defineProps<Props>(), {
  params: () => ({
    button: { text: '', action: '' },
    overlay: { text: '' }
  })
})

// 事件定义
const emit = defineEmits<{
  update: [updatedParams: any]
}>()

// 本地状态
const params = ref({ ...props.params })
const button = ref<Button>(props.params.button || { text: '', action: '' })
const overlay = ref<Overlay>(props.params.overlay || { text: '' })

// 更新按钮配置
const updateButton = () => {
  if (!params.value.button) {
    params.value.button = { text: '', action: '' }
  }
  params.value.button = { ...button.value }
  emit('update', params.value)
}

// 更新浮层配置
const updateOverlay = () => {
  if (!params.value.overlay) {
    params.value.overlay = { text: '' }
  }
  params.value.overlay = { ...overlay.value }
  emit('update', params.value)
}

// 监听本地状态变化并向父组件发送更新
watch(
  params,
  (newParams) => {
    emit('update', newParams)
  },
  { deep: true }
)

// 监听props变化来更新本地状态
watch(
  () => props.params,
  (newParams) => {
    // 避免循环更新
    if (JSON.stringify(newParams) !== JSON.stringify(params.value)) {
      params.value = { ...newParams }
      button.value = newParams.button || { text: '', action: '' }
      overlay.value = newParams.overlay || { text: '' }
    }
  },
  { deep: true }
)

// 初始化默认值
onMounted(() => {
  // 确保所有需要的字段都存在
  if (!params.value.button) {
    params.value.button = { text: '', action: '' }
    button.value = params.value.button
  }
  if (!params.value.overlay) {
    params.value.overlay = { text: '' }
    overlay.value = params.value.overlay
  }
})
</script>

<style lang="less" scoped>
.show-overlay-config {
  .config-section {
    margin-bottom: 24px;

    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: rgba(255, 255, 255, 0.7);
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .form-group {
      margin-bottom: 16px;

      label {
        display: block;
        font-size: 13px;
        color: rgba(255, 255, 255, 0.7);
        margin-bottom: 6px;

        &.required::after {
          content: '*';
          color: #e74c3c;
          margin-left: 4px;
        }
      }

      .custom-input {
        width: 100%;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 4px;
        color: #fff;
        padding: 8px 10px;
        transition: border-color 0.2s;
        font-size: 13px;

        &:focus {
          border-color: #ca93f2;
          outline: none;
        }
      }

      .custom-textarea {
        width: 100%;
        min-height: 80px;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 4px;
        color: #fff;
        padding: 8px 10px;
        transition: border-color 0.2s;
        font-size: 13px;
        resize: vertical;

        &:focus {
          border-color: #ca93f2;
          outline: none;
        }
      }

      .field-hint {
        margin-top: 4px;
        font-size: 12px;
        color: rgba(255, 255, 255, 0.4);
      }
    }
  }
}
</style>
