<template>
  <div class="voice-config-event-config">
    <div class="config-section">
      <div class="section-title">音色配置</div>

      <div class="form-group">
        <label>提供商 (provider)</label>
        <div class="provider-selector">
          <select v-model="params.provider" class="custom-select" disabled>
            <option value="minimax">MiniMax</option>
          </select>
          <div class="helper-text">当前仅支持 MiniMax</div>
        </div>
      </div>

      <div class="form-group">
        <label class="required">音色 ID (voice_id)</label>
        <input
          type="text"
          v-model="params.voice_id"
          class="custom-input"
          placeholder="请输入音色 ID"
        />
        <div class="helper-text">输入音色标识符，将应用于后续对话</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'

interface Props {
  params: {
    provider?: string
    voice_id?: string
  }
}

// 属性定义
const props = withDefaults(defineProps<Props>(), {
  params: () => ({
    provider: 'minimax',
    voice_id: ''
  })
})

// 事件定义
const emit = defineEmits<{
  update: [updatedParams: any]
}>()

// 本地状态
const params = ref({ ...props.params })

// 监听本地状态变化并向父组件发送更新
watch(
  params,
  (newParams) => {
    emit('update', newParams)
  },
  { deep: true }
)

// 监听props变化来更新本地状态
watch(
  () => props.params,
  (newParams) => {
    // 避免循环更新
    if (JSON.stringify(newParams) !== JSON.stringify(params.value)) {
      params.value = { ...newParams }
    }
  },
  { deep: true }
)

// 在组件挂载时确保所有需要的字段都存在
onMounted(() => {
  if (params.value.provider === undefined) {
    params.value.provider = 'minimax'
  }
  if (params.value.voice_id === undefined) {
    params.value.voice_id = ''
  }
})
</script>

<style lang="less" scoped>
.voice-config-event-config {
  .config-section {
    margin-bottom: 24px;

    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: rgba(255, 255, 255, 0.7);
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .form-group {
      margin-bottom: 16px;

      label {
        display: block;
        font-size: 13px;
        color: rgba(255, 255, 255, 0.7);
        margin-bottom: 6px;

        &.required::after {
          content: '*';
          color: #e74c3c;
          margin-left: 4px;
        }
      }

      .provider-selector {
        margin-bottom: 8px;
      }

      .helper-text {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.5);
        margin-top: 4px;
      }

      .custom-select {
        width: 100%;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 4px;
        color: rgba(255, 255, 255, 0.7);
        padding: 8px 10px;
        transition: border-color 0.2s;
        font-size: 13px;
        appearance: none;

        &:focus {
          border-color: #ca93f2;
          outline: none;
        }

        &:disabled {
          opacity: 0.7;
          cursor: not-allowed;
          background: rgba(255, 255, 255, 0.03);
        }

        option {
          background-color: #2c2c2c;
        }
      }

      .custom-input {
        width: 100%;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 4px;
        color: #fff;
        padding: 8px 10px;
        transition: border-color 0.2s;
        font-size: 13px;

        &:focus {
          border-color: #ca93f2;
          outline: none;
        }
      }
    }
  }
}
</style> 