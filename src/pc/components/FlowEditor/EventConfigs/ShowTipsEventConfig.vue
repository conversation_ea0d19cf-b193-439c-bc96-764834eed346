<template>
  <div class="show-tips-config">
    <div class="config-section">
      <div class="section-title">提示信息配置</div>

      <div class="form-group">
        <label class="required">提示内容 (content.text)</label>
        <textarea
          v-model="content.text"
          class="custom-textarea"
          rows="3"
          placeholder="请输入提示内容"
          required
          @input="updateContent"
        ></textarea>
      </div>

      <div class="form-group">
        <label>显示位置</label>
        <Select
          v-model="params.position"
          class="custom-select"
          :options="[
            { label: '顶部', value: 'top' },
            { label: '中间', value: 'center' },
            { label: '底部', value: 'bottom' }
          ]"
        ></Select>
      </div>

      <div class="form-group">
        <label>显示时长 (秒)</label>
        <input
          type="number"
          v-model.number="params.duration"
          min="0.5"
          step="0.5"
          class="custom-input"
          placeholder="默认为3秒"
        />
        <div class="field-hint">提示显示的持续时间，0表示不自动消失</div>
      </div>

      <div class="form-group">
        <label>提示样式</label>
        <!-- <select v-model="params.style" class="custom-select">
          <option value="info">信息 (默认)</option>
          <option value="success">成功</option>
          <option value="warning">警告</option>
          <option value="error">错误</option>
        </select> -->
        <Select
          v-model="params.style"
          class="custom-select"
          :options="[
            { label: '信息 (默认)', value: 'info' },
            { label: '成功', value: 'success' },
            { label: '警告', value: 'warning' },
            { label: '错误', value: 'error' }
          ]"
        ></Select>
      </div>

      <div class="form-group">
        <div class="checkbox-control">
          <input type="checkbox" id="showIcon" v-model="params.showIcon" />
          <label for="showIcon">显示图标</label>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import Select from '@/mobile/components/Select.vue'

interface MessageContent {
  text: string
  html?: string
  media_url?: string
}

interface Props {
  params: {
    content?: MessageContent
    position?: string
    duration?: number
    style?: string
    showIcon?: boolean
  }
}

// 属性定义
const props = withDefaults(defineProps<Props>(), {
  params: () => ({
    content: { text: '', html: '', media_url: '' },
    position: 'center',
    duration: 3,
    style: 'info',
    showIcon: true
  })
})

// 事件定义
const emit = defineEmits<{
  update: [updatedParams: any]
}>()

// 本地状态
const params = ref({ ...props.params })
const content = ref<MessageContent>(props.params.content || { text: '', html: '', media_url: '' })

// 更新内容
const updateContent = (event: Event) => {
  const value = (event.target as HTMLTextAreaElement).value
  content.value.text = value
  params.value.content = content.value
  emit('update', params.value)
}

// 监听本地状态变化并向父组件发送更新
watch(
  params,
  (newParams) => {
    emit('update', newParams)
  },
  { deep: true }
)

// 监听props变化来更新本地状态
watch(
  () => props.params,
  (newParams) => {
    // 避免循环更新
    if (JSON.stringify(newParams) !== JSON.stringify(params.value)) {
      params.value = { ...newParams }
      content.value = newParams.content || { text: '', html: '', media_url: '' }
    }
  },
  { deep: true }
)

// 初始化默认值
onMounted(() => {
  // 确保所有需要的字段都存在
  if (params.value.position === undefined) {
    params.value.position = 'center'
  }
  if (params.value.duration === undefined) {
    params.value.duration = 3
  }
  if (params.value.style === undefined) {
    params.value.style = 'info'
  }
  if (params.value.showIcon === undefined) {
    params.value.showIcon = true
  }

  // 确保content存在
  if (!params.value.content) {
    params.value.content = { text: '', html: '', media_url: '' }
    content.value = params.value.content
  }
})
</script>

<style lang="less" scoped>
.show-tips-config {
  .config-section {
    margin-bottom: 24px;

    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: rgba(255, 255, 255, 0.7);
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .form-group {
      margin-bottom: 16px;

      label {
        display: block;
        font-size: 13px;
        color: rgba(255, 255, 255, 0.7);
        margin-bottom: 6px;

        &.required::after {
          content: '*';
          color: #e74c3c;
          margin-left: 4px;
        }
      }

      .custom-input,
      .custom-textarea,
      .custom-select {
        width: 100%;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 4px;
        color: #fff;
        padding: 8px 10px;
        transition: border-color 0.2s;
        font-size: 13px;

        &:focus {
          border-color: #ca93f2;
          outline: none;
        }
      }

      .custom-textarea {
        resize: vertical;
        min-height: 60px;
      }

      .checkbox-control {
        display: flex;
        align-items: center;
        gap: 8px;

        input[type='checkbox'] {
          -webkit-appearance: none;
          appearance: none;
          width: 18px;
          height: 18px;
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 4px;
          background: rgba(255, 255, 255, 0.05);
          display: inline-block;
          position: relative;
          margin: 0;

          &:checked {
            background: #ca93f2;
            border-color: #ca93f2;

            &::after {
              content: '';
              position: absolute;
              width: 4px;
              height: 8px;
              border: solid white;
              border-width: 0 2px 2px 0;
              top: 3px;
              left: 6px;
              transform: rotate(45deg);
            }
          }
        }

        label {
          display: inline;
          margin: 0;
        }
      }

      .field-hint {
        margin-top: 4px;
        font-size: 12px;
        color: rgba(255, 255, 255, 0.4);
      }
    }
  }
}
</style>
