<template>
  <div class="audio-event-config">
    <div class="config-section">
      <div class="section-title">音频配置</div>

      <div class="form-group">
        <label class="required">音频 (url)</label>
        <div class="media-selector-container">
          <input
            type="text"
            v-model="params.url"
            class="custom-input"
            placeholder="输入音频文件URL..."
            required
          />
          <!-- 这里可以集成MediaSelector组件 -->
        </div>
        <div class="field-hint">支持MP3、WAV、OGG格式</div>
      </div>

      <div class="form-group">
        <div class="checkbox-control">
          <input type="checkbox" id="isBgm" v-model="params.is_bgm" />
          <label for="isBgm">作为背景音乐 (is_bgm)</label>
        </div>
        <div class="field-hint">设置为背景音乐后会循环播放且不会阻塞流程</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'

interface Props {
  params: {
    url: string
    is_bgm?: boolean
  }
}

// 属性定义
const props = withDefaults(defineProps<Props>(), {
  params: () => ({
    url: '',
    is_bgm: false
  })
})

// 事件定义
const emit = defineEmits<{
  update: [updatedParams: any]
}>()

// 本地状态
const params = ref({ ...props.params })

// 监听本地状态变化并向父组件发送更新
watch(
  params,
  (newParams) => {
    emit('update', newParams)
  },
  { deep: true }
)

// 监听props变化来更新本地状态
watch(
  () => props.params,
  (newParams) => {
    // 避免循环更新
    if (JSON.stringify(newParams) !== JSON.stringify(params.value)) {
      params.value = { ...newParams }
    }
  },
  { deep: true }
)

// 初始化默认值
onMounted(() => {
  // 确保所有需要的字段都存在
  if (params.value.is_bgm === undefined) {
    params.value.is_bgm = false
  }
})
</script>

<style lang="less" scoped>
.audio-event-config {
  .config-section {
    margin-bottom: 24px;

    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: rgba(255, 255, 255, 0.7);
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .form-group {
      margin-bottom: 16px;

      label {
        display: block;
        font-size: 13px;
        color: rgba(255, 255, 255, 0.7);
        margin-bottom: 6px;

        &.required::after {
          content: '*';
          color: #e74c3c;
          margin-left: 4px;
        }
      }

      .custom-input {
        width: 100%;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 4px;
        color: #fff;
        padding: 8px 10px;
        transition: border-color 0.2s;
        font-size: 13px;

        &:focus {
          border-color: #ca93f2;
          outline: none;
        }
      }

      .checkbox-control {
        display: flex;
        align-items: center;
        gap: 8px;

        input[type='checkbox'] {
          -webkit-appearance: none;
          appearance: none;
          width: 18px;
          height: 18px;
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 4px;
          background: rgba(255, 255, 255, 0.05);
          display: inline-block;
          position: relative;
          margin: 0;

          &:checked {
            background: #ca93f2;
            border-color: #ca93f2;

            &::after {
              content: '';
              position: absolute;
              width: 4px;
              height: 8px;
              border: solid white;
              border-width: 0 2px 2px 0;
              top: 3px;
              left: 6px;
              transform: rotate(45deg);
            }
          }
        }

        label {
          display: inline;
          margin: 0;
        }
      }

      .field-hint {
        margin-top: 4px;
        font-size: 12px;
        color: rgba(255, 255, 255, 0.4);
      }
    }
    
    .media-selector-container {
      margin-bottom: 8px;
    }
  }
}
</style>
