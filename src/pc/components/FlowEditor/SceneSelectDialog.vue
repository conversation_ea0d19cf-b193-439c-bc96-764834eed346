<template>
  <div class="scene-select-dialog" v-if="visible">
    <div class="dialog-overlay" @click="handleCancel"></div>
    <div class="dialog-content">
      <div class="dialog-header">
        <h3>{{ title }}</h3>
        <button class="close-button" @click="handleCancel">
          <svg viewBox="0 0 24 24" class="icon-close">
            <path
              fill="currentColor"
              d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
            />
          </svg>
        </button>
      </div>
      <div class="dialog-body">
        <div class="scene-list">
          <div class="scene-group" v-for="group in sceneGroups" :key="group.groupName">
            <div class="group-header">{{ group.groupName }}</div>
            <div class="group-scenes">
              <div
                v-for="scene in group.scenes"
                :key="scene.id"
                class="scene-item"
                :class="{ selected: selectedSceneId === scene.id }"
                @click="selectScene(scene.id)"
              >
                <div class="scene-icon" :class="getSceneIconClass(scene.id)"></div>
                <div class="scene-name">{{ scene.name }}</div>
              </div>
            </div>
          </div>
          <div class="scene-item none-option" @click="selectScene(null)">
            <div class="scene-icon none-icon"></div>
            <div class="scene-name">无（删除跳转）</div>
          </div>
        </div>
      </div>
      <div class="dialog-footer">
        <button class="cancel-button" @click="handleCancel">取消</button>
        <button class="confirm-button" @click="handleConfirm">确定</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useEditorStore } from '@/store/editor'
import type { Scene } from '@/types/editor'
import { cloneDeep } from 'lodash-es'

const props = defineProps<{
  visible: boolean
  title?: string
  currentSceneId?: string
  initialSelectedSceneId?: string | null
}>()

const emit = defineEmits<{
  (e: 'cancel'): void
  (e: 'confirm', sceneId: string | null): void
}>()

const editorStore = useEditorStore()
const selectedSceneId = ref<string | null>(props.initialSelectedSceneId || null)

// 按场景组分组的场景
const sceneGroups = computed(() => {
  const allScenes = [...editorStore.gameConfig.scenes].filter(
    (scene) => scene.id !== '~' && scene.id !== props.currentSceneId
  )
  const groups = new Map<string, Scene[]>()

  // 将场景按组分类
  allScenes.forEach((scene) => {
    const groupName = scene.scene_group || 'default'
    if (!groups.has(groupName)) {
      groups.set(groupName, [])
    }
    groups.get(groupName)?.push(scene)
  })

  // 将分组结果转换为数组并排序
  return Array.from(groups.entries())
    .sort(([groupNameA], [groupNameB]) => {
      // 默认组始终在最后
      if (groupNameA === 'default') return 1
      if (groupNameB === 'default') return -1
      // 其他组按名称字母顺序排序
      return groupNameA.localeCompare(groupNameB)
    })
    .map(([groupName, scenes]) => ({
      groupName,
      scenes: scenes.sort((a, b) => {
        // _BEGIN_ 场景始终在最前
        if (a.id === '_BEGIN_') return -1
        if (b.id === '_BEGIN_') return 1
        // _END_ 场景始终在最后
        if (a.id === '_END_') return 1
        if (b.id === '_END_') return -1
        // 其他场景保持原有顺序
        return 0
      })
    }))
})

// 选择场景
const selectScene = (sceneId: string | null) => {
  selectedSceneId.value = sceneId
}

// 获取场景图标样式
const getSceneIconClass = (sceneId: string) => {
  if (sceneId === '_BEGIN_') return 'begin-icon'
  if (sceneId === '_END_') return 'end-icon'
  return 'chapter-icon'
}

// 取消选择
const handleCancel = () => {
  emit('cancel')
}

// 确认选择
const handleConfirm = () => {
  emit('confirm', selectedSceneId.value)
}

// 初始化时设置默认选中的场景
onMounted(() => {
  selectedSceneId.value = props.initialSelectedSceneId || null
})
</script>

<style lang="less" scoped>
.scene-select-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dialog-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.dialog-content {
  position: relative;
  width: 500px;
  max-width: 90%;
  max-height: 90vh;
  background-color: #1e1e2e;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.dialog-header {
  padding: 16px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;

  h3 {
    margin: 0;
    color: #fff;
    font-size: 18px;
  }

  .close-button {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.6);
    cursor: pointer;
    padding: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;

    &:hover {
      color: #fff;
      background-color: rgba(255, 255, 255, 0.1);
    }

    .icon-close {
      width: 20px;
      height: 20px;
    }
  }
}

.dialog-body {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  max-height: 60vh;
}

.scene-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.scene-group {
  background-color: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  overflow: hidden;
}

.group-header {
  padding: 10px 16px;
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
  font-weight: 600;
  font-size: 14px;
}

.group-scenes {
  padding: 8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.scene-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: rgba(255, 255, 255, 0.08);
  }

  &.selected {
    background-color: rgba(202, 147, 242, 0.15);
    border: 1px solid rgba(202, 147, 242, 0.3);
  }

  &.none-option {
    margin-top: 8px;
    border: 1px dashed rgba(255, 255, 255, 0.2);
  }
}

.scene-icon {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;

  &.begin-icon {
    background-color: #2ecc71;
    &::after {
      content: 'B';
      color: white;
      font-weight: bold;
    }
  }

  &.end-icon {
    background-color: #e74c3c;
    &::after {
      content: 'E';
      color: white;
      font-weight: bold;
    }
  }

  &.chapter-icon {
    background-color: #3498db;
    &::after {
      content: 'C';
      color: white;
      font-weight: bold;
    }
  }

  &.none-icon {
    background-color: #7f8c8d;
    &::after {
      content: 'X';
      color: white;
      font-weight: bold;
    }
  }
}

.scene-name {
  flex: 1;
  font-size: 14px;
  color: #fff;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dialog-footer {
  padding: 16px 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  button {
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;
  }

  .cancel-button {
    background-color: transparent;
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #fff;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }
  }

  .confirm-button {
    background-color: #7c3aed;
    border: none;
    color: #fff;

    &:hover {
      background-color: #6d28d9;
    }
  }
}
</style>
