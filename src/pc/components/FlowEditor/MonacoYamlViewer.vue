<template>
  <div class="monaco-yaml-viewer" ref="editorContainer"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import loader from '@monaco-editor/loader'

// 添加预热Monaco的静态方法
let monacoPrewarmed = false

const prewarmMonaco = async () => {
  if (monacoPrewarmed) return

  try {
    console.log('预热Monaco编辑器...')
    await loader.init()
    monacoPrewarmed = true
    console.log('Monaco编辑器预热完成')
  } catch (error) {
    console.error('Monaco编辑器预热失败:', error)
  }
}

const props = defineProps<{
  content: string
  readOnly?: boolean
  highlightLines?: number[]
}>()

const editorContainer = ref<HTMLElement | null>(null)
let editor: any = null // monaco.editor.IStandaloneCodeEditor
let decorations: string[] = []
let monaco: any = null

// 初始化编辑器
onMounted(async () => {
  try {
    await initMonaco()
  } catch (error) {
    console.error('Monaco编辑器初始化失败:', error)
    emit('mount-error', error)
  }
})

// 监听内容变化
watch(
  () => props.content,
  async (newContent) => {
    if (editor) {
      editor.setValue(newContent)

      // 更新后重新应用高亮
      if (props.highlightLines && props.highlightLines.length > 0) {
        nextTick(() => {
          highlightLinesFun(props.highlightLines || [])
        })
      }
    }
  }
)

// 监听高亮行变化
watch(
  () => props.highlightLines,
  (newHighlightLines) => {
    if (editor && newHighlightLines) {
      highlightLinesFun(newHighlightLines)
    }
  },
  { deep: true }
)

// 销毁编辑器
onBeforeUnmount(() => {
  if (editor) {
    editor.dispose()
    editor = null
  }
})

// 初始化Monaco编辑器
const initMonaco = async () => {
  if (!editorContainer.value) return

  try {
    // 使用loader动态加载Monaco
    monaco = await loader.init()

    // 注册YAML语言
    monaco.languages.register({ id: 'yaml' })

    // 配置YAML语言特性
    monaco.languages.setMonarchTokensProvider('yaml', {
      defaultToken: '',
      tokenPostfix: '.yaml',

      // 特殊字符
      brackets: [
        { open: '{', close: '}', token: 'delimiter.curly' },
        { open: '[', close: ']', token: 'delimiter.square' },
        { open: '(', close: ')', token: 'delimiter.parenthesis' }
      ],

      // 关键字
      keywords: ['true', 'false', 'null', 'y', 'n', 'yes', 'no', 'on', 'off'],

      // 标记器规则
      tokenizer: {
        root: [
          { include: '@whitespace' },
          { include: '@comment' },

          // 键值对 - 修改正则表达式分组以避免错误
          [/^\s*[A-Za-z_]\w*:( |$)/, 'key'],
          [/\w*\s*:( )/, 'key'],

          // 其他元素
          { include: '@anchor' },
          { include: '@tagHandle' },
          { include: '@flowCollections' },
          { include: '@blockStyle' },

          // 数字 - 简化正则表达式
          [/[+-]?\d+(?:\.\d+)?/, 'number'],

          // 字符串 - 简化正则表达式
          [/'[^']*$/, 'string.invalid'], // 非终止单引号
          [/"[^"]*$/, 'string.invalid'], // 非终止双引号
          [/'/, 'string', '@stringBody'],
          [/"/, 'string', '@dblStringBody']
        ],

        whitespace: [[/\s+/, 'white']],

        comment: [[/#.*$/, 'comment']],

        // 锚点
        anchor: [[/&\S+/, 'tag']],

        // 标签
        tagHandle: [[/\![^ ]*/, 'tag']],

        // 流集合
        flowCollections: [
          [/\[/, 'delimiter.square', '@flowSequence'],
          [/\{/, 'delimiter.curly', '@flowMapping']
        ],

        // 流序列
        flowSequence: [
          [/\]/, 'delimiter.square', '@pop'],
          [/,/, 'delimiter.comma'],
          { include: '@flowCollectionValue' }
        ],

        // 流映射
        flowMapping: [
          [/\}/, 'delimiter.curly', '@pop'],
          [/,/, 'delimiter.comma'],
          { include: '@flowCollectionValue' }
        ],

        // 流集合值
        flowCollectionValue: [
          { include: '@whitespace' },
          { include: '@comment' },
          { include: '@flowCollections' },
          { include: '@stringLiteral' },
          { include: '@numericLiteral' },
          { include: '@booleanLiteral' }
        ],

        // 块样式 - 简化正则表达式
        blockStyle: [[/[|>][-+]?[0-9]*/, 'operators', '@multiString']],

        // 多行字符串 - 简化正则表达式
        multiString: [
          [/^ +.+$/, 'string'],
          [/^ *\S/, '@rematch', '@popall']
        ],

        // 字符串字面量 - 简化正则表达式以避免分组错误
        stringLiteral: [
          [/'[^']*'/, 'string'],
          [/"[^"]*"/, 'string']
        ],

        // 数字字面量 - 简化正则表达式
        numericLiteral: [
          [/0[xX][0-9a-fA-F]+/, 'number.hex'],
          [/[+-]?\.?[0-9]+(?:[eE][+-]?[0-9]+)?/, 'number']
        ],

        // 布尔字面量
        booleanLiteral: [[/\b(true|false|yes|no|y|n|on|off)\b/, 'keyword']],

        // 字符串内容 - 简化正则表达式
        stringBody: [
          [/[^']+/, 'string'],
          [/'/, 'string', '@pop']
        ],

        // 双引号字符串内容 - 简化正则表达式
        dblStringBody: [
          [/[^"]+/, 'string'],
          [/"/, 'string', '@pop']
        ]
      }
    })

    // 创建编辑器
    editor = monaco.editor.create(editorContainer.value, {
      value: props.content,
      language: 'yaml',
      theme: 'vs-dark',
      automaticLayout: true,
      readOnly: props.readOnly !== false,
      minimap: { enabled: true },
      scrollBeyondLastLine: false,
      fontSize: 13,
      lineNumbers: 'on',
      renderLineHighlight: 'all',
      scrollbar: {
        useShadows: false,
        verticalScrollbarSize: 10,
        horizontalScrollbarSize: 10,
        alwaysConsumeMouseWheel: false
      },
      lineNumbersMinChars: 3,
      folding: true,
      renderWhitespace: 'boundary',
      guides: {
        indentation: true
      }
    })

    // 如果有指定的高亮行，应用高亮
    if (props.highlightLines && props.highlightLines.length > 0) {
      highlightLinesFun(props.highlightLines)
    }
  } catch (error) {
    console.error('Monaco编辑器初始化失败:', error)
  }
}

// 高亮指定行
const highlightLinesFun = (lineNumbers: number[]) => {
  if (!editor || !monaco) return

  // 清除现有的高亮装饰
  decorations = editor.deltaDecorations(decorations, [])

  // 创建新的高亮装饰
  const newDecorations = lineNumbers.map((lineNumber) => ({
    range: new monaco.Range(lineNumber, 1, lineNumber, 1),
    options: {
      isWholeLine: true,
      className: 'monaco-highlighted-line',
      glyphMarginClassName: 'monaco-highlighted-glyph',
      linesDecorationsClassName: 'monaco-highlighted-decoration'
    }
  }))

  // 应用装饰
  decorations = editor.deltaDecorations([], newDecorations)

  // 滚动到第一个高亮行
  if (lineNumbers.length > 0) {
    editor.revealLineInCenter(lineNumbers[0])
  }
}

// 导出的事件
const emit = defineEmits(['mount-error'])

// 对外暴露的属性和方法
defineExpose({
  highlightLinesFun,
  scrollToLine: (lineNumber: number) => {
    if (editor) {
      editor.revealLineInCenter(lineNumber)
    }
  },
  getEditor: () => editor,
  prewarmMonaco
})
</script>

<style lang="less">
.monaco-yaml-viewer {
  width: 100%;
  height: 100%;

  .monaco-highlighted-line {
    background-color: rgba(202, 147, 242, 0.15);
    border-left: 3px solid #ca93f2 !important;
  }

  .monaco-highlighted-glyph {
    background-color: #ca93f2;
    width: 4px !important;
  }

  .monaco-highlighted-decoration {
    border-left: 3px solid #ca93f2;
  }

  // 自定义Monaco编辑器主题
  :deep(.monaco-editor) {
    .mtk1 {
      color: #abb2bf;
    } // 默认文本
    .mtk7,
    .mtk8,
    .mtk9 {
      color: #56b6c2;
    } // 数字
    .mtk5,
    .mtk14 {
      color: #98c379;
    } // 字符串
    .mtk3,
    .mtk4,
    .mtk10 {
      color: #c678dd;
    } // 关键字
    .mtk6,
    .mtk12,
    .mtk13 {
      color: #d19a66;
    } // 标签和注释
    .mtk2,
    .mtk11 {
      color: #e06c75;
    } // 分隔符和运算符
    .mtk15,
    .mtk16 {
      color: #61afef;
    } // 字段名和锚点
  }
}
</style>
