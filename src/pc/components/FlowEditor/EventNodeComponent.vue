<template>
  <div class="event-node-container">
    <div class="event-icon">
      <svg viewBox="0 0 24 24" width="16" height="16">
        <path
          fill="currentColor"
          d="M14 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V8l-6-6zm4 18H6V4h7v5h5v11z"
        />
      </svg>
    </div>
    <div class="event-content">
      <div class="event-name">{{ displayName }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { TreeNode } from './types'
import { getEventTypeName } from './utils/eventUtils'

const props = defineProps<{
  node: TreeNode
}>()

// 使用防抖处理节点数据变化
const updateKey = ref(Date.now())

// 使用更高效的watch替代watchEffect，只在必要时更新
watch(
  () => props.node,
  (newNode, oldNode) => {
    // 只在关键属性变化时更新
    if (
      newNode.id !== oldNode?.id ||
      newNode.name !== oldNode?.name ||
      newNode.type !== oldNode?.type
    ) {
      updateKey.value = Date.now()
    }
  },
  { deep: false }
)

// 优化计算属性，减少不必要的计算
const nodeData = computed(() => {
  const { data = {}, name, id, type } = props.node

  return {
    name: data && 'name' in data ? data.name : name || '',
    id: data && 'id' in data ? data.id : id || '',
    type: data && 'type' in data ? data.type : type || '',
    ...(data || {})
  }
})

// 获取显示名称
const displayName = computed(() => {
  return nodeData.value.name || getEventTypeName(props.node.type)
})
</script>

<style scoped>
.event-node-container {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
  padding: 6px 10px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  color: #fff;
}

.event-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  margin-right: 8px;
  flex-shrink: 0;
  color: #ca93f2;
}

.event-content {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  overflow: hidden;
}

.event-name {
  font-size: 12px;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.event-type {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.5);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
