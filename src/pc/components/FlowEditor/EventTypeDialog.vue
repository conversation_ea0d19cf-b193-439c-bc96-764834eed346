<template>
  <div class="event-type-dialog" v-if="visible">
    <div class="dialog-overlay" @click="close"></div>
    <div class="dialog-content">
      <div class="dialog-header">
        <h3>选择事件类型</h3>
        <button class="close-button" @click="close">
          <svg viewBox="0 0 24 24" width="16" height="16">
            <path
              fill="currentColor"
              d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"
            />
          </svg>
        </button>
      </div>
      <div class="dialog-body">
        <div class="event-type-grid">
          <div
            v-for="type in eventTypes"
            :key="type.value"
            class="event-type-card"
            @click="selectEventType(type.value)"
          >
            <div class="type-icon" :style="{ backgroundColor: type.color }">
              <svg viewBox="0 0 24 24" width="24" height="24">
                <path fill="currentColor" :d="type.icon" />
              </svg>
            </div>
            <div class="type-details">
              <div class="type-name">{{ type.label }}</div>
              <div class="type-desc">{{ type.description }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { FlowEventType } from '@/pc/components/FlowEditor/types'
import { getEventTypeIcon, getEventTypeColor } from './utils/eventUtils'

// 事件类型定义
interface EventTypeDefinition {
  value: FlowEventType
  label: string
  icon: string
  color: string
  description: string
}

// 定义属性
const props = defineProps<{
  visible: boolean
}>()

// 定义事件
const emit = defineEmits<{
  select: [type: FlowEventType]
  close: []
}>()

// 定义可用的事件类型
const eventTypes: EventTypeDefinition[] = [
  {
    value: 'message',
    label: '对话事件',
    icon: 'M20,2H4A2,2 0 0,0 2,4V22L6,18H20A2,2 0 0,0 22,16V4A2,2 0 0,0 20,2M6,9H18V11H6M14,14H6V12H14M18,8H6V6H18',
    color: '#3498db',
    description: '添加一条角色对话'
  },
  {
    value: 'wait',
    label: '等待',
    icon: 'M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z',
    color: '#9b59b6',
    description: '等待指定时间后继续'
  },
  {
    value: 'show_tips',
    label: '显示提示',
    icon: 'M11,9H13V7H11M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M11,13H13V17H11V13Z',
    color: '#f39c12',
    description: '在界面上显示提示信息'
  },
  {
    value: 'play_audio',
    label: '播放音频',
    icon: 'M14,3.23V5.29C16.89,6.15 19,8.83 19,12C19,15.17 16.89,17.84 14,18.7V20.77C18,19.86 21,16.28 21,12C21,7.72 18,4.14 14,3.23M16.5,12C16.5,10.23 15.5,8.71 14,7.97V16C15.5,15.29 16.5,13.76 16.5,12M3,9V15H7L12,20V4L7,9H3Z',
    color: '#27ae60',
    description: '播放背景音乐或音效'
  },
  {
    value: 'show_image',
    label: '显示图片',
    icon: 'M20,5A2,2 0 0,1 22,7V17A2,2 0 0,1 20,19H4C2.89,19 2,18.1 2,17V7C2,5.89 2.89,5 4,5H20M5,16H19L14.5,10L11,14.5L8.5,11.5L5,16Z',
    color: '#e74c3c',
    description: '在界面上显示一张图片'
  },
  {
    value: 'play_video',
    label: '播放视频',
    icon: 'M4,4H7L9,2H15L17,4H20A2,2 0 0,1 22,6V18A2,2 0 0,1 20,20H4A2,2 0 0,1 2,18V6A2,2 0 0,1 4,4M12,7A5,5 0 0,0 7,12A5,5 0 0,0 12,17A5,5 0 0,0 17,12A5,5 0 0,0 12,7M12,9A3,3 0 0,1 15,12A3,3 0 0,1 12,15A3,3 0 0,1 9,12A3,3 0 0,1 12,9Z',
    color: '#e74c3c',
    description: '播放视频'
  },
  {
    value: 'show_chat_options',
    label: '对话选项',
    icon: 'M3,9H17V7H3V9M3,13H17V11H3V13M3,17H17V15H3V17M19,17H21V15H19V17M19,7V9H21V7H19M19,13H21V11H19V13Z',
    color: '#2980b9',
    description: '显示可选择的对话选项'
  },
  {
    value: 'scene_transition',
    label: '场景转换',
    icon: 'M8,5.14V19.14L19,12.14L8,5.14Z',
    color: '#16a085',
    description: '切换到其他场景'
  },
  {
    value: 'show_ending',
    label: '显示结局',
    icon: 'M20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4C12.76,4 13.5,4.11 14.2,4.31L15.77,2.74C14.61,2.26 13.34,2 12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12M7.91,10.08L6.5,11.5L11,16L21,6L19.59,4.58L11,13.17L7.91,10.08Z',
    color: '#8e44ad',
    description: '显示游戏结局内容'
  },
  {
    value: 'heart_value',
    label: '好感度',
    icon: 'M12,21.35L10.55,20.03C5.4,15.36 2,12.27 2,8.5C2,5.41 4.42,3 7.5,3C9.24,3 10.91,3.81 12,5.08C13.09,3.81 14.76,3 16.5,3C19.58,3 22,5.41 22,8.5C22,12.27 18.6,15.36 13.45,20.03L12,21.35Z',
    color: '#e74c3c',
    description: '更新角色好感度值'
  },
  {
    value: 'show_overlay',
    label: '显示覆盖层',
    icon: 'M3,3H21V5H3V3M3,7H21V9H3V7M3,11H21V13H3V11M3,15H21V17H3V15M3,19H21V21H3V19Z',
    color: '#f1c40f',
    description: '显示浮层提示信息'
  },
  {
    value: 'animated_images',
    label: '动画图片',
    icon: 'M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z',
    color: '#9b59b6',
    description: '显示一组连续的图片动画'
  },
  {
    value: 'update_task_progress',
    label: '更新任务进度',
    icon: 'M19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3M19,19H5V5H19V19M10,17L6,13L7.41,11.59L10,14.17L16.59,7.58L18,9',
    color: '#27ae60',
    description: '更新游戏任务的完成进度'
  },
  {
    value: 'interactive',
    label: '互动事件',
    icon: 'M7.5,5.6L5,7L6.4,4.5L5,2L7.5,3.4L10,2L8.6,4.5L10,7L7.5,5.6M19.5,15.4L22,14L20.6,16.5L22,19L19.5,17.6L17,19L18.4,16.5L17,14L19.5,15.4M22,2L20.6,4.5L22,7L19.5,5.6L17,7L18.4,4.5L17,2L19.5,3.4L22,2M13.34,12.78L15.78,10.34L13.66,8.22L11.22,10.66L13.34,12.78M14.37,7.29L16.71,9.63C17.1,10 17.1,10.65 16.71,11.04L5.04,22.71C4.65,23.1 4,23.1 3.63,22.71L1.29,20.37C0.9,20 0.9,19.35 1.29,18.96L12.96,7.29C13.35,6.9 14,6.9 14.37,7.29Z',
    color: '#3498db',
    description: '创建游戏中的互动环节'
  },
  {
    value: 'voice_config',
    label: '音色配置',
    icon: 'M12,2C6.48,2 2,6.48 2,12S6.48,22 12,22 22,17.52 22,12 17.52,2 12,2M13,17H11V15H13V17M13,13H11V7H13V13Z',
    color: '#FF5722',
    description: '配置对话的音色设置'
  }
]

// 选择事件类型
const selectEventType = (type: FlowEventType) => {
  emit('select', type)
  close()
}

// 关闭对话框
const close = () => {
  emit('close')
}
</script>

<style lang="less" scoped>
.event-type-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;

  .dialog-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1;
  }

  .dialog-content {
    width: 600px;
    max-width: 90%;
    max-height: 80vh;
    background-color: #1a1a1c;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    z-index: 2;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .dialog-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px;
      background-color: rgba(0, 0, 0, 0.2);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);

      h3 {
        margin: 0;
        color: #fff;
        font-size: 18px;
        font-weight: 600;
      }

      .close-button {
        background: transparent;
        border: none;
        color: rgba(255, 255, 255, 0.6);
        cursor: pointer;
        transition: color 0.2s;

        &:hover {
          color: #fff;
        }
      }
    }

    .dialog-body {
      flex: 1;
      overflow-y: auto;
      padding: 16px;

      .event-type-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;

        .event-type-card {
          display: flex;
          align-items: center;
          padding: 16px;
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.2s;

          &:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
          }

          .type-icon {
            width: 48px;
            height: 48px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            color: #fff;
          }

          .type-details {
            flex: 1;

            .type-name {
              font-weight: 600;
              font-size: 16px;
              color: #fff;
              margin-bottom: 6px;
            }

            .type-desc {
              font-size: 13px;
              color: rgba(255, 255, 255, 0.7);
            }
          }
        }
      }
    }
  }
}
</style>
