<template>
  <div class="chapter-config">
    <!-- 节点基本属性部分 -->
    <div class="panel-section">
      <div class="section-title">节点属性</div>
      <div class="form-item">
        <div class="form-label">节点名称</div>
        <input
          type="text"
          class="custom-input"
          v-model.lazy="localChapter.name"
          placeholder="输入节点名称"
        />
      </div>

      <div class="form-item">
        <div class="form-label">节点ID</div>
        <input
          type="text"
          class="custom-input"
          v-model.lazy="localChapter.id"
          :disabled="localChapter.id === '_BEGIN_' || localChapter.id === '_END_'"
          placeholder="输入节点ID"
          @blur="handleIdChange"
        />
        <div class="form-sublabel">唯一标识符，确保不重复</div>
      </div>

      <div class="form-item" v-if="localChapter.id !== '_BEGIN_' && localChapter.id !== '_END_'">
        <div class="form-label">下一场景ID</div>
        <input
          type="text"
          class="custom-input"
          v-model.lazy="localChapter.next_scene_id"
          placeholder="输入下一场景ID"
        />
        <div class="form-sublabel">留空则默认按序执行下一个场景</div>
      </div>
    </div>

    <!-- 素材配置部分 -->
    <div class="panel-section material-section">
      <div class="section-header">
        <div class="section-title">素材配置</div>
      </div>

      <div class="material-content">
        <div class="plot-list">
          <div v-for="(plot, plotIndex) in getScenePlots()" :key="plotIndex" class="plot-item">
            <div class="plot-header">
              <div class="plot-title">场景描述 {{ plotIndex + 1 }}</div>
              <button
                class="plot-delete-button"
                @click="removePlot(plotIndex)"
                v-if="getScenePlots().length > 1"
              >
                <svg viewBox="0 0 24 24" class="icon-delete" width="16" height="16">
                  <path
                    fill="currentColor"
                    d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
                  />
                </svg>
                <span>删除场景</span>
              </button>
            </div>

            <div class="form-item">
              <div class="form-label">场景描述</div>
              <textarea
                class="custom-textarea"
                :value="plot.sentence"
                @input="
                  updatePlotField(
                    plotIndex,
                    'sentence',
                    ($event.target as HTMLTextAreaElement).value
                  )
                "
                :rows="3"
                placeholder="请输入场景描述"
              ></textarea>
            </div>

            <div class="form-item">
              <div class="form-label">场景位置</div>
              <input
                type="text"
                class="custom-input"
                :value="plot.location"
                @input="
                  updatePlotField(plotIndex, 'location', ($event.target as HTMLInputElement).value)
                "
                placeholder="请输入场景位置"
              />
            </div>

            <div class="character-list">
              <div class="section-title">角色配置</div>
              <div
                v-for="(character, charIndex) in plot.character"
                :key="charIndex"
                class="character-item"
              >
                <div class="form-item">
                  <div class="form-label">角色名称</div>
                  <input
                    type="text"
                    class="custom-input"
                    :value="character.name"
                    @input="
                      updateCharacter(
                        plotIndex,
                        charIndex,
                        'name',
                        ($event.target as HTMLInputElement).value
                      )
                    "
                    placeholder="请输入角色名称"
                  />
                </div>
                <div class="form-item">
                  <div class="form-label">Lora ID</div>
                  <input
                    type="text"
                    class="custom-input"
                    :value="character.lora_id"
                    @input="
                      updateCharacter(
                        plotIndex,
                        charIndex,
                        'lora_id',
                        ($event.target as HTMLInputElement).value
                      )
                    "
                    placeholder="请输入Lora ID"
                  />
                </div>
                <div class="form-item">
                  <div class="form-label">服装</div>
                  <input
                    type="text"
                    class="custom-input"
                    :value="character.clothes"
                    @input="
                      updateCharacter(
                        plotIndex,
                        charIndex,
                        'clothes',
                        ($event.target as HTMLInputElement).value
                      )
                    "
                    placeholder="请输入服装描述"
                  />
                </div>
                <button
                  class="character-delete-button"
                  @click="removeCharacter(plotIndex, charIndex)"
                  v-if="plot.character.length > 1"
                >
                  <svg viewBox="0 0 24 24" class="icon-delete" width="16" height="16">
                    <path
                      fill="currentColor"
                      d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
                    />
                  </svg>
                  <span>删除角色</span>
                </button>
              </div>
              <button class="add-character-button" @click="addCharacter(plotIndex)">
                <svg viewBox="0 0 24 24" width="16" height="16">
                  <path fill="currentColor" d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
                </svg>
                <span>添加角色</span>
              </button>
            </div>
          </div>
          <button class="add-plot-button" @click="addPlot()">
            <svg viewBox="0 0 24 24" width="16" height="16">
              <path fill="currentColor" d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
            </svg>
            <span>添加场景描述</span>
          </button>
        </div>
      </div>
    </div>

    <!-- 事件列表部分 -->
    <div class="panel-section events-section">
      <div class="section-header">
        <div class="section-title">事件列表</div>
        <button class="add-event-button" @click="handleAddEvent">
          <svg viewBox="0 0 24 24" class="icon-add">
            <path fill="currentColor" d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
          </svg>
          添加事件
        </button>
      </div>

      <!-- 事件列表 -->
      <div class="events-list">
        <draggable
          v-model="localEvents"
          item-key="id"
          handle=".drag-handle"
          ghost-class="ghost-event"
          @change="handleDragChange"
        >
          <template #item="{ element: event, index }">
            <div
              class="event-item"
              :class="{
                expanded: expandedEvents.includes(event.id),
                selected: currentSelectedEventId === event.id
              }"
            >
              <div class="event-header">
                <div class="drag-handle" title="拖动排序">
                  <svg viewBox="0 0 24 24" class="icon-drag" width="16" height="16">
                    <path
                      fill="currentColor"
                      d="M7,19V17H9V19H7M11,19V17H13V19H11M15,19V17H17V19H15M7,15V13H9V15H7M11,15V13H13V15H11M15,15V13H17V15H15M7,11V9H9V11H7M11,11V9H13V11H11M15,11V9H17V11H15M7,7V5H9V7H7M11,7V5H13V7H11M15,7V5H17V7H15Z"
                    />
                  </svg>
                </div>
                <div class="event-main" @click="toggleEvent(event.id)">
                  <div
                    class="event-icon"
                    :style="{ backgroundColor: getEventTypeColor(event.type) }"
                  >
                    <svg viewBox="0 0 24 24" class="icon">
                      <path fill="currentColor" :d="getEventTypeIcon(event.type)" />
                    </svg>
                  </div>
                  <div class="event-name">
                    {{ getEventTypeName(event.type) || '未命名事件' }}
                  </div>
                </div>
                <div class="event-controls">
                  <button
                    class="control-button move-up-button"
                    @click.stop="moveEventUp(index)"
                    :disabled="index === 0"
                    :class="{ disabled: index === 0 }"
                    title="上移"
                  >
                    <svg viewBox="0 0 24 24" class="icon-up" width="16" height="16">
                      <path
                        fill="currentColor"
                        d="M7.41,15.41L12,10.83L16.59,15.41L18,14L12,8L6,14L7.41,15.41Z"
                      />
                    </svg>
                  </button>
                  <button
                    class="control-button move-down-button"
                    @click.stop="moveEventDown(index)"
                    :disabled="index === localEvents.length - 1"
                    :class="{ disabled: index === localEvents.length - 1 }"
                    title="下移"
                  >
                    <svg viewBox="0 0 24 24" class="icon-down" width="16" height="16">
                      <path
                        fill="currentColor"
                        d="M7.41,8.58L12,13.17L16.59,8.58L18,10L12,16L6,10L7.41,8.58Z"
                      />
                    </svg>
                  </button>
                  <button
                    class="control-button delete-button"
                    @click.stop="confirmDeleteEvent(event, index)"
                    title="删除"
                  >
                    <svg viewBox="0 0 24 24" class="icon-delete">
                      <path
                        fill="currentColor"
                        d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"
                      />
                    </svg>
                  </button>
                  <svg
                    viewBox="0 0 24 24"
                    class="icon-expand"
                    :class="{ expanded: expandedEvents.includes(event.id) }"
                  >
                    <path fill="currentColor" d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z" />
                  </svg>
                </div>
              </div>

              <!-- 展开的事件详情 -->
              <div class="event-details" v-if="expandedEvents.includes(event.id)">
                <div class="event-summary">
                  <!-- 动态加载事件配置组件 -->
                  <component
                    :is="getEventConfigComponent(event.type)"
                    v-if="getEventConfigComponent(event.type)"
                    :params="event.plot"
                    :availableNodes="availableNodes"
                    @update="(params: any) => updateEventParams(event, params)"
                  ></component>

                  <!-- 当没有对应配置组件时，显示简单摘要 -->
                  <div v-else>
                    <div v-if="event.type === 'message'" class="event-content">
                      {{ summarizeEvent(event) }}
                    </div>
                    <div v-else-if="event.type === 'wait'" class="event-content">
                      等待 {{ event.plot?.seconds || 1 }} 秒
                    </div>
                    <div v-else-if="event.type === 'scene_transition'" class="event-content">
                      跳转到场景: {{ getSceneTransitionTarget(event) }}
                    </div>
                    <div
                      v-else-if="(event.type as string) === 'voice_config'"
                      class="event-content"
                    >
                      {{ summarizeEvent(event) }}
                    </div>
                    <div v-else class="event-content">{{ event.type }} 事件</div>
                  </div>

                  <!-- 保存按钮 -->
                  <div class="event-actions" v-if="getEventConfigComponent(event.type)">
                    <button class="save-button" @click="saveEvent(event)">
                      <svg viewBox="0 0 24 24" class="icon-save" width="16" height="16">
                        <path
                          fill="currentColor"
                          d="M15,9H5V5H15M12,19A3,3 0 0,1 9,16A3,3 0 0,1 12,13A3,3 0 0,1 15,16A3,3 0 0,1 12,19M17,3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V7L17,3Z"
                        />
                      </svg>
                      保存事件
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </draggable>

        <div v-if="!chapterEvents || chapterEvents.length === 0" class="empty-events">
          <div class="empty-tip">
            <svg viewBox="0 0 24 24" class="icon-info">
              <path
                fill="currentColor"
                d="M11,9H13V7H11M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M11,17H13V11H11V17Z"
              />
            </svg>
            <span>暂无事件，点击"添加事件"按钮创建</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 删除确认对话框 -->
    <ConfirmDialog
      v-model:visible="showDeleteConfirm"
      title="删除事件"
      content="确定要删除此事件吗？此操作无法撤销。"
      confirmText="删除"
      cancelText="取消"
      :confirmButtonStyle="{ background: '#e74c3c', borderColor: '#e74c3c' }"
      @confirm="executeDeleteEvent"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import type { Scene, GameEvent, GameEventType, Plot, Character } from '@/types/editor'
import { useEditorStore } from '@/store/editor'
import ConfirmDialog from '@/components/ConfirmDialog.vue'
import { cloneDeep } from 'lodash-es'
import draggable from 'vuedraggable'

// 导入事件配置组件
import MessageEventConfig from '@/pc/components/FlowEditor/EventConfigs/MessageEventConfig.vue'
import WaitEventConfig from '@/pc/components/FlowEditor/EventConfigs/WaitEventConfig.vue'
import ShowTipsEventConfig from '@/pc/components/FlowEditor/EventConfigs/ShowTipsEventConfig.vue'
import ShowImageEventConfig from '@/pc/components/FlowEditor/EventConfigs/ShowImageEventConfig.vue'
import PlayVideoEventConfig from '@/pc/components/FlowEditor/EventConfigs/PlayVideoEventConfig.vue'
import AudioEventConfig from '@/pc/components/FlowEditor/EventConfigs/AudioEventConfig.vue'
import ChatOptionsEventConfig from '@/pc/components/FlowEditor/EventConfigs/ChatOptionsEventConfig.vue'
import SceneTransitionEventConfig from '@/pc/components/FlowEditor/EventConfigs/SceneTransitionEventConfig.vue'
import HeartValueEventConfig from '@/pc/components/FlowEditor/EventConfigs/HeartValueEventConfig.vue'
import ShowEndingEventConfig from '@/pc/components/FlowEditor/EventConfigs/ShowEndingEventConfig.vue'
import AnimatedImagesEventConfig from '@/pc/components/FlowEditor/EventConfigs/AnimatedImagesEventConfig.vue'
import InteractiveEventConfig from '@/pc/components/FlowEditor/EventConfigs/InteractiveEventConfig.vue'
import UpdateTaskProgressEventConfig from '@/pc/components/FlowEditor/EventConfigs/UpdateTaskProgressEventConfig.vue'
import ShowOverlayEventConfig from '@/pc/components/FlowEditor/EventConfigs/ShowOverlayEventConfig.vue'
import VoiceConfigEventConfig from '@/pc/components/FlowEditor/EventConfigs/VoiceConfigEventConfig.vue'

// 接收的属性
const props = defineProps<{
  scene?: Scene | null
  availableNodes?: { id: string; name: string; type: string }[]
  selectedEventId?: string
}>()

// 需要发出的事件
const emit = defineEmits<{
  'update-scene': [sceneData: Partial<Scene>]
  'add-event': []
  'select-event': [event: GameEvent]
  'delete-event': [event: GameEvent, index: number]
}>()

// 编辑器存储
const editorStore = useEditorStore()

// 本地状态
const localChapter = ref<Partial<Scene>>({})
const expandedEvents = ref<string[]>([])
const isUpdating = ref(false)
const currentSelectedEventId = ref('')
const showDeleteConfirm = ref(false)
const pendingDeleteEvent = ref<{ event: GameEvent | null; index: number }>({
  event: null,
  index: -1
})

// 章节的事件列表
const chapterEvents = computed(() => {
  const events = props.scene?.events || localChapter.value?.events || []

  // 检查是否存在互动事件，如果存在则添加到列表
  const interactiveEvents: GameEvent[] = []
  if (props.scene?.action_handlers && props.scene.action_handlers.length > 0) {
    interactiveEvents.push({
      id: `${props.scene.id}_interactive`,
      type: 'interactive' as GameEventType,
      plot: props.scene.action_handlers[0].params as any
    })
  } else if (localChapter.value?.action_handlers && localChapter.value.action_handlers.length > 0) {
    interactiveEvents.push({
      id: `${localChapter.value.id}_interactive`,
      type: 'interactive' as GameEventType,
      plot: localChapter.value.action_handlers[0].params as any
    })
  }

  // 返回所有事件，包括互动事件
  return [...events, ...interactiveEvents]
})

// 本地事件列表，用于拖拽排序
const localEvents = ref<GameEvent[]>([])

// 监听 chapterEvents 变化，更新本地事件列表
watch(
  chapterEvents,
  (newEvents) => {
    if (newEvents) {
      localEvents.value = [...newEvents]
    }
  },
  { immediate: true }
)

// 监听props变化，更新本地状态
watch(
  () => props.scene,
  (newScene) => {
    if (newScene) {
      // 比较新旧值，只在真正变化时更新
      const newValue = JSON.stringify(newScene)
      const currentValue = JSON.stringify(localChapter.value)
      if (newValue !== currentValue && !isUpdating.value) {
        // 创建深拷贝断开引用关系
        localChapter.value = JSON.parse(JSON.stringify(newScene))
      }
    } else {
      localChapter.value = {}
    }
  },
  { immediate: true, deep: true }
)

// 监听selectedEventId变化，自动展开新增事件
watch(
  () => props.selectedEventId,
  (newEventId) => {
    if (newEventId && !expandedEvents.value.includes(newEventId)) {
      // 自动展开新增事件
      expandedEvents.value.push(newEventId)
      // 设置选中状态
      currentSelectedEventId.value = newEventId
    }
  },
  { immediate: true }
)

// 本地编辑时，防抖处理更新
const debouncedUpdateScene = (() => {
  let timer: ReturnType<typeof setTimeout> | null = null
  let lastValue: string | null = null

  return (value: Partial<Scene>) => {
    if (timer) clearTimeout(timer)
    if (isUpdating.value) return

    // 比较新旧值，只在真正变化时触发更新
    const currentValue = JSON.stringify(value)
    if (currentValue === lastValue) {
      return
    }

    timer = setTimeout(() => {
      isUpdating.value = true
      lastValue = currentValue
      emit('update-scene', value)
      setTimeout(() => {
        isUpdating.value = false
      }, 100)
    }, 300)
  }
})()

// 处理 ID 变化
const handleIdChange = () => {
  if (!isUpdating.value && props.scene && props.scene.id !== localChapter.value.id) {
    console.log('检测到 ID 变化，从', props.scene.id, '到', localChapter.value.id)

    // 使用深拷贝确保对象引用变化
    const updatedValue = JSON.parse(JSON.stringify(localChapter.value))

    // 触发一个特殊的更新事件，包含旧 ID 和新 ID
    emit('update-scene', {
      ...updatedValue,
      _oldId: props.scene.id // 添加旧 ID 信息
    })
  }
}

// 监听本地章节变化，触发更新
watch(
  localChapter,
  (value) => {
    if (!isUpdating.value) {
      console.log('章节变化，触发更新:', JSON.stringify(value))

      // 使用深拷贝确保对象引用变化
      const updatedValue = JSON.parse(JSON.stringify(value))

      // 正常更新（不包含 ID 变化）
      if (props.scene && props.scene.id === value.id) {
        debouncedUpdateScene(updatedValue)
      }
      // ID 变化由 handleIdChange 函数处理，这里不处理
    }
  },
  { deep: true }
)

// 获取事件类型的显示名称
const getEventTypeName = (eventType?: GameEventType) => {
  if (!eventType) return '事件'
  const eventTypeMap: Record<string, string> = {
    message: '对话事件',
    wait: '等待事件',
    show_tips: '提示事件',
    show_overlay: '覆盖层事件',
    show_image: '图片事件',
    animated_images: '动画事件',
    play_video: '视频事件',
    play_audio: '音频事件',
    show_chat_options: '对话选项',
    update_user_coins: '金币事件',
    update_task_progress: '任务事件',
    scene_transition: '场景转换',
    heart_value: '好感度事件',
    interactive: '互动事件',
    show_ending: '结局事件',
    voice_config: '音色配置'
  }
  return eventTypeMap[eventType] || '未知事件类型'
}

// 获取事件类型的颜色
const getEventTypeColor = (eventType?: GameEventType) => {
  if (!eventType) return '#607d8b'

  const colorMap: Record<string, string> = {
    message: '#3498db',
    wait: '#607d8b',
    show_tips: '#9b59b6',
    show_overlay: '#34495e',
    show_image: '#2ecc71',
    animated_images: '#1abc9c',
    play_video: '#e74c3c',
    play_audio: '#f39c12',
    show_chat_options: '#d35400',
    update_user_coins: '#f1c40f',
    update_task_progress: '#27ae60',
    scene_transition: '#8e44ad',
    heart_value: '#e91e63',
    interactive: '#009688',
    show_ending: '#ff5722',
    voice_config: '#FF5722'
  }

  return colorMap[eventType] || '#607d8b'
}

// 获取事件类型的图标
const getEventTypeIcon = (eventType?: GameEventType) => {
  if (!eventType)
    return 'M20,2H4C2.9,2 2,2.9 2,4V22L6,18H20C21.1,18 22,17.1 22,16V4C22,2.9 21.1,2 20,2M6,9H18V11H6M14,14H6V12H14M18,8H6V6H18'

  const iconMap: Record<string, string> = {
    message:
      'M20,2H4C2.9,2 2,2.9 2,4V22L6,18H20C21.1,18 22,17.1 22,16V4C22,2.9 21.1,2 20,2M6,9H18V11H6M14,14H6V12H14M18,8H6V6H18',
    wait: 'M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z',
    show_tips:
      'M13,9H11V7H13M13,17H11V11H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z',
    show_image:
      'M19,19H5V5H19M19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3M13.96,12.29L11.21,15.83L9.25,13.47L6.5,17H17.5L13.96,12.29Z',
    play_video:
      'M12,2C6.48,2 2,6.48 2,12C2,17.52 6.48,22 12,22C17.52,22 22,17.52 22,12C22,6.48 17.52,2 12,2M10,16.5V7.5L16,12L10,16.5Z',
    play_audio:
      'M12,3V12.26C11.5,12.09 11,12 10.5,12C8,12 6,14 6,16.5C6,19 8,21 10.5,21C13,21 15,19 15,16.5V6H19V3H12Z',
    show_chat_options:
      'M20,2H4A2,2 0 0,0 2,4V22L6,18H20A2,2 0 0,0 22,16V4A2,2 0 0,0 20,2M11,17H9V15H11V17M14,13C13.45,13 13,12.55 13,12C13,11.45 13.45,11 14,11C14.55,11 15,11.45 15,12C15,12.55 14.55,13 14,13M17,9H7V7H17V9Z',
    scene_transition: 'M14,16V22L24,12L14,2V8H0V16H14Z',
    heart_value:
      'M12,21.35L10.55,20.03C5.4,15.36 2,12.27 2,8.5C2,5.41 4.42,3 7.5,3C9.24,3 10.91,3.81 12,5.08C13.09,3.81 14.76,3 16.5,3C19.58,3 22,5.41 22,8.5C22,12.27 18.6,15.36 13.45,20.03L12,21.35Z',
    interactive:
      'M7.5,5.6L5,7L6.4,4.5L5,2L7.5,3.4L10,2L8.6,4.5L10,7L7.5,5.6M19.5,15.4L22,14L20.6,16.5L22,19L19.5,17.6L17,19L18.4,16.5L17,14L19.5,15.4M22,2L20.6,4.5L22,7L19.5,5.6L17,7L18.4,4.5L17,2L19.5,3.4L22,2M13.34,12.78L15.78,10.34L13.66,8.22L11.22,10.66L13.34,12.78M14.37,7.29L16.71,9.63C17.1,10 17.1,10.65 16.71,11.04L5.04,22.71C4.65,23.1 4,23.1 3.63,22.71L1.29,20.37C0.9,20 0.9,19.35 1.29,18.96L12.96,7.29C13.35,6.9 14,6.9 14.37,7.29Z',
    show_ending: 'M2,5V19H8V5H2M9,5V10H15V5H9M16,5V14H22V5H16M9,11V19H15V11H9M16,15V19H22V15H16Z',
    show_overlay: 'M3,3H21V5H3V3M3,7H21V9H3V7M3,11H21V13H3V11M3,15H21V17H3V15M3,19H21V21H3V19Z',
    animated_images:
      'M8.5,13.5L11,16.5L14.5,12L19,18H5M21,19V5C21,3.89 20.1,3 19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19Z',
    update_task_progress:
      'M19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3M19,19H5V5H19V19M10,17L6,13L7.41,11.59L10,14.17L16.59,7.58L18,9',
    voice_config:
      'M12,2C6.48,2 2,6.48 2,12S6.48,22 12,22 22,17.52 22,12 17.52,2 12,2M13,17H11V15H13V17M13,13H11V7H13V13Z'
  }

  return (
    iconMap[eventType] ||
    'M3,3H21V21H3V3M7,7V9H9V7H7M11,7V9H13V7H11M15,7V9H17V7H15M7,11V13H9V11H7M11,11V13H13V11H11M15,11V13H17V11H15M7,15V17H9V15H7M11,15V17H13V15H11M15,15V17H17V15H15Z'
  )
}

// 事件摘要
const summarizeEvent = (event: GameEvent): string => {
  if (!event || !event.plot) return '无详细信息'

  if (event.type === 'message') {
    // 处理不同格式的消息内容
    if (typeof event.plot.content === 'object' && event.plot.content?.text) {
      return event.plot.content.text
    } else if (typeof event.plot.content === 'string') {
      return event.plot.content
    } else {
      return '无文本内容'
    }
  } else if (event.type === 'wait') {
    return `等待 ${event.plot.seconds || 1} 秒`
  } else if (event.type === 'scene_transition') {
    return `跳转到: ${getSceneTransitionTarget(event)}`
  } else if ((event.type as string) === 'voice_config') {
    return `音色: ${(event.plot as any).provider || 'minimax'} - ${
      (event.plot as any).voice_id || '未设置'
    }`
  }

  return '事件详情'
}

// 获取场景转换目标ID
const getSceneTransitionTarget = (event: GameEvent): string => {
  if (!event || !event.plot) return '未指定'

  // 对于场景转换事件，尝试从各种可能的位置获取目标ID
  if (event.type === 'scene_transition') {
    // 尝试访问不同可能的属性路径
    const target =
      (event.plot as any).target_scene_id ||
      (event.plot.scene_transition && (event.plot.scene_transition as any).target_id) ||
      '未指定'
    return target
  }

  return '未指定'
}

// 切换事件的展开/折叠状态
const toggleEvent = (eventId: string) => {
  const index = expandedEvents.value.indexOf(eventId)
  if (index > -1) {
    expandedEvents.value.splice(index, 1)
  } else {
    expandedEvents.value.push(eventId)
  }
}

// 添加事件
const handleAddEvent = () => {
  // 触发父组件的add-event事件，弹出事件类型选择对话框
  emit('add-event')
}

// 选择事件进行编辑 - 通过事件头部点击触发
// 此方法已通过toggleEvent方法间接调用

// 确认删除事件
const confirmDeleteEvent = (event: GameEvent, index: number) => {
  pendingDeleteEvent.value = { event, index }
  showDeleteConfirm.value = true
}

// 执行删除事件
const executeDeleteEvent = () => {
  if (pendingDeleteEvent.value.event) {
    const event = pendingDeleteEvent.value.event

    // 检查是否是互动事件
    if (event.type === 'interactive' && event.id.endsWith('_interactive')) {
      // 删除互动事件，清空 action_handlers
      if (localChapter.value.action_handlers) {
        localChapter.value.action_handlers = []

        // 触发场景更新
        debouncedUpdateScene(JSON.parse(JSON.stringify(localChapter.value)))
      }
    } else {
      // 删除常规事件
      const eventIndex = localEvents.value.findIndex((e) => e.id === event.id)
      emit('delete-event', event, eventIndex)
    }

    pendingDeleteEvent.value = { event: null, index: -1 }
  }
  showDeleteConfirm.value = false
}

// 映射事件类型到配置组件
const getEventConfigComponent = (eventType?: GameEventType) => {
  if (!eventType) return null
  const componentsMap: Record<string, any> = {
    message: MessageEventConfig,
    wait: WaitEventConfig,
    show_tips: ShowTipsEventConfig,
    show_overlay: ShowOverlayEventConfig,
    show_image: ShowImageEventConfig,
    animated_images: AnimatedImagesEventConfig,
    play_video: PlayVideoEventConfig,
    play_audio: AudioEventConfig,
    show_chat_options: ChatOptionsEventConfig,
    scene_transition: SceneTransitionEventConfig,
    heart_value: HeartValueEventConfig,
    show_ending: ShowEndingEventConfig,
    interactive: InteractiveEventConfig,
    update_task_progress: UpdateTaskProgressEventConfig,
    voice_config: VoiceConfigEventConfig
  }
  return componentsMap[eventType] || null
}

// 更新事件参数
const updateEventParams = (event: GameEvent, params: any) => {
  if (!event) return

  // 检查是否是互动事件
  if (event.type === 'interactive' && event.id.endsWith('_interactive')) {
    // 互动事件的更新逻辑
    if (!localChapter.value.action_handlers) {
      localChapter.value.action_handlers = [
        {
          type: 'ScoreLimitWithLLMChatV2',
          params: {
            level: '',
            background: '',
            heart_key: '',
            heart_value: 0,
            clean_history: false,
            limit_chat_count: 3,
            agree_sentences: [],
            streamer_tpl: ''
          }
        }
      ]
    }

    // 更新action_handlers参数
    localChapter.value.action_handlers[0] = {
      type: 'ScoreLimitWithLLMChatV2',
      params: JSON.parse(JSON.stringify(params))
    }

    // 触发场景更新
    debouncedUpdateScene(JSON.parse(JSON.stringify(localChapter.value)))
    return
  }

  // 常规事件的更新逻辑
  if (!localChapter.value.events) return

  // 查找事件在数组中的索引
  const eventIndex = localChapter.value.events.findIndex((e) => e.id === event.id)
  if (eventIndex === -1) return

  // 更新事件的plot属性 - 使用深拷贝断开引用
  const updatedEvents = [...localChapter.value.events]
  updatedEvents[eventIndex] = {
    ...updatedEvents[eventIndex],
    plot: JSON.parse(JSON.stringify(params))
  }

  // 更新本地章节对象
  localChapter.value = {
    ...localChapter.value,
    events: updatedEvents
  }

  // 触发场景更新 - 使用深拷贝确保对象引用变化
  debouncedUpdateScene(JSON.parse(JSON.stringify(localChapter.value)))
}

// 保存事件
const saveEvent = (event: GameEvent) => {
  // 检查是否是互动事件
  if (event.type === 'interactive' && event.id.endsWith('_interactive')) {
    // 互动事件已通过 updateEventParams 方法保存，此处只需显示提示
    // 显示保存成功提示
    alert('互动事件已保存！')
    return
  }

  // 常规事件的保存
  // 显示保存成功提示
  alert('事件已保存！')
}

// 处理拖拽排序变化
const handleDragChange = (evt: any) => {
  if (evt.moved) {
    // 如果是普通事件的移动，需要更新 localChapter
    const { element } = evt.moved

    // 检查是否是互动事件
    if (element.type === 'interactive' && element.id.endsWith('_interactive')) {
      // 互动事件不能移动，恢复原状
      localEvents.value = [...chapterEvents.value]
      return
    }

    // 更新本地章节的事件顺序
    if (localChapter.value.events) {
      // 找出非互动事件
      const regularEvents = localEvents.value.filter(
        (e) => !(e.type === 'interactive' && e.id.endsWith('_interactive'))
      )

      // 更新本地章节的事件
      localChapter.value.events = [...regularEvents]

      // 触发场景更新
      debouncedUpdateScene(JSON.parse(JSON.stringify(localChapter.value)))
    }
  }
}

// 上移事件
const moveEventUp = (index: number) => {
  if (index <= 0) return

  const event = localEvents.value[index]

  // 检查是否是互动事件
  if (event.type === 'interactive' && event.id.endsWith('_interactive')) {
    // 互动事件不能移动
    return
  }

  // 交换位置
  const temp = localEvents.value[index - 1]
  localEvents.value[index - 1] = event
  localEvents.value[index] = temp

  // 更新本地章节的事件顺序
  if (localChapter.value.events) {
    // 找出非互动事件
    const regularEvents = localEvents.value.filter(
      (e) => !(e.type === 'interactive' && e.id.endsWith('_interactive'))
    )

    // 更新本地章节的事件
    localChapter.value.events = [...regularEvents]

    // 触发场景更新
    debouncedUpdateScene(JSON.parse(JSON.stringify(localChapter.value)))
  }
}

// 下移事件
const moveEventDown = (index: number) => {
  if (index >= localEvents.value.length - 1) return

  const event = localEvents.value[index]

  // 检查是否是互动事件
  if (event.type === 'interactive' && event.id.endsWith('_interactive')) {
    // 互动事件不能移动
    return
  }

  // 交换位置
  const temp = localEvents.value[index + 1]
  localEvents.value[index + 1] = event
  localEvents.value[index] = temp

  // 更新本地章节的事件顺序
  if (localChapter.value.events) {
    // 找出非互动事件
    const regularEvents = localEvents.value.filter(
      (e) => !(e.type === 'interactive' && e.id.endsWith('_interactive'))
    )

    // 更新本地章节的事件
    localChapter.value.events = [...regularEvents]

    // 触发场景更新
    debouncedUpdateScene(JSON.parse(JSON.stringify(localChapter.value)))
  }
}

// 素材配置相关方法
// 获取场景的素材配置
const getScenePlots = (): Plot[] => {
  if (!localChapter.value.story_generation_params?.plots) {
    return [
      {
        sentence: '',
        character: [],
        location: ''
      }
    ]
  }
  return localChapter.value.story_generation_params.plots
}

// 直接更新编辑器存储中的场景
const updateEditorScene = (updatedScene: Scene) => {
  // 先更新本地状态
  localChapter.value = cloneDeep(updatedScene)
  // 然后直接更新编辑器存储
  editorStore.updateScene(updatedScene)
  // 标记为已修改
  editorStore.isModified = true
}

// 更新素材配置字段
const updatePlotField = (
  plotIndex: number,
  field: Exclude<keyof Plot, 'character'>,
  value: string
) => {
  if (!localChapter.value || !props.scene) return

  const updatedScene = cloneDeep(props.scene)
  if (!updatedScene.story_generation_params) {
    updatedScene.story_generation_params = {
      plots: [
        {
          sentence: '',
          character: [],
          location: ''
        }
      ]
    }
  }

  if (!updatedScene.story_generation_params.plots[plotIndex]) {
    updatedScene.story_generation_params.plots[plotIndex] = {
      sentence: '',
      character: [],
      location: ''
    }
  }

  updatedScene.story_generation_params.plots[plotIndex][field] = value
  updateEditorScene(updatedScene)
}

// 更新角色信息
const updateCharacter = (
  plotIndex: number,
  charIndex: number,
  key: keyof Character,
  value: string
) => {
  if (!localChapter.value || !props.scene) return

  const updatedScene = cloneDeep(props.scene)
  if (!updatedScene.story_generation_params) {
    updatedScene.story_generation_params = {
      plots: [
        {
          sentence: '',
          character: [],
          location: ''
        }
      ]
    }
  }

  if (!updatedScene.story_generation_params.plots[plotIndex]) {
    updatedScene.story_generation_params.plots[plotIndex] = {
      sentence: '',
      character: [],
      location: ''
    }
  }

  if (!updatedScene.story_generation_params.plots[plotIndex].character[charIndex]) {
    updatedScene.story_generation_params.plots[plotIndex].character[charIndex] = {
      name: '',
      lora_id: '',
      clothes: ''
    }
  }

  updatedScene.story_generation_params.plots[plotIndex].character[charIndex][key] = value
  updateEditorScene(updatedScene)
}

// 添加角色
const addCharacter = (plotIndex: number) => {
  if (!localChapter.value || !props.scene) return

  const updatedScene = cloneDeep(props.scene)
  if (!updatedScene.story_generation_params) {
    updatedScene.story_generation_params = {
      plots: [
        {
          sentence: '',
          character: [],
          location: ''
        }
      ]
    }
  }

  if (!updatedScene.story_generation_params.plots[plotIndex]) {
    updatedScene.story_generation_params.plots[plotIndex] = {
      sentence: '',
      character: [],
      location: ''
    }
  }

  updatedScene.story_generation_params.plots[plotIndex].character.push({
    name: '',
    lora_id: '',
    clothes: ''
  })

  updateEditorScene(updatedScene)
}

// 删除角色
const removeCharacter = (plotIndex: number, charIndex: number) => {
  if (!props.scene?.story_generation_params?.plots[plotIndex]?.character) return

  const updatedScene = cloneDeep(props.scene)
  updatedScene.story_generation_params.plots[plotIndex].character.splice(charIndex, 1)
  updateEditorScene(updatedScene)
}

// 添加场景描述
const addPlot = () => {
  if (!localChapter.value || !props.scene) return

  const updatedScene = cloneDeep(props.scene)
  if (!updatedScene.story_generation_params) {
    updatedScene.story_generation_params = {
      plots: []
    }
  }

  updatedScene.story_generation_params.plots.push({
    sentence: '',
    character: [],
    location: ''
  })

  updateEditorScene(updatedScene)
}

// 删除场景描述
const removePlot = (plotIndex: number) => {
  if (!props.scene?.story_generation_params?.plots) return

  const updatedScene = cloneDeep(props.scene)
  updatedScene.story_generation_params.plots.splice(plotIndex, 1)
  updateEditorScene(updatedScene)
}
</script>

<style lang="less" scoped>
.chapter-config {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  color: rgba(255, 255, 255, 0.85);
  font-size: 14px;

  .panel-section {
    margin-bottom: 20px;
    padding: 0 0 8px 0;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 0 12px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .section-title {
    font-size: 15px;
    font-weight: 500;
    margin: 0 0 12px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    color: #ca93f2;
  }

  .events-section,
  .material-section {
    .section-title {
      margin: 0;
      padding-bottom: 0;
      border-bottom: none;
    }
  }

  .form-item {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .form-label {
    margin-bottom: 6px;
    font-size: 13px;
    color: rgba(255, 255, 255, 0.7);
  }

  .form-sublabel {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.5);
    margin-top: 2px;
  }

  .custom-input {
    width: 100%;
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    color: rgba(255, 255, 255, 0.9);
    padding: 8px 10px;
    font-size: 13px;
    transition: all 0.2s;

    &:focus {
      border-color: #ca93f2;
      outline: none;
      box-shadow: 0 0 0 2px rgba(202, 147, 242, 0.2);
    }

    &::placeholder {
      color: rgba(255, 255, 255, 0.3);
    }
  }

  .events-section {
    .add-event-button {
      display: flex;
      align-items: center;
      gap: 4px;
      background: rgba(46, 204, 113, 0.15);
      border: 1px solid rgba(46, 204, 113, 0.3);
      color: #2ecc71;
      border-radius: 4px;
      padding: 6px 10px;
      font-size: 13px;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        background: rgba(46, 204, 113, 0.25);
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }

      &:active {
        transform: translateY(0);
      }

      .icon-add {
        width: 16px;
        height: 16px;
      }
    }
  }

  .event-item {
    margin-bottom: 8px;
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    overflow: hidden;
    transition: all 0.3s ease;

    &.expanded {
      background: rgba(0, 0, 0, 0.3);
    }

    &.selected {
      border-color: #ca93f2;
      background: rgba(202, 147, 242, 0.07);
      box-shadow: 0 0 0 1px rgba(202, 147, 242, 0.3);
    }
  }

  .ghost-event {
    opacity: 0.5;
    background: #3498db;
    border: 1px dashed #3498db;
  }

  .event-header {
    display: flex;
    align-items: center;
    padding: 10px 12px;
    transition: background-color 0.2s;

    &:hover {
      background: rgba(255, 255, 255, 0.05);
    }

    .drag-handle {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      margin-right: 6px;
      cursor: move;
      color: rgba(255, 255, 255, 0.5);
      border-radius: 4px;

      &:hover {
        background: rgba(255, 255, 255, 0.1);
        color: rgba(255, 255, 255, 0.8);
      }
    }

    .event-main {
      display: flex;
      align-items: center;
      flex: 1;
      cursor: pointer;
    }
  }

  .event-icon {
    width: 26px;
    height: 26px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    flex-shrink: 0;

    .icon {
      width: 16px;
      height: 16px;
      color: white;
    }
  }

  .event-name {
    flex: 1;
    font-weight: 500;
    font-size: 13px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .event-controls {
    display: flex;
    align-items: center;
    gap: 2px;
    margin-left: 8px;

    .control-button {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 26px;
      height: 26px;
      border: none;
      background: transparent;
      border-radius: 4px;
      cursor: pointer;
      color: rgba(255, 255, 255, 0.6);
      padding: 0;

      &:hover {
        background: rgba(255, 255, 255, 0.1);
        color: rgba(255, 255, 255, 0.9);
      }

      &.disabled {
        opacity: 0.3;
        cursor: not-allowed;

        &:hover {
          background: transparent;
          color: rgba(255, 255, 255, 0.6);
        }
      }
    }

    .move-up-button,
    .move-down-button {
      color: #3498db;
      &:hover {
        background: rgba(52, 152, 219, 0.2);
        color: #3498db;
      }
    }

    .delete-button {
      color: #e74c3c;
      &:hover {
        background: rgba(231, 76, 60, 0.2);
        color: #e74c3c;
      }
    }

    .icon-edit,
    .icon-delete {
      width: 16px;
      height: 16px;
    }

    .icon-expand {
      width: 20px;
      height: 20px;
      transition: transform 0.3s ease;
      opacity: 0.5;

      &.expanded {
        transform: rotate(180deg);
        opacity: 0.8;
      }
    }
  }

  .event-details {
    padding: 12px 14px 16px;
    background: rgba(0, 0, 0, 0.15);
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    margin-top: -1px;
  }

  .event-summary {
    font-size: 13px;
    color: rgba(255, 255, 255, 0.7);
    word-break: break-word;

    :deep(.form-item) {
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    :deep(.form-label) {
      color: rgba(255, 255, 255, 0.85);
      font-size: 13px;
      margin-bottom: 4px;
    }

    :deep(input),
    :deep(select),
    :deep(textarea) {
      width: 100%;
      background: rgba(0, 0, 0, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 4px;
      color: rgba(255, 255, 255, 0.9);
      padding: 6px 8px;
      font-size: 13px;

      &:focus {
        border-color: #ca93f2;
        outline: none;
      }
    }

    :deep(.button),
    :deep(button) {
      background: rgba(52, 152, 219, 0.2);
      border: 1px solid rgba(52, 152, 219, 0.4);
      color: #3498db;
      border-radius: 4px;
      padding: 5px 10px;
      font-size: 12px;
      cursor: pointer;

      &:hover {
        background: rgba(52, 152, 219, 0.3);
      }
    }
  }

  .event-actions {
    margin-top: 16px;
    text-align: right;

    .save-button {
      display: inline-flex;
      align-items: center;
      gap: 6px;
      background: rgba(46, 204, 113, 0.2);
      border: 1px solid rgba(46, 204, 113, 0.4);
      color: #2ecc71;
      border-radius: 4px;
      padding: 6px 12px;
      font-size: 13px;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        background: rgba(46, 204, 113, 0.3);
        transform: translateY(-1px);
      }

      .icon-save {
        opacity: 0.9;
      }
    }
  }

  .empty-events {
    padding: 24px 0;
    text-align: center;
  }

  .empty-tip {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: rgba(255, 255, 255, 0.5);
    font-size: 13px;

    .icon-info {
      width: 20px;
      height: 20px;
      opacity: 0.7;
    }
  }

  /* 素材配置样式 */
  .material-content {
    .plot-list {
      .plot-item {
        margin-bottom: 20px;
        padding: 16px;
        border-radius: 8px;
        background-color: rgba(255, 255, 255, 0.03);
      }

      .plot-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        .plot-title {
          font-size: 14px;
          font-weight: 500;
          color: #fff;
        }

        .plot-delete-button {
          display: flex;
          align-items: center;
          gap: 4px;
          padding: 4px 8px;
          background-color: rgba(255, 59, 48, 0.1);
          border: none;
          border-radius: 4px;
          color: #ff3b30;
          font-size: 12px;
          cursor: pointer;
          transition: background-color 0.2s;

          &:hover {
            background-color: rgba(255, 59, 48, 0.2);
          }
        }
      }
    }

    .custom-textarea {
      width: 100%;
      padding: 10px 16px;
      border: 1px solid rgba(184, 196, 255, 0.1);
      background: rgba(204, 213, 255, 0.05);
      color: #fff;
      border-radius: 8px;
      font-size: 14px;
      outline: none;
      transition: all 0.3s ease;
      resize: vertical;
      min-height: 80px;

      &:hover,
      &:focus {
        border-color: #7c3aed;
        background: rgba(204, 213, 255, 0.08);
      }
    }

    .character-list {
      margin-top: 20px;

      .section-title {
        font-size: 14px;
        font-weight: 500;
        color: #fff;
        margin-bottom: 16px;
        border-bottom: none;
        padding-bottom: 0;
      }

      .character-item {
        margin-bottom: 16px;
        padding: 16px;
        border-radius: 8px;
        background-color: rgba(255, 255, 255, 0.05);
        position: relative;
      }

      .character-delete-button {
        position: absolute;
        top: 8px;
        right: 8px;
        display: flex;
        align-items: center;
        gap: 4px;
        padding: 4px 8px;
        background-color: rgba(255, 59, 48, 0.1);
        border: none;
        border-radius: 4px;
        color: #ff3b30;
        font-size: 12px;
        cursor: pointer;
        transition: background-color 0.2s;

        &:hover {
          background-color: rgba(255, 59, 48, 0.2);
        }
      }
    }

    .add-character-button,
    .add-plot-button {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      width: 100%;
      padding: 10px;
      background-color: rgba(255, 255, 255, 0.05);
      border: 1px dashed rgba(255, 255, 255, 0.2);
      border-radius: 8px;
      color: rgba(255, 255, 255, 0.8);
      font-size: 14px;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.3);
        color: #fff;
      }
    }
  }
}
</style>
