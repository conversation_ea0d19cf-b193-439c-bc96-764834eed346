<template>
  <Teleport to="body">
    <Transition name="fade">
      <div v-if="visible" class="pc-confirm-dialog-overlay" @click.stop="handleOverlayClick">
        <div class="pc-confirm-dialog" @click.stop>
          <button class="close-button" @click="() => emit('update:visible', false)">
            <IconClose />
          </button>
          <div v-if="showIcon" class="dialog-icon">
            <slot name="icon">
              <div class="default-icon">
                <IconConfirmDefault />
              </div>
            </slot>
          </div>
          <div class="dialog-title" :style="titleStyle">
            <slot>{{ title }}</slot>
          </div>
          <div v-if="content || $slots.content" class="dialog-content" :style="contentStyle">
            <slot name="content">{{ content }}</slot>
          </div>
          <div v-if="$slots.input" class="dialog-input">
            <slot name="input"></slot>
          </div>
          <div class="dialog-actions">
            <button
              v-if="showCancel"
              class="cancel-button"
              :style="cancelButtonStyle"
              @click="handleCancel"
            >
              {{ cancelText }}
            </button>
            <button class="confirm-button" :style="confirmButtonStyle" @click="handleConfirm">
              {{ confirmText }}
            </button>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import IconConfirmDefault from '@/assets/icon/confirm-default.svg'
import IconClose from '@/assets/icon/close.svg'
import { defineProps, defineEmits, withDefaults } from 'vue'

interface Props {
  visible: boolean
  title?: string
  content?: string
  cancelText?: string
  confirmText?: string
  closeOnClickOverlay?: boolean
  showCancel?: boolean
  showIcon?: boolean
  titleStyle?: Record<string, string>
  contentStyle?: Record<string, string>
  confirmButtonStyle?: Record<string, string>
  cancelButtonStyle?: Record<string, string>
  onBeforeConfirm?: () => boolean | Promise<boolean>
}

const props = withDefaults(defineProps<Props>(), {
  showCancel: true,
  showIcon: true,
  cancelText: 'Cancel',
  confirmText: 'Confirm',
  closeOnClickOverlay: true,
  cancelButtonStyle: () => ({}),
  confirmButtonStyle: () => ({})
})

const emit = defineEmits<{
  'update:visible': [value: boolean]
  confirm: []
  cancel: []
}>()

const handleConfirm = async () => {
  try {
    // 如果有验证函数，先执行验证
    if (props.onBeforeConfirm) {
      const isValid = await props.onBeforeConfirm()
      if (!isValid) {
        return
      }
    }
    // 验证通过或没有验证函数，触发确认事件
    emit('confirm')
    emit('update:visible', false)
  } catch (error) {
    console.error('Validation error:', error)
  }
}

const handleCancel = () => {
  emit('cancel')
  emit('update:visible', false)
}

const handleOverlayClick = () => {
  if (props.closeOnClickOverlay) {
    handleCancel()
  }
}
</script>

<style lang="less" scoped>
@import '@/assets/style/theme.less';

.pc-confirm-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.pc-confirm-dialog {
  width: calc(100% - 32px);
  max-width: 400px;
  background: linear-gradient(180deg, #2b1b3b 0%, #1a0f24 100%);
  border-radius: 16px;
  padding: 32px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  color: white;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  animation: zoomIn 0.3s ease;
  position: relative;

  .close-button {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: white;
    transition: all 0.2s ease;
    z-index: 10;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      transform: scale(1.05);
    }

    &:active {
      transform: scale(0.95);
    }

    // 亮色主题适配
    body.light-theme & {
      background: rgba(0, 0, 0, 0.05);
      color: #424242;
      border: 1px solid #e0e0e0;

      &:hover {
        background: rgba(0, 0, 0, 0.1);
      }
    }
  }

  // 亮色主题适配
  body.light-theme & {
    background: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    box-shadow: 0 10px 30px var(--shadow-color);
  }

  .dialog-content {
    width: 100%;
    text-align: center;
    color: rgba(255, 255, 255, 0.9);
    font-size: 16px;
    line-height: 1.6;

    // 亮色主题适配
    body.light-theme & {
      color: #424242;
    }
  }

  .dialog-input {
    width: 100%;
    margin: 0 0 8px;

    :deep(input) {
      width: 100%;
      height: 42px;
      border-radius: 40px;
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      color: white;
      font-size: 15px;
      padding: 10px 16px;
      outline: none;
      transition: all 0.3s ease;

      &:focus {
        border-color: #ca93f2;
        background: rgba(255, 255, 255, 0.15);
      }

      &::placeholder {
        color: rgba(255, 255, 255, 0.5);
      }

      // 亮色主题适配
      body.light-theme & {
        background: var(--bg-tertiary);
        border: 1px solid var(--border-color);
        color: var(--text-primary);

        &:focus {
          border-color: var(--accent-color);
          background: var(--bg-secondary);
        }

        &::placeholder {
          color: var(--text-tertiary);
        }
      }
    }

    :deep(.error-message) {
      color: #ff4d4f;
      font-size: 12px;
      margin-top: 4px;
      text-align: left;

      // 亮色主题适配
      body.light-theme & {
        color: #f5222d;
      }
    }
  }
}

.dialog-icon {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;

  .default-icon {
    width: 60px;
    height: 60px;
  }

  // 亮色主题适配
  body.light-theme & {
    .default-icon {
      filter: hue-rotate(20deg) brightness(0.9);
    }
  }
}

.dialog-title {
  color: #ca93f2;
  text-align: center;
  font-size: 20px;
  font-weight: 700;

  // 亮色主题适配
  body.light-theme & {
    color: #5e35b1;
  }
}

.dialog-actions {
  width: 100%;
  display: flex;
  gap: 16px;
  margin-top: 8px;

  button {
    flex: 1;
    height: 48px;
    border-radius: 24px;
    font-size: 16px;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-2px);
    }

    &:active {
      transform: translateY(0);
      opacity: 0.9;
    }
  }

  .cancel-button {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.2);

    // 亮色主题适配
    body.light-theme & {
      background: rgba(0, 0, 0, 0.05);
      color: #424242;
      border: 1px solid #e0e0e0;
    }
  }

  .confirm-button {
    background: linear-gradient(90deg, #ca93f2 0%, #9b6cc8 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(202, 147, 242, 0.3);

    // 亮色主题适配
    body.light-theme & {
      background: linear-gradient(90deg, #7e57c2 0%, #5e35b1 100%);
      color: white;
      box-shadow: 0 4px 12px rgba(94, 53, 177, 0.2);
    }
  }
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
</style>
