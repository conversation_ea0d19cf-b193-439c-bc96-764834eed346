<template>
  <div class="icon-picker">
    <a-button class="icon-button" @click="showDrawer = true">
      <template #icon>
        <component :is="currentIcon" />
      </template>
      选择图标
    </a-button>

    <BaseDrawer v-model:visible="showDrawer" height="auto">
      <div class="icon-grid">
        <div
          v-for="icon in icons"
          :key="icon.name"
          class="icon-item"
          @click="selectIcon(icon.name)"
        >
          <component :is="icon.component" />
        </div>
      </div>
    </BaseDrawer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import * as Icons from '@arco-design/web-vue/es/icon'
import BaseDrawer from '@/mobile/components/BaseDrawer.vue'

const props = defineProps<{
  modelValue: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
}>()

const showDrawer = ref(false)

const icons = Object.entries(Icons).map(([name, component]) => ({
  name,
  component
}))

const currentIcon = computed(() => {
  return Icons[props.modelValue as keyof typeof Icons] || Icons.IconQuestion
})

const selectIcon = (iconName: string) => {
  emit('update:modelValue', iconName)
  showDrawer.value = false
}
</script>

<style lang="less" scoped>
.icon-picker {
  .icon-button {
    width: 100%;
    height: 42px;
    border-radius: 40px;
    background: rgba(204, 213, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: #fff;
    font-size: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;

    &:hover,
    &:focus {
      border-color: #ca93f2;
      background: rgba(204, 213, 255, 0.08);
    }

    .arco-icon {
      font-size: 20px;
      color: #ca93f2;
    }
  }
}

.icon-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 1px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  overflow: hidden;

  .icon-item {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #1f0038;
    font-size: 24px;
    color: #ca93f2;
    cursor: pointer;

    &:active {
      background: rgba(204, 213, 255, 0.05);
    }
  }
}
</style>
