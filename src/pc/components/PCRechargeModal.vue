<template>
  <PCModal v-model="modalVisible" title="Diamond" @close="handleClose">
    <div class="recharge-content">
      <!-- 标签页导航 -->
      <div class="tabs">
        <div
          class="tab-item"
          :class="{ active: activeTab === 'purchase' }"
          @click="activeTab = 'purchase'"
        >
          Purchase
        </div>
        <div
          class="tab-item"
          :class="{ active: activeTab === 'details' }"
          @click="activeTab = 'details'"
        >
          Details
        </div>
      </div>

      <!-- Purchase 标签页内容 -->
      <div v-if="activeTab === 'purchase'" class="tab-content">
        <div class="price-list">
          <!-- 显示实际价格项目（数据加载后） -->
          <template v-if="rechargeStore.priceList && rechargeStore.priceList.length > 0">
            <div
              v-for="item in rechargeStore.priceList"
              :key="item.id"
              class="price-item"
              :class="{ 'is-selected': selectedPrice?.id === item.id }"
              @click="selectPrice(item)"
            >
              <img :src="item.extra?.background_url" :alt="item.name" class="background-image" />
              <div class="price-info">
                <div class="info-content">
                  <div class="amount">${{ (item.amount / 100).toFixed(2) }}</div>
                  <div class="coins">
                    <img
                      class="diamond-icon"
                      src="https://cdn.magiclight.ai/assets/mobile/diamond.png"
                      alt="diamond"
                    />
                    {{ item.coins }}
                  </div>
                </div>
                <div v-if="item.extra?.discount_percent" class="discount">
                  {{ item.extra.discount_percent }}% OFF
                </div>
              </div>
            </div>
          </template>

          <!-- 加载中或无数据时显示骨架屏 -->
          <template v-else>
            <div v-for="i in 3" :key="`skeleton-${i}`" class="price-item skeleton-item">
              <div class="skeleton-image"></div>
              <div class="price-info">
                <div class="info-content">
                  <div class="amount skeleton-text"></div>
                  <div class="coins skeleton-text"></div>
                </div>
                <div class="discount skeleton-discount" v-if="i === 1"></div>
              </div>
            </div>
          </template>
        </div>

        <div class="purchase-button-container">
          <button
            class="purchase-button"
            :disabled="!selectedPrice || loading"
            @click="handlePayment(selectedPrice!)"
          >
            Purchase Now
          </button>
        </div>
      </div>

      <!-- Details 标签页内容 -->
      <div v-else-if="activeTab === 'details'" class="tab-content details-tab">
        <div class="details-content">
          <div class="history-list">
            <div v-if="historyLoading" class="loading-state">
              <a-spin />
              <p>Loading history...</p>
            </div>

            <div v-else-if="historyList.length === 0" class="empty-state">
              <p>No diamond history available yet.</p>
            </div>

            <template v-else>
              <div v-for="item in historyList" :key="item.action_time" class="history-card">
                <div class="history-card-left">
                  <div class="history-info">
                    <div class="history-type">{{ formatActionType(item.action_type) }}</div>
                    <div class="history-time">{{ formatTime(item.action_time) }}</div>
                  </div>
                </div>
                <div
                  class="history-amount"
                  :class="{
                    positive: item.coin_delta > 0,
                    neutral: item.coin_delta === 0,
                    negative: item.coin_delta < 0
                  }"
                >
                  {{ item.coin_delta > 0 ? '+' : '' }}{{ item.coin_delta }}
                </div>
              </div>

              <div class="no-more">No more history</div>
            </template>
          </div>
        </div>
      </div>
    </div>
  </PCModal>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { createStripeCheckout } from '@/utils/stripe-loader'
import { Message } from '@/mobile/components/Message'
import { useUserStore } from '@/store/user'
import { useRechargeStore } from '@/store/recharge'
import { reportEvent } from '@/utils'
import { ReportEvent } from '@/interface'
import type { PriceItem } from '@/api/payment'
import { useRoute } from 'vue-router'
import PCModal from '@/pc/components/PCModal.vue'
import { getCoinHistory, type CoinHistoryItem } from '@/api/coins'
import dayjs from 'dayjs'
import { Spin } from '@arco-design/web-vue'

// 定义emit事件
const emit = defineEmits<{
  (e: 'need-login'): void
}>()

const userStore = useUserStore()
const rechargeStore = useRechargeStore()
const route = useRoute()

const currentURL = `${window.location.origin}${route.fullPath}`
const cancelURL = currentURL

const loading = ref(false)
const selectedPrice = ref<PriceItem | null>(null)
const activeTab = ref('purchase') // 默认显示 Purchase 标签页

// 钻石消耗历史记录相关
const historyList = ref<CoinHistoryItem[]>([])
const historyLoading = ref(false)

// 格式化时间
const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm')
}

// 格式化操作类型
const formatActionType = (type: string) => {
  const typeMap: Record<string, string> = {
    signup: 'Sign Up Bonus',
    purchase: 'Purchase',
    chat: 'Chat Consumption',
    story: 'Story Creation',
    refund: 'Refund',
    discord: 'Discord Sign Up Bonus',
    google: 'Google Sign Up Bonus',
    apple: 'Apple Sign Up Bonus',
    daily_checkin: 'Daily Check-in',
    task_reward: 'Task Reward'
  }
  return (
    typeMap[type] ||
    type
      .split('_')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ')
  )
}

// 加载钻石消耗历史记录
const loadCoinHistory = async () => {
  if (!userStore.isAuthenticated || userStore.isGuest) return

  try {
    historyLoading.value = true
    const { data: response } = await getCoinHistory()
    if (response.code === '0') {
      historyList.value = response.data
    }
  } catch (error) {
    console.error('Failed to load coin history:', error)
  } finally {
    historyLoading.value = false
  }
}

// 使用计算属性连接 rechargeStore.visible 和 PCModal 的 v-model
const modalVisible = computed({
  get: () => rechargeStore.visible,
  set: (value: boolean) => {
    if (!value) {
      handleClose()
    }
  }
})

// Stripe 相关函数已移至 stripe-loader.ts 统一管理

const selectPrice = (price: PriceItem) => {
  selectedPrice.value = price
  reportEvent(ReportEvent.ClickRechargeButton, {
    userId: userStore.userInfo?.uuid,
    path: route.fullPath,
    priceId: price.id,
    amount: price.amount,
    coins: price.coins
  })
}

const handlePayment = async (price: PriceItem) => {
  if (loading.value || !price) return
  if (userStore.isGuest) {
    modalVisible.value = false
    emit('need-login')
    return
  }
  loading.value = true

  // Facebook Pixel tracking
  if (window.fbq) {
    window.fbq('track', 'AddToCart')
    window.fbq('track', 'InitiateCheckout')
  }

  reportEvent(ReportEvent.ClickPaymentButton, {
    userId: userStore.userInfo?.uuid,
    amount: price.amount,
    coins: price.coins
  })

  // 显示准备支付会话的消息
  Message.loading('Preparing payment session...', 10000)

  try {
    // 使用优化后的 Stripe 工具
    await createStripeCheckout({
      priceId: price.id,
      successUrl: `${window.location.origin}/payment/stripe-callback?amount=${price.coins}`,
      cancelUrl: cancelURL
    })

    // 注意：这里不需要关闭 loading，因为页面会跳转
  } catch (err: any) {
    // 处理所有错误
    console.error('Payment error:', err)
    Message.clear()
    Message.error(err.message || 'Payment failed, please try again later')
  } finally {
    // 确保在组件内部的 loading 状态被重置
    loading.value = false
  }
}

const handleClose = () => {
  selectedPrice.value = null
  loading.value = false
  activeTab.value = 'purchase' // 重置为默认标签页
  rechargeStore.hideRechargeModal()
}

// 监听弹窗显示状态
watch(
  () => rechargeStore.visible,
  (visible) => {
    if (visible) {
      loading.value = false
      selectedPrice.value = null
      reportEvent(ReportEvent.ShowRechargeModal, {
        userId: userStore.userInfo?.uuid,
        path: route.fullPath
      })
      rechargeStore.fetchPriceList()
    }
  }
)

// 监听标签页切换
watch(
  () => activeTab.value,
  (tab) => {
    if (tab === 'details') {
      loadCoinHistory()
    }
  }
)

onMounted(() => {
  loading.value = false
  selectedPrice.value = null
})
</script>

<style lang="less" scoped>
@import '@/assets/style/theme.less';

.recharge-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.tabs {
  display: flex;
  margin-bottom: 24px;
}

.tab-item {
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 500;
  color: var(--text-secondary);
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;

  &:hover {
    color: var(--text-primary);
  }

  &.active {
    color: var(--accent-color);
    font-weight: 600;

    &::after {
      content: '';
      position: absolute;
      bottom: -1px;
      left: 0;
      width: 100%;
      height: 2px;
      background-color: var(--accent-color);
    }
  }
}

.tab-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.price-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.price-item {
  position: relative;
  height: 100px;
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
  display: flex;
  background: var(--bg-tertiary);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
  }

  &.is-selected {
    border-color: #ca93f2;
    box-shadow: 0 0 0 2px rgba(202, 147, 242, 0.3);
  }

  .background-image {
    width: 120px;
    height: 100%;
    object-fit: cover;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
  }

  .price-info {
    position: relative;
    z-index: 2;
    padding: 16px;
    padding-left: 140px; /* 为左侧图片留出空间 */
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 100%;

    .info-content {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .amount {
      font-size: 24px;
      font-weight: 600;
      color: var(--text-primary);
    }

    .coins {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 18px;
      font-weight: 700;
      color: var(--coins-color, #daff96);

      .diamond-icon {
        width: 24px;
        height: 24px;
      }
    }

    .discount {
      position: absolute;
      top: 12px;
      right: 12px;
      background: #e064ff;
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 600;
      color: white;
    }
  }

  // Skeleton styles
  &.skeleton-item {
    cursor: default;

    &:hover {
      transform: none;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .skeleton-image {
      width: 120px;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      background: linear-gradient(
        90deg,
        var(--bg-tertiary) 25%,
        var(--bg-hover) 37%,
        var(--bg-tertiary) 63%
      );
      background-size: 200% 100%;
      animation: shimmer 1.5s infinite;
    }

    .price-info {
      padding-left: 140px;
    }

    .skeleton-text {
      background: linear-gradient(
        90deg,
        var(--bg-hover) 25%,
        var(--bg-tertiary) 37%,
        var(--bg-hover) 63%
      );
      background-size: 200% 100%;
      animation: shimmer 1.5s infinite;
      border-radius: 4px;
      height: 20px;
    }

    .amount.skeleton-text {
      width: 70%;
      height: 24px;
    }

    .coins.skeleton-text {
      width: 50%;
      height: 16px;
    }

    .skeleton-discount {
      position: absolute;
      top: 12px;
      right: 12px;
      width: 60px;
      height: 24px;
      background: linear-gradient(
        90deg,
        var(--bg-hover) 25%,
        var(--bg-tertiary) 37%,
        var(--bg-hover) 63%
      );
      background-size: 200% 100%;
      animation: shimmer 1.5s infinite;
      border-radius: 12px;
    }
  }
}

.purchase-button-container {
  margin-top: 16px;
}

.purchase-button {
  width: 100%;
  height: 50px;
  border-radius: 25px;
  border: none;
  background: linear-gradient(90deg, #ca93f2 0%, #9b6cc8 100%);
  color: white;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &:not(:disabled):hover {
    opacity: 0.9;
    transform: translateY(-2px);
  }

  &:not(:disabled):active {
    opacity: 0.8;
    transform: translateY(0);
  }
}

.details-tab {
  .details-content {
    color: var(--text-primary);

    h3 {
      font-size: 20px;
      font-weight: 600;
      margin-bottom: 24px;
      color: var(--text-primary);
    }

    .history-list {
      display: flex;
      flex-direction: column;

      .loading-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px 0;
        gap: 16px;

        p {
          color: var(--text-secondary);
        }
      }

      .empty-state {
        text-align: center;
        padding: 40px 0;
        color: var(--text-secondary);
      }

      .history-card {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;
        margin-bottom: 16px;
        background: var(--bg-tertiary);
        border-radius: 16px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

        .history-card-left {
          display: flex;
          align-items: center;
          gap: 16px;

          .diamond-icon-container {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            background: rgba(202, 147, 242, 0.15);
            display: flex;
            align-items: center;
            justify-content: center;

            .diamond-icon {
              width: 24px;
              height: 24px;
            }
          }

          .history-info {
            .history-type {
              font-size: 16px;
              font-weight: 600;
              margin-bottom: 4px;
              color: var(--text-primary);
            }

            .history-time {
              font-size: 14px;
              color: var(--text-tertiary);
            }
          }
        }

        .history-amount {
          font-size: 18px;
          font-weight: 600;

          &.positive {
            color: #52c41a;
          }

          &.neutral {
            color: var(--text-secondary);
          }

          &.negative {
            color: #ff4d4f;
          }
        }
      }

      .no-more {
        text-align: center;
        padding: 16px 0;
        color: var(--text-tertiary);
        font-size: 14px;
      }
    }
  }
}

@keyframes shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
</style>
