<template>
  <component :is="currentComponent" v-if="currentComponent" v-bind="$attrs" />
  <div v-else class="component-not-found">
    <h2>Component Not Found</h2>
    <p>The PC version of this component is not available yet.</p>
    <p>Path: {{ componentPath }}</p>
  </div>
</template>

<script setup lang="ts">
import { computed, defineAsyncComponent, ref, onMounted, watch } from 'vue'
import { getComponentForPath } from '@/pc/routes/pc-route-mapping'

// 定义props
const props = defineProps<{
  // 组件路径，例如 'stories/index'
  path: string
}>()

const currentComponent = ref(null)
const componentPath = computed(() => `@/pc/views/${props.path}.vue`)
const componentLoadError = ref(false)

// 尝试加载组件
const loadComponent = async () => {
  try {
    // 重置错误状态
    componentLoadError.value = false

    // 尝试动态导入PC端组件
    const path = props.path

    // 使用集中的路由映射配置加载组件
    const componentModule = getComponentForPath(path)
    if (componentModule) {
      currentComponent.value = defineAsyncComponent(() => componentModule)
    } else {
      // 如果没有找到对应的组件，将显示"组件未找到"的提示
      componentLoadError.value = true
      currentComponent.value = null
    }
  } catch (error) {
    console.error('Failed to load component:', error)
    componentLoadError.value = true
    currentComponent.value = null
  }
}

// 监听路径变化
watch(
  () => props.path,
  () => {
    loadComponent()
  },
  { immediate: true }
)

onMounted(() => {
  loadComponent()
})
</script>

<style lang="less" scoped>
.component-not-found {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 300px;
  padding: 20px;
  background-color: #180430;
  color: #fff;
  text-align: center;

  h2 {
    font-size: 24px;
    margin-bottom: 16px;
    color: #ca93f2;
  }

  p {
    font-size: 16px;
    margin-bottom: 8px;
    color: rgba(255, 255, 255, 0.8);
  }
}
</style>
