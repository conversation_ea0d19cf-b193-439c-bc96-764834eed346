<template>
  <Teleport to="body">
    <Transition name="fade">
      <div v-if="modelValue" class="pc-modal-overlay" @click.self="handleClose">
        <div class="pc-modal" :style="modalStyle">
          <div class="modal-header" v-if="!hideHeader">
            <h2 class="title">{{ title }}</h2>
            <div class="close-button" @click="handleClose">
              <icon-close />
            </div>
          </div>

          <div class="modal-content">
            <slot></slot>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { IconClose } from '@arco-design/web-vue/es/icon'

const props = defineProps<{
  modelValue: boolean
  title?: string
  width?: string | number
  maxHeight?: string | number
  hideHeader?: boolean
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
  (e: 'close'): void
}>()

const modalStyle = computed(() => {
  const style: Record<string, string> = {}

  if (props.width) {
    style.width = typeof props.width === 'number' ? `${props.width}px` : props.width
  }

  if (props.maxHeight) {
    style.maxHeight = typeof props.maxHeight === 'number' ? `${props.maxHeight}px` : props.maxHeight
  }

  return style
})

const handleClose = () => {
  emit('update:modelValue', false)
  emit('close')
}
</script>

<style lang="less" scoped>
@import '@/assets/style/theme.less';

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.pc-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.pc-modal {
  width: 600px;
  max-height: 90vh;
  background: var(--bg-secondary);
  border-radius: 20px;
  position: relative;
  overflow: hidden;
  animation: zoomIn 0.3s ease;
  box-shadow: 0 10px 30px var(--shadow-color);
  display: flex;
  flex-direction: column;
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.modal-header {
  padding: 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  // border-bottom: 1px solid var(--divider-color);
  text-align: center;

  .title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    width: 100%;
  }
}

.close-button {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--text-secondary);
  transition: color 0.2s ease;
  z-index: 10;

  &:hover {
    color: var(--text-primary);
  }
}

.modal-content {
  padding: 0 24px 24px 24px;
  overflow-y: auto;
}
</style>
