/**
 * PC端路由映射配置
 *
 * 这个文件集中管理PC端的路由映射，避免在多个文件中重复配置
 *
 * 使用方式：
 * 1. 在AppPC.vue中使用shouldUsePCLayout判断是否使用PCLayout
 * 2. 在PCLayout.vue中使用getComponentPathForRoute获取组件路径
 * 3. 在AdaptiveComponentLoader.vue中使用getComponentForPath加载组件
 */

// 定义路由映射类型
export interface RouteMapping {
  // 路由路径（可以是精确匹配或前缀匹配）
  path: string
  // 是否是前缀匹配
  isPrefix?: boolean
  // 对应的PC端组件路径（相对于@/pc/views/）
  componentPath: string
}

// PC端路由映射配置
export const PC_ROUTE_MAPPINGS: RouteMapping[] = [
  // 首页和故事列表
  { path: '/', componentPath: 'stories/index' },
  { path: '/stories', componentPath: 'stories/index' },

  // 用户相关页面
  { path: '/user/profile', componentPath: 'user/profile' },
  { path: '/user/settings', componentPath: 'user/settings' },

  // 聊天页面
  { path: '/chat/', isPrefix: true, componentPath: 'chat/index' },
  { path: '/chat2/', isPrefix: true, componentPath: 'chat/index' },
  { path: '/chat3/', isPrefix: true, componentPath: 'chat/index' },
  { path: '/chat4/', isPrefix: true, componentPath: 'chat/index' }

  // 可以在这里添加更多的路由映射
]

/**
 * 判断给定的路由路径是否应该使用PC布局
 * @param path 路由路径
 * @returns 是否应该使用PC布局
 */
export function shouldUsePCLayout(path: string): boolean {
  // 如果是PC管理路由，直接返回false（使用原生RouterView）
  if (path.startsWith('/pc')) {
    return false
  }

  // 检查是否有匹配的路由映射
  return PC_ROUTE_MAPPINGS.some((mapping) => {
    if (mapping.isPrefix) {
      return path.startsWith(mapping.path)
    } else {
      return path === mapping.path
    }
  })
}

/**
 * 根据路由路径获取对应的组件路径
 * @param path 路由路径
 * @returns 组件路径，如果没有匹配则返回null
 */
export function getComponentPathForRoute(path: string): string | null {
  // 查找匹配的路由映射
  const mapping = PC_ROUTE_MAPPINGS.find((mapping) => {
    if (mapping.isPrefix) {
      return path.startsWith(mapping.path)
    } else {
      return path === mapping.path
    }
  })

  return mapping ? mapping.componentPath : null
}

/**
 * 根据组件路径获取对应的组件
 * @param path 组件路径
 * @returns 动态导入的组件，如果没有匹配则返回null
 */
export function getComponentForPath(path: string): Promise<any> | null {
  // 根据组件路径动态导入组件
  switch (path) {
    case 'PCLayout':
      return import('@/pc/views/PCLayout.vue')
    case 'stories/index':
      return import('@/pc/views/stories/index.vue')
    case 'user/profile':
      return import('@/pc/views/user/profile.vue')
    case 'user/settings':
      return import('@/pc/views/user/settings.vue')
    case 'chat/index':
      return import('@/pc/views/chat/index.vue')
    default:
      return null
  }
}
