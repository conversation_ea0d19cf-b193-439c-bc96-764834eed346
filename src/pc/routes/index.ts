import { RouteRecordRaw } from 'vue-router'

const pcRoutes: RouteRecordRaw[] = [
  // 原有PC端管理路由
  {
    path: '/pc',
    component: () => import('@/pc/views/PCLayout.vue'),
    meta: {
      requiresAuth: true,
      title: 'PC管理界面'
    },
    children: [
      {
        path: '',
        component: () => import('@/pc/views/PCHome.vue'),
        meta: {
          requiresAuth: true,
          title: 'PC端首页'
        }
      },
      {
        path: 'flow-editor/:id',
        component: () => import('@/pc/views/flow-editor/index.vue'),
        meta: {
          requiresAuth: true,
          title: '流程编辑器'
        }
      },
      {
        path: 'character-select',
        component: () => import('@/pc/views/flow-editor/CharacterSelectPage.vue'),
        meta: {
          requiresAuth: true,
          title: '角色设置'
        }
      }
    ]
  }
]

// PC端适配路由 - 这些路由将在PC端访问时使用PC端组件
// 注意：这些路由不会被添加到路由表中，而是通过动态组件加载机制实现
// 在AppRoot.vue中，我们会根据设备类型决定加载PC端还是移动端组件

export default pcRoutes
