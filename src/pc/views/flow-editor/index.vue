<template>
  <div class="flow-editor">
    <div class="flow-header">
      <h1 class="editor-title">Story Configuration</h1>
      <div class="editor-tools" v-if="!isFlowEmpty">
        <button class="action-btn" @click="handleImport" title="导入YAML配置">
          <svg viewBox="0 0 24 24" width="16" height="16">
            <path
              fill="currentColor"
              d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4M7 10l5 5 5-5M12 15V3"
            />
          </svg>
          导入
        </button>
        <button class="action-btn" @click="handleExport" title="导出为YAML">
          <svg viewBox="0 0 24 24" width="16" height="16">
            <path
              fill="currentColor"
              d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4M7 10l5-5 5 5M12 4v12"
            />
          </svg>
          导出
        </button>
        <!-- <button class="action-btn" @click="loadExampleData" title="加载示例数据">
          <svg viewBox="0 0 24 24" width="16" height="16">
            <path
              fill="currentColor"
              d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6z"
            />
            <polyline points="14 2 14 8 20 8" />
            <line x1="12" y1="18" x2="12" y2="12" />
            <line x1="9" y1="15" x2="15" y2="15" />
          </svg>
          示例数据
        </button> -->
        <button class="action-btn primary" @click="handleSave" title="保存当前编辑">
          <svg viewBox="0 0 24 24" width="16" height="16">
            <path
              fill="currentColor"
              d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"
            />
            <polyline points="17 21 17 13 7 13 7 21" />
            <polyline points="7 3 7 8 15 8" />
          </svg>
          保存
        </button>
        <button
          class="action-btn generate-material-btn"
          @click="toggleMaterialGenerator"
          title="生成素材"
        >
          <svg viewBox="0 0 24 24" width="16" height="16">
            <path
              fill="currentColor"
              d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-2 10h-4v4h-2v-4H7v-2h4V7h2v4h4v2z"
            />
          </svg>
          生成素材
        </button>
        <button class="action-btn yaml-preview-btn" @click="toggleYamlPreview" title="YAML预览">
          <svg viewBox="0 0 24 24" width="16" height="16">
            <path
              fill="currentColor"
              d="M14.59 14.59h3.76v3.76h-3.76v-3.76zm-4.47 0h3.76v3.76h-3.76v-3.76zm-4.47 0h3.76v3.76H5.65v-3.76zm8.94-4.47h3.76v3.76h-3.76v-3.76zm-4.47 0h3.76v3.76h-3.76v-3.76zm-4.47 0h3.76v3.76H5.65v-3.76zm8.94-4.47h3.76v3.76h-3.76V5.65zm-4.47 0h3.76v3.76h-3.76V5.65zm-4.47 0h3.76v3.76H5.65V5.65z"
            />
          </svg>
          YAML预览
        </button>
      </div>
    </div>

    <div class="editor-content">
      <div class="flow-editor-main">
        <!-- 当树图为空时显示的新建流程按钮 -->
        <div class="empty-flow-container" v-if="isFlowEmpty">
          <div class="empty-flow-content">
            <svg viewBox="0 0 24 24" width="64" height="64">
              <path fill="currentColor" d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z" />
            </svg>
            <h2>开始创建你的故事</h2>
            <p>暂无节点，点击按钮开始创建你的故事</p>
            <button class="create-flow-btn" @click="createNewFlow">创建故事</button>
          </div>
        </div>
        <StoryFlowEditor
          ref="storyFlowEditor"
          @node-selected="handleNodeSelected"
          @nodes-changed="handleNodesChanged"
        />
      </div>

      <!-- 使用组件化的操作指南 -->
      <!-- <OperationGuide v-model="showGuide" /> -->

      <!-- 加载状态指示器 -->
      <div class="loading-overlay" v-if="isLoading">
        <div class="loading-spinner"></div>
        <div class="loading-text">{{ loadingMessage }}</div>
      </div>
    </div>

    <!-- YAML预览面板 -->
    <YamlPreviewPanel
      :visible="showYamlPreview"
      :content="editorStore.gameConfig"
      :highlightLines="highlightedLines"
      @close="toggleYamlPreview"
      @copy-success="showMessage('复制成功', 'success')"
      @copy-error="showMessage('复制失败', 'error')"
      @download-success="showMessage('下载成功', 'success')"
      @download-error="showMessage('下载失败', 'error')"
    />

    <!-- 素材生成器弹窗 -->
    <MaterialGeneratorModal
      :visible="showMaterialGenerator"
      @close="toggleMaterialGenerator"
      @generate-success="handleMaterialGenerateSuccess"
    />

    <!-- 用于导入YAML的隐藏文件输入 -->
    <input
      ref="fileInputRef"
      type="file"
      accept=".yaml,.yml"
      class="file-input"
      @change="onFileChange"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useEditorStore } from '@/store/editor'
import StoryFlowEditor from '@/pc/components/FlowEditor/StoryFlowEditor.vue'
import YamlPreviewPanel from '@/pc/components/FlowEditor/YamlPreviewPanel.vue'
import MaterialGeneratorModal from '@/pc/components/FlowEditor/MaterialGeneratorModal.vue'
import { Message } from '@/mobile/components/Message'
import { nanoid } from 'nanoid'
import { dump } from 'js-yaml'

// 使用 YamlPreviewPanel 组件替代原来的 MonacoYamlViewer 组件
// 组件加载状态跟踪
const monacoDynamicComponent = ref(false)

const route = useRoute()
const router = useRouter()
const storyId = route.params.id as string
const editorStore = useEditorStore()
const storyFlowEditor = ref<InstanceType<typeof StoryFlowEditor> | null>(null)
const fileInputRef = ref<HTMLInputElement>()
const showYamlPreview = ref(false)
const showMaterialGenerator = ref(false)
const selectedNodeId = ref<string | null>(null)
const highlightedLines = ref<number[]>([])

// 显示消息
const showMessage = (message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info') => {
  if (type === 'success') {
    Message.success(message)
  } else if (type === 'error') {
    Message.error(message)
  } else if (type === 'warning') {
    Message.warning(message)
  } else {
    Message.info(message)
  }
}

// YAML内容
const yamlContent = computed(() => {
  try {
    return dump(editorStore.gameConfig, {
      indent: 2, // 增加缩进
      lineWidth: -1, // 禁止自动换行
      noRefs: true, // 禁用引用，避免使用对象引用
      noCompatMode: true // 使用现代YAML格式
    })
  } catch (e) {
    return '无法生成YAML预览: ' + (e as Error).message
  }
})

// 处理节点选择事件
const handleNodeSelected = (nodeId: string) => {
  selectedNodeId.value = nodeId

  // 查找节点在YAML中的行数
  if (nodeId && showYamlPreview.value) {
    nextTick(() => {
      findNodeLinesInYaml(nodeId)
    })
  } else {
    highlightedLines.value = []
  }
}

// 在YAML文本中查找节点ID对应的行号
const findNodeLinesInYaml = (nodeId: string) => {
  if (!nodeId) {
    highlightedLines.value = []
    return
  }

  const lines = yamlContent.value.split('\n')
  let foundLine = -1
  let startLine = -1
  let endLine = -1
  let indentLevel = 0

  // 查找节点ID所在行
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i]
    // 使用更精确的匹配方式，确保找到的是完整的ID
    if (
      line.includes(`id: ${nodeId}`) ||
      line.includes(`id: '${nodeId}'`) ||
      line.includes(`id: "${nodeId}"`)
    ) {
      foundLine = i + 1 // 行号从1开始
      startLine = foundLine

      // 记录当前缩进级别
      indentLevel = (line.match(/^\s*/) || [''])[0].length
      break
    }
  }

  // 如果找到了节点行，向下查找该节点内容的结束行
  if (foundLine > 0) {
    for (let i = foundLine; i < lines.length; i++) {
      const line = lines[i]

      // 跳过空行
      if (line.trim() === '') continue

      // 获取当前行的缩进
      const currentIndent = (line.match(/^\s*/) || [''])[0].length

      // 如果遇到相同或更低级别的缩进，说明节点内容已结束
      if (i > foundLine && currentIndent <= indentLevel && line.trim() !== '') {
        endLine = i // 前一行是结束行
        break
      }
    }

    // 如果没找到结束行，则取到文件末尾
    if (endLine === -1) {
      endLine = lines.length
    }

    // 生成要高亮的行号列表
    const linesToHighlight = []
    for (let i = startLine; i <= endLine; i++) {
      linesToHighlight.push(i)
    }

    highlightedLines.value = linesToHighlight
  } else {
    highlightedLines.value = []
  }
}

// 加载状态
const isLoading = ref(false)
const loadingMessage = ref('正在加载...')

// 加载数据
const loadGameData = async () => {
  try {
    isLoading.value = true
    loadingMessage.value = '正在加载故事数据...'

    // 确保角色列表已加载
    if (editorStore.actors.length === 0) {
      await editorStore.fetchActorList()
    }

    // 预加载X6相关库
    await preloadX6Libraries()

    if (storyId && storyId !== 'new') {
      // 从API加载故事数据
      const success = await editorStore.loadStoryDetail(storyId)
      if (!success) {
        // 加载失败，返回上一页
        Message.error('故事数据加载失败')
        router.back()
        return false
      }

      // 加载成功后，检查是否已选择角色，如果没有，重定向到角色选择页面
      if (!editorStore.selectedActor) {
        console.log('未选择角色，重定向到角色选择页面')
        router.replace({
          path: '/pc/character-select',
          query: { storyId }
        })
        return false
      }

      return true
    } else {
      // 创建新的空白配置
      createEmptyConfig()
      return true
    }
  } catch (error) {
    console.error('加载游戏数据失败:', error)
    Message.error('加载游戏数据失败')
    return false
  } finally {
    setTimeout(() => {
      isLoading.value = false
    }, 500)
  }
}

// 预加载X6相关库，确保它们在组件初始化前已准备好
const preloadX6Libraries = async () => {
  try {
    // 利用动态导入来确保库已加载，但不使用返回结果
    // 这会将库加载到全局作用域中，但不会使其与当前组件耦合
    await Promise.all([
      import('@antv/x6'),
      import('@antv/x6-vue-shape'),
      import('@antv/x6/es/registry/tool') // 确保tool模块被加载
    ])
    console.log('X6库预加载完成')

    // 给库足够时间初始化
    await new Promise((resolve) => setTimeout(resolve, 100))

    return true
  } catch (error) {
    console.error('X6库预加载失败:', error)
    throw error
  }
}

// 创建空的配置
const createEmptyConfig = () => {
  // 创建根场景
  const rootScene = {
    id: '~', // 使用'~'作为数据模型的根ID，这与UI渲染中的'virtual_root_node'区分开
    name: '新故事',
    events: [],
    scene_group: 'root_data' // 更改为不同的名称，避免与UI渲染中的"root"冲突
  }
  // 设置场景集合
  editorStore.gameConfig.scenes = [rootScene] as any // 使用类型断言忽略类型错误
}

// 导入YAML功能
const handleImport = () => {
  fileInputRef.value?.click()
}

// 文件选择变化处理
const onFileChange = async (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (!file) return

  try {
    const content = await file.text()
    const success = editorStore.importFromYAML(content)
    if (success) {
      // 重置视图
      setTimeout(() => {
        storyFlowEditor.value?.resetView()
      }, 100)
      Message.success('导入成功')
    } else {
      Message.error('导入失败')
    }
  } catch (error) {
    Message.error('导入失败')
  }

  // 重置 input 以允许重复导入相同文件
  if (fileInputRef.value) {
    fileInputRef.value.value = ''
  }
}

// 导出YAML功能
const handleExport = () => {
  const yamlStr = editorStore.exportToYAML()
  const blob = new Blob([yamlStr], { type: 'text/yaml' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `story-config-${Date.now()}.yaml`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
  Message.success('导出成功')
}

// 保存功能
const handleSave = async () => {
  try {
    isLoading.value = true
    loadingMessage.value = '正在保存故事数据...'

    await editorStore.saveStory()
    Message.success('保存成功')

    // 如果当前是新建故事，保存成功后更新路由
    if (route.params.id === 'new' && editorStore.currentStoryId) {
      router.replace(`/pc/flow-editor/${editorStore.currentStoryId}`)
    }
  } catch (error) {
    if (error instanceof Error) {
      Message.error(error.message)
    } else {
      Message.error('保存失败')
    }
  } finally {
    setTimeout(() => {
      isLoading.value = false
    }, 500)
  }
}

// 使用 YamlPreviewPanel 组件替代原来的 MonacoYamlViewer 组件
// 不需要预热加载

// 在组件挂载时初始化，但不同时加载不同的组件
onMounted(async () => {
  try {
    // 确保Monaco组件能够加载
    monacoDynamicComponent.value = true

    // 先加载游戏数据
    const loaded = await loadGameData()

    // 确保等待一点时间让 DOM 完全渲染
    if (loaded) {
      // 错开组件初始化的时间，避免资源竞争
      setTimeout(() => {
        try {
          // 初始化主流程编辑器
          storyFlowEditor.value?.resetView()
        } catch (err) {
          console.error('初始化流程编辑器失败:', err)
          Message.error('初始化流程编辑器失败，请尝试刷新页面')
        }
      }, 300)
    }
  } catch (error) {
    console.error('组件初始化失败:', error)
    Message.error('组件初始化失败，请尝试刷新页面')
  }
})

// 切换YAML预览
const toggleYamlPreview = () => {
  try {
    const wasVisible = showYamlPreview.value
    showYamlPreview.value = !wasVisible

    // 如果是从隐藏变为显示，需要确保组件已加载
    if (!wasVisible && showYamlPreview.value) {
      // 如果有选中节点，查找并高亮节点行
      if (selectedNodeId.value) {
        nextTick(() => {
          findNodeLinesInYaml(selectedNodeId.value!)
        })
      }
    }
  } catch (error) {
    console.error('切换YAML预览时出错:', error)
    Message.error('操作失败，请稍后重试')
    showYamlPreview.value = !showYamlPreview.value // 恢复原状态
  }
}

// 切换素材生成器面板
const toggleMaterialGenerator = () => {
  showMaterialGenerator.value = !showMaterialGenerator.value
}

// 处理素材生成成功事件
const handleMaterialGenerateSuccess = () => {
  Message.success('素材生成任务提交成功，请稍后查看生成结果')
}

// 当选中节点变化时，如果YAML预览正在显示，重新计算高亮行
watch(selectedNodeId, (newValue) => {
  if (newValue && showYamlPreview.value) {
    nextTick(() => {
      findNodeLinesInYaml(newValue)
    })
  }
})

// 监听YAML内容变化，更新后重新计算高亮行
watch(yamlContent, () => {
  if (selectedNodeId.value && showYamlPreview.value) {
    nextTick(() => {
      findNodeLinesInYaml(selectedNodeId.value!)
    })
  }
})

// 使用 YamlPreviewPanel 组件替代原来的 MonacoYamlViewer 组件

// 计算流程是否为空
const isFlowEmpty = computed(() => {
  // 如果场景只有一个空的_BEGIN_节点，或者没有任何场景，则认为流程为空
  const scenes = editorStore.gameConfig.scenes || []
  if (scenes.length === 0) return true

  if (scenes.length === 1 && scenes[0].id === '_BEGIN_') {
    // 检查开始节点是否没有事件或下一个场景
    const beginScene = scenes[0]
    return !beginScene.next_scene_id && (!beginScene.events || beginScene.events.length === 0)
  }

  return false
})

// 监听节点变化
const handleNodesChanged = () => {
  // 节点发生变化时重新计算是否为空
  // 这里不需要额外处理，因为isFlowEmpty是计算属性，会自动重新计算
}

// 创建新流程
const createNewFlow = async () => {
  try {
    console.log('创建新故事...')
    isLoading.value = true
    loadingMessage.value = '正在创建新故事...'

    // 创建一个简单的空白流程，只包含开始节点和第一个章节
    // 创建根场景
    const rootScene = {
      id: '~',
      name: '新故事',
      events: [],
      scene_group: 'root'
    }

    // 创建开始节点
    const startScene = {
      id: '_BEGIN_',
      name: '开始章节',
      parent_id: '~', // 根场景作为开始章节的父级
      events: [], // 没有任何事件
      next_scene_id: '', // 先设为空字符串，后面会更新
      scene_group: 'default'
    }

    // 为了让流程不显示为空，添加一个默认的第一章节
    const firstChapter = {
      id: nanoid(),
      name: '第一章节',
      parent_id: '_BEGIN_', // 开始节点作为第一章节的父级
      events: [],
      scene_group: 'default'
    }

    // 设置开始节点连接到第一章节
    startScene.next_scene_id = firstChapter.id

    // 更新到编辑器存储
    editorStore.gameConfig.scenes = [rootScene, startScene, firstChapter] as any // 使用类型断言忽略类型错误

    // 获取角色列表（如果还没加载）
    if (editorStore.actors.length === 0) {
      await editorStore.fetchActorList()
    }

    // 重置视图
    setTimeout(() => {
      storyFlowEditor.value?.resetView()
      Message.success('新故事创建成功！')

      // 直接打开角色选择模态框
      if (storyFlowEditor.value) {
        console.log('创建故事成功，即将打开角色选择模态框')
        storyFlowEditor.value.handleOpenStorySettings()
      }

      isLoading.value = false
    }, 300)
  } catch (error) {
    console.error('创建新故事失败:', error)
    Message.error('创建新故事失败')
    isLoading.value = false
  }
}
</script>

<style lang="less" scoped>
.flow-editor {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  overflow: hidden;
  background-color: #1a0030;
  color: white;
  position: relative;

  .flow-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    background-color: rgba(26, 0, 48, 0.9);
    backdrop-filter: blur(8px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 10;

    .editor-title {
      font-size: 18px;
      font-weight: 500;
      margin: 0;
      color: #fff;
    }

    .editor-tools {
      display: flex;
      gap: 8px;

      .action-btn {
        height: 36px;
        padding: 0 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        background: rgba(202, 147, 242, 0.1);
        border: 1px solid rgba(202, 147, 242, 0.2);
        border-radius: 8px;
        color: rgba(255, 255, 255, 0.9);
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

        &:hover {
          background: rgba(202, 147, 242, 0.15);
          border-color: rgba(202, 147, 242, 0.3);
          transform: translateY(-1px);
        }

        &:active {
          transform: translateY(0);
        }

        &.primary {
          background: linear-gradient(90deg, #9b6cc8 0%, #ca93f2 100%);
          border: none;
          color: #fff;
          font-weight: 600;

          &:hover {
            box-shadow: 0 2px 8px rgba(202, 147, 242, 0.3);
            transform: translateY(-1px);
          }

          &:active {
            transform: translateY(0);
          }
        }

        &.yaml-preview-btn {
          background: linear-gradient(
            90deg,
            rgba(243, 156, 18, 0.1) 0%,
            rgba(243, 156, 18, 0.2) 100%
          );
          border-color: rgba(243, 156, 18, 0.3);
          color: #f39c12;

          &:hover {
            background: linear-gradient(
              90deg,
              rgba(243, 156, 18, 0.15) 0%,
              rgba(243, 156, 18, 0.25) 100%
            );
            border-color: rgba(243, 156, 18, 0.4);
            box-shadow: 0 2px 8px rgba(243, 156, 18, 0.2);
          }
        }

        &.generate-material-btn {
          background: linear-gradient(
            90deg,
            rgba(46, 204, 113, 0.1) 0%,
            rgba(46, 204, 113, 0.2) 100%
          );
          border-color: rgba(46, 204, 113, 0.3);
          color: #2ecc71;

          &:hover {
            background: linear-gradient(
              90deg,
              rgba(46, 204, 113, 0.15) 0%,
              rgba(46, 204, 113, 0.25) 100%
            );
            border-color: rgba(46, 204, 113, 0.4);
            box-shadow: 0 2px 8px rgba(46, 204, 113, 0.2);
          }
        }

        svg {
          width: 16px;
          height: 16px;
        }
      }
    }
  }

  .editor-content {
    flex: 1;
    display: flex;
    overflow: hidden;
    position: relative;

    .flow-editor-main {
      flex: 1;
      height: 100%;
      position: relative;
      overflow: hidden;

      // 添加新建流程按钮的样式
      .empty-flow-container {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10;
        background: rgba(26, 0, 48, 0.7);

        .empty-flow-content {
          background: rgba(255, 255, 255, 0.05);
          border-radius: 16px;
          padding: 40px;
          max-width: 500px;
          text-align: center;
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.1);
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);

          svg {
            color: #ca93f2;
            margin-bottom: 20px;
          }

          h2 {
            font-size: 24px;
            margin: 0 0 12px 0;
            color: #fff;
          }

          p {
            font-size: 16px;
            color: rgba(255, 255, 255, 0.7);
            margin: 0 0 30px 0;
          }

          .create-flow-btn {
            background: linear-gradient(90deg, #9b6cc8 0%, #ca93f2 100%);
            border: none;
            color: #fff;
            padding: 12px 28px;
            font-size: 16px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.2s;
            font-weight: 600;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);

            &:hover {
              box-shadow: 0 4px 12px rgba(202, 147, 242, 0.4);
              transform: translateY(-2px);
            }

            &:active {
              box-shadow: 0 2px 8px rgba(202, 147, 242, 0.3);
              transform: translateY(0);
            }
          }
        }
      }
    }
  }

  /* 确保操作指南有正确的堆叠顺序 */
  :deep(.operation-guide) {
    z-index: 50;
  }

  .yaml-preview {
    position: fixed;
    right: -400px;
    top: 0;
    width: 400px;
    height: 100vh;
    background: rgba(26, 0, 48, 0.95);
    backdrop-filter: blur(15px);
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.3);
    z-index: 100;
    transition: right 0.3s ease;
    display: flex;
    flex-direction: column;
    border-left: 1px solid rgba(255, 255, 255, 0.1);

    &.yaml-preview-active {
      right: 0;
    }

    .yaml-preview-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
        color: #f39c12;
      }

      .close-btn {
        background: transparent;
        border: none;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        cursor: pointer;
        color: rgba(255, 255, 255, 0.6);

        &:hover {
          background: rgba(255, 255, 255, 0.1);
          color: rgba(255, 255, 255, 0.9);
        }
      }
    }

    .yaml-content-wrapper {
      flex: 1;
      display: flex;
      overflow: hidden;

      .empty-state {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        color: rgba(255, 255, 255, 0.7);
        font-size: 16px;
      }

      .loading-placeholder {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        color: rgba(255, 255, 255, 0.7);
        font-size: 16px;

        .loading-spinner-small {
          width: 20px;
          height: 20px;
          border: 2px solid rgba(255, 255, 255, 0.1);
          border-radius: 50%;
          border-top-color: #ca93f2;
          animation: spin 1s ease-in-out infinite;
          margin-bottom: 10px;
        }

        @keyframes spin {
          to {
            transform: rotate(360deg);
          }
        }
      }

      .error-component {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        color: #ff6b6b;
        font-size: 16px;
        text-align: center;
        padding: 20px;
        background-color: rgba(255, 0, 0, 0.1);
        border-radius: 4px;
      }
    }
  }

  .file-input {
    display: none;
  }

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(26, 0, 48, 0.8);
    backdrop-filter: blur(4px);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;

    .loading-spinner {
      width: 50px;
      height: 50px;
      border: 3px solid rgba(255, 255, 255, 0.1);
      border-radius: 50%;
      border-top-color: #ca93f2;
      animation: spin 1s ease-in-out infinite;
      margin-bottom: 20px;
    }

    .loading-text {
      color: white;
      font-size: 18px;
      font-weight: 500;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    @keyframes spin {
      to {
        transform: rotate(360deg);
      }
    }
  }
}
</style>
