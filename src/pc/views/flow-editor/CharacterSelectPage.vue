<template>
  <div class="character-select-page">
    <div class="page-container">
      <div class="select-header">
        <h1>Character Settings</h1>
      </div>

      <div class="select-body">
        <!-- Character image selection -->
        <div class="section">
          <h2 class="section-title">Character</h2>
          <div class="character-wrapper">
            <!-- Navigation arrows -->
            <button class="nav-arrow left-arrow" @click="scrollLeft">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <polyline points="15 18 9 12 15 6"></polyline>
              </svg>
            </button>
            <div class="character-container">
              <div class="character-gallery">
                <!-- Create a role button -->
                <!-- <div class="character-card create-card" @click="handleCreateNewActor">
                  <div class="create-icon">+</div>
                  <span>Create a role</span>
                </div> -->

                <!-- Character cards -->
                <div
                  v-for="actor in actors"
                  :key="actor.id"
                  class="character-card"
                  :class="{ 'is-selected': actor.id === selectedActorId }"
                  @click="selectActor(actor)"
                >
                  <div class="card-content">
                    <img :src="actor.avatar_url" :alt="actor.name" class="actor-image" />
                    <div class="character-name">{{ actor.name }}</div>

                    <!-- 角色详细信息 - 动态显示所有extra字段 -->
                    <div
                      class="character-details"
                      v-if="getActorDisplayProperties(actor).length > 0"
                    >
                      <div class="detail-row">
                        <strong>Name:</strong>&nbsp;<strong>{{ actor.name }}</strong>
                      </div>
                      <div
                        v-for="{ key, value } in getActorDisplayProperties(actor)"
                        :key="key"
                        class="detail-row"
                      >
                        <strong>{{ key }}:</strong>&nbsp;<strong>{{ value }}</strong>
                      </div>
                    </div>

                    <!-- Free标签 -->
                    <div class="free-tag" v-if="!actor.is_purchased || actor.coins === 0">Free</div>

                    <!-- 分享按钮 -->
                    <!-- <button class="share-button">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      >
                        <path d="M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8"></path>
                        <polyline points="16 6 12 2 8 6"></polyline>
                        <line x1="12" y1="2" x2="12" y2="15"></line>
                      </svg>
                    </button> -->
                  </div>
                </div>
              </div>
            </div>
            <button class="nav-arrow right-arrow" @click="scrollRight">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <polyline points="9 18 15 12 9 6"></polyline>
              </svg>
            </button>
          </div>
        </div>

        <!-- Character details fields -->
        <div class="section">
          <h2 class="section-title">Character name</h2>
          <input 
            type="text" 
            v-model="editedActor.name" 
            class="detail-input" 
            placeholder="Enter name" 
          />
        </div>

        <!-- 角色副标题 -->
        <div class="section">
          <h2 class="section-title">Character subtitle</h2>
          <input 
            type="text" 
            v-model="editedActor.subtitle" 
            class="detail-input" 
            placeholder="Enter subtitle" 
          />
        </div>

        <!-- 头像和预览图 -->
        <div class="section">
          <h2 class="section-title">Avatar URL</h2>
          <div class="image-input-group">
            <div class="image-preview">
              <img :src="editedActor.avatar_url || 'https://cdn.magiclight.ai/assets/mobile/default-avatar.png'" alt="Avatar" class="avatar-preview" />
            </div>
            <input 
              type="text" 
              v-model="editedActor.avatar_url" 
              class="detail-input" 
              placeholder="Enter avatar URL" 
            />
          </div>
        </div>

        <div class="section">
          <h2 class="section-title">Preview URL</h2>
          <div class="image-input-group">
            <div class="image-preview">
              <img :src="editedActor.preview_url || 'https://cdn.magiclight.ai/assets/mobile/default-preview.png'" alt="Preview" class="preview-image" />
            </div>
            <input 
              type="text" 
              v-model="editedActor.preview_url" 
              class="detail-input" 
              placeholder="Enter preview URL" 
            />
          </div>
        </div>

        <!-- 金币和购买状态 -->
        <div class="section">
          <h2 class="section-title">Character cost</h2>
          <div class="input-group">
            <input 
              type="number" 
              v-model="editedActor.coins" 
              class="detail-input" 
              placeholder="Enter coins" 
            />
            
            <!-- <div class="select-wrapper">
              <select v-model="editedActor.is_purchased" class="detail-input">
                <option :value="true">已购买</option>
                <option :value="false">未购买</option>
              </select>
            </div> -->
          </div>
        </div>

        <!-- 版本 -->
        <div class="section">
          <h2 class="section-title">Character version</h2>
          <input 
            type="text" 
            v-model="editedActor.version" 
            class="detail-input" 
            placeholder="Enter version" 
          />
        </div>

        <!-- 动态显示所有extra字段的编辑表单 -->
        <div v-for="key in allExtraFieldsKeys" :key="key" class="section">
          <h2 class="section-title">Character {{ key.charAt(0).toUpperCase() + key.slice(1) }}</h2>
          <input
            type="text"
            v-model="extraFields[key]"
            class="detail-input"
            :placeholder="`Enter ${key}`"
          />
        </div>

        <!-- 以下是原有的表单字段，保留作为参考 -->
        <!-- <div class="section">
          <h2 class="section-title">Character gender and age</h2>
          <div class="input-group">
            <input
              type="text"
              v-model="characterGender"
              class="detail-input"
              placeholder="Enter gender"
            />
            <input
              type="text"
              v-model="characterAge"
              class="detail-input"
              placeholder="Enter age"
            />
          </div>
        </div>

        <div class="section">
          <h2 class="section-title">Character relationship</h2>
          <input
            type="text"
            v-model="characterRelationship"
            class="detail-input"
            placeholder="Enter"
          />
        </div> -->

        <!-- <div class="section">
          <h2 class="section-title">Character voice</h2>
          <div class="voice-section">
            <select v-model="voiceType" class="voice-select">
              <option value="Male">Male</option>
              <option value="Female">Female</option>
            </select>

            <div class="voice-player">
              <button class="play-button">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M8 5V19L19 12L8 5Z" fill="currentColor" />
                </svg>
              </button>
              <div class="timeline">
                <span class="current-time">00:30</span>
                <div class="progress-bar">
                  <div class="progress" :style="{ width: voiceProgress + '%' }"></div>
                </div>
                <span class="total-time">05:00</span>
              </div>
            </div>
          </div>
        </div>

         <div class="section">
          <h2 class="section-title">Character tag</h2>
          <div class="tag-section">
            <div class="tag-input-container">
              <input
                type="text"
                v-model="tagInput"
                class="detail-input tag-input"
                placeholder="Lin the distant future, human civilization has Lin the distant future, human"
              />
              <button class="clear-tag" v-if="tagInput" @click="tagInput = ''">
                <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M6 18L18 6M6 6L18 18"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </button>
            </div>
            <button class="add-tag-btn">+ Add</button>
          </div>
        </div>

        <div class="section">
          <h2 class="section-title">World background</h2>
          <div class="tag-section">
            <input type="text" class="detail-input" placeholder="Enter" />
            <button class="add-tag-btn">+ Add</button>
          </div>
        </div>

        <div class="section">
          <h2 class="section-title">Character introduce (public)</h2>
          <textarea class="detail-textarea" placeholder="Character information"></textarea>
        </div> -->
      </div>

      <div class="select-footer">
        <button class="confirm-button" @click="confirmSelection">Confirm and save</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useEditorStore } from '@/store/editor'
import { Message } from '@/mobile/components/Message'
import type { Actor } from '@/api/stories'
import { updateActor } from '@/api/editor'

const router = useRouter()
const editorStore = useEditorStore()

// State
const actors = ref<Actor[]>([])
const selectedActorId = ref<string>('')
const isLoading = ref(false)

// 动态表单字段 - 所有extra字段映射
const extraFields = ref<Record<string, string>>({})

// 标准字段
const characterName = ref('')
const characterSubtitle = ref('')
const avatarUrl = ref('')
const previewUrl = ref('')
const characterCoins = ref<number>(0)
const characterVersion = ref('')
const isPurchased = ref(false)
const isEditing = ref(true)
const editedActor = reactive<{
  name: string
  subtitle: string
  avatar_url: string
  preview_url: string
  coins: number
  is_purchased: boolean
  version: string
  chat_config?: Record<string, string>
}>({
  name: '',
  subtitle: '',
  avatar_url: '',
  preview_url: '',
  coins: 0,
  is_purchased: false,
  version: '',
  chat_config: {}
})

const voiceType = ref('Male')
const voiceProgress = ref(10)
const tagInput = ref('')

// Load actors when component mounts
onMounted(async () => {
  isLoading.value = true
  try {
    // Check if actors are already loaded in the store
    if (editorStore.actors.length === 0) {
      await editorStore.fetchActorList()
    }

    actors.value = editorStore.actors

    // Auto-select first actor if available
    if (actors.value.length > 0 && !selectedActorId.value) {
      selectedActorId.value = actors.value[0].id
      selectActor(actors.value[0])
    }
  } catch (error) {
    console.error('Failed to load actors:', error)
    Message.error('Failed to load characters')
  } finally {
    isLoading.value = false
  }
})

// Select an actor
const selectActor = (actor: Actor) => {
  selectedActorId.value = actor.id

  // 更新所有基本字段
  characterName.value = actor.name || ''
  characterSubtitle.value = actor.subtitle || ''
  avatarUrl.value = actor.avatar_url || ''
  previewUrl.value = actor.preview_url || ''
  characterCoins.value = actor.coins || 0
  characterVersion.value = actor.version || ''
  isPurchased.value = actor.is_purchased || false

  // 初始化编辑表单
  editedActor.name = actor.name || ''
  editedActor.subtitle = actor.subtitle || ''
  editedActor.avatar_url = actor.avatar_url || ''
  editedActor.preview_url = actor.preview_url || ''
  editedActor.coins = actor.coins || 0
  editedActor.is_purchased = actor.is_purchased || false
  editedActor.version = actor.version || ''
  editedActor.chat_config = actor.chat_config || {}

  // 重置extra字段映射
  extraFields.value = {}

  // 复制actor.extra中的所有字段到extraFields
  if (actor.extra) {
    Object.entries(actor.extra).forEach(([key, value]) => {
      // console.log(key, String(value) || '')
      extraFields.value[key] = String(value) || ''
    })
  }
}

// Navigate through character gallery
const navigateCharacters = (direction: 'prev' | 'next') => {
  if (actors.value.length <= 1) return

  const currentIndex = actors.value.findIndex((actor) => actor.id === selectedActorId.value)
  let newIndex = currentIndex

  if (direction === 'prev') {
    newIndex = currentIndex > 0 ? currentIndex - 1 : actors.value.length - 1
  } else {
    newIndex = currentIndex < actors.value.length - 1 ? currentIndex + 1 : 0
  }

  selectActor(actors.value[newIndex])
}

// Confirm selection and proceed
const confirmSelection = async () => {
  if (!selectedActorId.value || isLoading.value) return

  try {
    isLoading.value = true

    // 先保存所有修改
    await saveChanges()

    const selectedActor = actors.value.find((actor) => actor.id === selectedActorId.value)
    if (!selectedActor) {
      throw new Error('未选择角色')
    }

    // 将所有extraFields的内容更新到actor的extra中
    const updatedExtra = { ...(selectedActor.extra || {}) }
    Object.entries(extraFields.value).forEach(([key, value]) => {
      if (value.trim()) {
        updatedExtra[key] = value.trim()
      }
    })

    // Update actor with custom properties
    const actorWithCustomProps = {
      ...selectedActor,
      custom_name: characterName.value || selectedActor.name,
      extra: updatedExtra
    }

    // Update store with selected actor
    editorStore.setSelectedActor(actorWithCustomProps)

    // Get the story ID from the route query or use 'new' as default
    const storyId = router.currentRoute.value.query.storyId || 'new'

    // Navigate back to the flow editor with the story ID
    router.push(`/pc/flow-editor/${storyId}`)
  } catch (error) {
    console.error('确认角色选择时出错:', error)
    Message.error('确认角色选择失败')
  } finally {
    isLoading.value = false
  }
}

// 平滑滚动左右导航
const scrollLeft = () => {
  const gallery = document.querySelector('.character-gallery')
  if (gallery) {
    const scrollAmount = Math.min(300, gallery.clientWidth * 0.75)
    gallery.scrollBy({ left: -scrollAmount, behavior: 'smooth' })
  }
}

const scrollRight = () => {
  const gallery = document.querySelector('.character-gallery')
  if (gallery) {
    const scrollAmount = Math.min(300, gallery.clientWidth * 0.75)
    gallery.scrollBy({ left: scrollAmount, behavior: 'smooth' })
  }
}

// Handler for creating a new actor
const handleCreateNewActor = () => {
  // Navigate to actor creation page or show creation dialog
  router.push('/flow-editor/create-character')
}

// 获取角色的可显示属性
const getActorDisplayProperties = (actor: Actor) => {
  if (!actor.extra) return []

  return (
    Object.entries(actor.extra)
      // .filter(([_, value]) => value && String(value).trim())
      .map(([key, value]) => ({ key, value }))
  )
}

// 动态获取所有可用的extra字段
const allExtraFieldsKeys = computed(() => {
  const keysSet = new Set<string>()
  actors.value.forEach((actor) => {
    if (actor.extra) {
      Object.keys(actor.extra).forEach((key) => keysSet.add(key))
    }
  })
  return Array.from(keysSet)
})

// 检查是否有修改
const hasChanges = () => {
  const selectedActor = actors.value.find((actor) => actor.id === selectedActorId.value)
  if (!selectedActor) return false

  const originalActor = {
    name: selectedActor.name,
    subtitle: selectedActor.subtitle,
    avatar_url: selectedActor.avatar_url,
    preview_url: selectedActor.preview_url,
    coins: selectedActor.coins,
    is_purchased: selectedActor.is_purchased,
    version: selectedActor.version,
    extra: selectedActor.extra
  }

  const currentActor = {
    name: editedActor.name,
    subtitle: editedActor.subtitle,
    avatar_url: editedActor.avatar_url,
    preview_url: editedActor.preview_url,
    coins: editedActor.coins,
    is_purchased: editedActor.is_purchased,
    version: editedActor.version,
    extra: extraFields.value
  }

  // 比较两个对象是否不同
  return JSON.stringify(originalActor) !== JSON.stringify(currentActor)
}

// 保存修改
const saveChanges = async () => {
  const selectedActor = actors.value.find((actor) => actor.id === selectedActorId.value)
  if (!selectedActor) return

  try {
    isLoading.value = true
    const updatedActor = {
      ...selectedActor,
      name: editedActor.name,
      subtitle: editedActor.subtitle,
      avatar_url: editedActor.avatar_url,
      preview_url: editedActor.preview_url,
      coins: editedActor.coins,
      is_purchased: editedActor.is_purchased,
      version: editedActor.version,
      extra: { ...extraFields.value }
    }

    const { data } = await updateActor(updatedActor)
    if (data.code === '0') {
      // 更新本地角色数据
      Object.assign(selectedActor, {
        name: editedActor.name,
        subtitle: editedActor.subtitle,
        avatar_url: editedActor.avatar_url,
        preview_url: editedActor.preview_url,
        coins: editedActor.coins,
        is_purchased: editedActor.is_purchased,
        version: editedActor.version,
        extra: { ...extraFields.value }
      })
      // 更新界面显示的值
      characterName.value = editedActor.name
      characterSubtitle.value = editedActor.subtitle
      avatarUrl.value = editedActor.avatar_url
      previewUrl.value = editedActor.preview_url
      characterCoins.value = editedActor.coins
      characterVersion.value = editedActor.version
      isPurchased.value = editedActor.is_purchased
      
      Message.success('角色更新成功')
      // 保持编辑模式
    } else {
      throw new Error(data.message || '更新角色失败')
    }
  } catch (error) {
    console.error('更新角色失败:', error)
    Message.error(error instanceof Error ? error.message : '更新角色失败')
  } finally {
    isLoading.value = false
  }
}
</script>

<style lang="less" scoped>
.character-select-page {
  height: 100%;
  background: linear-gradient(135deg, #180021, #350057);
  color: white;
  padding: 24px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(159, 53, 255, 0.5);
    border-radius: 10px;

    &:hover {
      background: rgba(159, 53, 255, 0.7);
    }
  }
}

.page-container {
  max-width: 1200px;
  margin: 0 auto;
}

.select-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;

  h1 {
    font-size: 28px;
    font-weight: 600;
    background: linear-gradient(90deg, #ffffff, #e0aaff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin: 0;
  }
}

.select-body {
  display: flex;
  flex-direction: column;
}

.section {
  margin-bottom: 8px;
  transition: all 0.3s ease;

  &:hover {
    .section-title {
      color: rgba(224, 170, 255, 1);
    }
  }
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 12px;
  color: white;
  transition: color 0.3s ease;
}

.character-wrapper {
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
}

.character-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.character-gallery {
  display: flex;
  overflow-x: auto;
  padding: 20px 10px;
  margin: 0 0px;
  scroll-behavior: smooth;
  scroll-snap-type: x mandatory;
  -ms-overflow-style: none;
  scrollbar-width: thin;
  scrollbar-color: rgba(159, 53, 255, 0.5) rgba(0, 0, 0, 0.2);

  &::-webkit-scrollbar {
    height: 8px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(159, 53, 255, 0.5);
    border-radius: 4px;
    
    &:hover {
      background: rgba(159, 53, 255, 0.7);
    }
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
  }

  &::after {
    content: '';
    flex: 0 0 10px;
  }
}

.character-card {
  position: relative;
  width: 167px;
  height: 294px;
  flex: 0 0 auto;
  scroll-snap-align: start;
  margin-right: 20px;
  cursor: pointer;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: translateY(-8px);
    z-index: 1;
  }

  &.is-selected {
    z-index: 1;
    animation: float 1.5s ease-in-out infinite;

    .card-content {
      border-color: #ca93f2;
      box-shadow: 0 0 20px rgba(202, 147, 242, 0.3);
    }

    .character-name {
      display: none;
    }

    img,
    .actor-image {
      transform: scale(1.02);
    }

    .character-details {
      display: block;
      opacity: 1;
    }
  }

  .card-content {
    width: 100%;
    height: 100%;
    border-radius: 20px;
    overflow: hidden;
    position: relative;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.05);
  }

  img,
  .actor-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
  }

  .character-name {
    position: absolute;
    left: 16px;
    bottom: 16px;
    font-size: 20px;
    font-weight: 600;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    z-index: 3;
  }

  .free-tag {
    position: absolute;
    left: 12px;
    top: 12px;
    display: flex;
    align-items: center;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    background: rgba(218, 255, 150, 0.2);
    color: #daff96;
    backdrop-filter: blur(4px);
  }

  &.create-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.05);
    border: 2px dashed rgba(159, 53, 255, 0.5);
    border-radius: 20px;

    .create-icon {
      font-size: 42px;
      margin-bottom: 15px;
      color: rgba(255, 255, 255, 0.7);
      transition: all 0.3s ease;
    }

    span {
      font-size: 16px;
      color: rgba(255, 255, 255, 0.7);
      transition: all 0.3s ease;
    }

    &:hover {
      transform: translateY(-8px);
      border-color: #ca93f2;
      box-shadow: 0 0 20px rgba(202, 147, 242, 0.3);

      .create-icon,
      span {
        color: #ca93f2;
      }
    }
  }

  .character-details {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 60px 16px 16px;
    background: linear-gradient(
      to top,
      rgba(0, 0, 0, 0.85) 0%,
      rgba(0, 0, 0, 0.7) 60%,
      rgba(0, 0, 0, 0.4) 80%,
      rgba(0, 0, 0, 0) 100%
    );
    color: white;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    z-index: 2;

    .detail-row {
      font-size: 14px;
      margin-bottom: 6px;
      line-height: 1.4;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      color: rgba(255, 255, 255, 0.9);
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
      display: flex;

      strong {
        color: #ca93f2;
        min-width: 50px;
        text-transform: capitalize;
        margin-right: 4px;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// 新增动画
@keyframes float {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-8px);
  }
  100% {
    transform: translateY(0);
  }
}

.nav-arrow {
  position: absolute;
  width: 50px;
  height: 50px;
  flex: 0 0 auto;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  margin: 0 10px;

  svg {
    width: 24px;
    height: 24px;
  }

  &:hover {
    background: rgba(159, 53, 255, 0.5);
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 15px rgba(159, 53, 255, 0.4);
  }

  &.left-arrow {
    left: 0px;
  }

  &.right-arrow {
    right: 0px;
  }
}

.detail-input {
  width: 100%;
  padding: 12px 16px;
  border-radius: 12px;
  border: 1px solid rgba(159, 53, 255, 0.3);
  background: rgba(46, 0, 81, 0.5);
  color: white;
  font-size: 16px;
  transition: all 0.3s ease;

  &:focus {
    outline: none;
    border-color: rgba(159, 53, 255, 0.8);
    box-shadow: 0 0 0 2px rgba(159, 53, 255, 0.2);
  }

  &::placeholder {
    color: rgba(255, 255, 255, 0.5);
  }
}

.detail-textarea {
  width: 100%;
  min-height: 120px;
  padding: 12px 16px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 16px;
  resize: vertical;

  &:focus {
    outline: none;
    border-color: #9f35ff;
    background: rgba(255, 255, 255, 0.08);
  }

  &::placeholder {
    color: rgba(255, 255, 255, 0.4);
  }
}

.input-group {
  display: flex;
  gap: 16px;

  .detail-input {
    flex: 1;
  }
}

.voice-section {
  display: flex;
  flex-direction: column;
  gap: 12px;

  .voice-select {
    width: 100%;
    padding: 12px 16px;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 16px;
    -webkit-appearance: none;
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24'%3e%3cpath fill='white' d='M7 10l5 5 5-5z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 12px center;
    cursor: pointer;

    &:focus {
      outline: none;
      border-color: #9f35ff;
    }
  }

  .voice-player {
    display: flex;
    align-items: center;
    gap: 16px;

    .play-button {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      background: #9f35ff;
      border: none;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      flex-shrink: 0;

      svg {
        width: 18px;
        height: 18px;
        color: white;
      }
    }

    .timeline {
      flex: 1;
      display: flex;
      align-items: center;
      gap: 12px;

      .current-time,
      .total-time {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.8);
        flex-shrink: 0;
      }

      .progress-bar {
        flex: 1;
        height: 4px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 2px;
        overflow: hidden;

        .progress {
          height: 100%;
          background: #9f35ff;
          width: 30%;
        }
      }
    }
  }
}

.tag-section {
  display: flex;
  gap: 12px;

  .tag-input-container {
    flex: 1;
    position: relative;

    .clear-tag {
      position: absolute;
      right: 12px;
      top: 50%;
      transform: translateY(-50%);
      width: 20px;
      height: 20px;
      background: transparent;
      border: none;
      cursor: pointer;
      color: rgba(255, 255, 255, 0.6);
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;

      svg {
        width: 16px;
        height: 16px;
      }
    }
  }

  .add-tag-btn {
    padding: 12px 16px;
    border-radius: 8px;
    background: rgba(159, 53, 255, 0.2);
    border: 1px solid rgba(159, 53, 255, 0.3);
    color: #9f35ff;
    font-size: 16px;
    cursor: pointer;
    white-space: nowrap;

    &:hover {
      background: rgba(159, 53, 255, 0.3);
    }
  }
}

.select-footer {
  margin-top: 24px;
  padding: 16px 0;

  .confirm-button {
    width: 100%;
    padding: 14px 32px;
    font-size: 16px;
    border-radius: 12px;
    background: linear-gradient(135deg, #9f35ff, #e331ff);
    color: white;
    border: none;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(159, 53, 255, 0.4);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(159, 53, 255, 0.6);
    }

    &:active {
      transform: translateY(1px);
      box-shadow: 0 2px 10px rgba(159, 53, 255, 0.4);
    }
  }
}

.image-preview {
  display: flex;
  flex-direction: column;
  gap: 12px;

  .avatar-preview {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid rgba(159, 53, 255, 0.5);
  }

  .preview-image {
    width: 100%;
    max-height: 200px;
    object-fit: contain;
    border-radius: 12px;
    background-color: rgba(0, 0, 0, 0.2);
  }
}

.select-wrapper {
  position: relative;
  flex: 1;
  
  select {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3e%3cpath d='M7 10l5 5 5-5z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 16px;
  }
}

.image-input-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
</style>
