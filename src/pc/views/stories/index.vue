<template>
  <div class="pc-stories-content">
    <section class="hottest-section">
      <div class="section-header">
        <h2 class="section-title">
          <span class="icon">🔥</span>
          Hottest
        </h2>
      </div>

      <div class="filter-container">
        <!-- 第一行：下拉菜单 -->
        <div class="dropdown-row">
          <FilterDropdown :has-selection="selectedPopular !== 'popular'">
            <template #trigger>
              {{ selectedPopularLabel() }}
              <DropdownIcon />
            </template>
            <template #default="{ closeDropdown }">
              <div class="dropdown-options">
                <div
                  v-for="option in popularOptions"
                  :key="option.value"
                  class="dropdown-option"
                  :class="{ active: selectedPopular === option.value }"
                  @click="
                    () => {
                      handlePopularChange(option.value)
                      closeDropdown()
                    }
                  "
                >
                  {{ option.label }}
                </div>
              </div>
            </template>
          </FilterDropdown>

          <!-- <FilterDropdown :has-selection="!!selectedGender">
            <template #trigger>
              {{ selectedGenderLabel() }}
              <DropdownIcon />
            </template>
            <template #default="{ closeDropdown }">
              <div class="dropdown-options">
                <div
                  class="dropdown-option"
                  :class="{ active: !selectedGender }"
                  @click="
                    () => {
                      handleGenderChange('')
                      closeDropdown()
                    }
                  "
                >
                  All
                </div>
                <div
                  v-for="category in genderCategories"
                  :key="category.id"
                  class="dropdown-option"
                  :class="{ active: selectedGender === category.id }"
                  @click="
                    () => {
                      handleGenderChange(category.id)
                      closeDropdown()
                    }
                  "
                >
                  {{ category.name }}
                </div>
              </div>
            </template>
          </FilterDropdown> -->
        </div>

        <!-- 第二行：标签直接展示 -->
        <div class="tags-row">
          <div
            v-for="tag in availableTags"
            :key="tag.id"
            class="tag-pill"
            :class="{ active: selectedTags.includes(tag.id) }"
            @click="toggleTag(tag.id)"
          >
            {{ tag.name }}
          </div>
        </div>
      </div>

      <div class="story-grid-container">
        <!-- 优化加载状态判断，确保在初始化完成前显示加载状态 -->
        <template v-if="storyStore.loading || isLoading || !isInitialized">
          <VirtualStoryGrid
            :stories="Array(12).fill({})"
            :loading="true"
            @story-click="handleStoryClick"
            @image-loaded="handleImageLoaded"
            @subscription-change="handleSubscriptionChange"
            @need-login="showLoginModal('Sign up to subscribe!')"
            @need-email="showLoginModal('Please update your email to subscribe')"
          />
        </template>
        <template v-else-if="storyStore.hotStories.length === 0">
          <div class="no-data">
            <p>No characters available under current conditions.</p>
          </div>
        </template>
        <template v-else>
          <VirtualStoryGrid
            :stories="storyStore.hotStories"
            @story-click="handleStoryClick"
            @image-loaded="handleImageLoaded"
            @subscription-change="handleSubscriptionChange"
            @need-login="showLoginModal('Sign up to subscribe!')"
            @need-email="showLoginModal('Please update your email to subscribe')"
          />
        </template>
      </div>
    </section>

    <!-- 故事详情弹窗 -->
    <StoryDetailModal
      v-model:visible="showStoryDetailModal"
      :story-id="selectedStoryId"
      @play="handlePlayStory"
    />
  </div>
</template>

<script setup lang="ts">
// 定义组件名称，用于keep-alive
defineOptions({
  name: 'PCStories'
})
import { onMounted, getCurrentInstance, ref, computed, inject, watch, nextTick } from 'vue'
import { useStoryStore, useUserStore } from '@/store'
import { useTagsFilter } from '@/composables/useTagsFilter'
import { setDynamicSEO } from '@/router/seo-guard'
import type { Story, Actor } from '@/api/stories'
import { reportEvent } from '@/utils'
import { ReportEvent } from '@/interface'

import DropdownIcon from '@/assets/icon/dropdown-arrow.svg'

import StoryDetailModal from '@/pc/components/StoryDetailModal.vue'
import FilterDropdown from '@/pc/components/FilterDropdown.vue'
import VirtualStoryGrid from '@/pc/components/VirtualStoryGrid.vue'

const storyStore = useStoryStore()
const userStore = useUserStore()

const showStoryDetailModal = ref(false)
const selectedStoryId = ref('')

const showLoginModal = inject('showLoginModal') as (title?: string) => void

// 使用标签筛选composable
const {
  selectedPopular,
  selectedTags,
  selectedPopularLabel,
  handlePopularChange,
  handleTagsChange,
  initFromUrlParams,
  isLoading
} = useTagsFilter()

// 使用动态SEO

// 下拉菜单选项
const popularOptions = [
  { label: 'Most popular', value: 'popular' },
  { label: 'Newest', value: 'newest' }
]

// 获取所有可用标签
const availableTags = computed(() => {
  const tags: { id: string; name: string }[] = []
  if (!storyStore.storyCategories?.length) return []
  const categories = storyStore.storyCategories.filter(
    (category) => category?.name?.toUpperCase() === 'HOBBY'
  )
  categories.forEach((category) => {
    if (category.subcategories) {
      category.subcategories.forEach((subcategory) => {
        if (!tags.some((tag) => tag.id === subcategory.id)) {
          tags.push({
            id: subcategory.id,
            name: subcategory.name
          })
        }
      })
    }
  })
  return tags
})

// 切换标签选中状态
const toggleTag = (tagId: string) => {
  const newSelectedTags = [...selectedTags.value]
  const index = newSelectedTags.indexOf(tagId)

  if (index === -1) {
    newSelectedTags.push(tagId)
  } else {
    newSelectedTags.splice(index, 1)
  }

  handleTagsChange(newSelectedTags)
}

const handleStoryClick = async (story: Story) => {
  reportEvent(ReportEvent.ClickIndexPageStoryCard, {
    storyId: story.id
  })
  storyStore.setCurrentStory(story)
  storyStore.setCurrentActors(story.actors)

  // 显示故事详情弹窗
  selectedStoryId.value = story.id
  showStoryDetailModal.value = true
}

// 处理开始游戏
const handlePlayStory = (actor: Actor) => {
  reportEvent(ReportEvent.ClickToPlayInStoryIntro, {
    storyId: storyStore.currentStory?.id,
    actorId: actor.id
  })

  // 关闭弹窗
  showStoryDetailModal.value = false
}

const handleImageLoaded = (story: Story) => {
  reportEvent(ReportEvent.IndexPageStoryCardExposure, {
    storyId: story.id
  })
}

// 标记是否已经初始化
const isInitialized = ref(false)

// 初始化数据加载的函数
const initializeData = async () => {
  if (isInitialized.value) {
    // 只处理URL参数变化，不重新加载所有数据
    await initFromUrlParams()
    updateSEO()
    return
  }

  // 首次加载时初始化标签筛选
  await initFromUrlParams()

  // 标记为已初始化
  isInitialized.value = true

  // 初始化SEO
  updateSEO()
}

// 更新SEO信息
const updateSEO = () => {
  setDynamicSEO('Stories', {
    filters: {
      selectedTags: selectedTags.value,
      selectedPopular: selectedPopular.value,
      totalStories: storyStore.hotStories.length
    }
  })
}

// 监听用户认证状态，确保在认证完成后再加载数据
watch(
  () => userStore.isAuthenticated,
  async (isAuthenticated) => {
    if (isAuthenticated && !isInitialized.value) {
      await nextTick() // 确保DOM更新完成
      await initializeData()
    }
  },
  { immediate: true }
)

// 监听筛选条件变化，更新SEO
watch(
  [selectedTags, selectedPopular, () => storyStore.hotStories.length],
  () => {
    if (isInitialized.value) {
      updateSEO()
    }
  },
  { deep: true }
)

// 使用onMounted进行基础初始化
onMounted(async () => {
  reportEvent(ReportEvent.VisitIndexPage)
  const { proxy } = getCurrentInstance()

  // @ts-ignore
  proxy?.$tracker?.start()
  // @ts-ignore
  proxy?.$tracker?.setUserID(userStore.userInfo?.uuid)
})

const handleSubscriptionChange = (updatedStory: Story) => {
  // 更新 store 中的故事订阅状态
  const index = storyStore.hotStories.findIndex((s) => s.id === updatedStory.id)
  if (index !== -1) {
    storyStore.hotStories[index] = updatedStory
  }
}
</script>

<style lang="less" scoped>
@import '@/assets/style/mixin.less';
@import '@/assets/style/theme.less';

.pc-stories-content {
  width: 100%;
}

.hottest-section {
  margin-bottom: 40px;

  .section-header {
    margin-bottom: 24px;

    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 24px;
      color: var(--text-primary);
      margin: 0;

      .icon {
        font-size: 24px;
      }
    }
  }

  .filter-container {
    margin-bottom: 24px;
    display: flex;
    flex-direction: column;
    gap: 16px;

    // 第一行：下拉菜单
    .dropdown-row {
      display: flex;
      gap: 12px;
      align-items: center;
      svg {
        // fill: var(--filter-dropdown-text);
        stroke: var(--filter-dropdown-text) !important;
      }
    }

    // 第二行：标签展示
    .tags-row {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-top: 4px;
    }

    // 标签样式
    .tag-pill {
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 8px 12px;
      border-radius: 100px;
      background-color: var(--tag-bg);
      color: var(--tag-text);
      font-family: 'Work Sans', sans-serif;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      border: 1px solid var(--tag-border);
      white-space: nowrap;

      &:hover {
        background-color: var(--tag-hover-bg);
      }

      &.active {
        display: flex;
        align-items: center;
        gap: 4px;
        background-color: var(--tag-active-bg);
        color: var(--tag-active-text);
        border: 1px solid var(--tag-active-border);
        font-weight: 600;

        &::before {
          content: '';
          display: inline-block;
          width: 12px;
          height: 8px;
          background: url('@/assets/icon/tag-check.svg') no-repeat center center;
          background-size: contain;
        }
      }
    }
  }

  // 下拉菜单选项
  .dropdown-options {
    padding: 10px 0;

    .dropdown-option {
      padding: 10px 16px;
      color: var(--filter-dropdown-text);
      font-family: 'Work Sans', sans-serif;
      font-size: 12px;
      font-weight: 500;
      cursor: pointer;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: var(--filter-option-hover-bg);
      }

      &.active {
        color: var(--filter-option-active-text);
        background-color: var(--filter-option-active-bg);
        font-weight: 700;
      }
    }
  }

  .story-grid-container {
    // 移除固定高度，让虚拟滚动组件自己管理高度和滚动
    position: relative; // 确保虚拟滚动定位正确
  }

  // 骨架屏网格样式（仅用于加载状态）
  .story-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 30px;
    padding: 0 24px; // 与虚拟滚动保持一致的padding
  }
}

.no-data {
  grid-column: 1 / -1;
  text-align: center;
  padding: 40px;
  color: var(--text-secondary);
  font-size: 16px;
}

// 响应式调整（仅用于骨架屏）
@media screen and (max-width: 1600px) {
  .hottest-section {
    .story-grid {
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 25px;
      padding: 0 20px;
    }
  }
}

@media screen and (max-width: 1400px) {
  .hottest-section {
    .story-grid {
      grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
      gap: 20px;
      padding: 0 18px;
    }
  }
}

@media screen and (max-width: 1200px) {
  .hottest-section {
    .story-grid {
      grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
      gap: 18px;
      padding: 0 16px;
    }
  }
}

@media screen and (max-width: 768px) {
  .hottest-section {
    .story-grid {
      grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
      gap: 16px;
      padding: 0 14px;
    }
  }
}
</style>
