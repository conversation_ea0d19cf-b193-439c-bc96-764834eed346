<template>
  <div class="pc-settings-view">
    <div class="settings-container">
      <!-- 顶部图标 -->
      <div class="header-icon">
        <img src="https://cdn.magiclight.ai/assets/playshot/default-avatar.png" alt="" />
      </div>

      <!-- 设置内容 -->
      <div class="settings-content">
        <div class="settings-form">
          <!-- 第一行设置项 -->
          <div class="settings-row">
            <!-- 用户名设置 -->
            <div class="settings-item">
              <div class="item-label">Username</div>
              <div class="item-input" @click="showUsernameModal = true">
                <span>{{ userStore.userInfo?.name }}</span>
              </div>
            </div>

            <!-- 性别设置 -->
            <div class="settings-item">
              <div class="item-label">Gender</div>
              <FilterDropdown :has-selection="!!userStore.userInfo?.gender" class="gender-dropdown">
                <template #trigger>
                  <span>{{ userStore.userInfo?.gender === 'male' ? 'Male' : 'Female' }}</span>
                  <div class="arrow-icon">
                    <svg
                      width="12"
                      height="12"
                      viewBox="0 0 12 12"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M3.5 4.5L6 7L8.5 4.5"
                        stroke="currentColor"
                        stroke-width="1"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                  </div>
                </template>
                <template #default="{ closeDropdown }">
                  <div class="gender-options">
                    <div
                      class="gender-option"
                      :class="{ active: userStore.userInfo?.gender === 'male' }"
                      @click="handleGenderSelect('male', closeDropdown)"
                    >
                      Male
                    </div>
                    <div
                      class="gender-option"
                      :class="{ active: userStore.userInfo?.gender === 'female' }"
                      @click="handleGenderSelect('female', closeDropdown)"
                    >
                      Female
                    </div>
                  </div>
                </template>
              </FilterDropdown>
            </div>
          </div>

          <!-- 第二行设置项 -->
          <!-- <div class="settings-row">
            <div class="settings-item">
              <div class="item-label">Diamond</div>
              <div class="item-input diamond-input" @click="handleDiamondClick">
                <div class="diamond-display">
                  <img
                    src="https://cdn.magiclight.ai/assets/mobile/diamond.png"
                    alt="diamond"
                    class="diamond-icon"
                  />
                  <span>{{ userStore.userInfo?.coins || 0 }}</span>
                </div>
                <span class="details-text">Details</span>
              </div>
            </div>
          </div> -->

          <!-- 第三行设置项 -->
          <div class="settings-row">
            <!-- UID设置 -->
            <div class="settings-item">
              <div class="item-label">UID (cannot be modified)</div>
              <div class="item-input non-editable">
                <span>{{ userStore.userInfo?.uuid?.slice(0, 7) }}</span>
              </div>
            </div>

            <!-- 账号设置 -->
            <div class="settings-item">
              <div class="item-label">Account</div>
              <div class="item-input non-editable">
                <span>{{ userStore.userInfo?.email }}</span>
              </div>
            </div>
          </div>

          <!-- 确认按钮 -->
          <button class="confirm-button" @click="handleLogout">Log out</button>
        </div>
      </div>
    </div>

    <!-- 用户名修改弹窗 -->
    <PCModal v-model="showUsernameModal" title="Change Username" width="400">
      <div class="modal-body">
        <input
          v-model="newUsername"
          type="text"
          placeholder="Enter new username"
          maxlength="20"
          class="modal-input"
        />
        <div class="modal-buttons">
          <button class="cancel-button" @click="handleCancelUsername">Cancel</button>
          <button class="confirm-button" @click="handleUpdateUsername" :disabled="updating">
            <span v-if="!updating">Confirm</span>
            <span v-else class="loading-dots"> <span>.</span><span>.</span><span>.</span> </span>
          </button>
        </div>
      </div>
    </PCModal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store/user'
import { useRechargeStore } from '@/store/recharge'
import { Message } from '@/mobile/components/Message'
import { updateUserInfo } from '@/api/user'
import { reportEvent } from '@/utils'
import { ReportEvent } from '@/interface'
import PCModal from '@/pc/components/PCModal.vue'
import FilterDropdown from '@/pc/components/FilterDropdown.vue'

// 定义组件名称
defineOptions({
  name: 'PCUserSettings'
})

const router = useRouter()
const userStore = useUserStore()
const rechargeStore = useRechargeStore()

// 状态
const showUsernameModal = ref(false)
const newUsername = ref(userStore.userInfo?.name || '')
const updating = ref(false)

// 处理用户名更新
const handleUpdateUsername = async () => {
  if (!newUsername.value.trim()) {
    Message.error('Please enter a username')
    return
  }

  try {
    updating.value = true
    await updateUserInfo({ name: newUsername.value.trim() })
    userStore.handleUpdateUserInfo({ name: newUsername.value.trim() })
    Message.success('Username updated successfully')
    showUsernameModal.value = false
  } catch (error: any) {
    Message.error(error.message || 'Failed to update username')
  } finally {
    updating.value = false
  }
}

// 处理性别选择
const handleGenderSelect = async (gender: string, closeDropdown: () => void) => {
  if (gender === userStore.userInfo?.gender) {
    closeDropdown()
    return
  }

  try {
    updating.value = true
    await updateUserInfo({ gender })
    userStore.handleUpdateUserInfo({ gender })
    Message.success('Gender updated successfully')
    closeDropdown()
  } catch (error: any) {
    Message.error(error.message || 'Failed to update gender')
  } finally {
    updating.value = false
  }
}

// 处理钻石点击
const handleDiamondClick = () => {
  // 打开充值弹窗
  rechargeStore.showRechargeModal()
  // 同时获取价格列表
  rechargeStore.fetchPriceList()

  // 上报事件
  reportEvent(ReportEvent.ClickRechargeButton, {
    userId: userStore.userInfo?.uuid,
    path: '/user/settings'
  })
}

// 取消用户名修改
const handleCancelUsername = () => {
  showUsernameModal.value = false
  newUsername.value = userStore.userInfo?.name || ''
}

// 处理退出登录
const handleLogout = async () => {
  try {
    userStore.logout()
    reportEvent(ReportEvent.ClickSettingsPageSignOut, {
      userId: userStore.userInfo?.uuid
    })
    router.push('/')
  } catch (error: any) {
    Message.error(error.message || 'Failed to logout')
  }
}

onMounted(() => {
  // 获取用户信息
  if (userStore.isAuthenticated) {
    userStore.getUserInfo()
  }

  // 上报页面访问事件
  reportEvent(ReportEvent.SettingsPageView, {
    userId: userStore.userInfo?.uuid
  })
})
</script>

<style lang="less" scoped>
@import '@/assets/style/theme.less';

.pc-settings-view {
  width: 100%;
  min-height: 100%;
  padding: 24px;
}

.settings-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 40px;
  max-width: 684px;
  margin: 0 auto;
}

.header-icon {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.icon-circle {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: rgba(202, 147, 242, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
}

.settings-content {
  width: 100%;
}

.settings-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
  width: 100%;
}

.settings-row {
  display: flex;
  gap: 20px;
  width: 100%;
}

.settings-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.item-label {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-secondary, rgba(0, 0, 0, 0.65));
}

.item-input {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  border: 0.5px solid var(--text-secondary, rgba(0, 0, 0, 0.45));
  border-radius: 40px;
  font-size: 14px;
  color: var(--text-primary, rgba(0, 0, 0, 0.85));
  cursor: pointer;

  &.non-editable {
    background-color: rgba(0, 0, 0, 0.03);
    cursor: default;
    color: var(--text-tertiary, rgba(0, 0, 0, 0.45));
    border-color: var(--text-tertiary, rgba(0, 0, 0, 0.25));
  }

  &.diamond-input {
    .diamond-display {
      display: flex;
      align-items: center;
      gap: 8px;

      .diamond-icon {
        width: 20px;
        height: 20px;
      }
    }

    .details-text {
      color: var(--accent-color, #ca93f2);
      font-weight: 500;
    }
  }
}

.arrow-icon {
  display: flex;
  align-items: center;
}

.gender-selector {
  position: absolute;
  width: 332px;
  background: white;
  border-radius: 20px;
  box-shadow: 0px 2px 5px 0px rgba(91, 91, 91, 0.12);
  border: 0.5px solid #e0e0e0;
  overflow: hidden;
  z-index: 10;
  top: 170px;
  right: calc(50% - 166px);
}

.gender-dropdown {
  width: 100%;

  :deep(.dropdown-trigger) {
    width: 100%;
    height: 42px;
    border-radius: 36px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 16px;
    font-family: 'Work Sans', sans-serif;
    font-size: 12px;
    font-weight: 700;
  }

  :deep(.dropdown-menu) {
    width: 100%;
    margin-top: 8px;
    border-radius: 16px;
  }

  .dropdown-arrow {
    width: 12px;
    height: 6px;
  }

  .gender-options {
    padding: 10px 0;
  }
}

.gender-option {
  padding: 10px 16px;
  font-family: 'Work Sans', sans-serif;
  font-size: 12px;
  font-weight: 500;
  color: var(--filter-dropdown-text);
  cursor: pointer;

  &:hover {
    background-color: var(--filter-option-hover-bg);
  }

  &.active {
    color: var(--filter-option-active-text);
    background-color: var(--filter-option-active-bg);
    font-weight: 700;
  }
}

.confirm-button {
  width: 100%;
  height: 42px;
  background: var(--accent-color, #ca93f2);
  border: none;
  border-radius: 54px;
  color: var(--text-on-accent, rgba(0, 0, 0, 0.85));
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  margin-top: 16px;
  transition: all 0.3s ease;

  &:hover {
    opacity: 0.9;
  }
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  width: 400px;
  background: var(--bg-secondary, #2a2430);
  border-radius: 24px;
  padding: 24px;
  box-shadow: 0 10px 30px var(--shadow-color, rgba(0, 0, 0, 0.2));
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h3 {
    color: var(--text-primary, white);
    font-size: 18px;
    font-weight: 600;
    margin: 0;
  }

  .close-button {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-tertiary, rgba(255, 255, 255, 0.1));
    border: none;
    border-radius: 50%;
    color: var(--text-primary, white);
    cursor: pointer;

    &:hover {
      background: var(--bg-hover, rgba(255, 255, 255, 0.15));
    }

    &:active {
      background: var(--bg-hover, rgba(255, 255, 255, 0.2));
    }
  }
}

.modal-body {
  .modal-input {
    width: 100%;
    height: 48px;
    background: var(--bg-tertiary, rgba(255, 255, 255, 0.05));
    border: 1px solid var(--border-color, rgba(255, 255, 255, 0.1));
    border-radius: 12px;
    padding: 0 16px;
    color: var(--text-primary, white);
    font-size: 15px;
    margin-bottom: 20px;

    &::placeholder {
      color: var(--text-tertiary, rgba(255, 255, 255, 0.3));
    }

    &:focus {
      outline: none;
      border-color: var(--accent-color, rgba(255, 255, 255, 0.2));
    }
  }

  .modal-buttons {
    display: flex;
    gap: 12px;
    align-items: flex-end;
    button {
      flex: 1;
      height: 48px;
      border-radius: 24px;
      font-size: 15px;
      font-weight: 600;
      border: none;
      cursor: pointer;
      transition: all 0.2s ease;

      &:active {
        transform: scale(0.98);
      }
    }

    .cancel-button {
      background: var(--bg-tertiary, rgba(255, 255, 255, 0.1));
      color: var(--text-primary, white);
      border: 1px solid var(--border-color, rgba(255, 255, 255, 0.1));

      &:hover {
        background: var(--bg-hover, rgba(255, 255, 255, 0.15));
      }

      &:active {
        background: var(--bg-hover, rgba(255, 255, 255, 0.2));
      }
    }

    .confirm-button {
      background: var(--accent-color, #ca93f2);
      color: var(--bg-primary, #1f0038);

      &:hover {
        background: var(--accent-hover, #b87de0);
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      &:not(:disabled):active {
        background: var(--accent-hover, #d5a6f5);
      }
    }
  }
}

.loading-dots {
  display: inline-flex;
  gap: 2px;

  span {
    animation: dots 1.4s infinite;

    &:nth-child(2) {
      animation-delay: 0.2s;
    }

    &:nth-child(3) {
      animation-delay: 0.4s;
    }
  }
}

@keyframes dots {
  0%,
  20% {
    opacity: 0;
    transform: translateY(0);
  }
  50% {
    opacity: 1;
    transform: translateY(-4px);
  }
  80%,
  100% {
    opacity: 0;
    transform: translateY(0);
  }
}
</style>
