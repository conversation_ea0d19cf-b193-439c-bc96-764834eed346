<template>
  <div class="pc-layout" :class="{ 'chat-mode': isChatPage }">
    <!-- 顶部导航栏 -->
    <header v-if="!isChatPage" class="top-header" role="banner">
      <!-- 左侧Logo -->
      <div class="logo" @click="router.push('/')">
        <img
          :src="logoUrl"
          :alt="`${websiteTitle} logo`"
          loading="eager"
          width="142"
          height="31"
          role="img"
        />
      </div>

      <!-- 右侧用户操作区 -->
      <div class="user-actions" role="navigation" aria-label="User actions">
        <ThemeToggle />
        <a
          v-if="appName === 'ReelPlay'"
          href="https://x.com/Reelplay197835"
          target="_blank"
          rel="noopener noreferrer"
          class="x-btn"
          @click="handleTwitter"
          aria-label="Follow ReelPlay on X (Twitter)"
          role="button"
        >
          <XIcon />
          <span class="x-text">Follow</span>
        </a>
        <a
          v-if="appName === 'PlayShot'"
          href="https://t.me/+bnRxPQGjVPM5MzM1 "
          target="_blank"
          rel="noopener noreferrer"
          class="telegram-btn"
          @click="handleTelegram"
          aria-label="Join PlayShot on Telegram"
          role="button"
        >
          <TelegramIcon />
          <span class="telegram-text">Join</span>
        </a>
        <button
          v-if="!userStore.userInfo?.uuid || userStore.isGuest"
          class="sign-in-btn"
          @click="handleSignIn"
          aria-label="Sign in to your account"
          type="button"
        >
          Sign in
        </button>
        <div v-else class="user-info-container">
          <CreditDisplay
            :amount="userStore.userInfo?.coins || 0"
            @add="handleRecharge"
            :show-add-button="true"
          />
          <div
            class="user-avatar-wrapper"
            @click="showUserMenu = !showUserMenu"
            @keydown.enter="showUserMenu = !showUserMenu"
            @keydown.space.prevent="showUserMenu = !showUserMenu"
            tabindex="0"
            role="button"
            :aria-expanded="showUserMenu"
            aria-haspopup="true"
            aria-label="User menu"
          >
            <img
              :src="
                userStore.userInfo?.avatar_url ||
                'https://cdn.magiclight.ai/assets/playshot/default-avatar.png'
              "
              alt="User avatar"
              class="user-avatar"
              role="img"
            />
            <!-- 用户下拉菜单 -->
            <div v-if="showUserMenu" class="user-menu" role="menu" aria-label="User menu options">
              <div
                class="menu-item"
                @click="handleSettings"
                @keydown.enter="handleSettings"
                tabindex="0"
                role="menuitem"
                aria-label="Open settings"
              >
                <SettingsIcon />
                <span>Settings</span>
              </div>
              <div
                class="menu-item"
                @click="handleLogout"
                @keydown.enter="handleLogout"
                tabindex="0"
                role="menuitem"
                aria-label="Logout from account"
              >
                <LogoutIcon />
                <span>Logout</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- 主体内容区域 -->
    <div class="content-container" :class="{ 'chat-mode': isChatPage }">
      <!-- 左侧导航栏 -->
      <aside
        v-if="!isChatPage"
        class="sidebar"
        :class="{ collapsed: sidebarCollapsed }"
        role="navigation"
        aria-label="Main navigation"
      >
        <nav class="nav-menu" role="menubar">
          <div
            class="nav-item"
            :class="{ active: route.path === '/' || route.path === '/stories' }"
            @click="router.push('/')"
            @keydown.enter="router.push('/')"
            @keydown.space.prevent="router.push('/')"
            tabindex="0"
            role="menuitem"
            :aria-current="route.path === '/' || route.path === '/stories' ? 'page' : undefined"
            aria-label="Go to home page"
          >
            <HomeIcon :active="route.path === '/' || route.path === '/stories'" />
            <span v-show="!sidebarCollapsed">Home</span>
          </div>
          <!-- <div class="nav-item">
            <UserIcon />
            <span v-show="!sidebarCollapsed">AI Character</span>
          </div>
          <div class="nav-item">
            <CreateIcon />
            <span v-show="!sidebarCollapsed">Create</span>
          </div> -->
          <div
            class="nav-item"
            :class="{ active: route.path === '/user/profile' }"
            @click="!userStore.isGuest ? router.push('/user/profile') : showLoginModal()"
            @keydown.enter="!userStore.isGuest ? router.push('/user/profile') : showLoginModal()"
            @keydown.space.prevent="
              !userStore.isGuest ? router.push('/user/profile') : showLoginModal()
            "
            tabindex="0"
            role="menuitem"
            :aria-current="route.path === '/user/profile' ? 'page' : undefined"
            :aria-label="userStore.isGuest ? 'Sign in to access account' : 'Go to account page'"
          >
            <AccountIcon :active="route.path === '/user/profile'" />
            <span v-show="!sidebarCollapsed">Account</span>
          </div>
        </nav>
        <div class="sidebar-bottom">
          <button
            class="daily-reward-btn"
            @click="
              checkinStore.canShowCheckin
                ? handleShowCheckin()
                : showLoginModal('Sign up to claim your rewards!')
            "
            type="button"
            :aria-label="
              checkinStore.canShowCheckin ? 'Claim daily reward' : 'Sign up to claim daily rewards'
            "
          >
            <span role="img" aria-label="Gift emoji">🎁</span>
            <span
              class="reward-text"
              v-if="!sidebarCollapsed"
              :class="{ 'collapsed-text': sidebarCollapsed }"
              >Daily Reward</span
            >
          </button>
          <div class="join-discord" v-if="appName === 'ReelPlay'">
            <a
              href="https://discord.gg/FdZJmU4a8x"
              target="_blank"
              rel="noopener noreferrer"
              class="discord-btn"
              @click="handleDiscord"
              aria-label="Join our Discord community (opens in new tab)"
              role="button"
            >
              <DiscordIcon />
              <span v-if="!sidebarCollapsed" :class="{ 'collapsed-text': sidebarCollapsed }"
                >Join Our Discord</span
              >
              <svg
                v-if="!sidebarCollapsed"
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
              >
                <path
                  d="M12.6055 5.01465L16.5234 9.11621C16.7578 9.34277 16.875 9.65137 16.875 9.99121C16.875 10.3311 16.7578 10.6357 16.5234 10.8662L12.6055 14.9834C12.1406 15.4717 11.3828 15.4717 10.918 14.9834C10.4531 14.4951 10.4531 13.7061 10.918 13.2178L12.8008 11.249H4.32422C3.66016 11.249 3.125 10.6904 3.125 9.99902C3.125 9.30762 3.66016 8.74902 4.32031 8.74902H12.7969L10.9141 6.78027C10.4492 6.29199 10.4492 5.50293 10.9141 5.01465C11.3828 4.52637 12.1367 4.52637 12.6055 5.01465Z"
                  fill="white"
                />
              </svg>
            </a>
          </div>

          <!-- 收起/展开按钮 -->
          <button
            class="sidebar-toggle"
            @click="toggleSidebar"
            @keydown.enter="toggleSidebar"
            @keydown.space.prevent="toggleSidebar"
            type="button"
            :aria-label="sidebarCollapsed ? 'Expand sidebar menu' : 'Collapse sidebar menu'"
            :aria-expanded="!sidebarCollapsed"
          >
            <svg
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              :class="{ 'rotate-180': sidebarCollapsed }"
            >
              <polyline points="15 18 9 12 15 6"></polyline>
            </svg>
            <span v-if="!sidebarCollapsed" :class="{ 'collapsed-text': sidebarCollapsed }"
              >Hide Menu</span
            >
          </button>

          <!-- Terms text - only show when sidebar is expanded -->
          <div v-if="!sidebarCollapsed" class="terms-text">
            By signing in you agree with our<br />
            <a href="/terms" @click.prevent="router.push('/terms')">Terms of Service</a>
            ,
            <a href="/privacy" @click.prevent="router.push('/privacy')">Privacy Policy</a>,
            <a href="/complaints" @click.prevent="router.push('/complaints')">Complaints Policy</a>,
            <a href="/content-removal" @click.prevent="router.push('/content-removal')"
              >Content Removal Policy</a
            >,
            <a href="/record-keeping" @click.prevent="router.push('/record-keeping')"
              >18 U.S.C. 2257 Compliance</a
            >, <a href="/about" @click.prevent="router.push('/about')">About Us</a>,
            <a href="/refund" @click.prevent="router.push('/refund')">Refund and Returns Policy</a>.
          </div>
        </div>
      </aside>

      <!-- 主要内容 -->
      <main
        :class="['main-content', { 'chat-page-content': isChatPage }]"
        role="main"
        id="main-content"
        :aria-label="isChatPage ? 'Chat interface' : 'Main content'"
      >
        <AdaptiveComponentLoader :path="currentPath" />
      </main>
    </div>

    <!-- 抽屉组件 -->
    <AuthDrawer
      :visible="authDrawerVisible"
      :is-in-landing-page="false"
      :title="authDrawerTitle"
      @update:visible="authDrawerVisible = $event"
      @login="handleAuthSuccess"
      @register="handleAuthSuccess"
    />

    <!-- PC登录弹窗 -->
    <LoginModal
      v-model:visible="loginModalVisible"
      :title="loginModalTitle"
      @login="handleLoginSuccess"
    />

    <!-- PC签到弹窗 -->
    <CheckinModal />

    <!-- PC充值弹窗 -->
    <PCRechargeModal @need-login="handleNeedLogin" />

    <!-- 故事详情弹窗 -->
    <StoryDetailModal
      v-model:visible="storyDetailVisible"
      :story-id="selectedStoryId"
      @need-login="handleNeedLogin"
    />

    <!-- PC钻石用完弹窗 -->
    <PCDiamondUsedUpModal
      :visible="chatEventsStore.paymentRequired"
      @update:visible="handleDiamondModalClose"
      @leave="handleLeaveGame"
    />
  </div>
</template>

<script setup lang="ts">
import { onMounted, onBeforeUnmount, defineComponent, h, ref, computed, provide } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore, useRechargeStore } from '@/store'
import { useCheckinStore } from '@/store/checkin'
import { useTasksStore } from '@/store/tasks'
import { useChatEventsStore } from '@/store/chat-events'
import { reportEvent } from '@/utils'
import { ReportEvent } from '@/interface'
import { getComponentPathForRoute } from '@/pc/routes/pc-route-mapping'
import TelegramIcon from '@/assets/icon/telegram.svg'

// 定义Discord图标组件，支持亮色和暗色模式
const DiscordIcon = defineComponent({
  name: 'DiscordIcon',
  render() {
    return h(
      'svg',
      {
        viewBox: '0 0 24 24',
        fill: 'none',
        width: '20',
        height: '20',
        class: 'discord-icon'
      },
      [
        h('path', {
          d: 'M19.6695 5.01885C19.6634 5.00697 19.6532 4.99767 19.6409 4.9926C18.2112 4.33667 16.7026 3.86895 15.1526 3.60112C15.1386 3.5985 15.124 3.60039 15.1111 3.60652C15.0981 3.61265 15.0874 3.6227 15.0805 3.63525C14.8751 4.00811 14.6886 4.39109 14.5218 4.78275C12.851 4.52913 11.1516 4.52913 9.4808 4.78275C9.31287 4.3901 9.12339 4.00702 8.91324 3.63525C8.90603 3.62297 8.8953 3.61315 8.88243 3.60706C8.86956 3.60098 8.85515 3.59891 8.84109 3.60112C7.29098 3.86838 5.78223 4.33614 4.35279 4.99264C4.34056 4.99782 4.33023 5.00668 4.32324 5.01799C1.46465 9.28691 0.681579 13.4509 1.06573 17.5633C1.06681 17.5734 1.06991 17.5831 1.07483 17.592C1.07976 17.6008 1.08642 17.6086 1.09442 17.6148C2.75895 18.8473 4.62074 19.788 6.60035 20.3969C6.6143 20.4011 6.62918 20.4009 6.64301 20.3964C6.65684 20.3919 6.66895 20.3832 6.67772 20.3716C7.10289 19.7931 7.47963 19.1805 7.8041 18.54C7.80856 18.5312 7.81111 18.5216 7.81157 18.5117C7.81204 18.5019 7.81041 18.4921 7.8068 18.4829C7.80319 18.4737 7.79768 18.4654 7.79063 18.4585C7.78358 18.4517 7.77515 18.4463 7.76589 18.443C7.1718 18.2156 6.59665 17.9416 6.04584 17.6235C6.03584 17.6176 6.02743 17.6093 6.02137 17.5994C6.01532 17.5895 6.01179 17.5783 6.0111 17.5667C6.01042 17.5551 6.01259 17.5435 6.01744 17.533C6.02229 17.5224 6.02966 17.5133 6.0389 17.5062C6.15481 17.4197 6.2687 17.3304 6.38049 17.2386C6.3903 17.2305 6.40216 17.2253 6.41475 17.2236C6.42734 17.222 6.44015 17.2238 6.45174 17.229C10.0603 18.8759 13.9671 18.8759 17.533 17.229C17.5446 17.2235 17.5576 17.2214 17.5703 17.2229C17.5831 17.2245 17.5952 17.2296 17.6051 17.2377C17.7171 17.33 17.8312 17.4196 17.9476 17.5062C17.9569 17.5132 17.9643 17.5223 17.9692 17.5328C17.9742 17.5433 17.9764 17.5549 17.9758 17.5665C17.9752 17.5781 17.9718 17.5893 17.9658 17.5993C17.9598 17.6092 17.9515 17.6175 17.9415 17.6235C17.392 17.9443 16.8163 18.2181 16.2206 18.4421C16.2114 18.4456 16.203 18.451 16.196 18.458C16.189 18.465 16.1835 18.4734 16.18 18.4827C16.1764 18.4919 16.1749 18.5018 16.1754 18.5117C16.176 18.5215 16.1786 18.5312 16.1831 18.54C16.513 19.1769 16.8892 19.7887 17.3087 20.3706C17.3172 20.3825 17.3292 20.3915 17.3431 20.3962C17.357 20.4009 17.372 20.4011 17.386 20.3968C19.3692 19.79 21.2342 18.8492 22.9009 17.6148C22.909 17.6089 22.9157 17.6013 22.9207 17.5926C22.9256 17.5838 22.9286 17.5741 22.9295 17.5641C23.3894 12.8098 22.1596 8.67994 19.6695 5.01885ZM8.34302 15.0592C7.25657 15.0592 6.36137 14.0622 6.36137 12.8378C6.36137 11.6133 7.2392 10.6162 8.34302 10.6162C9.45545 10.6162 10.342 11.622 10.3246 12.8377C10.3246 14.0622 9.44675 15.0592 8.34302 15.0592ZM15.6698 15.0592C14.5834 15.0592 13.6882 14.0622 13.6882 12.8378C13.6882 11.6133 14.566 10.6162 15.6698 10.6162C16.7823 10.6162 17.6688 11.622 17.6514 12.8377C17.6514 14.0622 16.7823 15.0592 15.6698 15.0592Z',
          fill: 'currentColor'
        })
      ]
    )
  }
})
// 定义X图标
const XIcon = defineComponent({
  name: 'XIcon',
  render() {
    return h(
      'svg',
      {
        viewBox: '0 0 24 24',
        fill: 'currentColor',
        width: '18',
        height: '18',
        class: 'x-icon'
      },
      [
        h('path', {
          d: 'M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z'
        })
      ]
    )
  }
})
// 定义设置和登出图标
const SettingsIcon = defineComponent({
  name: 'SettingsIcon',
  render() {
    return h(
      'svg',
      {
        viewBox: '0 0 24 24',
        fill: 'none',
        stroke: 'currentColor',
        'stroke-width': '2',
        'stroke-linecap': 'round',
        'stroke-linejoin': 'round',
        class: 'menu-icon'
      },
      [
        h('circle', { cx: '12', cy: '12', r: '3' }),
        h('path', {
          d: 'M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z'
        })
      ]
    )
  }
})

const LogoutIcon = defineComponent({
  name: 'LogoutIcon',
  render() {
    return h(
      'svg',
      {
        viewBox: '0 0 24 24',
        fill: 'none',
        stroke: 'currentColor',
        'stroke-width': '2',
        'stroke-linecap': 'round',
        'stroke-linejoin': 'round',
        class: 'menu-icon'
      },
      [
        h('path', { d: 'M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4' }),
        h('polyline', { points: '16 17 21 12 16 7' }),
        h('line', { x1: '21', y1: '12', x2: '9', y2: '12' })
      ]
    )
  }
})
import AuthDrawer from '@/mobile/components/AuthDrawer.vue'
import ThemeToggle from '@/pc/components/ThemeToggle.vue'
import { defineAsyncComponent } from 'vue'
import AdaptiveComponentLoader from '@/pc/components/AdaptiveComponentLoader.vue'
import LoginModal from '@/pc/components/LoginModal.vue'
import CheckinModal from '@/pc/components/CheckinModal.vue'
import PCRechargeModal from '@/pc/components/PCRechargeModal.vue'
import StoryDetailModal from '@/pc/components/StoryDetailModal.vue'
import PCDiamondUsedUpModal from '@/pc/components/PCDiamondUsedUpModal.vue'

const route = useRoute()

// 获取当前路由对应的内容组件路径
const currentPath = computed(() => {
  // 使用集中的路由映射配置获取组件路径
  return getComponentPathForRoute(route.path)
})

// 判断当前是否是聊天页面
const isChatPage = computed(() => {
  const path = route.path
  return (
    path.startsWith('/chat/') ||
    path.startsWith('/chat2/') ||
    path.startsWith('/chat3/') ||
    path.startsWith('/chat4/')
  )
})

// 导航图标
const HomeIcon = defineComponent({
  name: 'HomeIcon',
  props: {
    active: {
      type: Boolean,
      default: false
    }
  },
  render() {
    return h(
      'svg',
      {
        viewBox: '0 0 22 20',
        fill: 'none',
        class: 'nav-icon'
      },
      [
        h('path', {
          'fill-rule': 'evenodd',
          'clip-rule': 'evenodd',
          d: 'M21.7341 9.32921L11.9676 0.376785C11.7032 0.134439 11.3576 0 10.9989 0C10.6403 0 10.2947 0.134439 10.0303 0.376785L0.264829 9.32921C0.143469 9.44076 0.0585832 9.58632 0.0212672 9.74688C-0.0160489 9.90743 -0.00405933 10.0755 0.0556686 10.2291C0.115396 10.3828 0.220084 10.5148 0.356049 10.608C0.492013 10.7012 0.652929 10.7512 0.817764 10.7515C1.35104 10.7515 1.78335 11.1838 1.78335 11.7171V17.9192C1.78335 18.1881 1.83632 18.4544 1.93924 18.7029C2.04216 18.9513 2.193 19.1771 2.38317 19.3672C2.57334 19.5574 2.7991 19.7083 3.04756 19.8112C3.29602 19.9141 3.56232 19.9671 3.83126 19.9671H18.1666C18.4355 19.9671 18.7018 19.9141 18.9503 19.8112C19.1988 19.7083 19.4245 19.5574 19.6147 19.3672C19.8049 19.1771 19.9557 18.9513 20.0586 18.7029C20.1615 18.4544 20.2145 18.1881 20.2145 17.9192V11.7181C20.2145 11.1844 20.6474 10.7519 21.1811 10.7525C21.3462 10.7524 21.5074 10.7025 21.6436 10.6093C21.7798 10.516 21.8846 10.3838 21.9444 10.23C22.0042 10.0761 22.0161 9.9078 21.9785 9.74706C21.941 9.58633 21.8558 9.44068 21.7341 9.32921ZM6.95868 11.8423C6.92923 11.8398 6.89964 11.8385 6.86998 11.8385C6.73542 11.8385 6.60217 11.865 6.47787 11.9165C6.35356 11.968 6.24063 12.0436 6.14553 12.1388C5.95357 12.3308 5.84573 12.5912 5.84573 12.8627C5.84573 13.1342 5.95357 13.3946 6.14553 13.5866C7.26926 14.7104 8.75002 15.3949 10.3212 15.5299C10.5125 15.5464 10.7051 15.5548 10.8986 15.5548C12.6806 15.5548 14.3896 14.8469 15.6497 13.5868C15.8363 13.3937 15.9395 13.135 15.9371 12.8666C15.9348 12.5981 15.8271 12.3413 15.6373 12.1514C15.4657 11.9799 15.2395 11.8754 14.9994 11.8551C14.9732 11.8529 14.9467 11.8516 14.9202 11.8514C14.6517 11.849 14.3931 11.9523 14.2 12.1388C13.3239 13.0146 12.1359 13.5066 10.8972 13.5066C10.7644 13.5066 10.6323 13.501 10.501 13.4898C9.40812 13.3964 8.37801 12.9205 7.59633 12.1389C7.50124 12.0437 7.38831 11.9682 7.264 11.9167C7.1665 11.8763 7.0635 11.8512 6.95868 11.8423Z',
          fill: 'currentColor'
        })
      ]
    )
  }
})

const AccountIcon = defineComponent({
  name: 'AccountIcon',
  props: {
    active: {
      type: Boolean,
      default: false
    }
  },
  render() {
    return h(
      'svg',
      {
        viewBox: '0 0 22 22',
        fill: 'none',
        class: 'nav-icon'
      },
      [
        h('path', {
          d: 'M11 0C4.92422 0 0 4.92422 0 11C0 17.0758 4.92422 22 11 22C17.0758 22 22 17.0758 22 11C22 4.92422 17.0758 0 11 0ZM11 5.5C12.7089 5.5 14.0937 6.88531 14.0937 8.59375C14.0937 10.3022 12.7102 11.6875 11 11.6875C9.29156 11.6875 7.90625 10.3022 7.90625 8.59375C7.90625 6.88531 9.28984 5.5 11 5.5ZM11 19.25C8.72566 19.25 6.66445 18.3249 5.16914 16.8313C5.86523 15.0348 7.58398 13.75 9.625 13.75H12.375C14.4177 13.75 16.1365 15.0339 16.8309 16.8313C15.3355 18.3262 13.273 19.25 11 19.25Z',
          fill: 'currentColor'
        })
      ]
    )
  }
})

// Lazy load components
const CreditDisplay = defineAsyncComponent(() => import('@/shared/components/CreditDisplay.vue'))

const router = useRouter()
const userStore = useUserStore()
const rechargeStore = useRechargeStore()
const checkinStore = useCheckinStore()
const tasksStore = useTasksStore()
const chatEventsStore = useChatEventsStore()

// 检查是否有未完成的任务或可以签到
const hasRewards = computed(() => {
  // 如果可以签到，返回 true
  if (checkinStore.canClaim) return true

  // 如果有未完成的任务，返回 true
  if (tasksStore.pendingTasks.length > 0) return true

  return false
})

// 从环境变量获取网站标题和Logo URL
const websiteTitle = computed(() => import.meta.env.VITE_WEBSITE_TITLE || 'Playshot')
const logoUrl = computed(
  () => import.meta.env.VITE_LOGO_URL || 'https://cdn.magiclight.ai/assets/playshot/logo-v2.png'
)
const appName = computed(() => import.meta.env.VITE_APP_NAME || 'Playshot')
const authDrawerVisible = ref(false)
const authDrawerTitle = ref('')
const loginModalVisible = ref(false)
const loginModalTitle = ref('')
const storyDetailVisible = ref(false)
const selectedStoryId = ref('')
const showUserMenu = ref(false) // 控制用户菜单的显示/隐藏
const sidebarCollapsed = ref(false) // 控制侧边栏是否收起

const handleSignIn = () => {
  reportEvent(ReportEvent.ClickIndexPageSignIn)
  // 在PC端显示登录弹窗，而不是跳转到登录页面
  showLoginModal('Sign in to continue!')
}

const handleLoginSuccess = async () => {
  loginModalVisible.value = false
  // 登录成功后的处理逻辑，与handleAuthSuccess类似
  if (userStore.isGuest) return

  // 并行获取签到信息和任务列表
  await Promise.all([checkinStore.fetchCheckinInfo(), tasksStore.fetchTasks()])

  // 如果有可领取的奖励，跳转到每日任务页面
  if (hasRewards.value) {
    handleDailyTasks()
  }
}

const handleRecharge = () => {
  // 打开充值弹窗
  rechargeStore.showRechargeModal()
  // 同时获取价格列表
  rechargeStore.fetchPriceList()

  // 上报事件
  reportEvent(ReportEvent.ClickRechargeButton, {
    userId: userStore.userInfo?.uuid,
    path: route.fullPath
  })
}

const handleDiscord = () => {
  reportEvent(ReportEvent.SocialButtonJoinClick, {
    type: 'discord'
  })
}

const handleTwitter = () => {
  reportEvent(ReportEvent.SocialButtonJoinClick, {
    type: 'twitter'
  })
}

const handleTelegram = () => {
  reportEvent(ReportEvent.SocialButtonJoinClick, {
    type: 'telegram'
  })
}
// 使用PC版登录弹窗替代移动端抽屉
const showLoginModal = (title?: string) => {
  // 设置标题并显示登录弹窗
  loginModalTitle.value = title || 'Sign in to continue!'
  loginModalVisible.value = true
}

// 保留此方法以兼容其他可能的调用
const showAuthDrawer = (title?: string) => {
  // 在PC端使用登录弹窗替代抽屉
  showLoginModal(title)
}

// 确保在StoryDetailModal中使用showAuthDrawer
const handleNeedLogin = () => {
  showAuthDrawer('Sign up to continue!')
}

const handleAuthSuccess = async () => {
  authDrawerVisible.value = false
  // After successful login, fetch check-in info and tasks, then redirect to daily tasks page if needed
  if (userStore.isGuest) return

  // 并行获取签到信息和任务列表
  await Promise.all([checkinStore.fetchCheckinInfo(), tasksStore.fetchTasks()])

  // 如果有可领取的奖励，跳转到每日任务页面
  if (hasRewards.value) {
    handleDailyTasks()
  }
}

const handleDailyTasks = () => {
  reportEvent(ReportEvent.ClickProfilePageDailyTasks, {
    userId: userStore.userInfo?.uuid
  })
  router.push('/daily-tasks')
}

const handleShowCheckin = () => {
  reportEvent(ReportEvent.ClickProfilePageDailyTasks, {
    userId: userStore.userInfo?.uuid
  })
  // 显示签到弹窗而不是跳转页面
  checkinStore.showModal()
}

// 处理设置按钮点击
const handleSettings = () => {
  showUserMenu.value = false
  router.push('/user/settings')
}

// 处理登出按钮点击
const handleLogout = async () => {
  showUserMenu.value = false
  try {
    userStore.logout()
    reportEvent(ReportEvent.ClickSettingsPageSignOut, {
      userId: userStore.userInfo?.uuid
    })
    router.push('/')
  } catch (error: any) {
    console.error('Failed to logout:', error)
  }
}

// 点击外部关闭用户菜单
const handleClickOutside = (event: MouseEvent) => {
  const userAvatarWrapper = document.querySelector('.user-avatar-wrapper')
  if (
    showUserMenu.value &&
    userAvatarWrapper &&
    !userAvatarWrapper.contains(event.target as Node)
  ) {
    showUserMenu.value = false
  }
}

// 切换侧边栏收起/展开状态
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
  // 保存状态到本地存储，以便在页面刷新后保持状态
  localStorage.setItem('sidebarCollapsed', sidebarCollapsed.value ? 'true' : 'false')
}

// 处理钻石用完模态框关闭
const handleDiamondModalClose = (value: boolean) => {
  if (!value) {
    chatEventsStore.paymentRequired = false
  }
}

// 处理离开游戏
const handleLeaveGame = () => {
  // 返回主页
  router.push('/')
}

// 向子组件提供showLoginModal方法
provide('showLoginModal', showLoginModal)

onMounted(async () => {
  // 初始化数据
  await Promise.all([
    userStore.isAuthenticated ? userStore.getUserInfo() : Promise.resolve(),
    userStore.isAuthenticated && !userStore.isGuest
      ? checkinStore.fetchCheckinInfo()
      : Promise.resolve(),
    userStore.isAuthenticated && !userStore.isGuest ? tasksStore.fetchTasks() : Promise.resolve()
  ])

  // 检查用户是否为guest，如果是则自动弹出试玩弹窗
  // if (userStore.userInfo?.role === 'guest') {
  //   // 延迟一点时间确保页面完全加载
  //   setTimeout(() => {
  //     showLoginModal('Welcome! Try our demo features!')
  //   }, 500)
  // }

  // 添加点击外部关闭用户菜单的事件监听
  document.addEventListener('click', handleClickOutside)

  // 从本地存储中读取侧边栏状态
  const savedSidebarState = localStorage.getItem('sidebarCollapsed')
  if (savedSidebarState) {
    sidebarCollapsed.value = savedSidebarState === 'true'
  }

  // 确保 PC Layout 显示 - 监听首屏加载完成
  const checkAndShowLayout = () => {
    const layoutElement = document.querySelector('.pc-layout') as HTMLElement
    if (layoutElement) {
      layoutElement.classList.add('loaded')
      layoutElement.style.opacity = '1'
      console.log('✅ PC Layout 已显示')
    }
  }

  // 立即检查一次
  setTimeout(checkAndShowLayout, 100)

  // 备用方案：延迟显示
  setTimeout(checkAndShowLayout, 1000)
})

onBeforeUnmount(() => {
  // 移除事件监听
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style lang="less" scoped>
@import '@/assets/style/mixin.less';
@import '@/assets/style/theme.less';

.pc-layout {
  height: 100vh;
  background-color: var(--bg-primary);
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  // 确保 PC Layout 始终可见
  opacity: 1 !important;

  // 如果有 loaded 类，添加过渡效果
  &.loaded {
    opacity: 1;
    transition: opacity 0.3s ease;
  }

  // 聊天模式样式
  &.chat-mode {
    background-color: #000;
  }
}

// 文本过渡动画样式
.collapsed-text {
  max-width: 0;
  opacity: 0;
  overflow: hidden;
  white-space: nowrap;
  transition:
    max-width 0.3s ease,
    opacity 0.2s ease,
    margin 0.3s ease;
  margin-left: 0;
}

span:not(.collapsed-text) {
  max-width: 200px;
  opacity: 1;
  overflow: hidden;
  white-space: nowrap;
  transition:
    max-width 0.3s ease,
    opacity 0.3s ease,
    margin 0.3s ease;
  margin-left: 8px;
}

// 顶部导航栏
.top-header {
  height: 72px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 40px 0 20px;
  background-color: var(--pc-top-header-bg);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--divider-color);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  z-index: 10;

  .logo {
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;

    img {
      width: 142px;
      height: 31px;
      object-fit: contain;
    }
  }

  .user-actions {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .user-info-container {
    display: flex;
    align-items: center;
    gap: 16px;

    :deep(.credit-display) {
      height: 36px;
      padding: 0 32px;
      border-radius: 20px;
      // background-color: var(--accent-bg);
      border: 1px solid var(--accent-color);

      .credit-icon {
        width: 32px;
        height: 32px;
        left: -10px;
      }

      .credit-amount {
        font-size: 14px;
        font-weight: 600;
        color: var(--text-primary);
      }

      .add-button {
        width: 24px;
        height: 24px;
        top: 48%;
        svg {
          width: 24px;
          height: 24px;
        }
      }
    }
  }

  .user-avatar-wrapper {
    position: relative;
    cursor: pointer;
  }

  .user-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--accent-color);
    transition: all 0.2s ease;

    &:hover {
      transform: scale(1.05);
    }
  }

  .user-menu {
    position: absolute;
    top: calc(100% + 8px);
    right: 0;
    width: 180px;
    background-color: var(--bg-secondary);
    border-radius: 12px;
    box-shadow: 0 4px 12px var(--shadow-color);
    overflow: hidden;
    z-index: 100;
    border: 1px solid var(--border-color);

    .menu-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 16px;
      color: var(--text-primary);
      font-size: 14px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background-color: var(--bg-hover);
      }

      .menu-icon {
        width: 18px;
        height: 18px;
      }
    }
  }

  .x-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: 20px;
    background-color: #000000;
    color: white;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    border: 1px solid transparent;

    .x-icon {
      fill: white;
    }

    &:hover {
      background-color: #333333;
    }
  }

  .telegram-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: 20px;
    background-color: #0088cc;
    color: white;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    border: 1px solid transparent;

    svg {
      width: 18px;
      height: 18px;
      fill: white;
    }

    &:hover {
      background-color: #006699;
      box-shadow: 0 2px 8px rgba(0, 136, 204, 0.3);
    }

    &:active {
      transform: translateY(1px);
    }
  }

  .sign-in-btn {
    padding: 8px 20px;
    border-radius: 20px;
    background-color: var(--accent-color);
    color: var(--bg-primary);
    border: none;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background-color: var(--accent-hover);
    }
  }
}

// 主体内容区域
.content-container {
  flex: 1;
  display: flex;
  overflow: hidden;
  position: relative;

  // 聊天模式样式
  &.chat-mode {
    flex-direction: column;
  }
}

// 左侧导航栏
.sidebar {
  width: 280px;
  background-color: var(--sidebar-bg);
  display: flex;
  flex-direction: column;
  padding: 24px 0;
  justify-content: space-between;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  position: relative;
  transition: width 0.3s ease;

  // 收起状态
  &.collapsed {
    width: 80px;

    .nav-menu {
      padding: 0 12px;

      .nav-item {
        padding: 15px;
        justify-content: center;

        .nav-icon {
          margin: 0 auto; // 确保图标水平居中
        }
      }
    }

    .sidebar-toggle {
      padding: 13px 12px; // 调整padding保持右边间隙
      justify-content: center; // 在收起状态下居中对齐
      gap: 0; // 移除间距

      svg {
        margin: 0; // 重置margin，使用flex居中
      }
    }

    .sidebar-bottom {
      padding: 0 12px;

      .daily-reward-btn {
        padding: 15px;
        justify-content: center !important;
        img,
        span:first-child {
          font-size: 24px;
          line-height: 1.5;
          min-width: 24px; // 确保图标不会被压缩
          margin: 0 auto; // 确保图标水平居中
          display: block; // 使图标成为块级元素以便居中
        }

        &::after {
          display: none; // 隐藏钻石图标
        }
      }

      .discord-btn {
        padding: 15px;
        justify-content: center !important;

        .discord-icon {
          margin: 0 auto; // 确保图标水平居中
        }

        &::after {
          display: none; // 隐藏箭头图标
        }
      }
    }
  }

  // 收起/展开按钮
  .sidebar-toggle {
    display: flex;
    align-items: center;
    gap: 10px;
    width: 100%;
    padding: 13px 24px;
    border-radius: 20px;
    background-color: var(--bg-hover);
    color: var(--text-secondary);
    border: none;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-top: 12px;

    &:hover {
      background-color: var(--accent-bg);
      color: var(--text-primary);
    }

    svg {
      min-width: 20px;
      min-height: 20px;
      width: 20px;
      height: 20px;
      color: currentColor;
      transition: transform 0.3s ease;
      display: block; /* 确保SVG作为块级元素显示 */

      &.rotate-180 {
        transform: rotate(180deg);
      }
    }
  }

  .nav-menu {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 0 24px;

    .nav-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: var(--pc-sidebar-item-padding);
      color: var(--pc-sidebar-text);
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      border-radius: var(--pc-sidebar-item-radius);
      font-family: 'Work Sans', sans-serif;
      text-transform: capitalize;

      &:hover {
        background-color: var(--pc-sidebar-hover-bg);
        color: var(--text-primary);
      }

      &.active {
        background-color: var(--pc-sidebar-active-bg);
        color: var(--pc-sidebar-active-text);
        font-weight: 600;
      }

      .nav-icon {
        width: var(--pc-sidebar-icon-size);
        height: var(--pc-sidebar-icon-size);
        min-width: var(--pc-sidebar-icon-size); // 确保图标不会被压缩
        color: var(--pc-sidebar-text);
        transition: color 0.2s ease;
      }

      &.active .nav-icon {
        color: var(--pc-sidebar-active-text);
      }
    }
  }

  .sidebar-bottom {
    margin-top: auto;
    padding: 0 24px;
    display: flex;
    flex-direction: column;
    gap: 12px;

    .daily-reward-btn {
      display: flex;
      align-items: center;
      gap: 10px;
      width: 100%;
      padding: 8px 24px;
      border-radius: 20px;
      background-color: var(--accent-bg);
      color: var(--text-primary);
      border: none;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition:
        all 0.2s ease,
        padding 0.3s ease;
      position: relative;

      &:hover {
        background-color: rgba(202, 147, 242, 0.3);
      }

      .reward-text {
        flex: 1;
        text-align: left;
        color: var(--text-primary);
        font-family: 'Work Sans', sans-serif;
      }

      img,
      span:first-child {
        font-size: 24px;
        line-height: 1.5;
        min-width: 24px; // 确保图标不会被压缩
      }

      // 钻石图标
      &::after {
        content: '';
        position: absolute;
        right: 24px;
        top: 50%;
        transform: translateY(-50%);
        width: 28px;
        height: 28px;
        background-image: url('https://cdn.magiclight.ai/assets/mobile/diamond.png');
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
        transition: opacity 0.3s ease;
      }
    }

    .join-discord {
      .discord-btn {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 10px;
        width: 100%;
        padding: 13px 24px;
        border-radius: 20px;
        background-color: #6563ff;
        color: white;
        text-decoration: none;
        font-size: 14px;
        font-weight: 500;
        transition:
          all 0.2s ease,
          padding 0.3s ease;
        position: relative;

        &:hover {
          background-color: darken(#6563ff, 5%);
        }

        .discord-icon {
          width: 20px;
          height: 20px;
          color: white;
          min-width: 20px; // 确保图标不会被压缩
        }

        // 箭头图标
        // &::after {
        //   content: '';
        //   position: absolute;
        //   right: 24px;
        //   top: 50%;
        //   transform: translateY(-50%);
        //   width: 20px;
        //   height: 20px;
        //   background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='14' height='11' viewBox='0 0 14 11' fill='none'%3E%3Cpath d='M13.75 5.5L8.25 0.75V3.5H0.25V7.5H8.25V10.25L13.75 5.5Z' fill='white'/%3E%3C/svg%3E");
        //   background-size: contain;
        //   background-repeat: no-repeat;
        //   background-position: center;
        //   transition: opacity 0.3s ease;
        // }
      }
    }
  }
}

// 主要内容
.main-content {
  flex: 1;
  padding: 24px 40px;
  overflow-y: hidden;
  background-color: var(--bg-primary);
}

// 聊天页面不需要padding
.chat-page-content {
  padding: 0 !important;
}

// 响应式调整
@media screen and (max-width: 1400px) {
  .top-header {
    padding: 0 30px;
  }

  .sidebar {
    width: 260px;

    &.collapsed {
      width: 70px;

      .sidebar-bottom {
        .daily-reward-btn,
        .discord-btn {
          padding: 12px;
          justify-content: center;

          &::after {
            opacity: 0;
          }
        }
      }
    }

    .nav-menu {
      .nav-item {
        padding: 12px 16px;
      }
    }

    .sidebar-bottom {
      .daily-reward-btn,
      .discord-btn {
        padding: 8px 16px;

        &::after {
          right: 16px;
        }
      }
    }
  }

  .sidebar-toggle {
    padding: 10px 20px;
  }

  .main-content {
    padding: 20px 30px;
  }

  .chat-page-content {
    padding: 0 !important;
  }
}

@media screen and (max-width: 1200px) {
  .top-header {
    padding: 0 24px;
    height: 64px;

    .logo img {
      width: 120px;
      height: 26px;
    }
  }

  .sidebar {
    width: 240px;

    &.collapsed {
      width: 60px;

      .sidebar-bottom {
        .daily-reward-btn,
        .discord-btn {
          padding: 10px;
          justify-content: center;

          &::after {
            opacity: 0;
          }
        }
      }
    }

    .nav-menu {
      .nav-item {
        padding: 10px 16px;
        font-size: 14px;
      }
    }

    .sidebar-bottom {
      .daily-reward-btn,
      .join-discord .discord-btn {
        font-size: 13px;

        img,
        span:first-child {
          font-size: 20px;
        }

        &::after {
          width: 24px;
          height: 24px;
        }
      }
    }
  }

  .sidebar-toggle {
    padding: 8px 16px;
    font-size: 13px;
  }

  .main-content {
    padding: 20px 24px;
  }

  .chat-page-content {
    padding: 0 !important;
  }
}

.terms-text {
  margin-top: 24px;
  font-size: 12px;
  text-align: center;
  color: var(--text-tertiary);
  line-height: 1.17;

  a {
    color: var(--text-tertiary);
    text-decoration: underline;

    &:hover {
      color: var(--text-secondary);
    }
  }
}
</style>
