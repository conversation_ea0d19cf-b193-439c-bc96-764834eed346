<template>
  <div class="pc-home">
    <div class="welcome-section">
      <!-- <p>ReelPlay的管理界面，可以进行流程编辑、素材管理等操作。</p> -->
    </div>

    <div class="tools-section">
      <div class="tool-card" @click="navigateToEditor">
        <div class="tool-icon">
          <svg viewBox="0 0 24 24">
            <path
              fill="currentColor"
              d="M3,3H21V21H3V3M7.73,18.04C8.13,18.89 8.92,19.59 10.27,19.59C11.77,19.59 12.8,18.79 12.8,17.04V11.26H11.1V17C11.1,17.86 10.75,18.08 10.2,18.08C9.62,18.08 9.38,17.68 9.11,17.21L7.73,18.04M13.71,17.86C14.21,18.84 15.22,19.59 16.8,19.59C18.4,19.59 19.6,18.76 19.6,17.23C19.6,15.82 18.79,15.19 17.35,14.57L16.93,14.39C16.2,14.08 15.89,13.87 15.89,13.37C15.89,12.96 16.2,12.64 16.7,12.64C17.18,12.64 17.5,12.85 17.79,13.37L19.1,12.5C18.55,11.54 17.77,11.17 16.7,11.17C15.19,11.17 14.22,12.13 14.22,13.4C14.22,14.78 15.03,15.43 16.25,15.95L16.67,16.13C17.45,16.47 17.91,16.68 17.91,17.26C17.91,17.74 17.46,18.09 16.76,18.09C15.93,18.09 15.45,17.66 15.09,17.06L13.71,17.86Z"
            />
          </svg>
        </div>
        <div class="tool-content">
          <h3>故事编辑器</h3>
          <!-- <p>创建和编辑故事流程图，设计故事分支和情节走向。</p> -->
        </div>
      </div>

      <!-- <div class="tool-card">
        <div class="tool-icon">
          <svg viewBox="0 0 24 24">
            <path
              fill="currentColor"
              d="M19,19H5V5H19M19,3H5A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3M13.96,12.29L11.21,15.83L9.25,13.47L6.5,17H17.5L13.96,12.29Z"
            />
          </svg>
        </div>
        <div class="tool-content">
          <h3>素材管理</h3>
          <p>管理故事素材，包括图片、音频和视频资源。</p>
        </div>
      </div>

      <div class="tool-card">
        <div class="tool-icon">
          <svg viewBox="0 0 24 24">
            <path
              fill="currentColor"
              d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z"
            />
          </svg>
        </div>
        <div class="tool-content">
          <h3>用户管理</h3>
          <p>管理用户账号、权限和角色设置。</p>
        </div>
      </div> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const navigateToEditor = () => {
  // 导航到新建故事路由
  router.push('/pc/flow-editor/new')
}
</script>

<style lang="less" scoped>
.pc-home {
  padding: 30px;

  .welcome-section {
    margin-bottom: 40px;

    h1 {
      font-size: 28px;
      color: #fff;
      margin-bottom: 16px;
    }

    p {
      font-size: 16px;
      color: rgba(255, 255, 255, 0.8);
      max-width: 800px;
    }
  }

  .tools-section {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 24px;

    .tool-card {
      background: rgba(255, 255, 255, 0.05);
      border-radius: 12px;
      padding: 24px;
      display: flex;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 1px solid rgba(255, 255, 255, 0.1);

      &:hover {
        transform: translateY(-5px);
        background: rgba(255, 255, 255, 0.1);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        border-color: #ca93f2;
      }

      .tool-icon {
        margin-right: 20px;

        svg {
          width: 40px;
          height: 40px;
          color: #ca93f2;
        }
      }

      .tool-content {
        h3 {
          color: #fff;
          font-size: 18px;
          margin: 0 0 10px 0;
        }

        p {
          color: rgba(255, 255, 255, 0.7);
          margin: 0;
        }
      }
    }
  }
}
</style>
