/* 移动端样式变量 */
@import '@/assets/style/theme.less';

/* 颜色变量 - 已迁移到主题文件，使用CSS变量 */
@primary-bg: var(--mobile-bg-primary);
@secondary-bg: var(--mobile-bg-secondary);
@tertiary-bg: var(--mobile-bg-gradient-start);
@accent-color: var(--accent-color);

/* 安全区域变量 */
@safe-area-top: env(safe-area-inset-top);
@safe-area-bottom: env(safe-area-inset-bottom);
@safe-area-left: env(safe-area-inset-left);
@safe-area-right: env(safe-area-inset-right);

/* 布局变量 */
@menu-height: 60px;
@header-height: 60px;

/* 动画变量 */
@transition-duration: 0.3s;
@transition-timing: ease-in-out;

/* 字体变量 */
@font-size-small: 12px;
@font-size-normal: 14px;
@font-size-large: 16px;
@font-size-xl: 18px;

/* 间距变量 */
@spacing-xs: 4px;
@spacing-sm: 8px;
@spacing-md: 16px;
@spacing-lg: 24px;
@spacing-xl: 32px;

/* 圆角变量 */
@border-radius-sm: 4px;
@border-radius-md: 8px;
@border-radius-lg: 12px;
@border-radius-xl: 16px;

/* 阴影变量 */
@shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
@shadow-md: 0 4px 8px rgba(0, 0, 0, 0.15);
@shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.2);
