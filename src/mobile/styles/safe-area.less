/* 移动端安全区域适配样式 */

/* 全局安全区域变量 */
:root {
  --safe-area-inset-top: env(safe-area-inset-top);
  --safe-area-inset-bottom: env(safe-area-inset-bottom);
  --safe-area-inset-left: env(safe-area-inset-left);
  --safe-area-inset-right: env(safe-area-inset-right);
}

/* 移动端专用安全区域处理 */
@import '@/assets/style/theme.less';

@media screen and (max-width: 767px) {
  /* 确保html和body背景色正确 */
  html {
    background: var(--mobile-bg-primary);
    transition: background 0.3s ease;
  }

  body {
    background: var(--mobile-bg-primary);
    transition: background 0.3s ease;

    /* 处理状态栏区域背景 */
    &::before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      height: var(--safe-area-inset-top);
      background: var(--mobile-bg-primary);
      z-index: 9997;
      pointer-events: none;
      transition: background 0.3s ease;
    }
  }

  /* 主应用容器安全区域处理 */
  #app {
    /* 确保应用容器不被安全区域遮挡 */
    padding-top: var(--safe-area-inset-top);
    padding-bottom: var(--safe-area-inset-bottom);
    padding-left: var(--safe-area-inset-left);
    padding-right: var(--safe-area-inset-right);

    /* 调整高度计算 */
    height: calc(var(--vh, 1vh) * 100 - var(--safe-area-inset-top) - var(--safe-area-inset-bottom));
    min-height: calc(
      var(--vh, 1vh) * 100 - var(--safe-area-inset-top) - var(--safe-area-inset-bottom)
    );
  }

  /* 移动端布局组件安全区域处理 */
  .mobile-layout {
    /* 确保布局背景延伸到安全区域 */
    margin-top: calc(-1 * var(--safe-area-inset-top));
    margin-bottom: calc(-1 * var(--safe-area-inset-bottom));
    margin-left: calc(-1 * var(--safe-area-inset-left));
    margin-right: calc(-1 * var(--safe-area-inset-right));

    padding-top: var(--safe-area-inset-top);
    padding-bottom: var(--safe-area-inset-bottom);
    padding-left: var(--safe-area-inset-left);
    padding-right: var(--safe-area-inset-right);
  }

  /* 固定定位元素的安全区域处理 */
  .fixed-top {
    top: var(--safe-area-inset-top);
  }

  .fixed-bottom {
    bottom: var(--safe-area-inset-bottom);
  }

  .fixed-left {
    left: var(--safe-area-inset-left);
  }

  .fixed-right {
    right: var(--safe-area-inset-right);
  }

  /* 菜单容器安全区域处理 */
  .menu-container {
    padding-bottom: var(--safe-area-inset-bottom);
    bottom: 0;
  }

  /* 模态框和抽屉组件安全区域处理 */
  .modal-overlay,
  .drawer-overlay {
    /* 确保覆盖层延伸到安全区域 */
    top: calc(-1 * var(--safe-area-inset-top));
    bottom: calc(-1 * var(--safe-area-inset-bottom));
    left: calc(-1 * var(--safe-area-inset-left));
    right: calc(-1 * var(--safe-area-inset-right));
  }

  /* 加载指示器安全区域处理 */
  #loading-indicator {
    /* 确保加载指示器在安全区域内 */
    top: calc(50% + var(--safe-area-inset-top) / 2);
  }
}

/* iPhone X 及以上设备特殊处理 */
@supports (padding: max(0px)) {
  @media screen and (max-width: 767px) {
    body {
      /* 使用max()确保在没有安全区域的设备上也能正常显示 */
      padding-top: max(var(--safe-area-inset-top), 0px);
      padding-bottom: max(var(--safe-area-inset-bottom), 0px);
      padding-left: max(var(--safe-area-inset-left), 0px);
      padding-right: max(var(--safe-area-inset-right), 0px);
    }
  }
}

/* 横屏模式下的安全区域处理 */
@media screen and (max-width: 767px) and (orientation: landscape) {
  .mobile-layout {
    /* 横屏时调整左右安全区域 */
    padding-left: max(var(--safe-area-inset-left), 16px);
    padding-right: max(var(--safe-area-inset-right), 16px);
  }
}
