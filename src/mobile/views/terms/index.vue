<template>
  <div class="legal-page">
    <div class="header">
      <div class="back-button" @click="router.back()">
        <icon-left />
      </div>
      <h1>Terms of Service</h1>
    </div>

    <div class="content">
      <div class="section">
        <h2>Welcome to {{ appName }}</h2>
        <p>
          Welcome, and thank you for your interest in {{ appName }} Technology Co., Ltd ("{{
            appName
          }}," "we," or "us") and our website at www.{{ appName }}.ai, along with our related
          websites, hosted applications, and other services provided by us (collectively, the
          "Service").
        </p>
        <p>
          These Terms of Service are a legally binding contract between you and
          {{ appName }} regarding your use of the Service.
        </p>
      </div>

      <div class="section notice">
        <h3>Important Notice</h3>
        <p>
          By clicking "I Accept," or by downloading, installing, or otherwise accessing or using the
          Service, you agree that you have read and understood, and, as a condition to your use of
          the Service, you agree to be bound by the following terms and conditions, including
          {{ appName }}'s Privacy Policy.
        </p>
      </div>

      <div class="section">
        <h3>1. Service Overview</h3>
        <p>
          {{ appName }}'s Service allows users to turn unstructured data such as texts, images,
          radio, videos and other modalities of data, information into animations. To make use of
          the Service, you will be required to provide data such as text, image, radio, video to
          describe your input. We will then use our proprietary methodology to turn your input data
          into any expected form of file (Animations) and send the file back to you.
        </p>
      </div>

      <div class="section">
        <h3>2. Eligibility</h3>
        <p>
          You must be at least 18 years old to use the Service. By agreeing to these Terms, you
          represent and warrant to us that:
        </p>
        <ul>
          <li>You are at least 18 years old</li>
          <li>You have not previously been suspended or removed from the Service</li>
          <li>
            Your registration and your use of the Service is in compliance with any and all
            applicable laws and regulations
          </li>
        </ul>
      </div>

      <div class="section">
        <h3>3. Accounts and Registration</h3>
        <p>
          To access most features of the Service, you must register for an account. When you
          register for an account, you may be required to provide us with some information about
          yourself, such as your name, email address, or other contact information.
        </p>
      </div>

      <div class="section">
        <h3>4. Payment Terms</h3>
        <p>
          Certain features of the Service may require you to pay fees. Before you pay any fees, you
          will have an opportunity to review and accept the fees that you will be charged. All fees
          are non-refundable unless otherwise specifically provided for in these Terms.
        </p>
      </div>

      <!-- Add more sections as needed -->

      <div class="section">
        <h3>Contact Us</h3>
        <p>
          If you have any questions about product usage or billing, feel free to reach out to us by
          emailing <EMAIL> or by joining the {{ appName }} Discord server and
          sendingus a message. We'll get back to you as soon as possible.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { IconLeft } from '@arco-design/web-vue/es/icon'
import { computed } from 'vue'

const router = useRouter()
const appName = computed(() => import.meta.env.VITE_APP_NAME || 'Playshot')
</script>

<style lang="less" scoped>
.legal-page {
  height: calc(var(--vh, 1vh) * 100);
  background: #1f0038;
  color: rgba(255, 255, 255, 0.9);
  // padding-bottom: 40px;
}

.header {
  position: sticky;
  top: 0;
  z-index: 100;
  background: #1f0038;
  padding: 16px 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  .back-button {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: rgba(255, 255, 255, 0.6);
    transition: all 0.3s;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;

    &:hover {
      color: white;
      background: rgba(255, 255, 255, 0.15);
    }

    :deep(.arco-icon) {
      font-size: 18px;
    }
  }

  h1 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
  }
}

.content {
  margin: 0 auto;
  padding: 24px 20px;
  background: #1f0038;
  .section {
    margin-bottom: 32px;

    &.notice {
      background: rgba(202, 147, 242, 0.1);
      border-radius: 12px;
      padding: 20px;
      border: 1px solid rgba(202, 147, 242, 0.2);
    }

    h2 {
      font-size: 24px;
      font-weight: 600;
      margin: 0 0 16px;
      color: #ca93f2;
    }

    h3 {
      font-size: 18px;
      font-weight: 600;
      margin: 0 0 12px;
      color: #ca93f2;
    }

    p {
      margin: 0 0 16px;
      line-height: 1.6;
      color: rgba(255, 255, 255, 0.8);
      font-size: 15px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    ul {
      margin: 12px 0;
      padding-left: 20px;

      li {
        margin-bottom: 8px;
        color: rgba(255, 255, 255, 0.8);
        font-size: 15px;
        line-height: 1.6;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
