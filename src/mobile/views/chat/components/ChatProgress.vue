<template>
  <div class="chat-progress">
    <div class="progress-bar">
      <div class="progress-text">
        <span class="text">Complete</span>
        <span class="percentage" :data-text="formattedProgress + '%'">
          {{ formattedProgress }}%
        </span>
      </div>
      <div class="progress-fill">
        <div class="complete" :style="{ width: progress + '%' }"></div>
      </div>
    </div>

    <!-- Progress Detail Drawer -->
    <Teleport to="body">
      <div
        v-if="showProgressDetail"
        class="drawer-container"
        @click.self="showProgressDetail = false"
      >
        <div class="drawer-mask"></div>
        <div class="drawer-content">
          <div class="drawer-header">
            <h3>Story Progress</h3>
            <div class="close-btn" @click="showProgressDetail = false">
              <IconClose />
            </div>
          </div>

          <div class="progress-detail">
            <div class="main-progress">
              <div class="circle-progress">
                <svg viewBox="0 0 100 100">
                  <circle class="progress-bg" cx="50" cy="50" r="45" />
                  <circle
                    class="progress-bar"
                    cx="50"
                    cy="50"
                    r="45"
                    :style="{
                      strokeDasharray: progress * 2.827 + ', 282.7'
                    }"
                  />
                </svg>
                <div class="progress-text">
                  <div class="percentage">{{ formattedProgress }}%</div>
                  <div class="label">Complete</div>
                </div>
              </div>
            </div>

            <div class="progress-stats">
              <div class="stat-item">
                <div class="stat-label">Story Duration</div>
                <div class="stat-value">20 mins</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">Scenes Completed</div>
                <div class="stat-value">3/6</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">Time Spent</div>
                <div class="stat-value">12 mins</div>
              </div>
            </div>

            <div class="progress-tip">
              <IconInfoCircle style="color: #ca93f2" />
              <span>Keep going! You're making great progress in the story.</span>
            </div>
          </div>
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useTransition } from '@vueuse/core'
import { IconClose, IconInfoCircle } from '@arco-design/web-vue/es/icon'

const props = defineProps<{
  value: number // 0-100 之间的进度值
}>()

const showProgressDetail = ref(false)

// 使用 useTransition 创建平滑过渡效果
const progress = useTransition(
  computed(() => props.value),
  {
    duration: 500,
    transition: [0.25, 0.1, 0.25, 1] // 使用 ease 过渡
  }
)

// 格式化进度数字，保留整数
const formattedProgress = computed(() => Math.round(progress.value))
</script>

<style lang="less" scoped>
.chat-progress {
  width: 100%;
  height: 100%;
  .progress-bar {
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.4);
    border-radius: 20px;
    overflow: hidden;
    position: relative;

    .progress-text {
      position: absolute;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 12px;
      z-index: 2;

      .text {
        font-size: 13px;
        font-weight: 600;
        color: #1f0038;
        text-shadow: 0px 1px 2px rgba(0, 0, 0, 0.1);
        transition: color 0.3s ease;
      }

      .percentage {
        font-size: 13px;
        font-weight: 600;
        color: #fff;
        position: relative;
        padding: 4px 8px;
        border-radius: 20px;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        margin-right: 4px;

        &::before {
          content: attr(data-text);
          position: absolute;
          -webkit-text-stroke: 2px #1f0038;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          z-index: -1;
          color: transparent;
          filter: blur(0.3px);
          display: flex;
          align-items: center;
          justify-content: center;
        }

        &::after {
          content: attr(data-text);
          position: absolute;
          -webkit-text-stroke: 2px #1f0038;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          z-index: -2;
          color: transparent;
          filter: blur(1px);
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }

    .progress-fill {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
      z-index: 1;

      .complete {
        height: 100%;
        background: #daff96;
        transition: width 0.5s ease;
        position: relative;
        border-radius: 20px;

        &::after {
          content: '';
          position: absolute;
          top: 0;
          right: 0;
          width: 100%;
          height: 100%;
          background: linear-gradient(
            90deg,
            rgba(255, 255, 255, 0) 0%,
            rgba(255, 255, 255, 0.2) 50%,
            rgba(255, 255, 255, 0) 100%
          );
          animation: shine 2s infinite;
        }
      }
    }
  }
}

@keyframes shine {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.drawer-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  pointer-events: auto;

  .drawer-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.45);
    z-index: -1;
  }

  .drawer-content {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background: #200238;
    border-radius: 16px 16px 0 0;
    padding: 20px;
    transform: translateY(0);
    transition: transform 0.3s ease-out;
    max-height: 90vh;
    overflow-y: auto;
  }
}

.drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;

  h3 {
    color: #fff;
    font-size: 20px;
    font-weight: 600;
    margin: 0;
  }

  .close-btn {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    cursor: pointer;

    &:active {
      background: rgba(255, 255, 255, 0.2);
    }
  }
}

.progress-detail {
  .main-progress {
    display: flex;
    justify-content: center;
    margin-bottom: 32px;

    .circle-progress {
      position: relative;
      width: 200px;
      height: 200px;

      svg {
        transform: rotate(-90deg);

        circle {
          fill: none;
          stroke-width: 8;

          &.progress-bg {
            stroke: rgba(255, 255, 255, 0.1);
          }

          &.progress-bar {
            stroke: #daff96;
            transition: stroke-dasharray 0.5s ease;
          }
        }
      }

      .progress-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        color: #fff;

        .percentage {
          font-size: 40px;
          font-weight: 700;
          margin-bottom: 4px;
        }

        .label {
          font-size: 16px;
          opacity: 0.8;
        }
      }
    }
  }

  .progress-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
    margin-bottom: 24px;

    .stat-item {
      background: rgba(255, 255, 255, 0.05);
      padding: 16px;
      border-radius: 12px;
      text-align: center;

      .stat-label {
        color: rgba(255, 255, 255, 0.6);
        font-size: 12px;
        margin-bottom: 8px;
      }

      .stat-value {
        color: #fff;
        font-size: 16px;
        font-weight: 600;
      }
    }
  }

  .progress-tip {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(202, 147, 242, 0.1);
    padding: 12px 16px;
    border-radius: 12px;
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
  }
}
</style>
