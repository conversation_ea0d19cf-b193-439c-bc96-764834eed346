<template>
  <BaseDrawer
    :visible="visible"
    @update:visible="(val) => emit('update:visible', val)"
    height="auto"
    :mask-color="'rgba(0, 0, 0, 0.7)'"
    background="#1f0038"
    border-radius="24px 24px 0 0"
    padding="24px 16px"
  >
    <template #title>
      <div class="drawer-header">Create avatar to interact with characters</div>
    </template>

    <div class="avatar-select">
      <div class="section-label">Select Your Avatar</div>

      <div class="avatar-scroll-container">
        <div v-if="isLoading" class="avatar-loading">
          <div class="loading-spinner"></div>
          <div class="loading-text">Loading avatars...</div>
        </div>
        <div v-else class="avatar-grid">
          <div
            v-for="(avatar, index) in allAvatars"
            :key="avatar.id"
            class="avatar-item"
            :class="{
              active: selectedIndex === index,
              generating: avatar.type === 'generating',
              failed: avatar.type === 'failed'
            }"
            @click="handleSelectAvatar(avatar, index)"
            @contextmenu.prevent="showPreview(avatar, index)"
            @touchstart="handleTouchStart(avatar, index)"
            @touchend="handleTouchEnd"
            @touchmove="handleTouchMove"
          >
            <template v-if="avatar.url">
              <div class="skeleton-loader" v-show="!imageLoaded[avatar.id]"></div>
              <img
                :src="avatar.url"
                alt="Avatar"
                @load="handleImageLoaded(avatar.id)"
                :style="{ opacity: imageLoaded[avatar.id] ? 1 : 0 }"
              />
              <div class="preview-hint" @click.stop="showPreview(avatar, index)">
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M11 19C15.4183 19 19 15.4183 19 11C19 6.58172 15.4183 3 11 3C6.58172 3 3 6.58172 3 11C3 15.4183 6.58172 19 11 19Z"
                    stroke="white"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M21 21L16.65 16.65"
                    stroke="white"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>
            </template>
            <div v-else-if="avatar.type === 'generating'" class="generating-state">
              <div class="generating-text">{{ avatar.progress || 0 }}%</div>
            </div>
            <div v-else-if="avatar.type === 'failed'" class="failed-state">
              <span>Failed</span>
            </div>
          </div>
        </div>
      </div>

      <div class="section-label section-label-create">Or Create Your Avatar</div>

      <div class="create-avatar-button" @click="showCreateAvatar">
        <img
          src="https://static.playshot.ai/static/images/banner/story_create_avatar_banner.png"
          alt=""
        />
      </div>

      <button class="confirm-button" @click="handleConfirm" ref="confirmButtonRef">Confirm</button>
    </div>
  </BaseDrawer>

  <!-- Preview modal for enlarged avatar -->
  <Teleport to="body">
    <div class="avatar-preview-overlay" v-if="previewAvatar" @click="closePreview">
      <div class="avatar-preview-content" @click.stop>
        <img :src="previewAvatar.url" alt="Avatar Preview" class="preview-image" />
        <div class="preview-actions">
          <button class="select-button" @click="selectPreviewedAvatar">Select</button>
          <button class="close-button" @click="closePreview">Close</button>
        </div>
      </div>
    </div>
  </Teleport>

  <!-- 动态加载CreateDrawer组件，不再使用嵌套的BaseDrawer结构 -->
  <component
    :is="CreateDrawerComponent"
    v-if="CreateDrawerComponent && showUserCharacter"
    v-model:visible="showUserCharacter"
    @created="handleAvatarCreated"
  />
</template>

<script setup lang="ts">
import {
  ref,
  watch,
  computed,
  onMounted,
  shallowRef,
  defineAsyncComponent,
  watchEffect,
  onUnmounted
} from 'vue'
import { animate } from 'motion'
import BaseDrawer from '@/mobile/components/BaseDrawer.vue'
import type { UserAvatar } from '@/api/user-character'
import { reportEvent } from '@/utils'
import { ReportEvent } from '@/interface'
import { useUserAvatarStore } from '@/store/user-character'
import { Message } from '@/mobile/components/Message'
import { useGlobalAvatarPolling } from '@/mobile/composables/useGlobalAvatarPolling'

const CreateDrawerComponent = shallowRef(null)
const avatarStore = useUserAvatarStore()
const isLoading = ref(true)
const imageLoaded = ref<Record<string, boolean>>({})

const enterTimestamp = ref(0)

// 定义头像类型接口
interface PredefinedAvatar {
  id: string
  url: string
  type: 'predefined'
}

interface CustomAvatar {
  id: string
  url: string
  type: 'custom'
  userAvatar: UserAvatar
}

interface GeneratingAvatar {
  id: string
  url: string
  type: 'generating'
  userAvatar: UserAvatar
  progress: number
}

interface FailedAvatar {
  id: string
  url: string
  type: 'failed'
  userAvatar: UserAvatar
}

type AvatarItem = PredefinedAvatar | CustomAvatar | GeneratingAvatar | FailedAvatar

const props = defineProps<{
  visible: boolean
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'select', avatar: { id?: string; url: string; type: 'predefined' | 'custom' }): void
}>()

const showUserCharacter = ref(false)
const selectedIndex = ref<number | null>(null)
const selectedCustomAvatar = ref<UserAvatar | null>(null)
const confirmButtonRef = ref<HTMLButtonElement | null>(null)

const { startPolling } = useGlobalAvatarPolling()

// 所有可选头像，包括预设的和用户的
const allAvatars = computed<AvatarItem[]>(() => {
  // No longer need to check if store exists since we're initializing it directly
  // 获取预定义头像
  const predefinedAvatars: PredefinedAvatar[] = avatarStore.predefinedAvatarUrls.map(
    (url, index) => ({
      id: `predefined-${index}`,
      url,
      type: 'predefined' as const
    })
  )

  // 单次遍历获取所有用户的头像，保持原始顺序
  const userAvatars = avatarStore.avatars
    .filter((avatar) => avatar.style === 'yamato') // 只保留yamato风格的头像
    .map((avatar) => {
      if (avatar.status === 'finish') {
        return {
          id: avatar.id,
          url: avatar.image_urls[0],
          type: 'custom' as const,
          userAvatar: avatar
        } as CustomAvatar
      } else if (avatar.status === 'start' || avatar.status === 'submitted') {
        return {
          id: avatar.id,
          url: '',
          type: 'generating' as const,
          userAvatar: avatar,
          progress: avatarStore.fakeProgress[avatar.id] || 0
        } as GeneratingAvatar
      } else {
        // 'error' 或 'failed' 状态
        return {
          id: avatar.id,
          url: '',
          type: 'failed' as const,
          userAvatar: avatar
        } as FailedAvatar
      }
    })
  // 将用户头像放在预设头像前面
  return [...userAvatars, ...predefinedAvatars]
})

// 处理图片加载完成
const handleImageLoaded = (id: string) => {
  imageLoaded.value[id] = true
}

// 在组件挂载时异步加载依赖
onMounted(async () => {
  enterTimestamp.value = Date.now()
  try {
    reportEvent(ReportEvent.AvatarSelectExposure)
    isLoading.value = true
    // 初始化图片加载状态
    imageLoaded.value = {}
    // 异步加载 CreateDrawer 组件
    CreateDrawerComponent.value = defineAsyncComponent(
      () => import('@/mobile/views/user-character/components/CreateDrawer.vue')
    )

    // 加载用户头像和预定义头像
    await Promise.all([avatarStore.fetchAvatars(), avatarStore.fetchPredefinedAvatars()])
    isLoading.value = false
  } catch (error) {
    console.error('Failed to load dependencies or fetch avatars:', error)
    isLoading.value = false
  }
})

onUnmounted(() => {
  const duration = Date.now() - enterTimestamp.value
  reportEvent(ReportEvent.AvatarSelectDuration, { duration: duration / 1000 })
})

// 监听可见性变化，当显示时刷新头像列表
watch(
  () => props.visible,
  async (newVal) => {
    if (newVal) {
      try {
        isLoading.value = true
        // 重置图片加载状态
        imageLoaded.value = {}
        // 同时刷新用户头像和预定义头像
        await Promise.all([avatarStore.fetchAvatars(), avatarStore.fetchPredefinedAvatars()])
        isLoading.value = false
      } catch (error) {
        console.error('Failed to refresh avatars:', error)
        isLoading.value = false
      }
    }
  }
)

const handleSelectAvatar = (avatar: AvatarItem, index: number) => {
  // 如果是正在生成中的头像，不允许选择
  if (avatar.type === 'generating') {
    Message.error('Avatar is generating or failed, please try again later')
    return
  } else if (avatar.type === 'failed') {
    Message.error('Avatar is failed, please generate again')
    return
  }

  // If previously selected, add a small animation
  if (selectedIndex.value === index) {
    const avatarItems = document.querySelectorAll('.avatar-item')
    if (avatarItems[index]) {
      animate(avatarItems[index], { scale: [1, 1.05, 1] }, { duration: 0.3, easing: 'ease-in-out' })
    }
  }

  selectedIndex.value = index
  selectedCustomAvatar.value = avatar.type !== 'predefined' ? avatar.userAvatar : null

  reportEvent(ReportEvent.AvatarSelectClick, {
    avatarId: avatar.id,
    avatarUrl: avatar.url
  })
}

const showCreateAvatar = () => {
  showUserCharacter.value = true
  reportEvent(ReportEvent.ClickCreateCharacter, {
    action: 'create_ai_avatar'
  })
}

const handleAvatarCreated = async (avatar: UserAvatar) => {
  try {
    reportEvent(ReportEvent.UserAvatarCreated, {
      avatarId: avatar.id,
      avatarUrl: avatar.image_urls[0]
    })
    // 刷新头像列表
    await avatarStore.fetchAvatars()
    startPolling()
    // 找到新创建的头像在数组中的索引
    const index = allAvatars.value.findIndex((item) => item.id === avatar.id)
    if (index !== -1) {
      selectedIndex.value = index
    }

    selectedCustomAvatar.value = avatar
    showUserCharacter.value = false

    reportEvent(ReportEvent.ClickCreateCharacterSuccess, {
      action: 'avatar_created',
      avatarId: avatar.id
    })
  } catch (error) {
    console.error('Failed to handle avatar creation:', error)
  }
}

const handleConfirm = () => {
  if (selectedIndex.value === null && !selectedCustomAvatar.value) {
    // If no avatar selected, highlight the options with an animation
    const avatarGrid = document.querySelector('.avatar-grid')
    if (avatarGrid) {
      animate(avatarGrid, { x: [0, -5, 5, -5, 0] }, { duration: 0.4, easing: 'ease-in-out' })
    }
    return
  }

  // Add button press animation
  if (confirmButtonRef.value) {
    animate(
      confirmButtonRef.value,
      { scale: [1, 0.95, 1] },
      { duration: 0.3, easing: 'ease-in-out' }
    )
  }

  setTimeout(() => {
    if (selectedIndex.value !== null) {
      const selectedAvatar = allAvatars.value[selectedIndex.value]

      // 只有预设头像或完成的自定义头像才能被选择
      if (
        selectedAvatar.type === 'predefined' ||
        (selectedAvatar.type === 'custom' && selectedAvatar.url)
      ) {
        emit('select', {
          id: selectedAvatar.id,
          url: selectedAvatar.url,
          type: selectedAvatar.type === 'predefined' ? 'predefined' : 'custom'
        })
        emit('update:visible', false)
        reportEvent(ReportEvent.PageView, {
          action: 'confirm_avatar',
          avatarType: selectedAvatar.type,
          avatarId: selectedAvatar.id
        })
      }
    }
  }, 150) // Short delay for animation to complete
}

// Add new state for avatar preview
const previewAvatar = ref<AvatarItem | null>(null)
const previewIndex = ref<number | null>(null)
const touchTimer = ref<number | null>(null)

// Show preview of an avatar on long press
const showPreview = (avatar: AvatarItem, index: number) => {
  // Only allow previewing avatars with valid URLs
  if (avatar.url && (avatar.type === 'predefined' || avatar.type === 'custom')) {
    previewAvatar.value = avatar
    previewIndex.value = index
  }
}

// Touch event handlers
const handleTouchStart = (avatar: AvatarItem, index: number) => {
  touchTimer.value = setTimeout(() => showPreview(avatar, index), 500) as unknown as number
}

const handleTouchEnd = () => {
  if (touchTimer.value !== null) {
    clearTimeout(touchTimer.value)
    touchTimer.value = null
  }
}

const handleTouchMove = () => {
  if (touchTimer.value !== null) {
    clearTimeout(touchTimer.value)
    touchTimer.value = null
  }
}

// Close the preview modal
const closePreview = () => {
  previewAvatar.value = null
  previewIndex.value = null
}

// Select the currently previewed avatar
const selectPreviewedAvatar = () => {
  if (previewIndex.value !== null) {
    selectedIndex.value = previewIndex.value
    const avatar = allAvatars.value[previewIndex.value]
    selectedCustomAvatar.value = avatar.type !== 'predefined' ? avatar.userAvatar : null

    // Report the selection
    reportEvent(ReportEvent.AvatarSelectConfirm, {
      avatarId: avatar.id,
      avatarUrl: avatar.url,
      isPredefined: avatar.type === 'predefined'
    })

    closePreview()
  }
}
</script>

<style lang="less" scoped>
.avatar-select {
  padding: 0 16px 32px;
  color: white;
}

.drawer-header {
  font-size: 16px;
  font-weight: 600;
}

.section-label {
  margin: 20px 0 16px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);

  &.section-label-create {
    margin-top: 28px;
  }
}

.avatar-scroll-container {
  width: 100%;
  position: relative;
  overflow: hidden;
}

.avatar-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  min-height: 100px;

  .loading-spinner {
    width: 36px;
    height: 36px;
    border: 3px solid rgba(255, 255, 255, 0.2);
    border-top-color: #ca93f2;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .loading-text {
    margin-top: 10px;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
}

.avatar-grid {
  display: flex;
  overflow-x: auto;
  padding: 4px 0;
  gap: 12px;
  -webkit-overflow-scrolling: touch; /* 提升在 iOS 上的滚动体验 */
  scrollbar-width: none; /* Firefox */

  /* 隐藏滚动条但保持滚动功能 */
  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }

  .avatar-item {
    position: relative;
    flex: 0 0 auto;
    width: 80px;
    aspect-ratio: 1;
    border-radius: 16px;
    overflow: hidden;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.2s ease;

    &.active {
      border-color: #ca93f2;
      box-shadow: 0 0 0 2px rgba(202, 147, 242, 0.5);
    }

    &.generating,
    &.failed {
      cursor: not-allowed;
      opacity: 0.8;
    }

    .skeleton-loader {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        rgba(187, 166, 251, 0.1) 25%,
        rgba(187, 166, 251, 0.2) 50%,
        rgba(187, 166, 251, 0.1) 75%
      );
      background-size: 200% 100%;
      animation: shimmer 1.5s infinite;
      z-index: 1;
    }

    @keyframes shimmer {
      0% {
        background-position: 200% 0;
      }
      100% {
        background-position: -200% 0;
      }
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      object-position: top;
      transition: opacity 0.3s ease;
      position: relative;
      z-index: 2;
    }

    .generating-state {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(187, 166, 251, 0.2);

      .generating-text {
        font-size: 14px;
        color: #fff;
        font-weight: 500;
      }
    }

    .failed-state {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(255, 100, 100, 0.2);

      span {
        font-size: 14px;
        color: #fff;
        font-weight: 500;
      }
    }

    .preview-hint {
      position: absolute;
      bottom: 4px;
      right: 4px;
      background-color: rgba(0, 0, 0, 0.5);
      border-radius: 50%;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 3;
      opacity: 0.8;
      transition: all 0.2s ease;

      &:active {
        transform: scale(0.9);
        background-color: rgba(202, 147, 242, 0.5);
      }
    }
  }
}

.create-avatar-button {
  // background: rgba(187, 166, 251, 0.15);
  border-radius: 16px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 0 40px;
  cursor: pointer;
  overflow: hidden;

  img {
    width: 100%;
    max-height: 120px;
    object-fit: cover;
    display: block;
    border-radius: 16px;
  }

  .plus-icon {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(180deg, #d6cafe 0%, #ca93f2 100%);
    border-radius: 50%;
    font-size: 20px;
    font-weight: 600;
    color: #1f0038;
  }

  span {
    color: white;
    font-size: 15px;
    font-weight: 500;
  }
}

.confirm-button {
  display: flex;
  width: 100%;
  height: 42px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  flex-shrink: 0;
  border-radius: 40px;
  background: #ca93f2;
  color: #241d49;
  font-size: 15px;
  font-weight: 600;
  outline: none;
  border: none;
  &:active {
    transform: scale(0.98);
  }
}

.avatar-preview-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.avatar-preview-content {
  background-color: #1f0038;
  border-radius: 16px;
  overflow: hidden;
  width: 80%;
  max-width: 320px;
  display: flex;
  flex-direction: column;
  animation: zoom-in 0.2s ease;
}

@keyframes zoom-in {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.preview-image {
  width: 100%;
  aspect-ratio: 1;
  object-fit: cover;
  object-position: top;
}

.preview-actions {
  display: flex;
  padding: 16px;
  gap: 12px;

  button {
    flex: 1;
    height: 40px;
    border-radius: 20px;
    border: none;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;

    &.select-button {
      background: linear-gradient(180deg, #d6cafe 0%, #ca93f2 100%);
      color: #241d49;
    }

    &.close-button {
      background: rgba(255, 255, 255, 0.1);
      color: white;
    }
  }
}
</style>
