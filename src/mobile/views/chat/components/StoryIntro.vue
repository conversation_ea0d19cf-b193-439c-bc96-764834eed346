<template>
  <div class="story-intro-container">
    <LoadingOverlay v-if="isLoading" :show-progress="true" :progress="loadingProgress" />
    <!-- <VideoBackground
      v-if="showVideo"
      :video-url="storyStore.currentStory?.preview_video_url"
      :muted="videoMuted"
      :autoplay="true"
      :fade-out-on-end="true"
      :show-volume-button="true"
      :show-skip-button="true"
      @video-ended="handleVideoEnded"
      @video-skipped="handleVideoSkipped"
      @update:muted="handleVideoMuted"
      @video-loaded="handleVideoLoaded"
    /> -->
    <div
      class="story-intro"
      :class="{
        loading: imageLoading
      }"
    >
      <div class="background-image" v-if="storyStore.currentStory?.preview_url">
        <img
          v-img-compress
          :src="storyStore.currentStory.preview_url"
          alt="Story Preview"
          @load="imageLoading = false"
          @error="handleImageError"
        />
      </div>
      <div class="skeleton-loading" v-if="imageLoading"></div>

      <div class="back-button" @click="router.push('/')">
        <icon-left />
      </div>

      <div class="content">
        <h1 class="title" :data-text="storyStore.currentStory?.title">
          {{ storyStore.currentStory?.title }}
        </h1>
        <!-- <div class="cost" v-if="!storyStore.currentStory?.is_purchased">
          <template v-if="storyStore.currentStory?.coins > 0">
            <img src="https://cdn.magiclight.ai/assets/mobile/diamond.png" class="diamond-icon" />
            <span>{{ storyStore.currentStory?.coins }}</span>
          </template>
          <div v-else class="free-label">Free</div>
        </div>
        <div class="cost" v-else>
          <div class="unlocked-label">Unlocked</div>
        </div> -->
        <div class="description">
          <div>Description</div>
          {{ storyStore.currentStory?.description }}
        </div>
      </div>

      <div class="bottom-actions">
        <div class="favorite-button" :class="{ active: isFavorited }" @click="toggleFavorite">
          <div class="heart-icon" ref="favoriteIconRef">
            <span v-if="isDetailLoading" class="heart-loading-spinner"></span>
            <template v-else>
              <icon-favorited v-if="isFavorited" />
              <icon-favorite v-else />
            </template>
          </div>
          <span>{{ isFavorited ? 'Favorited!' : 'Add Favorite' }}</span>
        </div>

        <button class="play-button" @click="handlePlay" :disabled="isPlayLoading">
          <span v-if="isPlayLoading || isDetailLoading" class="loading-spinner"></span>
          <template v-else>
            Play
            <span v-if="showDiamonds" class="cost" :class="{ discount: storyStore.isDiscount }">
              <img src="https://cdn.magiclight.ai/assets/mobile/diamond.png" class="diamond-icon" />
              <template v-if="storyStore.isDiscount">
                <span class="original-price">{{ Math.floor(storyCost * 5) }}</span>
                <span class="discount-price">{{ storyCost }}</span>
              </template>
              <template v-else>
                {{ storyCost }}
              </template>
            </span>
            <span v-else-if="isStorePurchased" class="status unlocked"> Unlocked </span>
            <span v-else-if="isFreeStory" class="status free"> Free </span>
          </template>
        </button>
      </div>

      <AvatarSelect
        v-if="showAvatarSelect"
        v-model:visible="showAvatarSelect"
        @select="handleSelectAvatar"
        @update:visible="(visible) => !visible && (isPlayLoading = false)"
      />

      <CharacterSelect
        v-if="showCharacterSelect"
        v-model:visible="showCharacterSelect"
        :actors="storyStore.currentStory?.actors || []"
        :story-coins="storyStore.currentStory?.coins || 0"
        :story-is-purchased="storyStore.currentStory?.is_purchased || false"
        @select="handleSelectCharacter"
        @update:visible="(visible) => !visible && (isPlayLoading = false)"
      />
      <TalentSelect
        v-if="showTalentSelect"
        v-model:visible="showTalentSelect"
        @confirm="handleConfirmTalentSelect"
        @update:visible="(visible) => !visible && (isPlayLoading = false)"
      />

      <!-- <UploadImage
        :visible="true"
        @upload-success="handleUploadSuccess"
        @upload-error="handleUploadError"
      /> -->

      <ConfirmDialog
        v-model:visible="showDialog"
        content="Your previous progress has been safely saved, you can either continue or start over"
        confirm-text="Continue"
        cancel-text="Restart"
        :show-icon="false"
        :show-cancel="true"
        :close-on-click-overlay="false"
        :content-style="{
          color: '#FFF',
          'font-size': '15px',
          'font-weight': '700'
        }"
        :confirm-button-style="{
          'border-radius': '26px',
          'border-top': '2px solid #1F0038',
          'border-right': '2px solid #1F0038',
          'border-bottom': '6px solid #1F0038',
          'border-left': '2px solid #1F0038',
          background: 'linear-gradient(180deg, #F5FFE2 0%, #DAFF96 100%)',
          'box-shadow': '0px 1.721px 10.324px 0px #DAFF96',
          height: '48px',
          'font-size': '15px',
          'font-weight': '600'
        }"
        :cancel-button-style="{
          'border-radius': '26px',
          'border-top': '2px solid #1F0038',
          'border-right': '2px solid #1F0038',
          'border-bottom': '6px solid #1F0038',
          'border-left': '2px solid #1F0038',
          background: 'linear-gradient(180deg, #D6CAFE 0%, #CA93F2 100%)',
          'box-shadow': '0px 1.855px 11.13px 0px #B098FF',
          color: '#241D49',
          height: '48px',
          'font-size': '15px',
          'font-weight': '600'
        }"
        @confirm="handleContinue"
        @cancel="handleRestart"
        @update:visible="handleDialogClose"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { IconLeft } from '@arco-design/web-vue/es/icon'
import IconFavorited from '@/assets/icon/favorited.svg'
import IconFavorite from '@/assets/icon/favorite.svg'
import { useStoryStore } from '@/store/story'
import { useChatStore } from '@/store/chat'
import { useChatEventsStore } from '@/store/chat-events'
import { Message } from '@/mobile/components/Message'
import CharacterSelect from './CharacterSelect.vue'
import VideoBackground from '@/mobile/components/VideoBackground.vue'
import LoadingOverlay from '@/mobile/components/LoadingOverlay.vue'

import { reportEvent } from '@/utils'
import { ReportEvent } from '@/interface'
import { Actor } from '@/api/stories'
import { animate } from 'motion'
import ConfirmDialog from '@/components/ConfirmDialog.vue'
import { getUserChatHistory } from '@/api/chat'
import TalentSelect from './TalentSelect.vue'
import AvatarSelect from './AvatarSelect.vue'
import { setDynamicSEO } from '@/router/seo-guard'

const router = useRouter()
const route = useRoute()
const storyStore = useStoryStore()
const chatEventsStore = useChatEventsStore()

const isFavorited = ref(false)
const showCharacterSelect = ref(false)
const imageLoading = ref(true)
const isLoading = ref(false)
const videoMuted = ref(false)
const showVideo = ref(false)
const videoLoaded = ref(false)
const favoriteIconRef = ref<HTMLElement | null>(null)
const loadingProgress = ref(0)
let progressInterval: ReturnType<typeof setInterval> | null = null
const showDialog = ref(false)
const selectedActor = ref<Actor | null>(null)
const showTalentSelect = ref(false)
const chatStore = useChatStore()
const showAvatarSelect = ref(false)
const userAvatar = ref<{ id?: string; url: string; type: 'predefined' | 'custom' } | null>(null)
const isPlayLoading = ref(false)
const isDetailLoading = ref(false)

const props = defineProps<{
  previewMode?: boolean
}>()

const emit = defineEmits<{
  // (e: 'select', actor: Actor, restart: boolean): void
  (e: 'loading', loading: boolean): void
  (e: 'select-character', actor: Actor): void
}>()

onMounted(async () => {
  isDetailLoading.value = true
  await storyStore.getStoreDetail(route.params.storyId as string)
  isDetailLoading.value = false
  isFavorited.value = storyStore.currentStory?.is_fav

  // 设置动态SEO
  if (storyStore.currentStory) {
    setDynamicSEO('StoryIntro', { story: storyStore.currentStory })
  }

  reportEvent(ReportEvent.StoryIntroPageView, {
    storyId: storyStore.currentStory?.id
  })
  chatStore.userAvatar = null
  isPlayLoading.value = false // 确保初始化时加载状态为 false
})

const startFakeProgress = () => {
  // 清理之前的interval
  if (progressInterval) {
    clearInterval(progressInterval)
  }

  loadingProgress.value = 0

  // 创建一个渐进的加载进度，在15秒内达到85%
  progressInterval = setInterval(() => {
    if (loadingProgress.value >= 85) return

    // 进度增加速度随进度增加而减慢
    const increment = Math.max(1, Math.floor((85 - loadingProgress.value) / 20))
    loadingProgress.value = Math.min(85, Math.floor(loadingProgress.value + increment))
  }, 100)
}

const handleVideoLoaded = () => {
  // 视频加载完成，快速完成剩余进度
  if (progressInterval) {
    clearInterval(progressInterval)
  }

  // 使用动画快速完成剩余进度
  const finalProgress = () => {
    if (loadingProgress.value >= 100) {
      // 进度达到100%，延迟隐藏loading
      setTimeout(() => {
        videoLoaded.value = true
        isLoading.value = false
      }, 200)
      return
    }

    const remainingProgress = 100 - loadingProgress.value
    const increment = Math.max(1, Math.floor(remainingProgress / 10)) // 确保每次至少增加1%
    loadingProgress.value = Math.min(100, Math.floor(loadingProgress.value + increment))

    // 递归调用直到进度达到100%
    requestAnimationFrame(finalProgress)
  }

  requestAnimationFrame(finalProgress)
}

const handleVideoFinished = () => {
  if (progressInterval) {
    clearInterval(progressInterval)
  }
  showVideo.value = false
  videoLoaded.value = false
  isLoading.value = false

  const actors = storyStore.currentStory?.actors || []
  if (actors.length === 1) {
    isPlayLoading.value = true
    try {
      processSelectedActor(actors[0])
    } finally {
      isPlayLoading.value = false
    }
    return
  }

  showCharacterSelect.value = true
}

const handleVideoEnded = () => {
  handleVideoFinished()
}

const handleVideoSkipped = () => {
  handleVideoFinished()
}

const handleVideoMuted = (muted: boolean) => {
  videoMuted.value = muted
}

const playFavoriteAnimation = () => {
  if (!favoriteIconRef.value) return

  animate(
    favoriteIconRef.value,
    {
      transform: [
        'scale(1) rotate(0deg)',
        'scale(1.4) rotate(-15deg)',
        'scale(0.9) rotate(15deg)',
        'scale(1.1) rotate(0deg)',
        'scale(1) rotate(0deg)'
      ],
      opacity: [0.6, 1]
    },
    {
      duration: 0.8,
      easing: [0.22, 0.68, 0.22, 0.95]
    }
  )
}

const toggleFavorite = async () => {
  reportEvent(ReportEvent.ClickStoryIntroFavorite, {
    storyId: storyStore.currentStory?.id,
    isFavorited: isFavorited.value
  })

  // 乐观更新
  const previousState = isFavorited.value
  isFavorited.value = !previousState

  // 只在添加收藏时播放动画
  if (isFavorited.value) {
    playFavoriteAnimation()
  }

  const setSuccess = await storyStore.toggleFavorite()

  // 如果API调用失败，恢复之前的状态
  if (!setSuccess) {
    isFavorited.value = previousState
  }
}

const getChatHistory = async () => {
  try {
    const { data } = await getUserChatHistory(
      storyStore.currentStory?.id,
      selectedActor.value?.id,
      storyStore.currentStory?.version
    )

    if (data.isOk && data.data.history?.length > 0) {
      chatEventsStore.setShouldRestart(false)
      showDialog.value = true
      showCharacterSelect.value = false
      showTalentSelect.value = false
    } else {
      chatEventsStore.setShouldRestart(true)
    }
  } catch (error) {
    console.error('Failed to check chat history:', error)
    Message.error('Failed to check chat history')
    throw error // Rethrow the error so processSelectedActor can handle it in the finally block
  }
}

const handlePlay = () => {
  const actors = storyStore.currentStory?.actors || []
  reportEvent(ReportEvent.ClickStartToPlayInStoryIntro, {
    storyId: storyStore.currentStory?.id
  })

  if (actors.length === 0 && storyStore.currentStory?.version !== '3') {
    Message.error('No characters available')
    return
  }

  isPlayLoading.value = true

  // 如果是版本3的游戏，直接选择第一个角色并开始游戏
  if (storyStore.currentStory?.version === '3') {
    const actor = {
      id: 'reasoning',
      name: 'Reasoning'
    }
    processSelectedActor(actor as Actor)
    return
  }

  // If there's only one actor, select it automatically
  if (actors.length === 1) {
    processSelectedActor(actors[0])
    return
  }

  // Only show character selection if there are multiple actors
  showCharacterSelect.value = true
  isPlayLoading.value = false
}

// New method to extract common actor processing logic
const processSelectedActor = async (actor: Actor) => {
  selectedActor.value = actor

  await getChatHistory()

  if (!chatEventsStore.isShouldRestart) return
  // 如果需要选择技能，先展示技能选择
  if (storyStore.currentStory?.is_active_skill) {
    showTalentSelect.value = true
    return
  }

  // 如果不需要选择技能但支持面部交换，直接展示头像选择
  if (storyStore.currentStory?.is_support_face_swap) {
    showAvatarSelect.value = true
    return
  }
  // 常规流程，直接进入聊天
  navigateToChat(selectedActor.value)
}

const handleSelectCharacter = async (actor: Actor) => {
  showCharacterSelect.value = false
  isPlayLoading.value = true
  try {
    await processSelectedActor(actor)
  } finally {
    isPlayLoading.value = false
  }
}

const handleConfirmTalentSelect = () => {
  showTalentSelect.value = false

  // 如果支持面部交换，在选择技能后展示头像选择
  if (storyStore.currentStory?.is_support_face_swap) {
    showAvatarSelect.value = true
    return
  }

  // 常规流程，直接进入聊天
  navigateToChat(selectedActor.value)
}

const navigateToChat = async (actor: Actor) => {
  storyStore.setCurrentActor(actor)
  reportEvent(ReportEvent.ClickToPlayInStoryIntro, {
    storyId: storyStore.currentStory?.id,
    actorId: actor.id,
    userAvatarType: userAvatar.value?.type,
    userAvatarId: userAvatar.value?.id
  })
  showCharacterSelect.value = false

  // 如果是预览模式，直接触发选择角色事件
  if (props.previewMode) {
    emit('select-character', actor)
    return
  }

  let ChatType = 'Chat'

  if (storyStore.currentStory?.version === '3') {
    ChatType = 'Chat3'
  } else if (storyStore.currentActor?.version === '4') {
    ChatType = 'Chat4'
  } else if (storyStore.currentActor?.version === '2') {
    ChatType = 'Chat2'
  }

  // 优化：在路由跳转前预热聊天资源
  try {
    // 预热聊天store，提前初始化状态
    if (storyStore.currentStory?.version === '2' || storyStore.currentStory?.version === '3') {
      const chatEventsStore = useChatEventsStore()
      // 预设基本信息，减少初始化时间
      chatEventsStore.currentActorId = actor.id
    }

    // 预加载关键资源（角色预览图等）
    if (actor.preview_url) {
      const img = new Image()
      img.src = actor.preview_url
    }
  } catch (error) {
    console.warn('预热聊天资源失败:', error)
  }

  router.push({
    name: ChatType,
    params: {
      storyId: storyStore.currentStory?.id,
      actorId: actor.id
    }
  })
}

const handleContinue = () => {
  showDialog.value = false

  if (!selectedActor.value) return

  chatEventsStore.setShouldRestart(false)
  navigateToChat(selectedActor.value)
}

const handleRestart = () => {
  if (!selectedActor.value) {
    showDialog.value = false
    return
  }

  chatEventsStore.setShouldRestart(true)
  showDialog.value = false

  if (storyStore.currentStory?.is_active_skill) {
    showTalentSelect.value = true
    return
  }

  if (storyStore.currentStory?.is_support_face_swap) {
    showAvatarSelect.value = true
    return
  }

  navigateToChat(selectedActor.value)
}

const handleDialogClose = (visible: boolean) => {
  if (visible) return

  showDialog.value = false
  isPlayLoading.value = false // 重置加载状态

  // 对话框关闭且有选择的角色时，判断是否需要显示角色选择界面
  if (!selectedActor.value) return

  const actors = storyStore.currentStory?.actors || []
  if (actors.length > 1) {
    showCharacterSelect.value = true
  }
}

const handleImageError = () => {
  imageLoading.value = false
  Message.error('Failed to load preview image')
}

const handleSelectAvatar = (avatar: {
  id?: string
  url: string
  type: 'predefined' | 'custom'
}) => {
  userAvatar.value = avatar
  reportEvent(ReportEvent.PageView, {
    action: 'select_user_avatar',
    avatarType: avatar.type,
    avatarId: avatar.id
  })

  // 如果是支持面部交换的流程，选择完头像后直接进入聊天
  if (storyStore.currentStory?.is_support_face_swap && selectedActor.value) {
    chatStore.userAvatar = avatar
    navigateToChat(selectedActor.value)
    return
  }

  // 如果有视频，播放视频
  // if (storyStore.currentStory?.preview_video_url) {
  //   isLoading.value = true
  //   showVideo.value = true
  //   startFakeProgress() // Start simulating loading progress
  //   return
  // }

  // 没有视频，检查是否应该显示角色选择
  const actors = storyStore.currentStory?.actors || []
  if (actors.length === 1) {
    isPlayLoading.value = true
    try {
      processSelectedActor(actors[0])
    } finally {
      isPlayLoading.value = false
    }
    return
  }

  // 多角色情况，显示角色选择
  showCharacterSelect.value = true
}

// 组件卸载时清理interval和状态
onUnmounted(() => {
  if (progressInterval) {
    clearInterval(progressInterval)
  }
  isPlayLoading.value = false // 确保组件卸载时重置加载状态
})

// Add computed properties for cleaner templates
const storyCost = computed(() => {
  if (!storyStore.currentStory) return 0
  return storyStore.currentStory.coins || 0
})

const isStorePurchased = computed(() => {
  return storyStore.currentStory?.is_purchased || false
})

const isFreeStory = computed(() => {
  return !isStorePurchased.value && storyCost.value === 0
})

const showDiamonds = computed(() => {
  return !isStorePurchased.value && storyCost.value > 0
})

// 监听所有弹窗的关闭状态，确保重置加载状态
watch(
  [showCharacterSelect, showAvatarSelect, showTalentSelect, showDialog],
  ([characterSelect, avatarSelect, talentSelect, dialog]) => {
    // 如果所有弹窗都关闭了，确保加载状态被重置
    if (!characterSelect && !avatarSelect && !talentSelect && !dialog) {
      isPlayLoading.value = false
    }
  }
)
</script>

<style lang="less" scoped>
:deep(.video-background) {
  z-index: 9999;
}
.story-intro-container {
  position: relative;
  height: calc(var(--vh, 1vh) * 100);
}

.story-intro {
  height: calc(var(--vh, 1vh) * 100);
  background: #1f0038;
  color: white;
  padding: 20px 16px;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  opacity: 1;
  transition: opacity 0.6s ease;

  &.transparent {
    opacity: 0;
  }

  &.loading {
    .background-image img {
      opacity: 0;
    }
  }
}

.background-image {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 0;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0;
    transition: opacity 0.6s ease;
  }

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      180deg,
      rgba(31, 0, 56, 0) 0%,
      rgba(31, 0, 56, 0.5) 50%,
      rgba(31, 0, 56, 0.8) 100%
    );
    z-index: 1;
  }
}

.story-intro:not(.loading) {
  .background-image img {
    opacity: 1;
  }
}

.skeleton-loading {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: #1f0038;
  overflow: hidden;
  z-index: 0;

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: -150%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.08), transparent);
    animation: shimmer 1.5s infinite;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(250%);
  }
}

.back-button {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgb(255, 255, 255);
  border-radius: 50%;
  margin-bottom: 24px;
  cursor: pointer;
  position: relative;
  z-index: 1;
  svg {
    stroke: #000;
  }
  &:active {
    background: rgba(255, 255, 255, 0.2);
  }
}

.content {
  margin-top: auto;
  margin-bottom: 32px;
  position: relative;
  z-index: 1;
}

.title {
  font-size: 32px;
  font-weight: 800;
  // font-style: italic;
  margin: 0 0 16px;
  line-height: 1.2;
  position: relative;
  color: #fff;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  &::before {
    content: attr(data-text);
    position: absolute;
    -webkit-text-stroke: 4px #1f0038;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    color: transparent;
    filter: blur(0.3px);
  }

  // 添加第二层描边来增加平滑度
  &::after {
    content: attr(data-text);
    position: absolute;
    -webkit-text-stroke: 6px #1f0038;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
    color: transparent;
    filter: blur(1px);
  }
}

.cost {
  display: flex;
  align-items: center;
  gap: 4px;
  background: #1f0038;
  padding: 4px 12px;
  border-radius: 34px;
  font-weight: 600;
  font-size: 14px;
  color: #daff96;
  white-space: nowrap;
  width: fit-content;
  // margin-bottom: 16px;
  border: 1px solid #daff96;
  .diamond-icon {
    width: 20px;
    height: 20px;
  }
}

.description {
  font-size: 16px;
  line-height: 1.5;
  color: rgba(255, 255, 255, 0.8);
}

.bottom-actions {
  position: relative;
  z-index: 1;
}

.favorite-button {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 16px;
  cursor: pointer;
  user-select: none;
  -webkit-tap-highlight-color: transparent;

  .heart-icon {
    transform-origin: center;
    will-change: transform;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    .heart-loading-spinner {
      width: 56px;
      height: 56px;
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      border-top-color: #fff;
      animation: spin 0.8s linear infinite;
    }
  }

  &:active {
    .heart-icon {
      transform: scale(0.95);
    }
  }
}

.play-button {
  width: 100%;
  border-radius: 40px;
  background: #ca93f2;
  border: none;
  color: #241d49;
  font-size: 15px;
  font-weight: 600;
  padding: 14px;
  cursor: pointer;
  transition: transform 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }

  &:active:not(:disabled) {
    transform: scale(0.98);
  }

  .loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(36, 29, 73, 0.3);
    border-radius: 50%;
    border-top-color: #241d49;
    animation: spin 0.8s linear infinite;
  }

  .cost {
    display: flex;
    align-items: center;
    gap: 4px;
    background: rgba(36, 29, 73, 0.2);
    padding: 4px 10px;
    border-radius: 16px;
    margin-left: 4px;
    position: relative;

    .diamond-icon {
      width: 16px;
      height: 16px;
    }

    &.discount {
      background: rgba(36, 29, 73, 0.2);
      border: 1px solid rgba(202, 147, 242, 0.3);
      padding: 4px 10px;
      position: relative;
      overflow: visible;
      margin-left: 4px;

      // 右上角折扣标签
      &::after {
        content: '-80%';
        position: absolute;
        top: -10px;
        right: -10px;
        background: #ff3b30;
        color: white;
        font-size: 9px;
        font-weight: bold;
        padding: 2px 5px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        transform: rotate(0);
        z-index: 1;
        animation: pulse 1.5s infinite;
      }

      .original-price {
        text-decoration: line-through;
        opacity: 0.6;
        font-size: 13px;
        position: relative;
        margin-right: 5px;
      }

      .discount-price {
        color: #ffeb3b;
        font-weight: bold;
        text-shadow: 0 0 2px rgba(0, 0, 0, 0.2);
        font-size: 13px;
        position: relative;
        z-index: 2; // 确保折扣价格始终显示在最上层
      }
    }
  }

  .status {
    padding: 4px 10px;
    border-radius: 16px;
    font-size: 13px;
    margin-left: 4px;

    &.unlocked {
      background: rgba(36, 29, 73, 0.2);
      color: #ffffff;
    }

    &.free {
      background: rgba(36, 29, 73, 0.2);
      color: #daff96;
    }
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}
</style>
