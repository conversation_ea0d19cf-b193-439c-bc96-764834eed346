<template>
  <div class="system-message">
    <div class="system-message-content" :class="{ loading }">
      <template v-if="loading">
        <div class="dots">
          <span class="dot"></span>
          <span class="dot"></span>
          <span class="dot"></span>
        </div>
      </template>
      <template v-else>
        {{ message.content.text }}
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  message: {
    content: {
      text: string
    }
  }
  loading?: boolean
}>()
</script>

<style lang="less" scoped>
.system-message {
  display: flex;
  justify-content: center;
  margin: 8px 0;
  width: 100%;

  .system-message-content {
    background-color: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.6);
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 13px;
    max-width: 80%;
    text-align: center;
    backdrop-filter: blur(10px);
    min-width: 60px;

    &.loading {
      padding: 8px;
    }

    .dots {
      display: flex;
      gap: 4px;
      justify-content: center;
    }

    .dot {
      width: 6px;
      height: 6px;
      background: rgba(255, 255, 255, 0.6);
      border-radius: 50%;
      opacity: 0.6;
      animation: bounce 1.4s infinite;

      &:nth-child(2) {
        animation-delay: 0.2s;
      }

      &:nth-child(3) {
        animation-delay: 0.4s;
      }
    }
  }
}

@keyframes bounce {
  0%,
  60%,
  100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-4px);
  }
}
</style>
