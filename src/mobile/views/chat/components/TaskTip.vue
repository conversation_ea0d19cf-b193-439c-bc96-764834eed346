<template>
  <div class="task-tip-wrapper">
    <div class="progress-circle" :style="{ '--progress': progress }">
      <div class="progress-circle-bg"></div>
      <div class="progress-circle-value"></div>
    </div>

    <div class="task-tip" :class="{ collapsed }" @click="toggleCollapse">
      <div class="tip-content" ref="contentRef">
        <div class="tip-left">
          <div class="tip-icon">
            <IconChatTaskBar class="detail-progress-circle" ref="progressCircleRef" />
            <IconChatTaskBarBg class="bg-circle" ref="bgCircleRef" />
            <div class="progress-text" ref="levelTextRef">
              <div class="task-label" :data-text="currentLevel">{{ currentLevel }}</div>
              <div class="task-progress" :data-text="`${progress}%`">{{ progress }}%</div>
            </div>
          </div>
        </div>
        <div class="tip-right" ref="tipRightRef">
          <div class="tip-text" :class="{ expanded: !collapsed }">
            <span v-html="currentTask.description"></span>
          </div>
          <div class="progress-bar">
            <div class="progress-track" ref="progressTrackRef" @click.stop>
              <div class="progress-text">{{ currentLevel }}</div>
              <div
                class="progress-value"
                ref="progressValueRef"
                :style="{ width: `${progress}%` }"
              ></div>
              <div class="progress-percent">{{ progress }}%</div>
            </div>
          </div>
        </div>
        <button class="close-button" v-show="!collapsed" @click.stop="toggleCollapse">
          <!-- <img
            :src="`https://cdn.magiclight.ai/assets/playshot/${collapsed ? 'expand' : 'close'}.png`"
            :alt="collapsed ? 'expand' : 'close'"
          /> -->
          <img src="https://cdn.magiclight.ai/assets/playshot/close.png" alt="close" />
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch, nextTick } from 'vue'
import { animate } from 'motion'
import { useChatStore } from '@/store/chat'
import IconChatTaskBar from '@/assets/icon/chat-task-bar.svg'
import IconChatTaskBarBg from '@/assets/icon/chat-task-bar-bg.svg'

const chatStore = useChatStore()

// 获取当前任务数据和索引
const currentTaskData = computed(() => {
  if (!chatStore.currentTaskId) return { task: { percent: 0, description: '' }, index: 0 }
  const index = chatStore.tasks.findIndex((task) => task.task_id === chatStore.currentTaskId)
  return {
    task: index === -1 ? { percent: 0, description: '' } : chatStore.tasks[index],
    index: index === -1 ? 0 : index
  }
})

const currentTask = computed(() => currentTaskData.value.task)
const currentTaskIndex = computed(() => currentTaskData.value.index)

// 进度值
const progress = computed(() => currentTask.value.percent)

// 当前等级
const currentLevel = computed(() => `Level ${currentTaskIndex.value + 1}`)

const collapsed = ref(false)
const contentRef = ref<HTMLElement | null>(null)
const tipRightRef = ref<HTMLElement | null>(null)
const progressCircleRef = ref<SVGElement | null>(null)
const progressPathRef = ref<SVGPathElement | null>(null)
const progressValueRef = ref<HTMLElement | null>(null)
const progressTrackRef = ref<HTMLElement | null>(null)
const levelTextRef = ref<HTMLElement | null>(null)
const bgCircleRef = ref<SVGElement | null>(null)

// 添加进度条悬停效果
let progressAnimationId: number | null = null

const animateToProgress = (targetProgress: number) => {
  const step = () => {
    const diff = targetProgress - progress.value
    if (Math.abs(diff) < 0.5) {
      progressAnimationId = null
      return
    }
    progressAnimationId = requestAnimationFrame(step)
  }
  progressAnimationId = requestAnimationFrame(step)
}

const handleProgressHover = (e: MouseEvent) => {
  if (!progressTrackRef.value || collapsed.value) return

  const rect = progressTrackRef.value.getBoundingClientRect()
  const x = e.clientX - rect.left
  const width = rect.width
  const percentage = Math.min(100, Math.max(0, Math.round((x / width) * 100)))

  if (progressAnimationId) {
    cancelAnimationFrame(progressAnimationId)
  }

  animateToProgress(percentage)
}

const toggleCollapse = () => {
  collapsed.value = !collapsed.value

  if (
    contentRef.value &&
    tipRightRef.value &&
    progressCircleRef.value &&
    levelTextRef.value &&
    bgCircleRef.value
  ) {
    if (collapsed.value) {
      // 收缩动画序列
      animate(
        contentRef.value,
        {
          width: '64px',
          borderRadius: ['12px', '32px']
        },
        {
          duration: 0.5,
          easing: [0.22, 0.03, 0.26, 1]
        }
      )

      animate(
        tipRightRef.value,
        {
          opacity: [1, 0],
          scale: [1, 0.95],
          x: [0, -10]
        },
        {
          duration: 0.4,
          easing: 'ease-out'
        }
      )

      // 显示背景圆环和进度环
      animate(
        bgCircleRef.value,
        {
          opacity: [0, 1],
          scale: [0.5, 1],
          rotate: -90
        },
        {
          delay: 0.3,
          duration: 0.4,
          easing: [0.22, 0.03, 0.26, 1]
        }
      )

      animate(
        '.progress-circle',
        {
          opacity: [0, 1],
          scale: [0.5, 1]
        },
        {
          delay: 0.3,
          duration: 0.4,
          easing: [0.22, 0.03, 0.26, 1]
        }
      )

      animate(
        levelTextRef.value,
        {
          opacity: [0, 1],
          scale: [0.8, 1]
        },
        {
          delay: 0.4,
          duration: 0.4,
          easing: 'ease-out'
        }
      )
    } else {
      // 展开动画序列
      animate(
        bgCircleRef.value,
        {
          opacity: [1, 0],
          scale: [1, 0.5],
          rotate: [-90, 0]
        },
        {
          duration: 0.4,
          easing: 'ease-in'
        }
      )

      animate(
        '.progress-circle',
        {
          opacity: [1, 0],
          scale: [1, 0.5]
        },
        {
          duration: 0.4,
          easing: 'ease-in'
        }
      )

      animate(
        levelTextRef.value,
        {
          opacity: [1, 0]
        },
        {
          duration: 0.4,
          easing: 'ease-in'
        }
      )

      animate(
        contentRef.value,
        {
          width: '100%',
          borderRadius: ['32px', '12px']
        },
        {
          delay: 0.2,
          duration: 0.5,
          easing: [0.22, 0.03, 0.26, 1]
        }
      )

      animate(
        tipRightRef.value,
        {
          opacity: [0, 1],
          scale: [0.95, 1],
          x: [-10, 0]
        },
        {
          delay: 0.3,
          duration: 0.5,
          easing: [0.22, 0.03, 0.26, 1]
        }
      )
    }
  }
}

// 监听等级变化
watch(
  () => currentTaskIndex.value,
  (newIndex, oldIndex) => {
    // 只在等级更新时展开
    if (newIndex !== oldIndex && oldIndex !== undefined) {
      nextTick(() => {
        if (collapsed.value) {
          toggleCollapse()
        }
      })
    }
  }
)

// 监听进度变化
watch(
  [() => currentTaskIndex.value, () => progress.value],
  ([taskIndex, newProgress], [oldTaskIndex, oldProgress]) => {
    // 跳过首次触发和初始的0到1的变化
    if (oldProgress === undefined || (oldProgress === 0 && newProgress === 1)) {
      return
    }

    // 等待进度条更新动画完成后再触发收缩
    const handleCollapse = () => {
      // 进度到达100%时收缩
      if (newProgress === 100 && !collapsed.value) {
        toggleCollapse()
        return
      }

      // 同一等级且进度发生变化时收缩（不包括100%的情况）
      if (
        taskIndex === oldTaskIndex &&
        newProgress !== oldProgress &&
        newProgress !== 100 &&
        !collapsed.value
      ) {
        toggleCollapse()
      }
    }

    // 先等待 DOM 更新
    nextTick(() => {
      // 给进度条动画 300ms 的时间完成
      setTimeout(handleCollapse, 600)
    })
  }
)

onMounted(() => {
  // 确保初始状态为展开
  nextTick(() => {
    if (contentRef.value) {
      contentRef.value.style.width = '100%'
      contentRef.value.style.borderRadius = '12px'
    }
    if (tipRightRef.value) {
      tipRightRef.value.style.opacity = '1'
      tipRightRef.value.style.transform = 'scale(1) translateX(0)'
    }
  })

  if (progressTrackRef.value) {
    progressTrackRef.value.addEventListener('mousemove', handleProgressHover)
    progressTrackRef.value.addEventListener('mouseleave', () => {
      if (progressAnimationId) {
        cancelAnimationFrame(progressAnimationId)
      }
      animateToProgress(50)
    })
  }
})
</script>

<style lang="less" scoped>
.task-tip-wrapper {
  width: 100%;
  min-height: 64px;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  pointer-events: none;

  .progress-circle {
    position: absolute;
    top: -4px;
    left: 12px;
    width: 72px;
    height: 72px;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;

    &::before {
      content: '';
      position: absolute;
      inset: 0;
      border-radius: 50%;
      background: conic-gradient(
        #daff96 calc(var(--progress) * 1%),
        rgba(255, 255, 255, 0.2) calc(var(--progress) * 1%)
      );
      mask: radial-gradient(farthest-side, transparent calc(100% - 6px), #000 calc(100% - 6px));
      -webkit-mask: radial-gradient(
        farthest-side,
        transparent calc(100% - 6px),
        #000 calc(100% - 6px)
      );
    }
  }
}

// 通用样式
.progress-text-style {
  color: #daff96;
  font-size: 12px;
  white-space: nowrap;
}

.task-tip {
  position: relative;
  width: calc(100% - 32px);
  max-width: 400px;
  margin: 0 auto;
  padding: 4px 8px;
  border-radius: 30px;
  background: #74637d;
  backdrop-filter: blur(10px);
  pointer-events: auto;
  box-sizing: border-box;
  cursor: default;
  overflow: hidden;

  &.collapsed {
    position: absolute;
    left: 0;
    cursor: pointer;
    width: 64px;
    height: 64px;
    padding: 0;
    border-radius: 50%;
    margin: 0;
    margin-left: 16px;

    .tip-content {
      width: 64px;
      height: 64px;
      padding: 0;
    }

    .tip-right {
      opacity: 0;
      pointer-events: none;
    }

    .tip-icon {
      width: 64px;
      height: 64px;
      padding: 0;

      svg:first-child {
        display: none;
      }
      .bg-circle {
        opacity: 1;
        transform: rotate(-90deg);
      }

      .progress-text {
        opacity: 1;
      }
    }

    .close-button {
      opacity: 0;
      pointer-events: none;
      visibility: hidden;
      transition: none;
    }
  }

  .tip-content {
    display: flex;
    align-items: center;
    gap: 4px;
    position: relative;
    padding-right: 20px;
    height: 64px;
    width: 100%;
    transform-origin: center;
  }

  .tip-left {
    flex-shrink: 0;
  }

  .tip-right {
    flex: 1;
    min-width: 0;
    display: flex;
    flex-direction: column;
    gap: 8px;
    will-change: transform, opacity, clip-path;
    transform-origin: left center;
    overflow: hidden;

    .tip-text {
      color: #daff96;
      font-size: 12px;
      font-weight: 600;
      line-height: 1.2;
      transform-origin: left center;
      transition: all 0.3s ease;
      max-height: 200px;
      overflow: hidden;

      span {
        display: block;
        transition: transform 0.3s ease;
      }
    }

    .progress-bar {
      width: 100%;
      transform-origin: left center;

      .progress-track {
        position: relative;
        width: 100%;
        height: 15px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        overflow: hidden;
        cursor: pointer;
        display: flex;
        align-items: center;
        padding: 0 12px;

        .progress-text {
          font-size: 11px;
          font-weight: 600;
          z-index: 2;
        }

        .progress-value {
          position: absolute;
          left: 0;
          top: 0;
          height: 100%;
          background: #daff96;
          border-radius: 12px;
          transition: width 0.3s ease;
          z-index: 1;
        }

        .progress-percent {
          font-size: 11px;
          font-weight: 700;
          margin-left: auto;
          z-index: 2;
        }
      }
    }
  }

  .tip-icon {
    width: 32px;
    height: 32px;
    flex-shrink: 0;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;

    .bg-circle {
      position: absolute;
      inset: 0;
      margin: auto;
      width: 80%;
      height: 80%;
      opacity: 0;
      z-index: 1;
    }

    .detail-progress-circle {
      width: 32px;
      height: 32px;
    }

    .progress-circle {
      position: absolute;
      inset: -4px;
      margin: auto;
      width: calc(100% + 8px);
      height: calc(100% + 8px);
      opacity: 0;
      z-index: 2;
      transform-origin: center;
      transform: rotate(-90deg);
      pointer-events: none;

      path {
        transform-origin: center;
      }

      .progress-circle-value {
        transition: stroke-dasharray 0.3s ease;
      }
    }

    .progress-text {
      position: absolute;
      inset: 0;
      margin: auto;
      height: fit-content;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 2px;
      opacity: 0;
      z-index: 4;
      text-align: center;
      pointer-events: none;

      .task-label,
      .task-progress {
        position: relative;
        color: #ffffff;
        font-size: 14px;
        font-weight: 600;
        line-height: 1;

        &::before,
        &::after {
          content: attr(data-text);
          position: absolute;
          left: 0;
          z-index: -1;
          width: 100%;
          text-align: center;
          font-weight: 600;
          -webkit-text-stroke: 2px #000000;
        }

        &::before {
          top: 0.5px;
        }

        &::after {
          top: -0.5px;
        }
      }
    }
  }

  .close-button {
    position: absolute;
    right: -4px;
    top: 50%;
    transform: translateY(-50%);
    width: 24px;
    height: 24px;
    padding: 0;
    border: none;
    background: none;
    cursor: pointer;
    transition: opacity 0.3s ease;
    will-change: opacity;

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }

  &:not(.collapsed) .close-button {
    opacity: 1;
    pointer-events: auto;
    transition-delay: 0.6s;
  }
}
</style>
