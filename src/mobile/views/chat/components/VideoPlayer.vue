<template>
  <div class="video-player">
    <!-- Loading Overlay -->
    <LoadingOverlay
      v-show="(isLoading && !chatStore.isFirstVideo) || forceLoading"
      :progress="loadingProgress"
      :show-progress="true"
      :background-image="loadingBackground"
    />

    <!-- Play Button Overlay -->
    <div
      v-if="needsPlayButton || chatStore.isFirstVideo || (needsPlayButton && isPlaying)"
      class="play-button-overlay"
    >
      <button class="play-button" @click="handlePlayClick">
        <icon-play-circle-fill class="play-icon" />
        <span> Click to Play</span>
      </button>
    </div>

    <!-- Video Canvas -->
    <canvas v-show="isPlaying" ref="canvasRef" class="background-video" />

    <!-- Hidden Video Element -->
    <video
      ref="videoRef"
      class="hidden-video"
      @ended="onVideoEnded"
      @loadedmetadata="onVideoLoaded"
      @canplay="onVideoCanPlay"
      webkit-playsinline="true"
      playsinline="true"
      :muted="muted"
      x5-playsinline="true"
      x5-video-player-type="h5"
      x5-video-player-fullscreen="true"
      autoplay="true"
    />
    <!-- todo: above preload will cause autoplay to fail, need to investigate -->
    <!-- Video Controls -->
    <div v-if="isPlaying" class="video-controls">
      <button v-if="!isLoading" class="volume-button" @click="toggleSound">
        <icon-volume-notice v-if="!muted" />
        <icon-volume-mute v-else />
      </button>
      <button class="skip-button" @click="skipVideo">Skip</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onBeforeUnmount, computed } from 'vue'
import IconVolumeNotice from '@/assets/icon/volume-notice.svg'
import IconVolumeMute from '@/assets/icon/volume-mute.svg'
import IconPlayCircleFill from '@/assets/icon/play-circle-fill.svg'
import LoadingOverlay from '@/mobile/components/LoadingOverlay.vue'
import { useVideoCanvas } from '@/mobile/composables/useVideoCanvas'
import { useChatStore, useStoryStore } from '@/store'
import { reportEvent } from '@/utils'
import { ReportEvent } from '@/interface'
import { useLocalStorage } from '@vueuse/core'

const chatStore = useChatStore()
const storyStore = useStoryStore()

const props = defineProps<{
  videoUrl: string | null
  isPlaying: boolean
  muted: boolean
  storyId: string
  actorId: string
  loadingBackground?: string
  forceLoading?: boolean
}>()

const emit = defineEmits<{
  'update:isPlaying': [value: boolean]
  'update:muted': [value: boolean]
  'video-ended': []
  'video-loaded': []
  'video-skipped': []
}>()

const { canvasRef, videoRef, clearVideo, startVideo, stopVideo, needsPlayButton } = useVideoCanvas()

const isLoading = ref(false)
const loadingProgress = ref(0)
const playedVideos = useLocalStorage<Set<string>>('played_videos', new Set())
const skipRequestedDuringLoading = ref(false)
const loadingStartTime = ref<number>(0)
const currentXHR = ref<XMLHttpRequest | null>(null)
const loadingBackground = computed(() => props.loadingBackground)

// Video Event Handlers
const onVideoEnded = () => {
  isLoading.value = false
  emit('update:isPlaying', false)

  const video = videoRef.value
  if (!video || !props.videoUrl) return

  const videoName = props.videoUrl.split('/').pop() || 'unknown'
  const isFirstPlay = !playedVideos.value.has(videoName)

  // record played video
  playedVideos.value.add(videoName)

  // report video completed event
  reportEvent(ReportEvent.VideoCompleted, {
    videoName,
    duration: video.duration ? `${video.duration.toFixed(2)}s` : 'unknown',
    playedTime: video.currentTime ? `${video.currentTime.toFixed(2)}s` : '0s',
    isFirstPlay,
    storyId: props.storyId,
    actorId: props.actorId
  })
  emit('video-ended')
  clearVideo()
}

const onVideoLoaded = () => {
  const video = videoRef.value
  if (!video) return

  video.muted = props.muted

  if (video.readyState >= 2) {
    video
      .play()
      .then(() => {
        needsPlayButton.value = false
        emit('video-loaded')
      })
      .catch((error) => {
        console.log('Autoplay prevented:', error)
        needsPlayButton.value = true
      })
  }
}

const onVideoCanPlay = () => {
  emit('update:isPlaying', true)
}

// Control Functions
const toggleSound = () => {
  if (!videoRef.value) return
  reportEvent(ReportEvent.ClickVolume, {
    storyId: props.storyId,
    actorId: props.actorId,
    isMuted: props.muted
  })
  const newMutedState = !props.muted
  videoRef.value.muted = newMutedState
  emit('update:muted', newMutedState)
}

const skipVideo = async () => {
  // if there is a current XHR request, abort it immediately
  if (currentXHR.value) {
    currentXHR.value.abort()
    currentXHR.value = null
  }

  const video = videoRef.value
  if (!video) {
    console.warn('Video element not found')
    return
  }

  if (!props.videoUrl && !video.src) {
    console.warn('Neither props.videoUrl nor video.src available', {
      propsVideoUrl: props.videoUrl,
      videoSrc: video.src
    })
  }

  const videoName = props.videoUrl
    ? props.videoUrl.split('/').pop() || 'unknown'
    : video.src.split('/').pop() || 'unknown'

  const isFirstPlay = !playedVideos.value.has(videoName)
  playedVideos.value.add(videoName)

  const reportSkipped = (playedTime = '0s', duration = 'unknown') => {
    reportEvent(ReportEvent.VideoSkipped, {
      videoName,
      duration,
      playedTime,
      isFirstPlay,
      storyId: props.storyId,
      actorId: props.actorId
    })
  }

  try {
    // handle loading state
    if (isLoading.value) {
      skipRequestedDuringLoading.value = true
      isLoading.value = false
      await stopVideo()
      clearVideo()
      emit('update:isPlaying', false)
      emit('video-skipped')
      needsPlayButton.value = false
      chatStore.isFirstVideo = false
      reportSkipped()
      return
    }

    // handle video skip in normal state
    const playedTime = video.currentTime ? `${video.currentTime.toFixed(2)}s` : '0s'
    const duration = video.duration ? `${video.duration.toFixed(2)}s` : 'unknown'
    reportSkipped(playedTime, duration)

    // full cleanup process
    try {
      video.pause()
      video.currentTime = 0
      video.removeAttribute('src')
      video.load()
    } catch (e) {
      console.warn('Error pausing video:', e)
    }

    // remove all event listeners
    const events = ['ended', 'canplay', 'loadedmetadata', 'canplaythrough', 'play', 'playing']
    events.forEach((event) => {
      video.removeEventListener(event, () => {})
    })

    // stop and cleanup video
    await stopVideo()
    clearVideo()

    // reset all states
    emit('update:isPlaying', false)
    emit('video-skipped')
    needsPlayButton.value = false
    chatStore.isFirstVideo = false
    isLoading.value = false
    loadingProgress.value = 0
    currentVideoUrl.value = null

    // cleanup video source
    try {
      video.removeAttribute('src')
      video.load()
    } catch (e) {
      console.warn('Error clearing video source:', e)
    }
  } catch (error) {
    console.error('Error in skipVideo:', error)
    // even if error occurs, ensure skip event is triggered
    emit('video-skipped')
  }
}

// Add new play button handler
const handlePlayClick = async () => {
  reportEvent(ReportEvent.ClickToPlay, {
    storyId: props.storyId,
    actorId: props.actorId
  })
  const video = videoRef.value
  if (!video) return

  try {
    needsPlayButton.value = false
    chatStore.isFirstVideo = false
    await video.play()
    emit('update:isPlaying', true)
  } catch (error) {
    console.error('Play failed:', error)
    // needsPlayButton.value = true
  }
}

// Watch videoUrl changes
const currentVideoUrl = ref<string | null>(null)

watch(
  () => props.videoUrl,
  async (newUrl, oldUrl) => {
    if (newUrl) {
      currentVideoUrl.value = newUrl
    }

    if (oldUrl) {
      const video = videoRef.value
      if (video) {
        try {
          video.pause()
          video.currentTime = 0
          await stopVideo()
          clearVideo()
          video.removeAttribute('src')
          video.load()
          currentVideoUrl.value = null
        } catch (e) {
          console.warn('Error cleaning up old video:', e)
        }
      }
    }

    if (newUrl) {
      skipRequestedDuringLoading.value = false
      loadingStartTime.value = Date.now()
      try {
        // if there is a current XHR request, abort it immediately
        if (currentXHR.value) {
          currentXHR.value.abort()
        }

        isLoading.value = true
        loadingProgress.value = 0
        // check if there is a preloaded Blob
        const preloadedBlob = storyStore.getPreloadedBlob(newUrl)
        if (preloadedBlob && !skipRequestedDuringLoading.value) {
          const videoObjectUrl = URL.createObjectURL(preloadedBlob)
          // set video source and start loading
          const video = videoRef.value
          if (!video) return
          startVideo(videoObjectUrl)
          isLoading.value = false
          return
        }

        // create XMLHttpRequest to track loading progress
        const xhr = new XMLHttpRequest()
        currentXHR.value = xhr // save current XHR request
        xhr.open('GET', newUrl, true)
        xhr.responseType = 'blob'

        xhr.onprogress = (event) => {
          if (event.lengthComputable) {
            loadingProgress.value = Math.round((event.loaded / event.total) * 100)
          }
        }

        xhr.onload = async () => {
          currentXHR.value = null // clear reference after request is completed
          if (xhr.status === 200) {
            // if skip requested during loading, do not continue with video processing
            if (skipRequestedDuringLoading.value) {
              skipRequestedDuringLoading.value = false
              isLoading.value = false
              emit('video-skipped')
              return
            }

            const videoBlob = xhr.response
            const videoObjectUrl = URL.createObjectURL(videoBlob)

            // wait for video to load to smooth play
            await new Promise<void>((resolve) => {
              const video = videoRef.value
              if (!video) return resolve()

              // listen to video buffer progress
              const checkBuffer = () => {
                if (!video.buffered.length) return false

                // check if enough data is buffered (e.g. first 5 seconds)
                const bufferedEnd = video.buffered.end(0)
                const minBufferTime = Math.min(5, video.duration)
                return bufferedEnd >= minBufferTime
              }

              // set video source and start loading
              video.src = videoObjectUrl
              video.load()

              const bufferCheck = setInterval(() => {
                if (checkBuffer()) {
                  clearInterval(bufferCheck)
                  resolve()
                }
              }, 100)

              // if video is very short or loads quickly, canplaythrough event can also be used to determine
              video.oncanplaythrough = () => {
                clearInterval(bufferCheck)
                resolve()
              }
            })

            // calculate and report loading duration after video is successfully loaded
            const loadingDuration = ((Date.now() - loadingStartTime.value) / 1000).toFixed(2)
            reportEvent(ReportEvent.VideoLoaded, {
              videoName: newUrl.split('/').pop() || 'unknown',
              loadingDuration: `${loadingDuration}s`,
              storyId: props.storyId,
              actorId: props.actorId
            })

            // video is ready to play, start playing and end loading state
            await startVideo(videoObjectUrl)
            isLoading.value = false
          }
        }

        xhr.onerror = () => {
          currentXHR.value = null // clear reference even if error occurs
          console.error('Video loading failed')
          isLoading.value = false
          // report loading failure
          const loadingDuration = ((Date.now() - loadingStartTime.value) / 1000).toFixed(2)
          reportEvent(ReportEvent.VideoLoadFailed, {
            videoName: newUrl.split('/').pop() || 'unknown',
            loadingDuration: `${loadingDuration}s`,
            storyId: props.storyId,
            actorId: props.actorId
          })
        }

        xhr.onabort = () => {
          console.log('Video loading aborted')
          currentXHR.value = null
          isLoading.value = false
        }

        xhr.send()
      } catch (error) {
        currentXHR.value = null
        console.error('Error loading video:', error)
        isLoading.value = false
      }
    }
  }
)

// Cleanup
onBeforeUnmount(() => {
  if (currentXHR.value) {
    currentXHR.value.abort()
    currentXHR.value = null
  }
  stopVideo()
})
</script>

<style lang="less" scoped>
.video-player {
  position: relative;
  width: 100%;
  height: 100%;
}

.background-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 0;
  background: #000;
}

.hidden-video {
  position: absolute;
  width: 1px;
  height: 1px;
  opacity: 0;
  pointer-events: none;
}

.video-controls {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1001;
  display: flex;
  gap: 12px;
  align-items: center;
}

.volume-button {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  transition: all 0.2s ease;
  backdrop-filter: blur(4px);
  border: none;
}

.skip-button {
  height: 50px;
  padding: 0 20px;
  border-radius: 25px;
  background: rgba(255, 255, 255, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  cursor: pointer;
  color: #1f0038;
  font-size: 14px;
  transition: all 0.2s ease;
  backdrop-filter: blur(4px);
  border: none;
}

.play-button-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  backdrop-filter: blur(2px);
}

.play-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 32px;
  border-radius: 24px;
  background: transparent;
  border: none;
  cursor: pointer;
  color: white;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(8px);

  &:hover {
    transform: scale(1.02);
  }

  &:active {
    transform: scale(0.98);
  }

  .play-icon {
    width: 64px;
    height: 64px;
    opacity: 0.9;
    filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.2));
  }

  span {
    font-size: 16px;
    font-weight: 500;
    letter-spacing: 1px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
}
</style>
