<template>
  <div class="message user" :data-message-id="message.id">
    <div class="message-wrapper">
      <div class="name-container">
        <span class="sender-name">{{ message.sender?.name }}</span>
      </div>
      <div :class="['message-content', { 'message-content--image': message.msg_type === 'image' }]">
        <div
          v-if="message.msg_type === 'text'"
          class="content"
          @click="$emit('message-click', $event)"
        >
          <span v-html="message.content.text"></span>
        </div>
        <img
          v-else-if="message.msg_type === 'image'"
          class="image"
          :src="message.content?.media_url"
          alt="image"
          @load="$emit('scroll-to-bottom')"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  message: any
}>()

defineEmits<{
  (e: 'message-click', event: MouseEvent): void
  (e: 'scroll-to-bottom'): void
}>()
</script>

<style lang="less" scoped>
.message {
  display: flex;
  gap: 12px;
  align-self: flex-end;
  justify-content: flex-end;
  width: 100%;
  .message-wrapper {
    width: fit-content;
    display: flex;
    flex-direction: column;
    gap: 4px;
    align-items: flex-end;

    .name-container {
      position: relative;
      top: 16px;
      display: flex;
      align-items: center;
      gap: 4px;
      width: 100%;
      justify-content: flex-end;
      border-radius: 20px;
      border: 1px solid #a386ba;
      background: linear-gradient(149deg, #7f6f8c 8.67%, #754f93 45.07%, #5b1b7e 82.31%);
      box-shadow:
        -1px -3.25px 9.075px 0px #826097 inset,
        0.75px 3px 7.7px 0px #a268c5 inset;
      width: fit-content;
      padding: 3px 12px 4px 12px;
    }

    .sender-name {
      color: #fff;
      font-feature-settings:
        'liga' off,
        'clig' off;
      text-shadow: 0px 0px 4px rgba(0, 0, 0, 0.5);
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
    }
  }

  .message-content {
    width: 100%;
    padding: 12px 16px;
    border-radius: 15px 0px 15px 15px;
    border: 2px solid #754f93;
    display: flex;
    background: rgba(76, 51, 96, 0.9);
    user-select: all;
    -webkit-user-select: all;
    box-shadow: 0px 4px 0px 0px rgba(133, 117, 146, 0.25);

    &--image {
      padding: 24px 16px;

      .image {
        max-height: 200px;
        width: auto;
        border-radius: 8px;
      }
    }

    .content {
      width: fit-content;
      align-self: flex-end;
      font-size: 14px;
      line-height: 1.4;
      color: #fff;
      font-weight: 400;
      word-wrap: break-word;
      white-space: pre-wrap;
      overflow-wrap: break-word;
    }
  }
}
</style>
