<template>
  <BaseDrawer v-model:visible="isVisible" height="90vh">
    <div class="talent-title">
      Select Your Talent
      <button v-if="!isGuideActive" class="help-button" @click="handleStartTutorial">?</button>
    </div>
    <div class="talent-select">
      <div v-if="isLoading" class="loading-container">
        <div class="loading-spinner"></div>
        <div class="loading-text">Loading talents...</div>
      </div>
      <div v-else class="carousel-container">
        <div class="carousel-wrapper">
          <div
            class="carousel"
            ref="carouselRef"
            @touchstart="handleTouchStart"
            @touchmove="handleTouchMove"
            @touchend="handleTouchEnd"
          >
            <div
              v-for="(skill, index) in displayedSkills"
              :key="skill.id"
              class="skill-card"
              :class="{
                selected: isSelected(skill.id),
                active: currentSkillIndex === index,
                'prev-card': index === currentSkillIndex - 1,
                'next-card': index === currentSkillIndex + 1,
                'far-card': Math.abs(index - currentSkillIndex) > 1
              }"
              @click="selectSkill(skill, index, $event)"
            >
              <div class="skill-image">
                <div class="skeleton-loader" v-if="!isImageLoaded(skill.id)"></div>
                <img
                  v-img-compress
                  :src="skill.image_url"
                  :alt="skill.name"
                  @load="markImageLoaded(skill.id)"
                />
                <div
                  v-if="isSelected(skill.id) && pointsModifier !== 0"
                  class="points-badge"
                  :class="{ negative: pointsModifier < 0, positive: pointsModifier > 0 }"
                >
                  {{ pointsModifier > 0 ? '+' : '' }}{{ pointsModifier }} Points
                </div>
                <!-- Add particles container for selected active card -->
                <div
                  v-if="isSelected(skill.id) && currentSkillIndex === index"
                  class="particles-container"
                ></div>
              </div>
              <!-- <div
                class="checkmark"
                v-if="isSelected(skill.id) && currentSkillIndex === index"
              ></div> -->
            </div>

            <!-- Touch navigation areas -->
            <div
              class="touch-nav left"
              v-if="currentSkillIndex > 0"
              @click.stop="navigateCarousel(-1)"
            ></div>
            <div
              class="touch-nav right"
              v-if="currentSkillIndex < displayedSkills.length - 1"
              @click.stop="navigateCarousel(1)"
            ></div>
          </div>
        </div>

        <div class="refresh-section" v-if="refreshCount < 2">
          <button class="refresh-btn" @click="handleRefresh">
            <RefreshIcon />
            refresh ({{ 2 - refreshCount }})
          </button>
        </div>
      </div>

      <div class="points-info" :class="{ warning: showPointsWarning }">
        <div class="total-points">
          Available Points:
          <span class="points-value"
            >{{ userPoints - skillStore.usedUserPoints }}/{{ userPoints }}</span
          >
          <span
            v-if="pointsModifier !== 0"
            class="points-modifier"
            :class="{ negative: pointsModifier < 0, positive: pointsModifier > 0 }"
          >
            ({{ pointsModifier > 0 ? '+' : '' }}{{ pointsModifier }})
          </span>
        </div>
        <div class="points-warning" :class="{ show: showPointsWarning }">
          Please allocate all available points!
        </div>
      </div>
      <div class="attributes-section">
        <div class="attribute-grid">
          <div
            v-for="(value, attr) in skillStore.totalAttributes"
            :key="attr"
            class="attribute-card"
          >
            <div class="attribute-name">{{ attr }}</div>
            <div class="attribute-controls">
              <button
                class="control-btn minus"
                :class="{ disabled: skillStore.userAllocatedPoints[attr] <= 0 }"
                @click="skillStore.decreaseAttribute(attr)"
              >
                -
              </button>
              <div
                class="attribute-value"
                :class="{ 'talent-boosted': skillStore.initialAttributes[attr] > 0 }"
              >
                {{ value }}
              </div>
              <button
                class="control-btn plus"
                :class="{
                  disabled: skillStore.usedUserPoints >= userPoints || value >= maxAttributePoints
                }"
                @click="skillStore.increaseAttribute(attr)"
              >
                +
              </button>
            </div>
          </div>
        </div>
      </div>

      <button
        :class="{
          'confirm-btn': true,
          disabled: !skillStore.selectedSkills.length || skillStore.usedUserPoints < userPoints,
          'show-warning': showPointsWarning
        }"
        @click="handleConfirm"
        :data-warning="userPoints - skillStore.usedUserPoints + ' points remaining'"
      >
        Confirm
      </button>
    </div>
  </BaseDrawer>
</template>

<script setup lang="ts">
import { onMounted, ref, watch, computed, onUnmounted } from 'vue'
import { useSkillStore } from '@/store/skill'
import type { SkillInfo } from '@/interface/skill'
import BaseDrawer from '@/mobile/components/BaseDrawer.vue'
import { animate } from 'motion'
import RefreshIcon from '@/assets/icon/refresh.svg'
import { reportEvent } from '@/utils'
import { ReportEvent } from '@/interface'
import { useStoryStore } from '@/store'
import { useRouter } from 'vue-router'
import { useDebounceFn } from '@vueuse/core'
import { Message } from '@/mobile/components/Message'
import { useTalentGuide } from '@/composables/useTalentGuide'
import '@/assets/style/talent-guide.less'

const storyStore = useStoryStore()
const router = useRouter()

const isVisible = defineModel<boolean>('visible')
const emit = defineEmits<{
  (e: 'confirm'): void
}>()

const skillStore = useSkillStore()
const refreshCount = ref(0)
const displayedSkills = ref<SkillInfo[]>([])
const currentSkillIndex = ref(0)
const maxAttributePoints = 10 // 每个属性的最大值
const userPoints = computed(() => skillStore.totalAssignablePoints) // 使用store中的动态可分配点数
const carouselRef = ref<HTMLElement | null>(null)
const showPointsWarning = ref(false)
const loadedImages = ref<Set<string>>(new Set())
const isLoading = ref(true)

// 计算技能对可分配点数的影响
const pointsModifier = computed(() => {
  // 默认值是20，所以影响值是当前值减去20
  return skillStore.totalAssignablePoints - 20
})

// Watch for changes to currentSkillIndex and animate the carousel
watch(currentSkillIndex, (newIndex) => {
  if (carouselRef.value) {
    const cards = Array.from(carouselRef.value.querySelectorAll('.skill-card'))

    cards.forEach((card, index) => {
      const offset = index - newIndex
      const zIndex = 10 - Math.abs(offset)

      // Use a small delay to ensure proper z-index stacking
      setTimeout(() => {
        animate(
          card as HTMLElement,
          {
            zIndex: zIndex
          },
          { duration: 0.01 }
        )
      }, 50)
    })
  }
})

const getRandomSkills = (skills: SkillInfo[], count: number) => {
  return [...skills].sort(() => Math.random() - 0.5).slice(0, count)
}

const updateDisplayedSkills = () => {
  displayedSkills.value = getRandomSkills(skillStore.skillList, 4)
  // 上报每个技能的曝光
  displayedSkills.value.forEach((skill) => {
    reportEvent(ReportEvent.SkillExposure, {
      storyId: storyStore.currentStory?.id,
      actorId: storyStore.currentActor?.id,
      skillId: skill.id,
      skillName: skill.name
    })
  })
}

// Add function to create particles for the selected card
const createParticles = (container: HTMLElement, count = 20) => {
  // Clear existing particles
  container.innerHTML = ''

  for (let i = 0; i < count; i++) {
    const particle = document.createElement('div')
    particle.className = 'particle'

    // Random position
    const x = Math.random() * 100
    const y = Math.random() * 100

    // Random animation duration and delay
    const duration = 1.5 + Math.random() * 2
    const delay = Math.random() * 2

    // Set particle style
    particle.style.left = `${x}%`
    particle.style.top = `${y}%`

    // Add particle to container
    container.appendChild(particle)

    // Animate particle
    animate(
      particle,
      {
        opacity: [0, 0.7, 0],
        y: ['0%', `-${20 + Math.random() * 30}%`],
        x: [`0%`, `${(Math.random() - 0.5) * 30}%`],
        scale: [0.2, 0.8, 0.2]
      },
      {
        duration,
        delay,
        easing: 'ease-out',
        repeat: Infinity
      }
    )
  }
}

// Watch for changes to currentSkillIndex to update particles
watch(currentSkillIndex, () => {
  // Wait for DOM update
  setTimeout(() => {
    const container = document.querySelector('.active.selected .particles-container')
    if (container) {
      createParticles(container as HTMLElement)
    }
  }, 100)
})

// Add guide functionality
const showTutorial = ref(false)
const { startTour, isGuideActive } = useTalentGuide({
  onComplete: () => {
    // Report tutorial completion
    reportEvent(ReportEvent.CompleteTutorial, {
      tutorialName: 'talent-selection',
      storyId: storyStore.currentStory?.id,
      actorId: storyStore.currentActor?.id
    })
  },
  onSkip: () => {
    // Report tutorial skipped
    reportEvent(ReportEvent.SkipTutorial, {
      tutorialName: 'talent-selection',
      storyId: storyStore.currentStory?.id,
      actorId: storyStore.currentActor?.id
    })
  }
})

// Detect if this is the first time using the skill selection
const isFirstTimeUser = computed(() => {
  try {
    return localStorage.getItem('talent-guide-seen') !== 'true'
  } catch (e) {
    return false
  }
})

// Add manual tutorial start button if needed
const handleStartTutorial = () => {
  startTour()
  // Report tutorial started manually
  reportEvent(ReportEvent.StartTutorial, {
    tutorialName: 'talent-selection',
    storyId: storyStore.currentStory?.id,
    actorId: storyStore.currentActor?.id
  })
}

// Update the onMounted hook to handle guide initialization
onMounted(async () => {
  skillStore.reset()
  reportEvent(ReportEvent.OpenSkillSelect, {
    storyId: storyStore.currentStory?.id,
    actorId: storyStore.currentActor?.id
  })

  try {
    isLoading.value = true
    await skillStore.fetchSkillList(storyStore.currentStory.id)
    updateDisplayedSkills()
    // Report skill display
    reportEvent(ReportEvent.DisplaySkills, {
      storyId: storyStore.currentStory?.id,
      actorId: storyStore.currentActor?.id,
      skillCount: displayedSkills.value.length
    })

    // Auto-select the first skill
    if (displayedSkills.value.length > 0) {
      const firstSkill = displayedSkills.value[0]
      skillStore.selectSkill(firstSkill)
    }

    // Show tutorial for first-time users (auto-handled by useTalentGuide)
    if (isFirstTimeUser.value) {
      showTutorial.value = true
      // Report tutorial shown automatically
      reportEvent(ReportEvent.ShowTutorial, {
        tutorialName: 'talent-selection',
        storyId: storyStore.currentStory?.id,
        actorId: storyStore.currentActor?.id
      })
    }
  } catch (error) {
    console.error('Failed to fetch skill list:', error)
    // Consider adding error handling UI here
  } finally {
    isLoading.value = false
  }
})

// Update the touch event handler functions with complete swipe detection
let touchStartX = 0
let touchEndX = 0
const minSwipeDistance = 50 // Minimum distance for a swipe to be detected

const handleTouchStart = (e: TouchEvent) => {
  touchStartX = e.touches[0].clientX
}

const handleTouchMove = (e: TouchEvent) => {
  // Prevent scrolling during horizontal swipes
  const currentX = e.touches[0].clientX
  const deltaX = Math.abs(currentX - touchStartX)

  if (deltaX > 30) {
    e.preventDefault()
  }

  // Cancel tutorial during user interaction if needed
  if (isGuideActive.value && deltaX > 50) {
    // User is actively swiping, let them take control
    const carouselElement = carouselRef.value
    if (carouselElement) {
      carouselElement.classList.add('user-interacting')
    }
  }
}

const handleTouchEnd = (e: TouchEvent) => {
  touchEndX = e.changedTouches[0].clientX

  // Reset user interaction state
  const carouselElement = carouselRef.value
  if (carouselElement) {
    carouselElement.classList.remove('user-interacting')
  }

  handleSwipe()
}

const handleSwipe = () => {
  const swipeDistance = touchEndX - touchStartX

  // Only process significant swipes
  if (Math.abs(swipeDistance) < minSwipeDistance) return

  if (swipeDistance > 0 && currentSkillIndex.value > 0) {
    // Swipe right - go to previous card
    navigateCarousel(-1)
  } else if (swipeDistance < 0 && currentSkillIndex.value < displayedSkills.value.length - 1) {
    // Swipe left - go to next card
    navigateCarousel(1)
  }
}

// Create a debounced version of navigateCarousel
const debouncedNavigate = useDebounceFn((direction: number) => {
  const newIndex = currentSkillIndex.value + direction
  if (newIndex >= 0 && newIndex < displayedSkills.value.length) {
    currentSkillIndex.value = newIndex

    // Auto-select the skill after navigation
    const skill = displayedSkills.value[newIndex]
    if (skill) {
      skillStore.selectSkill(skill)

      // Report the selection
      reportEvent(ReportEvent.SelectSkill, {
        storyId: storyStore.currentStory?.id,
        actorId: storyStore.currentActor?.id,
        skillId: skill.id,
        skillName: skill.name
      })
    }
  }
}, 300) // 300ms debounce time

// Replace the navigateCarousel function with the debounced version
const navigateCarousel = (direction: number) => {
  debouncedNavigate(direction)
}

// Improve the handleRefresh function to properly reset the carousel
const handleRefresh = () => {
  if (refreshCount.value < 2) {
    refreshCount.value++

    // First animate the carousel fade out
    const carousel = document.querySelector('.carousel')
    if (carousel) {
      animate(
        carousel,
        {
          opacity: [1, 0.2],
          scale: [1, 0.95]
        },
        {
          duration: 0.3,
          easing: 'ease-in-out'
        }
      ).finished.then(() => {
        // Then update the skills and reset the index
        updateDisplayedSkills()
        currentSkillIndex.value = 0

        // Auto-select the first skill
        if (displayedSkills.value.length > 0) {
          const firstSkill = displayedSkills.value[0]
          skillStore.selectSkill(firstSkill)
        }

        // Then animate the carousel fade in
        animate(
          carousel,
          {
            opacity: [0.2, 1],
            scale: [0.95, 1]
          },
          {
            duration: 0.3,
            easing: 'ease-in-out'
          }
        )
      })
    }

    reportEvent(ReportEvent.RefreshSkill, {
      storyId: storyStore.currentStory?.id,
      actorId: storyStore.currentActor?.id,
      skillCount: displayedSkills.value.length
    })
  }
}

const isSelected = (skillId: string) => {
  return skillStore.selectedSkills.some((skill) => skill.id === skillId)
}

const createRippleEffect = (card: HTMLElement) => {
  const ripple = document.createElement('div')
  ripple.className = 'ripple'
  card.appendChild(ripple)

  animate(
    ripple,
    {
      scale: [0, 1.5],
      opacity: [0.8, 0]
    },
    {
      duration: 0.8,
      easing: 'ease-out'
    }
  ).finished.then(() => ripple.remove())
}

const selectSkill = (skill: SkillInfo, index: number, event: Event) => {
  // If we're already on this index, don't do anything
  if (currentSkillIndex.value === index) {
    return
  }

  const card = event.currentTarget as HTMLElement
  currentSkillIndex.value = index

  // 立即更新状态
  skillStore.selectSkill(skill)

  // 上报技能选择
  reportEvent(ReportEvent.SelectSkill, {
    storyId: storyStore.currentStory?.id,
    actorId: storyStore.currentActor?.id,
    skillId: skill.id,
    skillName: skill.name
  })

  // 创建能量波纹效果
  createRippleEffect(card)
}

const handleConfirm = () => {
  if (!skillStore.selectedSkills.length || skillStore.usedUserPoints < userPoints.value) {
    // Show warning animation and message
    showPointsWarning.value = true

    // Shake the confirm button
    const confirmBtn = document.querySelector('.confirm-btn')
    if (confirmBtn) {
      animate(
        confirmBtn as HTMLElement,
        { x: [-5, 5, -5, 5, -5, 5, -3, 3, -2, 2, 0] },
        { duration: 0.5, easing: 'ease-in-out' }
      )
    }

    // Highlight remaining points with pulse animation
    const pointsInfo = document.querySelector('.points-info')
    if (pointsInfo) {
      animate(
        pointsInfo as HTMLElement,
        {
          scale: [1, 1.05, 1],
          backgroundColor: ['transparent', 'rgba(202, 147, 242, 0.2)', 'transparent']
        },
        { duration: 0.7, easing: 'ease-in-out' }
      )
    }

    // Highlight attribute grid to draw attention
    const attributeGrid = document.querySelector('.attribute-grid')
    if (attributeGrid) {
      // Sequence of animations to highlight the attribute grid
      animate(
        attributeGrid as HTMLElement,
        {
          boxShadow: [
            '0 0 0 0 rgba(202, 147, 242, 0)',
            '0 0 10px 2px rgba(218, 255, 150, 0.5)',
            '0 0 0 0 rgba(202, 147, 242, 0)'
          ]
        },
        { duration: 1, easing: 'ease-in-out' }
      )

      // Highlight individual attribute cards that can still receive points
      // const attributeCards = document.querySelectorAll('.attribute-card')
      // attributeCards.forEach((card, index) => {
      //   const plusBtn = card.querySelector('.control-btn.plus')
      //   if (plusBtn && !plusBtn.classList.contains('disabled')) {
      //     setTimeout(() => {
      //       animate(
      //         card as HTMLElement,
      //         {
      //           backgroundColor: [
      //             'rgba(31, 0, 56, 0.6)',
      //             'rgba(202, 147, 242, 0.3)',
      //             'rgba(31, 0, 56, 0.6)'
      //           ]
      //         },
      //         { duration: 0.5, easing: 'ease-in-out', delay: index * 0.1 }
      //       )
      //     }, 200)
      //   }
      // })
    }

    // Display more prominent message
    // Message.error('Please allocate all available points')

    // Auto-hide warning after 3 seconds
    setTimeout(() => {
      showPointsWarning.value = false
    }, 3000)

    return
  }

  const storyId =
    storyStore.currentStory?.id ||
    (router.currentRoute.value.params.storyId as string) ||
    'stepsister1'
  const actorId = storyStore.currentActor?.id

  if (actorId) {
    reportEvent(ReportEvent.UserAllocateAttribute, {
      storyId: storyId,
      actorId: actorId,
      gameConfig: {
        skill_id: skillStore.selectedSkills[0]?.id,
        numerical_values: skillStore.userAllocatedPoints
      }
    })
  }

  emit('confirm')
  isVisible.value = false
}

const isImageLoaded = (skillId: string) => {
  return loadedImages.value.has(skillId)
}

const markImageLoaded = (skillId: string) => {
  loadedImages.value.add(skillId)
}
</script>

<style lang="less" scoped>
.talent-title {
  position: relative;
  font-size: 15px;
  font-weight: 600;
  color: #fff;
  text-align: center;
  margin-bottom: 16px;
}
.talent-select {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  color: #fff;
  padding: 0 16px 24px;
  overflow-x: hidden;
}

.carousel-container {
  // margin-bottom: 16px;
  // flex: 1;
}

.carousel-wrapper {
  position: relative;
  perspective: 1000px;
  margin: 0 auto;
}

.carousel {
  position: relative;
  height: 400px;
  transform-style: preserve-3d;
  user-select: none;
  touch-action: pan-y;

  &.user-interacting {
    // Subtle visual indicator that user is taking control
    animation: brief-glow 0.5s ease-in-out;
  }
}

.skill-card {
  position: absolute;
  width: 85%;
  max-width: 300px;
  height: 420px;
  left: 50%;
  top: 0;
  border-radius: 16px;
  overflow: hidden;
  transform-origin: center center;
  transition: all 0.5s cubic-bezier(0.23, 1, 0.32, 1);
  cursor: pointer;
  will-change: transform, opacity, filter;

  &.active {
    z-index: 10;
    transform: translateX(-50%) scale(1) translateZ(0);
  }

  &.selected:not(.active) {
    .skill-image::after {
      content: '';
      position: absolute;
      inset: 0;
      background: radial-gradient(
        circle at center,
        rgba(218, 255, 150, 0.2) 0%,
        rgba(218, 255, 150, 0.1) 30%,
        transparent 70%
      );
      pointer-events: none;
    }
  }

  &.prev-card {
    z-index: 9;
    transform: translateX(-120%) scale(0.85) rotateY(25deg) translateZ(-50px);
    filter: brightness(0.7);
    pointer-events: auto;
  }

  &.next-card {
    z-index: 9;
    transform: translateX(20%) scale(0.85) rotateY(-25deg) translateZ(-50px);
    filter: brightness(0.7);
    pointer-events: auto;
  }

  &.far-card {
    z-index: 8;
    opacity: 0;
    transform: translateX(-50%) scale(0.7) translateZ(-100px);
    pointer-events: none;
  }

  .skill-image {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
    border-radius: 16px;
    background: transparent;

    .skeleton-loader {
      position: absolute;
      inset: 0;
      background: linear-gradient(
        90deg,
        rgba(202, 147, 242, 0.1) 25%,
        rgba(202, 147, 242, 0.2) 37%,
        rgba(202, 147, 242, 0.1) 63%
      );
      background-size: 400% 100%;
      animation: skeleton-loading 1.4s ease infinite;
      z-index: 1;
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
      display: block;
      position: relative;
      z-index: 2;
    }

    .points-badge {
      position: absolute;
      top: 16px;
      right: 16px;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 700;
      background: rgba(31, 0, 56, 0.8);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
      z-index: 20;

      &.negative {
        color: #ff6b6b;
        border: 1px solid #ff6b6b;
      }

      &.positive {
        color: #daff96;
        border: 1px solid #daff96;
      }
    }
  }

  .ripple {
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(128, 90, 213, 0.4) 0%, rgba(128, 90, 213, 0) 70%);
    width: 200%;
    height: 200%;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    pointer-events: none;
  }
}

.refresh-section {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}

.refresh-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
  background: transparent;
  border: 1px solid transparent;
  padding: 8px 24px;
  border-radius: 20px;
  font-size: 14px;
  color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #ca93f2;
  font-size: 13px;
  font-weight: 700;
  :deep(svg) {
    width: 13px;
    height: 13px;
  }
}

.attribute-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.attribute-card {
  background: rgba(31, 0, 56, 0.6);
  border-radius: 12px;
  padding: 8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.attribute-name {
  font-size: 16px;
  font-weight: 600;
  text-transform: capitalize;
  text-align: center;
}

.attribute-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  border-radius: 39px;
  background: rgba(202, 147, 242, 0.2);
}

.control-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  color: #1f0038;
  font-size: 20px;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 33.333px;
  border-top: 0.833px solid #1f0038;

  border-right: 0.833px solid #1f0038;

  border-bottom: 0.83px solid #1f0038;

  border-left: 0.833px solid #1f0038;

  background: linear-gradient(180deg, #d6cafe 0%, #ca93f2 100%);

  &:active:not(.disabled) {
    transform: scale(0.95);
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.attribute-value {
  font-size: 14px;
  font-weight: 700;
  color: #daff96;
  text-align: center;
  min-width: 32px;

  &.talent-boosted {
    color: #daff96;
  }
}

.points-info {
  margin-top: 16px;
  text-align: center;
  padding: 8px;
  border-radius: 12px;
  transition: all 0.3s ease;
  position: relative;

  &.warning {
    background-color: rgba(202, 147, 242, 0.15);
    animation: pulse-guide 2s infinite;
  }
}

.total-points {
  font-size: 16px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  .points-value {
    color: #ca93f2;
  }
}

.points-modifier {
  font-size: 14px;
  font-weight: 600;
  margin-left: 6px;
  display: inline-block;

  &.negative {
    color: #ff6b6b;
  }

  &.positive {
    color: #daff96;
  }
}

.confirm-btn {
  width: 100%;
  height: 56px;
  border-radius: 40px;
  border: none;
  background: #ca93f2;
  color: #241d49;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: auto;
  box-shadow: 0 4px 10px rgba(202, 147, 242, 0.4);
  flex-shrink: 0;
  margin-top: 16px;
  position: relative;
  overflow: hidden;

  &:active {
    transform: scale(0.98);
  }

  &.disabled {
    opacity: 0.7;
    cursor: not-allowed;
    background: rgba(202, 147, 242, 0.3);
    color: rgba(255, 255, 255, 0.9);
    box-shadow: none;
    // border: 1px solid rgba(202, 147, 242, 0.5);

    &::after {
      content: attr(data-warning);
      position: absolute;
      bottom: -30px;
      left: 0;
      width: 100%;
      text-align: center;
      font-size: 14px;
      color: #daff96;
      font-weight: 500;
      opacity: 0;
      transition: all 0.3s ease;
    }

    &.show-warning::after {
      opacity: 1;
      bottom: -25px;
    }
  }
}

@keyframes glow {
  0% {
    opacity: 0.4;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 0.4;
  }
}

@keyframes pulse-guide {
  0% {
    background-color: rgba(202, 147, 242, 0.15);
  }
  50% {
    background-color: rgba(202, 147, 242, 0.3);
  }
  100% {
    background-color: rgba(202, 147, 242, 0.15);
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0 50%;
  }
}

.points-warning {
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  background: linear-gradient(135deg, rgba(202, 147, 242, 0.95), rgba(128, 90, 213, 0.95));
  color: #daff96;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  opacity: 0;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(202, 147, 242, 0.3);
  border: 1px solid rgba(218, 255, 150, 0.3);

  &.show {
    top: -40px;
    opacity: 1;
  }
}

.carousel-navigation,
.carousel-indicators,
.indicator,
.nav-btn {
  display: none;
}

// Add touch navigation areas
.touch-nav {
  position: absolute;
  top: 0;
  height: 100%;
  width: 30%;
  z-index: 15; // Lower than active card but higher than others
  cursor: pointer;

  &.left {
    left: 0;
  }

  &.right {
    right: 0;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 20px;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 3px solid rgba(202, 147, 242, 0.3);
  border-top-color: #ca93f2;
  animation: spinner 1s linear infinite;
  margin-bottom: 16px;
}

.loading-text {
  font-size: 16px;
  color: #ca93f2;
  font-weight: 500;
  text-align: center;
}

@keyframes spinner {
  to {
    transform: rotate(360deg);
  }
}

// Add specific guide styling overrides if needed
:deep(.shepherd-highlighted) {
  position: relative;
  z-index: 10000 !important;
}

// Help button styles
.help-button {
  position: absolute;
  top: -10px;
  right: 0px;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: rgba(202, 147, 242, 0.2);
  border: 1px solid rgba(202, 147, 242, 0.5);
  color: #fff;
  font-size: 16px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 100;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;

  &:active {
    transform: scale(0.95);
  }
}

@keyframes brief-glow {
  0%,
  100% {
    box-shadow: none;
  }
  50% {
    box-shadow: 0 0 20px rgba(218, 255, 150, 0.3);
  }
}
</style>
