<template>
  <div class="character-select-drawer" v-if="visible">
    <div class="drawer-content">
      <div class="drawer-header">
        <h2>Select Character</h2>
        <button class="close-button" @click="handleClose" :disabled="isLoading">
          <icon-close />
        </button>
      </div>

      <div class="loading-overlay" v-if="isLoading">
        <div class="loading-spinner"></div>
      </div>

      <div ref="scrollableContent" class="scrollable-content" @scroll="checkScroll">
        <div class="character-list">
          <div
            v-for="actor in actors"
            :key="actor.id"
            class="character-card"
            :class="{ 'is-selected': selectedActor?.id === actor.id }"
            @click="selectActor(actor)"
          >
            <div class="card-content">
              <img :src="actor.preview_url" :alt="actor.name" class="preview-url" />
              <div class="character-name">{{ actor.name }}</div>
              <div v-if="!actor.is_purchased" class="cost-badge">
                <img
                  src="https://cdn.magiclight.ai/assets/mobile/diamond.png"
                  class="diamond-icon"
                />
                {{ getActorCost(actor) }}
              </div>
              <div v-else-if="actor.is_purchased && actor.coins > 0" class="unlocked-badge">
                Unlocked
              </div>
              <div v-else class="unlocked-badge">Free</div>
            </div>
          </div>
        </div>

        <div
          class="character-info"
          v-if="selectedActor"
          @click="scrollToInfo"
          :class="{ 'has-more': hasMore }"
        >
          <div class="info-header">
            <div class="name">{{ selectedActor.name }}</div>
            <!-- <div class="play-button" @click="handlePlay">
              <img src="https://cdn.magiclight.ai/assets/playshot/play.png" alt="play" />
            </div> -->
          </div>
          <div class="info-content">
            <div v-for="(value, key) in selectedActor.extra" :key="key" class="info-item">
              <div class="label">{{ key || 'Unknown' }}:</div>
              <div class="value">{{ value || 'Unknown' }}</div>
            </div>
          </div>
        </div>

        <div class="coins-info">Current coins: {{ userStore.userInfo?.coins || 0 }}</div>
      </div>

      <button class="confirm-button" :disabled="!selectedActor || isLoading" @click="handleConfirm">
        <span v-if="isLoading">Loading...</span>
        <template v-else>
          Confirm
          <span v-if="selectedActor && !selectedActor.is_purchased" class="cost">
            <img src="https://cdn.magiclight.ai/assets/mobile/diamond.png" class="diamond-icon" />
            {{ getActorCost(selectedActor) }}
          </span>
        </template>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import IconClose from '@/assets/icon/close.svg'
import { Message } from '@/mobile/components/Message'
import { useUserStore, useStoryStore } from '@/store'
import { Actor } from '@/api/stories'
import { getUserChatHistory } from '@/api/chat'

const props = defineProps<{
  visible: boolean
  actors: Actor[]
  storyIsPurchased: boolean
  storyCoins: number
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'select', actor: Actor): void
}>()

const userStore = useUserStore()
const storyStore = useStoryStore()
const selectedActor = ref<Actor | null>(props.actors[0])
const isLoading = ref(false)

watch(
  () => props.visible,
  (newValue) => {
    if (!newValue) {
      isLoading.value = false
    }
  }
)

const getActorCost = (actor: Actor) => {
  if (!actor) return 0
  const totalCost = actor.coins + (props.storyIsPurchased ? 0 : props.storyCoins)
  return totalCost === 0 ? 'Free' : totalCost
}

const selectActor = (actor: Actor) => {
  selectedActor.value = actor
}

const scrollToInfo = () => {
  if (scrollableContent.value) {
    const characterInfo = document.querySelector('.character-info')
    if (characterInfo) {
      const containerRect = scrollableContent.value.getBoundingClientRect()
      const infoRect = characterInfo.getBoundingClientRect()
      const scrollTop = scrollableContent.value.scrollTop

      const targetScroll = scrollTop + (infoRect.top - containerRect.top)

      scrollableContent.value.scrollTo({
        top: targetScroll,
        behavior: 'smooth'
      })
    }
  }
}

const handleClose = () => {
  if (!isLoading.value) {
    selectedActor.value = null
    emit('update:visible', false)
  }
}

const handleConfirm = async () => {
  if (!selectedActor.value || isLoading.value) return
  isLoading.value = true

  try {
    emit('select', selectedActor.value)
  } catch (error) {
    console.error('Failed to check chat history:', error)
    Message.error('Failed to check chat history')
    isLoading.value = false
  }
}

const handlePlay = () => {
  handleConfirm()
}

const scrollableContent = ref<HTMLElement | null>(null)
const hasMore = ref(false)

const checkScroll = () => {
  if (!scrollableContent.value) return
  const characterInfo = document.querySelector('.character-info')
  if (!characterInfo) return

  const containerRect = scrollableContent.value.getBoundingClientRect()
  const infoRect = characterInfo.getBoundingClientRect()
  const scrollTop = scrollableContent.value.scrollTop

  // 检查 character-info 是否完全可见
  hasMore.value = infoRect.bottom > containerRect.bottom
}

onMounted(() => {
  if (scrollableContent.value) {
    scrollableContent.value.addEventListener('scroll', checkScroll)
    // Initial check
    checkScroll()
  }
})
</script>

<style lang="less" scoped>
.character-select-drawer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  animation: fadeIn 0.3s ease;
}

.drawer-content {
  max-height: calc(var(--vh, 1vh) * 80);
  width: 100%;
  background: linear-gradient(180deg, #2b1b3b 0%, #1a0f24 100%);
  border-radius: 24px 24px 0 0;
  padding: 24px 20px;
  animation: slideUp 0.3s ease;
  display: flex;
  flex-direction: column;
  gap: 24px;
  position: relative;
  overflow: hidden;
}

.drawer-header {
  position: sticky;
  top: 0;
  z-index: 10;
  padding: 0px 20px 8px;
  margin-top: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;

  h2 {
    white-space: nowrap;
    color: white;
    font-size: 20px;
    font-weight: 600;
    margin: 0;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }
}

.close-button {
  position: absolute;
  right: 0px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: transparent;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  padding: 0;

  :deep(svg) {
    width: 24px;
    height: 24px;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.scrollable-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 0 20px;
  margin: 0 -20px;
  position: relative;
  scroll-behavior: smooth;
}

.character-list {
  display: flex;
  flex-shrink: 0;
  gap: 16px;
  overflow-x: auto;
  padding: 16px 20px;
  scroll-snap-type: x mandatory;
  -webkit-overflow-scrolling: touch;

  &::-webkit-scrollbar {
    display: none;
  }
}

.character-card {
  flex: 0 0 auto;
  width: 167px;
  height: 294px;
  scroll-snap-align: start;
  cursor: pointer;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: translateY(-8px);
    z-index: 1;
  }

  &.is-selected {
    z-index: 1;
    animation: float 1.5s ease-in-out infinite;

    .card-content {
      border-color: #ca93f2;
      box-shadow: 0 0 20px rgba(202, 147, 242, 0.3);
    }

    .character-name {
      color: #ca93f2;
    }

    .avatar {
      transform: scale(1.02);
    }
  }

  .card-content {
    width: 100%;
    height: 100%;
    border-radius: 20px;
    overflow: hidden;
    position: relative;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.05);
  }

  .preview-url {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
  }

  .character-name {
    position: absolute;
    left: 16px;
    bottom: 16px;
    font-size: 20px;
    font-weight: 600;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    transition: color 0.3s ease;
  }

  .cost-badge,
  .unlocked-badge {
    position: absolute;
    left: 12px;
    top: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    backdrop-filter: blur(4px);
  }

  .cost-badge {
    background: rgba(0, 0, 0, 0.6);
    color: #daff96;

    .diamond-icon {
      width: 16px;
      height: 16px;
    }
  }

  .unlocked-badge {
    background: rgba(218, 255, 150, 0.2);
    color: #daff96;
  }
}

.character-info {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 20px;
  animation: fadeIn 0.3s ease;
  position: relative;
  cursor: pointer;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 120px;
    background: linear-gradient(
      to bottom,
      transparent 0%,
      rgba(26, 15, 36, 0.02) 20%,
      rgba(26, 15, 36, 0.05) 40%,
      rgba(26, 15, 36, 0.1) 60%,
      rgba(26, 15, 36, 0.15) 80%,
      rgba(26, 15, 36, 0.2) 100%
    );
    border-radius: 0 0 16px 16px;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: opacity;
  }

  &.has-more::after {
    opacity: 1;
  }

  .info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .name {
      font-size: 24px;
      font-weight: 600;
      color: white;
    }

    .play-button {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: #ca93f2;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: transform 0.2s ease;

      &:active {
        transform: scale(0.95);
      }

      :deep(svg) {
        width: 24px;
        height: 24px;
        fill: #241d49;
      }
    }
  }

  .info-content {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .info-item {
    display: flex;
    flex-direction: column;
    .label {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.6);
      margin-bottom: 4px;
    }

    .value {
      font-size: 16px;
      color: white;
      font-weight: 500;
    }
  }
}

.coins-info {
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

.confirm-button {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  background: inherit;
  z-index: 10;
  flex-shrink: 0;
  width: 100%;
  height: 50px;
  border-radius: 25px;
  border: none;
  background: linear-gradient(90deg, #ca93f2 0%, #9b6cc8 100%);
  color: white;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &:not(:disabled):hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(202, 147, 242, 0.3);
  }

  &:not(:disabled):active {
    transform: translateY(0);
  }

  .cost {
    display: flex;
    align-items: center;
    gap: 4px;

    .diamond-icon {
      width: 20px;
      height: 20px;
    }
  }
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(26, 15, 36, 0.8);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(202, 147, 242, 0.3);
  border-radius: 50%;
  border-top-color: #ca93f2;
  animation: spin 1s linear infinite;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes float {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-8px);
  }
  100% {
    transform: translateY(0);
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>
