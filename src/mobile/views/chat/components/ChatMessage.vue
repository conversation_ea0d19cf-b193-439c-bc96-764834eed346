<template>
  <user-message
    v-if="message.sender_type === 'user'"
    :message="message"
    @message-click="$emit('message-click', $event)"
    @scroll-to-bottom="$emit('scroll-to-bottom')"
  />
  <actor-message
    v-else
    :message="message"
    :visible-content="visibleContent"
    :is-message-playing="isMessagePlaying"
    :is-tts-loading="isTtsLoading"
    :current-playing-message-id="currentPlayingMessageId"
    @play-tts="$emit('play-tts', $event)"
    @message-click="$emit('message-click', $event)"
    @scroll-to-bottom="$emit('scroll-to-bottom')"
  />
</template>

<script setup lang="ts">
import UserMessage from './UserMessage.vue'
import ActorMessage from './ActorMessage.vue'

defineProps<{
  message: any
  visibleContent: Record<string, { text: string; mood: number }>
  isMessagePlaying: (message: any) => boolean
  isTtsLoading: boolean
  currentPlayingMessageId: string | null
}>()

defineEmits<{
  (e: 'play-tts', message: any): void
  (e: 'message-click', event: MouseEvent): void
  (e: 'scroll-to-bottom'): void
}>()
</script>
