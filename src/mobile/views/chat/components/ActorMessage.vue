<template>
  <div class="message actor" :data-message-id="message.id">
    <div class="message-wrapper">
      <div class="top-container">
        <div class="name-container">
          <span class="sender-name">{{ message.sender?.name }}</span>
        </div>
        <div class="wave-container" v-if="isMessagePlaying(message)">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="16"
            viewBox="0 0 20 16"
            fill="none"
          >
            <!-- Center wave group -->
            <g class="wave-group">
              <rect x="9" y="4" width="2" height="8" rx="1" fill="white">
                <animate
                  attributeName="height"
                  values="8;14;8"
                  dur="0.8s"
                  repeatCount="indefinite"
                />
                <animate attributeName="y" values="4;1;4" dur="0.8s" repeatCount="indefinite" />
              </rect>
            </g>

            <!-- Left wave group -->
            <g class="wave-group">
              <rect x="5" y="6" width="2" height="4" rx="1" fill="white">
                <animate
                  attributeName="height"
                  values="4;8;4"
                  dur="0.8s"
                  repeatCount="indefinite"
                  begin="0.2s"
                />
                <animate
                  attributeName="y"
                  values="6;4;6"
                  dur="0.8s"
                  repeatCount="indefinite"
                  begin="0.2s"
                />
              </rect>
              <rect x="1" y="7" width="2" height="2" rx="1" fill="white">
                <animate
                  attributeName="height"
                  values="2;6;2"
                  dur="0.8s"
                  repeatCount="indefinite"
                  begin="0.3s"
                />
                <animate
                  attributeName="y"
                  values="7;5;7"
                  dur="0.8s"
                  repeatCount="indefinite"
                  begin="0.3s"
                />
              </rect>
            </g>

            <!-- Right wave group -->
            <g class="wave-group">
              <rect x="13" y="6" width="2" height="4" rx="1" fill="white">
                <animate
                  attributeName="height"
                  values="4;8;4"
                  dur="0.8s"
                  repeatCount="indefinite"
                  begin="0.2s"
                />
                <animate
                  attributeName="y"
                  values="6;4;6"
                  dur="0.8s"
                  repeatCount="indefinite"
                  begin="0.2s"
                />
              </rect>
              <rect x="17" y="7" width="2" height="2" rx="1" fill="white">
                <animate
                  attributeName="height"
                  values="2;6;2"
                  dur="0.8s"
                  repeatCount="indefinite"
                  begin="0.3s"
                />
                <animate
                  attributeName="y"
                  values="7;5;7"
                  dur="0.8s"
                  repeatCount="indefinite"
                  begin="0.3s"
                />
              </rect>
            </g>
          </svg>
        </div>
        <div
          v-if="!!message?.content?.text?.replace(/\*[^*]*\*/g, '')"
          class="play-button"
          @click="$emit('play-tts', message)"
        >
          <template v-if="isTtsLoading && currentPlayingMessageId === message.id">
            <div class="loading-spinner">
              <div class="spinner small"></div>
            </div>
          </template>
          <icon-play-circle-fill v-else-if="!isMessagePlaying(message)" class="control-icon" />
          <icon-pause-circle-fill v-else class="control-icon" />
        </div>
        <div
          v-if="
            visibleContent[message.id]?.mood !== undefined && visibleContent[message.id].mood === -1
          "
          class="mood-indicator"
        >
          <span class="mood-text" :data-content="`❌ Incorrect entered`">
            ❌ Incorrect entered
          </span>
        </div>
      </div>
      <div :class="['message-content', { 'message-content--image': message.msg_type === 'image' }]">
        <template v-if="loading">
          <div class="dots">
            <span class="dot"></span>
            <span class="dot"></span>
            <span class="dot"></span>
          </div>
        </template>
        <template v-else>
          <div
            v-if="message.msg_type === 'text'"
            class="content"
            @click="$emit('message-click', $event)"
          >
            <span v-html="visibleContent[message.id]?.text || ''"></span>
          </div>
          <img
            v-else-if="message.msg_type === 'image'"
            class="image"
            :src="message.content?.media_url"
            alt="image"
            @load="$emit('scroll-to-bottom')"
          />
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import IconPlayCircleFill from '@/assets/icon/play-circle-fill.svg'
import IconPauseCircleFill from '@/assets/icon/pause-circle-fill.svg'
import { onMounted, ref, watch } from 'vue'

const props = defineProps<{
  message: any
  visibleContent: Record<string, { text: string; mood: number }>
  isMessagePlaying: (message: any) => boolean
  isTtsLoading: boolean
  currentPlayingMessageId: string | null
  loading?: boolean
}>()

defineEmits<{
  (e: 'play-tts', message: any): void
  (e: 'message-click', event: MouseEvent): void
  (e: 'scroll-to-bottom'): void
}>()
</script>

<style lang="less" scoped>
@keyframes audioWave {
  0% {
    transform: scaleY(1);
  }
  50% {
    transform: scaleY(0.7);
  }
  100% {
    transform: scaleY(1);
  }
}

.message {
  display: flex;
  gap: 12px;
  align-self: flex-start;
  width: 100%;

  .message-wrapper {
    display: flex;
    flex-direction: column;
    width: fit-content;
    position: relative;

    .top-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 8px;
      position: absolute;
      top: 6px;
      left: 0px;
      z-index: 2;
      padding: 8px 0 8px 12px;
      border-radius: 20px;
      border: 1px solid #c2a4ff;

      background: linear-gradient(149deg, #ca93f2 8.67%, #ba9eff 45.07%, #ad7af0 82.31%);

      box-shadow:
        -1px -3.25px 9.075px 0px rgba(88, 42, 255, 0.32) inset,
        0.75px 3px 7.7px 0px rgba(115, 82, 221, 0.43) inset;
      backdrop-filter: blur(8px);
      min-width: 140px;
      height: 22px;

      .wave-container {
        display: flex;
        align-items: center;
        height: 16px;
        width: 20px;
        margin: 0;
        position: absolute;
        right: 28px;
      }

      .wave-bar {
        width: 2px;
        background-color: #fff;
        border-radius: 2px;
        min-height: 4px;
        will-change: transform;
      }

      .play-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 20px;
        height: 20px;
        cursor: pointer;
        color: #fff;
        flex-shrink: 0;
        margin-left: auto;

        &:hover {
          opacity: 0.8;
        }

        .control-icon {
          width: 20px;
          height: 20px;
          color: #fff;
        }

        :deep(svg) {
          width: 20px;
          height: 20px;
        }
      }
    }

    .name-container {
      display: flex;
      align-items: center;
      padding: 4px 0;
      .sender-name {
        color: #fff;
        font-size: 12px;
        font-weight: 600;
        line-height: normal;
      }
    }

    .play-button {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 20px;
      height: 20px;
      cursor: pointer;
      color: #fff;
      flex-shrink: 0;

      &:hover {
        opacity: 0.8;
      }

      .control-icon {
        width: 20px;
        height: 20px;
        color: #fff;
      }

      :deep(svg) {
        width: 20px;
        height: 20px;
      }
    }
  }

  .message-content {
    padding: 12px 12px 12px 13px;
    border-radius: 0px 15px 15px 15px;
    border: 2px solid #e0b6ff;
    background: rgba(245, 230, 255, 0.9);
    // box-shadow: 0px 4px 0px 0px rgba(224, 182, 255, 0.5);
    user-select: all;
    -webkit-user-select: all;
    margin-top: 20px;
    min-width: 140px;
    &--image {
      padding: 24px 16px;

      .image {
        max-height: 200px;
        width: auto;
        border-radius: 8px;
      }
    }

    .content {
      font-size: 14px;
      line-height: 1.4;
      color: #1f0038;
      font-weight: 500;
      word-wrap: break-word;
      white-space: pre-wrap;
      overflow-wrap: break-word;

      :deep(.action-text) {
        color: #7812c5;
        font-style: italic;
        font-weight: 700;
        display: inline;
        margin-right: 4px;
      }
    }
  }
}

.mood-indicator {
  display: flex;
  padding: 2px 6px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 10px;
  color: #a837fc;
  font-family: 'Work Sans';
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  width: fit-content;

  img {
    width: 16px;
    height: 16px;
    margin-right: -4px;
  }

  .mood-text {
    font-size: 12px;
    font-weight: 500;
    color: #daff96;
    position: relative;
    z-index: 1;
    &::after {
      content: attr(data-content);
      -webkit-text-stroke: 2px #542c74;
      position: absolute;
      left: 0;
      top: 0;
      z-index: -1;
    }
  }
}

.wave-container {
  display: flex;
  align-items: center;
  margin: 0 8px;
  height: 16px;
  width: 26px;
  opacity: 0.9;

  :deep(svg) {
    filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.3));
  }

  .wave-group {
    opacity: 0.9;
  }
}

.dots {
  display: flex;
  gap: 4px;
  justify-content: left;
  padding: 8px 0;
}

.dot {
  width: 6px;
  height: 6px;
  background: #1f0038;
  border-radius: 50%;
  opacity: 0.6;
  animation: bounce 1.4s infinite;

  &:nth-child(2) {
    animation-delay: 0.2s;
  }

  &:nth-child(3) {
    animation-delay: 0.4s;
  }
}

@keyframes bounce {
  0%,
  60%,
  100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-4px);
  }
}
</style>
