.chat-container {
  position: relative;
  height: calc(var(--vh, 1vh) * 100);
  width: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .progress-container {
    position: absolute;
    top: 16px;
    right: 110px;
    z-index: 1000;
    width: 127px;
    height: 24px;
  }

  .audio-control {
    position: absolute;
    top: 56px;
    right: 16px;
    z-index: 1001;

    .audio-button {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.7);
      backdrop-filter: blur(10px);
      border: none;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      padding: 0;
      transition: all 0.2s ease;

      &:hover {
        transform: scale(1.05);
      }

      &:active {
        transform: scale(0.95);
      }
    }
  }

  .credit-display-container {
    position: absolute;
    top: 16px;
    right: 16px;
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 12px;
  }

  &.video-playing {
    .character-background {
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .chat-section {
      background: linear-gradient(180deg, rgba(31, 0, 56, 0) 0%, #1f0038 100%);
    }

    .background-video {
      z-index: 10;
    }

    .background-image {
      opacity: 0;
    }
  }

  &.character-topmost {
    .button-topmost {
      width: 80px;
      height: 74px;
      flex-shrink: 0;
      display: block;
      position: absolute;
      bottom: 45px;
      right: 20px;
      z-index: 1001;
      pointer-events: none;
      animation: breathe 2s ease-in-out infinite;
    }

    .background-layers {
      z-index: 1000;
    }

    .back-button,
    .play-button,
    .action-option-button,
    .chat-option-button,
    .input-container {
      z-index: 1001;
    }

    .messages-wrapper {
      position: relative;
      z-index: 999;
    }

    &.video-playing {
      .background-layers {
        z-index: 1;
      }
    }
  }
}

.background-layers {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  opacity: 1;
}

.character-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  transition: z-index 0s;

  .back-button {
    position: absolute;
    top: 16px;
    left: 16px;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 2;
    color: white;

    svg {
      stroke: #000;
    }
  }
}

.ending-content {
  z-index: 1000;
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translate(-50%, 0);

  .ending-text {
    width: 315px;
    padding: 20px;
    text-align: center;
    border-radius: 8px;
    border-top: 2px solid #1f0038;
    border-right: 2px solid #1f0038;
    border-bottom: 6px solid #1f0038;
    border-left: 2px solid #1f0038;
    background: linear-gradient(180deg, #f0dcff 0%, #ca93f2 100%);
    box-shadow: 0px 1px 6px 0px #9e81fe;
    z-index: 100;
  }
}

// .ending-buttons {
//   display: flex;
//   gap: 16px;
//   justify-content: center;
//   margin-top: 50px;

//   .ending-button {
//     border-radius: 40px;
//     background: #ca93f2;
//     color: #241d49;
//     font-family: 'Work Sans';
//     font-size: 15px;
//     font-style: normal;
//     font-weight: 600;
//     line-height: normal;
//     display: flex;
//     width: 315px;
//     height: 42px;
//     justify-content: center;
//     align-items: center;
//     gap: 5px;
//     flex-shrink: 0;
//     border: none;
//   }
// }

.overlay-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 100;
  width: 100%;
  height: 100%;
}

.character-overlay {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  z-index: 100;
  display: flex;
  width: 315px;
  padding: 10px 20px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 18px;
  border-top: 2px solid #1f0038;
  border-right: 2px solid #1f0038;
  border-bottom: 6px solid #1f0038;
  border-left: 2px solid #1f0038;
  background: linear-gradient(180deg, #f0dcff 0%, #daff96 100%);
  box-shadow: 0px 1px 6px 0px #9e81fe;
  color: #1f0038;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
  animation: fadeIn 1s ease forwards;
  opacity: 0;

  &.bottom {
    bottom: 160px;
  }

  &.top {
    top: 20px;
  }

  &.center {
    top: 50%;
    transform: translate(-50%, -50%);
  }
}

.overlay-button-container {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 100px;
  z-index: 100;
  padding: 0 20px;
  display: flex;
  justify-content: center;
  animation: fadeIn 1.5s ease forwards;
  opacity: 0;

  .overlay-button-icon {
    width: 20px;
    height: 20px;
  }

  .overlay-button-say-hi {
    position: absolute;
    top: 0px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 80px;
    animation: breathe 2s ease-in-out infinite;
  }
}

.overlay-button {
  width: fit-content;
  max-width: 340px;
  padding: 10px 20px;
  border-radius: 12px;
  border: none;
  color: #666;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 25px;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(10px);
  color: #daff96;
  -webkit-text-stroke-width: 1;
  -webkit-text-stroke-color: #542c74;
  font-family: 'Work Sans';
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;

  &-text {
    position: relative;
    z-index: 0;
    &::after {
      content: attr(data-content);
      -webkit-text-stroke: 2px #542c74;
      position: absolute;
      left: 0;
      top: 0;
      z-index: -1;
    }
  }

  &:active {
    background: rgba(128, 128, 128, 0.2);
  }
}

.animated-images-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #000;
  user-select: none;
  // -webkit-user-select: none;
  // -webkit-touch-callout: none;
  // pointer-events: none;

  .tap2continue {
    position: absolute;
    bottom: 20%;
    right: 20%;
    width: 100px;
    height: 100px;
    z-index: 1000;
    animation: breathe 2s ease-in-out infinite;
  }

  .animated-image {
    position: absolute;
    width: 100%;
    height: 100%;
    object-fit: contain;
    cursor: pointer;
    opacity: 0;
    transition: opacity 2s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: opacity;
    user-select: none;
    -webkit-user-drag: none;
    pointer-events: none;

    &.active {
      opacity: 1;
      pointer-events: auto;
    }

    &.previous {
      opacity: 0.99;
      z-index: 1;
    }

    &.active {
      opacity: 1;
      z-index: 2;
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes breathe {
  0% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(0.95);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.05);
  }
  100% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(0.95);
  }
}

.error-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.1) 0%, rgba(84, 44, 116, 0.3) 100%);
    backdrop-filter: blur(20px);
  }

  .error-content {
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 24px;
    padding: 32px;
    animation: fadeInUp 0.8s ease forwards;
  }

  .error-emoji {
    width: 120px;
    height: 120px;
    animation: float 3s ease-in-out infinite;
  }

  .error-message {
    text-align: center;
    color: #fff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .error-title {
    font-family: 'Work Sans';
    font-size: 32px;
    font-weight: 600;
    margin-bottom: 8px;
    background: linear-gradient(180deg, #fff 0%, #daff96 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .error-desc {
    font-size: 16px;
    opacity: 0.9;
  }

  .overlay-button {
    margin-top: 16px;
    padding: 12px 36px;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    font-family: 'Work Sans';
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    border: 1px solid rgba(255, 255, 255, 0.3);

    &:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-2px);
    }

    &-text {
      position: relative;
      z-index: 0;
      color: #daff96;
      -webkit-text-stroke-width: 1;
      -webkit-text-stroke-color: #542c74;

      &::after {
        content: attr(data-content);
        -webkit-text-stroke: 2px #542c74;
        position: absolute;
        left: 0;
        top: 0;
        z-index: -1;
      }
    }

    &:active {
      background: rgba(255, 255, 255, 0.4);
      transform: scale(0.98) translateY(0);
    }
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}
