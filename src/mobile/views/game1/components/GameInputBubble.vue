<template>
  <div v-if="isTextInputScene" :class="['input-bubble']" :style="computedBubbleStyle">
    <input
      v-if="inputType === 'name'"
      type="text"
      placeholder="希望妹妹怎么称呼你呢？"
      v-model="inputValue"
      @input="handleInput"
    />
    <input
      v-if="inputType === 'pose'"
      type="text"
      placeholder="希望妹妹摆什么姿势呢？"
      v-model="inputValue"
      @input="handleInput"
    />
    <input 
      v-if="inputType === 'time'" 
      type="text" 
      placeholder="请填写日期" 
      v-model="inputValue"
      @input="handleInput"
    />
    <input
      v-if="inputType === 'clothes'"
      type="text"
      placeholder="希望妹妹穿什么呢"
      v-model="inputValue"
      @input="handleInput"
    />
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import type { InputType } from '../types'

interface Props {
  inputType: InputType
  modelValue: string
  position: { x: number; y: number }
  isTextInputScene: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  isTextInputScene: false,
  position: () => ({ x: 0, y: 0 })
})

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
}>()

// 本地输入值
const localValue = ref(props.modelValue)

// 处理输入
const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  localValue.value = target.value.trim()
  emit('update:modelValue', localValue.value)
}

// 计算气泡样式
const computedBubbleStyle = computed(() => ({
  top: `${props.position.y}px`,
  left: `${props.position.x}px`,
  transform: 'translate(-50%, -50%)'
}))

// 双向绑定值
const inputValue = computed({
  get: () => props.modelValue,
  set: (value: string) => {
    localValue.value = value.trim()
    emit('update:modelValue', localValue.value)
  }
})
</script>

<style lang="less" scoped>
.input-bubble {
  position: absolute;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 16px;
  border-radius: 12px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
  width: 240px;
  min-height: 80px;
  border: none;
  transition: all 0.3s ease;

  &::after {
    content: '';
    position: absolute;
    bottom: -16px;
    right: 21%;
    width: 0;
    height: 0;
    border: 8px solid transparent;
    border-top-color: rgba(255, 255, 255, 0.8);
  }

  input {
    width: 100%;
    height: 40px;
    border: none;
    background: transparent;
    font-size: 16px;
    text-align: center;
    outline: none;
    color: #333;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.3s ease;

    &::placeholder {
      color: #666;
      opacity: 0.8;
    }

    &:focus {
      background-color: rgba(255, 255, 255, 0.9);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }

  @media (max-width: 768px) {
    width: 200px;
    min-height: 60px;
    padding: 12px;

    input {
      height: 36px;
      font-size: 14px;
    }
  }
}
</style>
