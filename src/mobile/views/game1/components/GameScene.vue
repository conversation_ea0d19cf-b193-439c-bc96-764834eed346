<template>
  <div class="scene" :key="currentScene">
    <template v-if="scene?.type === 'image'">
      <img
        :src="scene.preview_url.split(',')[currentPreviewIndex]"
        class="scene-image"
        alt="场景图片"
      />
    </template>
    <template v-else-if="scene?.type === 'video'">
      <video
        v-if="currentPreviewIndex === 0"
        :src="scene.preview_url.split(',')[0]"
        class="scene-video"
        autoplay
        loop
        muted
        webkit-playsinline="true"
        playsinline="true"
      ></video>
      <img v-else :src="scene.preview_url.split(',')[1]" alt="" />
    </template>
    <template v-else-if="scene?.type === 'interaction'">
      <video
        v-if="scene.preview_url.indexOf('mp4') > -1"
        :src="scene.preview_url"
        class="scene-video"
        autoplay
        loop
        muted
        webkit-playsinline="true"
        playsinline="true"
      ></video>
      <img v-else :src="scene.preview_url" class="scene-image" alt="场景图片" />
    </template>

    <div class="scene-text" v-if="scene?.caption">
      {{ scene.caption }}
    </div>

    <slot></slot>
  </div>
</template>

<script setup lang="ts">
import type { Scene } from '../types'

defineProps<{
  scene: Scene
  currentScene: number
  currentPreviewIndex: number
}>()
</script>

<style lang="less" scoped>
.scene {
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: opacity 1s ease-in-out;

  &-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  &-image {
    position: absolute;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  &-text {
    position: absolute;
    top: 10%;
    color: white;
    font-size: 24px;
    text-align: center;
    width: 90%;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 10px;
    border-radius: 8px;
    z-index: 1;
  }

  video {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  @media (max-width: 768px) {
    &-text {
      font-size: 18px;
      top: 5%;
      width: 95%;
    }
  }
}
</style>
