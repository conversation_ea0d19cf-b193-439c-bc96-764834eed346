<template>
  <div class="chat-wrapper">
    <div
      class="chat-container"
      :class="{
        'video-playing': isPlayingVideo,
        'character-topmost': isCharacterTopmost,
        'telepathy-complete': isTelepathyComplete
      }"
      @click="handleContainerClick"
    >
      <!-- 顶部导航栏 -->
      <ChatTopBar
        v-if="!chatUIStore.overlay && contentReady && !chatResourcesStore.isPlayingVideo"
        :show-diamond-tip="chatEventsStore.inPaidScene"
        :show-credit-display="true"
        :amount="userStore.userInfo?.coins || 0"
        :show-add-button="true"
        @back="handleBackHome"
        @add="handleAddCredit('top')"
      />
      <ChatContainer
        :is-playing-video="isPlayingVideo"
        :background-video="backgroundVideo"
        :background-image="backgroundImage"
        :default-image="defaultBackgroundImage"
        :animated-images="chatResourcesStore.animatedImages"
      >
        <!-- 视频播放器插槽 -->
        <template #video-player>
          <VideoPlayer
            ref="videoManager.videoPlayerRef"
            :storyId="storyStore.currentStory?.id || ''"
            :actorId="storyStore.currentActor?.id || ''"
            :video-url="chatResourcesStore.videoUrl"
            :is-playing="chatResourcesStore.isPlayingVideo"
            :muted="audioManager.isMuted"
            :loading-background="storyStore.currentActor?.preview_url"
            :force-loading="false"
            @update:is-playing="(val) => (chatResourcesStore.isPlayingVideo = val)"
            @update:muted="
              (val) => {
                audioManager.isMuted = val
                audioManager.updateAllAudioStates()
              }
            "
            @video-ended="videoManager.onVideoEnded"
            @video-loaded="videoManager.onVideoLoaded"
            @video-skipped="videoManager.handleVideoSkipped"
          />
        </template>

        <!-- 聊天界面插槽 -->
        <template #chat-interface>
          <div v-show="shouldShowChat" class="chat-interface-wrapper">
            <!-- 任务提示 -->
            <div
              class="task-tip-container"
              v-if="chatTasksStore.tasks.length && !chatUIStore.overlay && contentReady"
            >
              <TaskTip ref="taskTipRef" :description="chatTasksStore.tasks[0].description" />
            </div>
            <!-- 音频控制 -->
            <div class="audio-control" v-if="!chatUIStore.overlay && contentReady">
              <button class="audio-button" @click.stop="audioManager.toggleMute">
                <icon-volume-notice v-if="!audioManager.isMuted" />
                <icon-volume-mute v-else />
              </button>
            </div>
            <!-- 记事本按钮 -->
            <div class="evidence-book-button-container" v-if="!chatUIStore.overlay && contentReady">
              <button class="evidence-book-button" @click.stop="toggleEvidenceBook">
                <img src="https://static.playshot.ai/static/images/icon/notepad.png" alt="" />
              </button>
            </div>
            <!-- 分享按钮
        <div
          class="share-button-container"
          v-if="!chatUIStore.overlay && contentReady && !isAndroidWebView()"
        >
          <ShareButton ref="shareButtonRef" v-model="showShareModal" />
        </div> -->

            <!-- Overlay -->
            <div class="overlay-container" v-if="chatUIStore.overlay" @click="handleOverlayButton">
              <div class="character-overlay" :class="chatUIStore.overlay.position">
                <div class="overlay-content">
                  <div class="overlay-text" v-html="chatUIStore.overlay.text"></div>
                </div>
              </div>

              <div v-if="chatUIStore.overlay?.button" class="overlay-button-container">
                <button class="overlay-button" v-if="chatUIStore.overlay.button.text !== sayHiText">
                  <img
                    class="overlay-button-icon"
                    src="https://cdn.magiclight.ai/assets/playshot/target.png"
                    alt="task"
                  />
                  <span class="overlay-button-text" :data-content="chatUIStore.overlay.button.text">
                    {{ chatUIStore.overlay.button.text }}
                  </span>
                </button>
                <img
                  class="overlay-button-say-hi"
                  v-else
                  src="https://cdn.magiclight.ai/assets/playshot/sayHi.png"
                  alt="Play"
                />
              </div>
            </div>
            <!-- Ending Content -->
            <div v-if="chatUIStore.isEnding && chatUIStore.endings" class="ending-content">
              <div class="ending-text" v-html="chatUIStore.endings?.html"></div>
              <div class="ending-buttons">
                <button class="ending-button restart" @click="handleRestart">
                  <!-- <icon-restart /> -->
                  Play Again
                </button>
              </div>
            </div>

            <!-- 聊天区域 -->
            <div
              class="chat-section"
              v-show="
                !chatUIStore.overlay &&
                !chatUIStore.isEnding &&
                !chatResourcesStore.animatedImages.length &&
                contentReady
              "
            >
              <!-- 消息列表 -->
              <MessageList
                v-show="hasSelectedActor"
                ref="messageListRef"
                :messages="chatMessagesStore.messages"
                :visible-content="visibleContent"
                :is-message-playing="isMessagePlaying"
                @play-tts="handlePlayTTS"
                @thinking-change="handleThinkingChange"
              />

              <!-- Actor Card Options -->
              <ActorCardOptions
                :tooltip="chatResourcesStore.chatActorOptionsTooltip"
                v-if="chatResourcesStore.chatActorOptions?.length && !hasSelectedActor"
                @select="handleActorOptionSelect"
              />

              <!-- Back Button -->
              <BackSceneButton
                v-if="hasSelectedActor && sceneChanged"
                :actor-id="
                  storyStore.currentActor?.id ||
                  (route.params.actorId as string) ||
                  props.characterId
                "
                :is-thinking="isMessageThinking"
                @back-success="handleBackSuccess"
              />

              <!-- Next Scene Button -->
              <NextSceneButton
                v-if="
                  chatResourcesStore.chatActorOptions?.length &&
                  allActorsCompletedMission &&
                  !hasSelectedActor
                "
                :actor-id="
                  storyStore.currentActor?.id ||
                  (route.params.actorId as string) ||
                  props.characterId
                "
                @next-success="handleNextSuccess"
              />
              <!-- 输入区域 -->

              <ChatInputVersion2
                v-if="chatResourcesStore.chatActorOptions?.length && hasSelectedActor"
                ref="chatInputRefVersion2"
                @collapse-task-tip="handleCollapseTaskTip"
                @add-credit="handleAddCredit"
                @scroll-to-bottom="scrollToBottom"
                @toggle-history="handleToggleHistory"
              />
            </div>
          </div>
        </template>
      </ChatContainer>
    </div>

    <!-- 首次加载使用ResourceLoader -->
    <ResourceLoader
      v-if="shouldShowLoading"
      :visible="shouldShowLoading"
      :progress="loadingProgress"
      :loading-text="loadingText"
      :logo-url="logoUrl"
    />

    <!-- 当聊天容器不显示时，在当前背景上显示loading -->
    <div v-if="!shouldShowChat && !shouldShowLoading" class="background-loading-overlay">
      <div class="loading-spinner-center"></div>
    </div>

    <!-- 记事本组件 -->
    <EvidenceBook :is-visible="showEvidenceBook" @close="toggleEvidenceBook" />

    <div v-show="chatUIStore.isError" class="error-wrapper">
      <div
        class="error-container"
        :style="{ backgroundImage: `url(${storyStore.currentActor?.preview_url})` }"
      >
        <div class="error-content">
          <div class="error-message">
            <div class="error-title">Oops!</div>
            <div class="error-desc">This story seems unstable</div>
          </div>
          <button class="overlay-button" @click="handleBackHome">
            <span class="overlay-button-text" data-content="Back to Index">Back to Index</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Modals -->
    <LeaveConfirmModal
      v-model:visible="showLeaveConfirmModal"
      @confirm="handleLeaveConfirm"
      @cancel="handleLeaveCancel"
    />
    <AuthDrawer
      :isInLandingPage="!!props.characterId"
      v-model:visible="showAuthDrawer"
      @login="handleLoginSuccess"
      @register="handleRegisterSuccess"
    />
    <RatingModal
      v-model:visible="storyStore.isShowRatingModal"
      @close="storyStore.isShowRatingModal = false"
      @submit="handleRatingSubmit"
    />
    <TagRatingModal
      v-model:visible="storyStore.isShowTagRatingModal"
      @close="storyStore.isShowTagRatingModal = false"
    />
    <!-- <ShareModal v-model:visible="showShareModal" /> -->
    <!-- <EndingShareModal /> -->

    <!-- 付费场景提醒对话框 -->
    <ConfirmDialog
      v-model:visible="chatEventsStore.showPaidSceneDialog"
      title="Spicy Level"
      :title-style="{ color: '#FFF' }"
      :content-style="{ color: '#CA93F2', fontWeight: '600', textAlign: 'left' }"
      confirm-text="I Understand"
      :show-cancel="false"
      :show-icon="false"
      :close-on-click-overlay="false"
      @confirm="chatEventsStore.showPaidSceneDialog = false"
    >
      <template #content>
        <p>You have entered the spicy level. Enjoy your spicy time.</p>
        <p>
          Note: This level offers 5 minutes of free time, and the subsequent content is priced at 5
          diamonds per 5 minutes.
        </p>
      </template>
    </ConfirmDialog>
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  computed,
  onMounted,
  onBeforeMount,
  onBeforeUnmount,
  watch,
  provide,
  getCurrentInstance
} from 'vue'
import { useRoute, useRouter, onBeforeRouteLeave } from 'vue-router'
import { useChatEventsStore } from '@/store/chat-events'
import { useChatUIStore } from '@/store/chat-ui'
import { useChatMessagesStore } from '@/store/chat-messages'
import { useChatResourcesStore } from '@/store/chat-resources'
import { useChatTasksStore } from '@/store/chat-tasks'
import { useStoryStore } from '@/store/story'
import { useUserStore } from '@/store/user'
import { useRechargeStore } from '@/store/recharge'
import { useSkillStore } from '@/store/skill'
import { setDynamicSEO } from '@/router/seo-guard'
import { reportEvent } from '@/utils/report'
import { ReportEvent } from '@/interface'
import { ChatOptions } from '@/types/chat'
import ResourceLoader from '@/shared/components/ResourceLoader.vue'
import MessageList from '@/mobile/views/chat2/components/MessageList/index.vue'
import ChatInputVersion2 from '@/mobile/views/chat2/components/ChatInput/index.vue'
import TaskTip from '@/mobile/views/chat3/components/TaskTip3.vue'
import LeaveConfirmModal from '@/mobile/components/LeaveConfirmModal.vue'
import AuthDrawer from '@/mobile/components/AuthDrawer.vue'
import RatingModal from '@/mobile/components/RatingModal.vue'
import TagRatingModal from '@/mobile/components/TagRatingModal.vue'
import { Message } from '@/mobile/components/Message'
// import ChatProgress from '@/mobile/components/ChatProgress.vue'
import { useGameInitialization } from '@/mobile/views/chat2/composables/useGameInitialization'
import { useAudioManager } from '@/mobile/composables/useAudioManager'
import VideoPlayer from '@/mobile/components/VideoPlayer.vue'
import { useVideoManager } from '@/mobile/composables/useVideoManager'
import ActorCardOptions from '@/mobile/views/chat3/components/ActorCardOptions/index.vue'
import BackSceneButton from '@/mobile/views/chat3/components/BackSceneButton.vue'
import NextSceneButton from '@/mobile/views/chat3/components/NextSceneButton.vue'
import ChatTopBar from '@/mobile/components/ChatTopBar.vue'
// import ShareModal from '@/mobile/views/chat2/components/ShareModal/index.vue'
import IconVolumeNotice from '@/assets/icon/voice-notice-white.svg'
import IconVolumeMute from '@/assets/icon/voice-mute-white.svg'
import IconEvidenceBook from '@/assets/icon/evidence-book.svg'
import ChatContainer from '@/mobile/components/ChatContainer.vue'
import { cancelAllSSEConnections } from '@/utils/EventSourcePolyfill'
import EvidenceBook from '@/mobile/views/chat3/components/EvidenceBook/index.vue'
import ConfirmDialog from '@/components/ConfirmDialog.vue'
// import ShareButton from '@/mobile/views/chat2/components/ShareButton/index.vue'
// import { isAndroidWebView } from '@/utils/isAndroidWebView'
// import SkillModal from '@/mobile/views/chat2/components/SkillModal/index.vue'
// import EndingShareModal from '@/mobile/views/chat2/components/EndingShareModal/index.vue'
// Props
const props = defineProps<{
  characterId?: string
  storyId?: string
}>()

// Store
const route = useRoute()
const router = useRouter()
const chatEventsStore = useChatEventsStore()
const chatUIStore = useChatUIStore()
const chatMessagesStore = useChatMessagesStore()
const chatResourcesStore = useChatResourcesStore()
const chatTasksStore = useChatTasksStore()
const storyStore = useStoryStore()
const userStore = useUserStore()
const rechargeStore = useRechargeStore()
const skillStore = useSkillStore()
// 动态SEO将通过路由守卫自动处理
// Constants
const sayHiText = 'Say Hi'

// State
const isCharacterTopmost = ref(false)
const visibleContent = ref<Record<string, { text: string; mood?: number }>>({})
const messageListRef = ref<InstanceType<typeof MessageList> | null>(null)
const chatInputRefVersion2 = ref<InstanceType<typeof ChatInputVersion2> | null>(null)
const showAuthDrawer = ref(false)
const showChatOptions = ref(false)
const taskTipRef = ref<InstanceType<typeof TaskTip> | null>(null)
const contentReady = ref(false)
// const showShareModal = ref(false)
const shareButtonRef = ref()
const hasSelectedActor = ref(false)
const sceneChanged = ref(false)
const isMessageThinking = ref(false)
const showEvidenceBook = ref(false)

// 简化的状态管理
const isAnyLoading = computed(() => {
  return !initialized.value || chatUIStore.isInitializing || chatResourcesStore.isResourceLoading
})

const shouldShowLoading = computed(() => {
  // 只在首次加载时使用ResourceLoader
  return isFirstTimeLoading.value && isAnyLoading.value
})

// ResourceLoader相关状态
const loadingProgress = ref(0)
const loadingText = ref('Initializing...')
const logoUrl = computed(
  () => import.meta.env.VITE_LOGO_URL || 'https://cdn.magiclight.ai/assets/playshot/logo-v2.png'
)

// 首次加载标记 - 每次组件创建时都重置
const isFirstTimeLoading = ref(true)

// 资源预加载进度管理
const updateLoadingProgress = (progress: number, text?: string) => {
  loadingProgress.value = Math.min(100, Math.max(0, progress))
  if (text) {
    loadingText.value = text
  }
}

// Composables
const { startGame, handleVisibilityChange, cleanup, initialized } = useGameInitialization()
const audioManager = useAudioManager()
const videoManager = useVideoManager()

// 提供 shareButtonRef 给子组件
provide('shareButtonRef', {
  checkShareStatus: () => shareButtonRef.value?.checkShareStatus()
})

// // 监听分享模态框关闭
// watch(showShareModal, (newVal) => {
//   if (!newVal) {
//     // 模态框关闭时，检查分享状态
//     shareButtonRef.value?.checkShareStatus()
//   }
// })

// Computed
const isPlayingVideo = computed(() => chatResourcesStore.isPlayingVideo)
const backgroundVideo = computed(() => chatResourcesStore.backgroundVideo)
const backgroundImage = computed(() => chatResourcesStore.backgroundImage)
const defaultBackgroundImage = computed(() => storyStore.currentActor?.preview_url)
const isTelepathyComplete = computed(() => chatMessagesStore.heartValue >= 100)
const isLegacyVersion = computed(() => route.meta.version === 'legacy')
const allActorsCompletedMission = computed(() => {
  return chatEventsStore.checkAllActorsCompletedMission()
})

const shouldShowChat = computed(() => {
  // 只有在没有错误、已初始化、不在任何加载状态时才显示聊天界面
  return !chatUIStore.isError && initialized.value && !isAnyLoading.value
})

// Methods
const handleCollapseTaskTip = () => {
  taskTipRef.value?.collapse()
}
const handleRestart = async () => {
  reportEvent(ReportEvent.GameEndClickRestart, {
    storyId: storyStore.currentStory?.id,
    actorId: props.characterId || route.params.actorId
  })
  hasSelectedActor.value = false
  // 清空聊天，调用各个 store 的清空方法
  chatEventsStore.clearChat()
  const actorId = (route.params.actorId as string) || props.characterId
  const storyId = (route.params.storyId as string) || props.storyId
  if (actorId) {
    chatResourcesStore.isFirstVideo = false
    await chatEventsStore.initializeChat(actorId, storyId)
  }
}
const handleContainerClick = async (e: MouseEvent) => {
  // 如果点击的是输入框区域，不处理
  const target = e.target as HTMLElement
  if (
    target.closest('.chat-input') ||
    target.closest('.task-tip-container') ||
    target.closest('.heart-value-container') ||
    target.closest('.audio-control') ||
    target.closest('.evidence-book-button-container') ||
    target.closest('.overlay-container') ||
    target.closest('.back-button') ||
    target.closest('.ending-content') ||
    target.closest('.video-controls') ||
    target.closest('.chat-option-button') ||
    target.closest('.back-scene-button') ||
    target.closest('.next-scene-button') ||
    target.closest('.actor-card')
  ) {
    return
  }
  // 如果有消息正在打印，优先结束打字动画
  if (chatMessagesStore.messageTypingPromise) {
    // 通过 messageListRef 找到当前正在打字的消息组件并调用其 handleMessageClick 方法
    await messageListRef.value?.skipCurrentTyping()
    return
  }
  // 如果输入框存在且可见，显示提示动画
  if (chatInputRefVersion2.value && !isTelepathyComplete.value && !chatUIStore.isLegacyVersion) {
    chatInputRefVersion2.value.showAttentionAnimation()
  }
}

const handleBackHome = () => {
  reportEvent(ReportEvent.BackHome, {
    userId: userStore.userInfo?.uuid,
    recentMessages: chatMessagesStore.messages.slice(-3),
    actorId: props.characterId || route.params.actorId
  })
  router.push('/')
}

const handleAddCredit = (actionType: 'pay_now' | 'option' | 'top') => {
  if (userStore.isGuest) {
    showAuthDrawer.value = true
  } else {
    rechargeStore.toggleRechargeModal()
  }
  reportEvent(ReportEvent.ClickAddCreditInChat, {
    userId: userStore.userInfo?.uuid,
    actorId: props.characterId || route.params.actorId,
    storyId: storyStore.currentStory?.id,
    actionType
  })
}

const handleLoginSuccess = async () => {
  await userStore.getUserInfo()
  showAuthDrawer.value = false
}

const handleRegisterSuccess = () => {
  showAuthDrawer.value = false
}

const scrollToBottom = () => {
  if (chatResourcesStore.isPlayingVideo) return
  setTimeout(() => {
    const messagesList = messageListRef.value?.messagesListRef
    if (messagesList) {
      messagesList.scrollTo({
        top: messagesList.scrollHeight,
        behavior: 'smooth'
      })
    }
  }, 500)
}

const isMessagePlaying = (message: any) => {
  return message.content.audio_url && chatMessagesStore.currentPlayingMessageId === message.id
}

const handlePlayTTS = (message: any) => {
  if (!message.content.audio_url || !hasSelectedActor.value) return

  audioManager.playTTSFromUrl(
    message.content.audio_url,
    message.id,
    chatMessagesStore.voiceConfig.voice_id,
    chatMessagesStore.voiceConfig.provider
  )
}

// 处理思考状态变化
const handleThinkingChange = (isThinking: boolean) => {
  isMessageThinking.value = isThinking
}

const handleOverlayButton = () => {
  if (chatUIStore.overlay?.button) {
    chatUIStore.handleOverlayButtonClick()
  }
}

const handleToggleHistory = () => {
  messageListRef.value?.toggleHistory()
}

const toggleEvidenceBook = () => {
  showEvidenceBook.value = !showEvidenceBook.value
}

const handleActorOptionSelect = async (option: any) => {
  if (
    option.paid_required &&
    !option.is_purchased &&
    userStore.userInfo?.coins < (option.coins || 0)
  ) {
    handleAddCredit('option')
    return
  }

  // 检查该角色是否已经完成任务
  const isActorCompletedMission =
    option.actor_name && chatEventsStore.completedMissionActors.includes(option.actor_name)

  // 如果角色已完成任务，显示提示
  if (isActorCompletedMission) {
    Message.success(
      `This stage is complete. You can still chat with ${option.actor_name},or tap 'Finish' to go back.`,
      5000
    )
  }

  // Cancel any existing SSE connections and pending requests
  cancelAllSSEConnections()

  // Reset scene changed flag before sending new message
  sceneChanged.value = false

  // Set actor information
  if (option.actor_id) chatResourcesStore.setCurrentActorId(option.actor_id)
  if (option.avatar_url) chatResourcesStore.setCurrentActorAvatarUrl(option.avatar_url)
  if (option.actor_name) chatResourcesStore.setCurrentActorName(option.actor_name)

  // Send the message - the watch function will handle setting sceneChanged to true
  // after the message is processed
  if (option.scene_id) {
    await chatEventsStore.sendMessage(
      option.text,
      option.option_id || option.actor_id,
      'text',
      true,
      option.scene_id
    )
  } else {
    await chatEventsStore.sendMessage(option.text, option.option_id || option.actor_id)
  }

  reportEvent(ReportEvent.ClickActorOption, {
    actorId: option.actor_id,
    actorName: option.actor_name,
    sceneId: option.scene_id,
    isCompletedMission: isActorCompletedMission
  })

  showChatOptions.value = false
  // 设置已选择演员状态
  hasSelectedActor.value = true
  handleCollapseTaskTip()
}

// 处理返回成功
const handleBackSuccess = () => {
  // 重置已选择演员状态
  hasSelectedActor.value = false
  // 重置场景变化状态
  sceneChanged.value = false
}

// 处理跳转到下一场景成功
const handleNextSuccess = () => {
  // 清空当前选项
  chatResourcesStore.chatActorOptions = null
  // 重置已选择演员状态
  hasSelectedActor.value = false
  // 重置场景变化状态
  sceneChanged.value = false
  // 重置已完成任务的角色列表
  chatEventsStore.resetCompletedMissionActors()
}

// Navigation Guards
let nextCallback: any = null

const showLeaveConfirmModal = ref(false)
const lastHiddenTime = ref(0)
const totalHiddenTime = ref(0)
const startTime = ref(0)

const handleLeaveConfirm = () => {
  showLeaveConfirmModal.value = false
  if (nextCallback) {
    chatEventsStore.clearChat()
    nextCallback()
    if (document.hidden && lastHiddenTime.value > 0) {
      totalHiddenTime.value += Date.now() - lastHiddenTime.value
    }

    const totalDuration = Date.now() - startTime.value
    const visibleDuration = Math.max(0, Math.floor((totalDuration - totalHiddenTime.value) / 1000))

    reportEvent(ReportEvent.ChatDuration, {
      characterId: props.characterId,
      duration: visibleDuration,
      userId: userStore.userInfo?.uuid,
      isInChat: !props.characterId,
      actorId: props.characterId || route.params.actorId
    })
  }
}

const handleLeaveCancel = () => {
  showLeaveConfirmModal.value = false
  nextCallback = null
}

onBeforeRouteLeave((to, _from, next) => {
  console.log(to)
  // 如果是前往登录页面、发生错误或已经通过钻石用完模态框确认离开，则直接离开
  if (to.path === '/user/login' || chatUIStore.isError || chatEventsStore.isConfirmedLeave) {
    // 如果是通过钻石用完模态框确认离开，重置标记
    if (chatEventsStore.isConfirmedLeave) {
      chatEventsStore.isConfirmedLeave = false
    }
    next()
    return
  }
  nextCallback = next
  showLeaveConfirmModal.value = true
})

const handleRatingSubmit = async (
  _ratings: { story: number; character: number; image: number },
  _comment: string
) => {
  try {
    // todo: 评分表单
    const isOk = true
    if (isOk) {
      storyStore.isShowRatingModal = false
      Message.success('Thank you for your feedback!')
    } else {
      Message.error('Failed to submit feedback')
    }
  } catch (error) {
    Message.error('Failed to submit feedback')
  }
}
// Lifecycle Hooks
onBeforeMount(async () => {
  if (isLegacyVersion.value) {
    chatUIStore.setLegacyVersion(true)
  }
})

onMounted(async () => {
  const { proxy } = getCurrentInstance()
  // @ts-ignore
  proxy?.$tracker?.setUserID(userStore.userInfo?.uuid)
  if (!props.characterId) {
    reportEvent(ReportEvent.StartChatFromIndexPage, {
      actorId: route.params.actorId
    })
  }

  const actorId = (route.params.actorId as string) || props.characterId
  const storyId = (route.params.storyId as string) || props.storyId
  if (actorId) {
    await startGame(actorId, storyId)
  }

  // 设置聊天页面SEO
  if (storyStore.currentStory) {
    const actorName = storyStore.currentActor?.name
    setDynamicSEO('Chat3', {
      story: storyStore.currentStory,
      actorName
    })
  }

  document.addEventListener('visibilitychange', handleVisibilityChange)
})

// 监听overlay的变化
watch(
  [
    () => chatUIStore.overlay,
    () => chatUIStore.isRedirectToChat,
    () => chatMessagesStore.chatHistory
  ],
  ([_overlay, isRedirectToChat, chatHistory]) => {
    if (isRedirectToChat || chatHistory.length >= 0) {
      contentReady.value = true
    }
    // 恢复聊天记录
    if (chatHistory.length > 0) {
      chatMessagesStore.messages = chatHistory
      chatResourcesStore.isFirstVideo = false
    }
  },
  { immediate: true }
)

// 移除旧的智能加载状态管理逻辑，现在使用computed属性

// 监听初始化状态，更新加载进度
watch(
  () => chatUIStore.isInitializing,
  (isInitializing) => {
    if (isInitializing) {
      // 开始初始化时，确保进度从0开始
      if (isFirstTimeLoading.value) {
        updateLoadingProgress(0, 'Initializing...')

        // 模拟进度更新
        const progressInterval = setInterval(() => {
          if (!chatUIStore.isInitializing) {
            clearInterval(progressInterval)
            return
          }

          const currentProgress = loadingProgress.value
          if (currentProgress < 90) {
            // 渐进式增加进度，但不超过90%
            const increment = Math.max(1, Math.floor((90 - currentProgress) / 10))
            updateLoadingProgress(currentProgress + increment)
          }
        }, 200)

        // 监听资源加载状态
        const resourceInterval = setInterval(() => {
          if (!chatUIStore.isInitializing) {
            clearInterval(resourceInterval)
            return
          }

          if (chatResourcesStore.isResourceLoading) {
            updateLoadingProgress(Math.min(85, loadingProgress.value), 'Loading resources...')
          } else if (initialized.value) {
            updateLoadingProgress(Math.min(95, loadingProgress.value), 'Almost ready...')
          }
        }, 100)
      }
    } else {
      // 初始化完成，快速完成进度
      if (isFirstTimeLoading.value) {
        updateLoadingProgress(100, 'Ready!')
      }
    }
  }
)

// 简化的首次加载完成监听
watch(
  () => isAnyLoading.value,
  (loading) => {
    // 当所有加载都完成时，切换到后续模式
    if (!loading && isFirstTimeLoading.value && initialized.value) {
      setTimeout(() => {
        isFirstTimeLoading.value = false
      }, 800) // 给ResourceLoader足够时间显示"Ready!"
    }
  }
)

// 监听场景变化
// watch(
//   () => chatEventsStore.lastScene,
//   (newScene, oldScene) => {
//     if (newScene !== oldScene) {
//       // 场景发生变化
//       sceneChanged.value = true
//     }
//   },
//   { immediate: true }
// )

// Watch for message processing completion
watch(
  [() => chatEventsStore.messageQueue, () => chatEventsStore.isProcessingQueue],
  ([messageQueue, isProcessingQueue]) => {
    // Only set sceneChanged to true when message queue is empty AND processing is complete
    if (Array.isArray(messageQueue) && messageQueue.length === 0 && !isProcessingQueue) {
      console.log('Message queue processed completely')
      sceneChanged.value = true
    }
  },
  { immediate: true }
)

onBeforeUnmount(() => {
  cleanup()
  skillStore.selectedSkills = []
  document.removeEventListener('visibilitychange', handleVisibilityChange)
})

defineOptions({
  name: 'Chat2View'
})
</script>

<style lang="less" scoped>
.chat-wrapper {
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
  position: relative;
  overflow: hidden;
}

.loading-wrapper,
.error-wrapper {
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
  position: relative;
}

.chat-container {
  position: relative;
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
  overflow: hidden;
  background-color: #000;
  &.video-playing {
    background-color: transparent;
  }

  &.character-topmost {
    .chat-background {
      transform: scale(1.1);
    }
  }

  &.telepathy-complete {
    .chat-section {
      background: none;
    }
  }

  .progress-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 2;
  }
  .share-button-container {
    position: absolute;
    top: 134px;
    right: 16px;
    z-index: 2;
    .share-button {
      background: rgba(31, 0, 56, 0.5);
      border: none;
      border-radius: 50%;
      width: 46px;
      height: 46px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;

      :deep(svg) {
        width: 20px;
        height: 20px;
        color: #333;
      }
    }
  }
  .audio-control {
    position: absolute;
    top: 80px;
    right: 16px;
    z-index: 2;

    .audio-button {
      background: rgba(31, 0, 56, 0.5);
      border: none;
      border-radius: 50%;
      width: 46px;
      height: 46px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;

      :deep(svg) {
        width: 20px;
        height: 20px;
        color: #333;
      }
    }
  }

  .evidence-book-button-container {
    position: absolute;
    top: 204px;
    right: 16px;
    z-index: 2;

    .evidence-book-button {
      background: rgb(202, 147, 242);
      border: none;
      border-radius: 50%;
      width: 46px;
      height: 46px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;
      border: 2px solid #1f0038;

      &:hover {
        transform: scale(1.05);
      }

      &:active {
        transform: scale(0.95);
      }

      :deep(img) {
        width: 26px;
        height: 26px;
      }
    }
  }
  .task-tip-container {
    position: absolute;
    top: 142px;
    right: 16px;
    z-index: 3;
  }

  .heart-value-container {
    position: absolute;
    right: 16px;
    top: 60%;
    transform: translateY(-50%);
    z-index: 101;
    pointer-events: auto;
  }

  .skill-container {
    position: absolute;
    right: 16px;
    top: 192px;
    z-index: 101;
  }

  .chat-section {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    // height: 60%;
    z-index: 2;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.6) 100%);
  }

  .overlay-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 3;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .character-overlay {
      max-width: 80%;
      padding: 16px 24px;
      background: rgba(255, 255, 255, 0.9);
      border-radius: 16px;
      backdrop-filter: blur(10px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      margin-bottom: 24px;

      &.top {
        margin-top: 20%;
      }

      &.middle {
        margin-top: 0;
      }

      &.bottom {
        margin-top: -20%;
      }
    }
    .character-overlay {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      z-index: 100;
      display: flex;
      width: 315px;
      padding: 10px 20px;
      justify-content: center;
      align-items: center;
      gap: 10px;
      border-radius: 18px;
      border-top: 2px solid #1f0038;
      border-right: 2px solid #1f0038;
      border-bottom: 6px solid #1f0038;
      border-left: 2px solid #1f0038;
      background: linear-gradient(180deg, #f0dcff 0%, #daff96 100%);
      box-shadow: 0px 1px 6px 0px #9e81fe;
      color: #1f0038;
      text-align: center;
      font-size: 14px;
      font-weight: 500;

      &.bottom {
        bottom: 160px;
      }

      &.top {
        top: 20px;
      }

      &.center {
        top: 50%;
        transform: translate(-50%, -50%);
      }
    }
    .overlay-button-container {
      position: absolute;
      left: 0;
      right: 0;
      bottom: 100px;
      z-index: 100;
      padding: 0 20px;
      display: flex;
      justify-content: center;
      opacity: 1;

      .overlay-button-icon {
        width: 20px;
        height: 20px;
      }

      .overlay-button-say-hi {
        position: absolute;
        top: 0px;
        left: 50%;
        transform: translateX(-50%);
        width: 80px;
        height: 80px;
        animation: breathe 2s ease-in-out infinite;
      }
      .overlay-button {
        width: fit-content;
        max-width: 340px;
        padding: 10px 20px;
        border-radius: 12px;
        border: none;
        color: #666;
        font-size: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        cursor: pointer;
        transition: all 0.2s ease;
        border-radius: 25px;
        background: rgba(255, 255, 255, 0.6);
        backdrop-filter: blur(10px);
        color: #daff96;
        -webkit-text-stroke-width: 1;
        -webkit-text-stroke-color: #542c74;
        font-family: 'Work Sans';
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;

        &-text {
          position: relative;
          z-index: 0;
          &::after {
            content: attr(data-content);
            -webkit-text-stroke: 2px #542c74;
            position: absolute;
            left: 0;
            top: 0;
            z-index: -1;
          }
        }

        &:active {
          background: rgba(128, 128, 128, 0.2);
        }
      }

      .overlay-button-say-hi {
        width: 120px;
        height: 120px;
        cursor: pointer;
        transition: transform 0.3s;

        &:hover {
          transform: scale(1.05);
        }
      }
    }
  }
}
.ending-content {
  z-index: 1000;
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translate(-50%, 0);

  .ending-text {
    width: 315px;
    padding: 20px;
    text-align: center;
    border-radius: 8px;
    border-top: 2px solid #1f0038;
    border-right: 2px solid #1f0038;
    border-bottom: 6px solid #1f0038;
    border-left: 2px solid #1f0038;
    background: linear-gradient(180deg, #f0dcff 0%, #ca93f2 100%);
    box-shadow: 0px 1px 6px 0px #9e81fe;
    z-index: 100;
  }
}

.ending-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-top: 50px;

  .ending-button {
    border-radius: 40px;
    background: #ca93f2;
    color: #241d49;
    font-family: 'Work Sans';
    font-size: 15px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    display: flex;
    width: 315px;
    height: 42px;
    justify-content: center;
    align-items: center;
    gap: 5px;
    flex-shrink: 0;
  }
}
.error-container {
  position: relative;
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
  }

  .error-content {
    position: relative;
    z-index: 1;
    text-align: center;
    color: #fff;

    .error-message {
      margin-bottom: 24px;

      .error-title {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 8px;
      }

      .error-desc {
        font-size: 16px;
        opacity: 0.8;
      }
    }

    .overlay-button {
      background: linear-gradient(180deg, #ff70df 0%, #f64c9c 100%);
      border: none;
      border-radius: 999px;
      padding: 12px 32px;
      color: #fff;
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
      transition: opacity 0.3s;

      &:hover {
        opacity: 0.8;
      }

      .overlay-button-text {
        position: relative;

        &::before {
          content: attr(data-content);
          position: absolute;
          left: 0;
          width: 100%;
          color: rgba(255, 255, 255, 0.4);
        }
      }
    }
  }
}

.chat-options {
  position: fixed;
  left: 50%;
  top: 65%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 400px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
  z-index: 11;

  .chat-option-button {
    padding: 16px;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: #333;
    font-size: 15px;
    cursor: pointer;
    display: flex;
    align-items: center;
    backdrop-filter: blur(10px);
    transition: all 0.2s ease;
    position: relative;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      background: #fff;
    }

    &:active {
      transform: translateY(1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    span {
      flex: 1;
      text-align: center;
      line-height: 1.4;
    }

    &.paid-required {
      background: linear-gradient(90deg, #ff70df 0%, #f64c9c 100%);
      color: #fff;

      &:hover {
        background: linear-gradient(90deg, #ff85e5 0%, #ff61a7 100%);
      }
    }

    .coins-container {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;
      position: absolute;
      right: -6px;
      top: -6px;
      border-radius: 34px;
      border: 1px solid #daff96;
      padding: 2px 10px 2px 7px;
      background: #1f0038;
      color: #daff96;
      font-size: 11px;
      font-weight: 600;
      height: 20px;

      img {
        width: 13px;
        height: 13px;
      }
    }

    .unlock-container {
      position: absolute;
      right: 0px;
      top: -6px;
    }
  }
}

// 背景上的loading覆盖层
.background-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
  z-index: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none; // 允许点击穿透到背景

  .loading-spinner-center {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top-color: #fff;
    animation: spin 1s linear infinite;
    background: rgba(0, 0, 0, 0.3); // 轻微的背景，增强可见性
    backdrop-filter: blur(4px); // 轻微的毛玻璃效果
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
