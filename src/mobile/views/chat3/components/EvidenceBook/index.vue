<template>
  <div class="evidence-book-container" v-if="props.isVisible" @click.self="handleClose">
    <div class="evidence-book">
      <!-- 关闭按钮 -->
      <div class="evidence-book-close" @click="handleClose">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <line x1="18" y1="6" x2="6" y2="18"></line>
          <line x1="6" y1="6" x2="18" y2="18"></line>
        </svg>
      </div>

      <!-- 内容区域 - 放在白色纸张区域内 -->
      <div class="evidence-book-inner">
        <!-- 标题区域 -->
        <div class="evidence-book-header">
          <div class="evidence-book-title">Evidence Book</div>
        </div>

        <!-- 线索内容 -->
        <div class="evidence-book-content">
          <div class="evidence-list">
            <div
              v-for="(scene, sceneIndex) in groupedEvidences"
              :key="sceneIndex"
              class="evidence-scene"
            >
              <div class="scene-title">{{ scene.title }}</div>
              <div class="scene-evidences">
                <div
                  v-for="(evidence, evidenceIndex) in scene.evidences"
                  :key="evidenceIndex"
                  class="evidence-item"
                >
                  <div class="evidence-number">{{ evidenceIndex + 1 }}.</div>
                  <div class="evidence-text">{{ evidence }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useChatEventsStore } from '@/store/chat-events'

const props = defineProps<{
  isVisible: boolean
}>()

const emit = defineEmits<{
  (e: 'close'): void
}>()

const chatEventsStore = useChatEventsStore()

// 将线索按场景分组
const groupedEvidences = computed(() => {
  const groups: { title: string; evidences: string[] }[] = []

  // 从 chatEventsStore 获取线索
  const clues = chatEventsStore.gameClues || []

  if (clues.length > 0) {
    // 按场景ID分组
    const sceneMap = new Map<string, string[]>()

    clues.forEach((clue) => {
      const sceneId = clue.scene_id || 'Unknown Scene'
      // 格式化场景名称，使其更易读
      const sceneName = sceneId ? `Scene ${sceneId.replace(/[^a-zA-Z0-9]/g, ' ')}` : 'Unknown Scene'

      if (!sceneMap.has(sceneName)) {
        sceneMap.set(sceneName, [])
      }
      sceneMap.get(sceneName)?.push(clue.text)
    })

    // 将分组后的线索添加到结果中
    sceneMap.forEach((evidenceList, sceneName) => {
      groups.push({
        title: sceneName,
        evidences: evidenceList
      })
    })
  }

  // 如果没有数据，显示提示信息
  if (groups.length === 0) {
    groups.push({
      title: 'No Evidence Yet',
      evidences: ['Continue exploring to discover clues.']
    })
  }

  return groups
})

const handleClose = () => {
  emit('close')
}
</script>

<style lang="less" scoped>
.evidence-book-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeIn 0.3s ease;
}

.evidence-book {
  position: relative;
  width: 85%;
  max-width: 400px;
  height: 80%;
  max-height: 600px;
  background: url('https://static.playshot.ai/static/images/story-rpg/notepad-v1.png');
  background-size: contain; /* 确保整个背景图片可见 */
  background-position: center;
  background-repeat: no-repeat; /* 确保背景图片不重复 */
  border-radius: 8px;
  // box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
  padding: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: slideIn 0.4s ease;
}

/* 内部容器样式 - 确保内容位于白色纸张区域内 */
.evidence-book-inner {
  position: absolute;
  top: 12%; /* 调整顶部位置，避开紫色边框 */
  left: 12%; /* 调整左侧位置，避开紫色边框 */
  width: 76%; /* 调整宽度，确保内容在白色区域内 */
  height: 76%; /* 调整高度，确保内容在白色区域内 */
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.evidence-book-header {
  width: 100%;
  text-align: center;
  padding-bottom: 10px;
}

.evidence-book-title {
  font-size: 22px;
  font-weight: 700;
  color: #333333;
  font-family: 'Pacifico';
  text-align: center;
  width: 80%;
  margin: 0 auto;
  padding: 5px 0;
  position: relative;

  /* 添加左右两条直线 */
  &::before,
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    width: 30px;
    height: 1px;
    background-color: #666;
  }

  &::before {
    left: 0;
  }

  &::after {
    right: 0;
  }
}

.evidence-book-close {
  width: 21px;
  height: 21px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #fff;
  position: absolute;
  right: 9%;
  top: 7%;
  z-index: 10;
  border-radius: 50%;
  background: rgba(31, 0, 56, 0.5);
  &:hover {
    color: #666666;
  }
}

.evidence-book-content {
  flex: 1;
  overflow-y: auto;
  padding: 0 10px 20px 10px;

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 10px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(117, 79, 147, 0.5);
    border-radius: 10px;
  }
}

.evidence-book-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #754f93;
  font-style: italic;
  text-align: center;
  padding: 20px;
}

.evidence-book-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #754f93;
  font-style: italic;
}

.evidence-scene {
  margin-bottom: 24px;
}

.scene-title {
  font-size: 18px;
  font-weight: 700;
  color: #333333;
  margin-bottom: 15px;
  margin-top: 5px;
  font-family: 'Pacifico';
  text-decoration: underline;
  text-align: left;
}

.scene-evidences {
  padding-left: 10px;
}

.evidence-item {
  display: flex;
  margin-bottom: 8px;
  line-height: 1.4;
}

.evidence-number {
  margin-right: 8px;
  color: #333333;
  font-weight: 600;
}

.evidence-text {
  color: #333333;
  font-family: 'Pacifico';
  line-height: 1.5;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
</style>
