<template>
  <div class="task-tip-wrapper">
    <div
      class="task-tip"
      :class="{ collapsed: chatUIStore.isTaskTipCollapsed, animating: isAnimating }"
      ref="taskTipRef"
    >
      <div class="task-tip-header" @click="throttledToggle" ref="headerRef">
        <div class="header-top">
          <div class="task-icon" ref="iconRef">
            <template v-if="!isShowMissionProgress || !chatUIStore.isTaskTipCollapsed">
              <img
                src="https://cdn.magiclight.ai/assets/playshot/target.png"
                alt="mission"
                ref="iconImgRef"
              />
            </template>
            <template v-else>
              <div class="progress-text" :data-content="`${progress ?? 0}%`">
                {{ progress ?? 0 }}%
              </div>
            </template>
          </div>
          <div class="task-title" ref="titleRef">Mission</div>
          <div class="collapse-icon" ref="collapseIconRef">
            <icon-right />
          </div>
        </div>
        <div
          class="task-description"
          ref="contentRef"
          :class="{ 'with-progress': isShowMissionProgress }"
        >
          <div class="description-content" v-html="formattedDescription"></div>
          <div
            v-if="isShowMissionProgress"
            class="progress-bar"
            :style="{ width: `${progress ?? 0}%` }"
          ></div>
          <div
            v-if="isShowMissionProgress"
            class="progress-text"
            :data-content="`${progress ?? 0}%`"
          >
            {{ progress ?? 0 }}%
          </div>
        </div>
      </div>
      <div v-if="chatUIStore.isShowAlert && !chatUIStore.isTaskTipCollapsed" class="skip-button">
        <button class="skip-action" @click="handleSkip">Game stuck? Skip to next scene</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch, defineExpose, defineOptions } from 'vue'
import { IconRight } from '@arco-design/web-vue/es/icon'
import { animate } from 'animejs'
import { useThrottleFn } from '@vueuse/core'
import { useChatUIStore } from '@/store/chat-ui'
import { useChatTasksStore } from '@/store/chat-tasks'
import { useChatEventsStore } from '@/store/chat-events'
import { reportEvent } from '@/utils/report'
import { ReportEvent } from '@/interface'

defineOptions({
  name: 'TaskTip'
})

const chatUIStore = useChatUIStore()
const chatTasksStore = useChatTasksStore()
const chatEventsStore = useChatEventsStore()

const props = defineProps<{ description: string }>()

// ===== 基础状态 =====
const isAnimating = ref(false)
const taskTipRef = ref<HTMLElement>()
const headerRef = ref<HTMLElement>()
const iconRef = ref<HTMLElement>()
const iconImgRef = ref<HTMLElement>()
const contentRef = ref<HTMLElement>()
const collapseIconRef = ref<HTMLElement>()
const titleRef = ref<HTMLElement>()

// ===== 计算属性 =====
const formattedDescription = computed(() => {
  if (!props.description) return ''
  const matches = props.description.match(/(.*?)\*(.*?)\*(.*)/)
  if (!matches) return props.description
  const [_, beforeText, highlightText, afterText] = matches
  return `${beforeText}<span class="highlight" style="color: #ED2A2E; font-weight: 600;">${highlightText}</span>${afterText}`
})

const isShowMissionProgress = computed(() => false)

const currentTaskData = computed(() => {
  if (!chatTasksStore.currentTaskId) return { task: { percent: 0, description: '' }, index: 0 }
  const index = chatTasksStore.tasks.findIndex(
    (task) => task.task_id === chatTasksStore.currentTaskId
  )
  return {
    task: index === -1 ? { percent: 0, description: '' } : chatTasksStore.tasks[index],
    index: index === -1 ? 0 : index
  }
})

const progress = computed(() => {
  if (!currentTaskData.value.task) return 0
  return Math.floor(currentTaskData.value.task.percent || 0)
})

// ===== 辅助函数 =====
const setStyle = (element: HTMLElement | null | undefined, styles: Record<string, string>) => {
  if (!element) return
  Object.entries(styles).forEach(([key, value]) => {
    element.style[key as any] = value
  })
}

// ===== 动画控制 =====
// iOS风格的弹性动画
// 展开动画 - 完全重写的方法
const expandAnimation = async () => {
  if (!taskTipRef.value || !chatUIStore.isTaskTipCollapsed || isAnimating.value) return

  isAnimating.value = true

  try {
    // 第一步：首先计算展开后的高度
    // 先将内容元素设置为可见但透明，以便计算高度
    if (contentRef.value) {
      setStyle(contentRef.value, {
        opacity: '0',
        visibility: 'visible',
        display: 'block',
        padding: '0 30px 0 0',
        height: 'auto',
        overflow: 'visible'
      })
    }

    if (titleRef.value) {
      setStyle(titleRef.value, {
        opacity: '0',
        visibility: 'visible',
        display: 'block',
        textAlign: 'left'
      })
    }

    if (collapseIconRef.value) {
      setStyle(collapseIconRef.value, {
        opacity: '0',
        visibility: 'visible',
        display: 'flex'
      })
    }

    // 设置头部容器样式
    if (headerRef.value) {
      setStyle(headerRef.value, {
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'flex-start',
        justifyContent: 'flex-start',
        padding: '0'
      })
    }

    // 等待一小段时间，确保 DOM 已经更新
    await new Promise((resolve) => setTimeout(resolve, 10))

    // 计算高度
    const contentHeight = contentRef.value ? contentRef.value.scrollHeight : 0
    console.log('Content height:', contentHeight) // 调试日志
    const skipButtonHeight = chatUIStore.isShowAlert ? 54 : 0
    const headerHeight = 30
    const totalHeight = headerHeight + contentHeight + skipButtonHeight + 40

    // 保存原始宽度和高度
    const originalWidth = taskTipRef.value.offsetWidth
    const originalHeight = taskTipRef.value.offsetHeight

    // 计算目标位置
    const targetTop = 10
    const targetLeft = 10

    // 将图标设置为绝对定位，以便实现独立动画
    if (iconRef.value && iconImgRef.value) {
      setStyle(iconRef.value, {
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        width: '22px',
        height: '22px',
        margin: '0',
        zIndex: '10',
        transition: 'all 350ms cubic-bezier(0.34, 1.56, 0.64, 1)'
      })

      setStyle(iconImgRef.value, {
        width: '30px',
        height: '30px'
      })
    }

    // 直接使用 anime.js 来实现从右向左的展开动画
    // 设置容器样式为可见
    setStyle(taskTipRef.value, {
      width: `${originalWidth}px`,
      height: `${originalHeight}px`,
      opacity: '1',
      overflow: 'hidden',
      transformOrigin: 'right center'
    })

    // 使用 anime.js 执行容器展开动画
    animate(taskTipRef.value, {
      width: [`${originalWidth}px`, 'calc(100vw - 50px)'],
      maxWidth: [`${originalWidth}px`, '400px'],
      height: [`${originalHeight}px`, `${totalHeight}px`],
      borderRadius: ['23px', '20px'],
      background: ['#ca93f2', 'linear-gradient(180deg, #e0b6ff 0%, #ca93f2 100%)'],
      padding: ['0px', '12px 16px'],
      duration: 350,
      easing: 'spring(1, 80, 10, 0)' // 弹性曲线
    })

    // 在下一帧移动图标
    setTimeout(() => {
      if (iconRef.value) {
        // 设置目标位置：左上角
        setStyle(iconRef.value, {
          top: `${targetTop}px`,
          left: `${targetLeft}px`,
          transform: 'none',
          width: '20px',
          height: '20px'
        })

        // 在图标移动一半时显示标题
        setTimeout(() => {
          if (titleRef.value) {
            animate(titleRef.value, {
              opacity: [0, 1],
              translateX: ['-20px', '0px'],
              duration: 250,
              easing: 'easeOutQuad'
            })
          }
        }, 175) // 图标动画时间的一半
      }
    }, 10)

    // 等待容器动画完成
    await new Promise((resolve) => setTimeout(resolve, 400))

    // 显示内容和折叠图标
    if (contentRef.value) {
      animate(contentRef.value, {
        opacity: [0, 1],
        translateY: ['10px', '0px'],
        duration: 250,
        easing: 'easeOutQuad'
      })
    }

    // 标题已经在图标移动中显示了

    if (collapseIconRef.value) {
      animate(collapseIconRef.value, {
        opacity: [0, 1],
        duration: 200,
        easing: 'easeOutQuad'
      })
    }

    // 重置图标样式为非绝对定位（但保持在左上角）
    setTimeout(() => {
      if (iconRef.value) {
        setStyle(iconRef.value, {
          position: 'static',
          transition: 'none',
          transform: 'none'
        })
      }

      if (iconImgRef.value) {
        setStyle(iconImgRef.value, {
          width: '20px',
          height: '20px',
          position: 'static',
          transform: 'none'
        })
      }
    }, 400)

    // 等待内容动画完成
    await new Promise((resolve) => setTimeout(resolve, 200))

    // 更新状态
    chatUIStore.isTaskTipCollapsed = false
    isAnimating.value = false
  } catch (error) {
    console.error('[TaskTip] 展开动画错误:', error)
    // 确保在出错时内容仍然可见
    if (contentRef.value) setStyle(contentRef.value, { opacity: '1', visibility: 'visible' })
    if (titleRef.value) setStyle(titleRef.value, { opacity: '1', visibility: 'visible' })
    if (collapseIconRef.value)
      setStyle(collapseIconRef.value, { opacity: '1', visibility: 'visible' })

    // 重置图标样式
    if (iconRef.value) {
      setStyle(iconRef.value, {
        position: 'static',
        transform: 'none',
        width: '20px',
        height: '20px'
      })
    }

    chatUIStore.isTaskTipCollapsed = false
    isAnimating.value = false
  }
}

// 收起动画
const collapseAnimation = async () => {
  if (!taskTipRef.value || chatUIStore.isTaskTipCollapsed || isAnimating.value) return

  isAnimating.value = true

  try {
    // 1. 内容淡出动画 - 快速淡出
    animate([contentRef.value, titleRef.value, collapseIconRef.value], {
      opacity: [1, 0],
      translateY: ['0px', '5px'],
      duration: 150,
      easing: 'easeOutQuad'
    })

    // 2. 等待内容淡出
    await new Promise((resolve) => setTimeout(resolve, 100))

    // 3. 隐藏内容元素
    if (contentRef.value) setStyle(contentRef.value, { visibility: 'hidden', display: 'none' })
    if (titleRef.value) setStyle(titleRef.value, { visibility: 'hidden', display: 'none' })
    if (collapseIconRef.value)
      setStyle(collapseIconRef.value, { visibility: 'hidden', display: 'none' })

    // 4. 图标动画 - 从当前位置直接移动到居中
    if (iconImgRef.value) {
      // 获取当前位置信息
      const rect = iconImgRef.value.getBoundingClientRect()
      const parentRect = taskTipRef.value?.getBoundingClientRect() || rect

      // 计算相对于父元素的位置
      const leftPos = rect.left - parentRect.left
      const topPos = rect.top - parentRect.top

      // 设置为绝对定位，但保持在当前位置
      setStyle(iconImgRef.value, {
        position: 'absolute',
        width: '20px',
        height: '20px',
        left: `${leftPos}px`,
        top: `${topPos}px`,
        transform: 'none',
        opacity: '1',
        visibility: 'visible',
        display: 'block'
      })

      // 执行图标动画 - 移动到居中
      animate(iconImgRef.value, {
        width: ['20px', '30px'],
        height: ['20px', '30px'],
        left: [`${leftPos}px`, '50%'],
        top: [`${topPos}px`, '50%'],
        translateX: ['0%', '-50%'],
        translateY: ['0%', '-50%'],
        duration: 300,
        easing: 'spring(1, 80, 12, 0)' // 弹性曲线
      })
    }

    // 5. 容器收缩动画 - 同时进行
    animate(taskTipRef.value, {
      width: ['calc(100vw - 50px)', '46px'],
      maxWidth: ['400px', '46px'],
      height: [taskTipRef.value.offsetHeight + 'px', '46px'],
      padding: ['12px 16px', '0px'],
      borderRadius: ['20px', '23px'],
      background: ['linear-gradient(180deg, #e0b6ff 0%, #ca93f2 100%)', '#ca93f2'],
      duration: 350,
      easing: 'spring(1, 80, 10, 0)' // 弹性曲线
    })

    // 6. 等待所有动画完成
    await new Promise((resolve) => setTimeout(resolve, 350))

    // 7. 确保最终状态正确
    if (iconImgRef.value) {
      setStyle(iconImgRef.value, {
        width: '30px',
        height: '30px',
        position: 'absolute',
        left: '50%',
        top: '50%',
        transform: 'translate(-50%, -50%)',
        opacity: '1',
        visibility: 'visible',
        display: 'block'
      })
    }

    if (headerRef.value) {
      setStyle(headerRef.value, {
        padding: '0',
        height: '100%',
        alignItems: 'center',
        justifyContent: 'center',
        display: 'flex'
      })
    }

    // 8. 更新状态
    chatUIStore.isTaskTipCollapsed = true
    isAnimating.value = false
  } catch (error) {
    console.error('[TaskTip] 收起动画错误:', error)
    chatUIStore.isTaskTipCollapsed = true
    isAnimating.value = false
  }
}

// ===== 事件处理 =====
// 切换状态
const handleToggle = async () => {
  reportEvent(ReportEvent.Chat2TaskTipClick, {
    isCollapsed: chatUIStore.isTaskTipCollapsed
  })

  if (isAnimating.value) return

  if (chatUIStore.isTaskTipCollapsed) {
    await expandAnimation()
  } else {
    await collapseAnimation()
  }
}

const throttledToggle = useThrottleFn(handleToggle, 300)

// 跳过当前场景
const handleSkip = () => {
  reportEvent(ReportEvent.ClickChat2TaskTipSkip, {
    isCollapsed: chatUIStore.isTaskTipCollapsed
  })
  chatUIStore.isShowAlert = false
  collapseAnimation()
  chatEventsStore.sendMessage('__NEXT_SCENE__', '__NEXT_SCENE__')
}

// ===== 监听和生命周期 =====
// 监听折叠状态变化
watch(
  () => chatUIStore.isTaskTipCollapsed,
  async (newVal) => {
    if (newVal && !isAnimating.value) {
      // 确保收起状态样式正确
      if (taskTipRef.value) {
        setStyle(taskTipRef.value, {
          width: '46px',
          maxWidth: '46px',
          height: '46px',
          maxHeight: '46px',
          borderRadius: '23px',
          background: '#ca93f2',
          padding: '0',
          opacity: '1'
        })
      }

      // 隐藏内容元素
      const hideStyles = { visibility: 'hidden', opacity: '0', display: 'none' }

      if (contentRef.value) setStyle(contentRef.value, hideStyles)
      if (titleRef.value) setStyle(titleRef.value, hideStyles)
      if (collapseIconRef.value) setStyle(collapseIconRef.value, hideStyles)

      // 设置图标样式
      if (iconImgRef.value) {
        setStyle(iconImgRef.value, {
          width: '30px',
          height: '30px',
          position: 'absolute',
          left: '50%',
          top: '50%',
          transform: 'translate(-50%, -50%)'
        })
      }

      // 调整头部元素样式
      if (headerRef.value) {
        setStyle(headerRef.value, {
          padding: '0',
          height: '100%',
          alignItems: 'center',
          justifyContent: 'center'
        })
      }
    } else if (!newVal && !isAnimating.value) {
      // 确保展开状态样式正确
      // 动态计算展开后的高度
      // 先设置内容元素为可见，然后再计算高度
      if (contentRef.value) {
        setStyle(contentRef.value, {
          visibility: 'visible',
          display: 'block',
          height: 'auto',
          overflow: 'visible'
        })
      }

      if (titleRef.value) {
        setStyle(titleRef.value, {
          visibility: 'visible',
          display: 'block'
        })
      }

      // 使用 setTimeout 而不是 await，因为 watch 不是 async 函数
      setTimeout(() => {
        if (!taskTipRef.value) return

        const contentHeight = contentRef.value ? contentRef.value.scrollHeight : 0
        console.log('Watch content height:', contentHeight) // 调试日志
        const skipButtonHeight = chatUIStore.isShowAlert ? 54 : 0
        const headerHeight = 30
        // 添加额外的间距，确保有足够的空间
        const totalHeight = headerHeight + contentHeight + skipButtonHeight + 40

        setStyle(taskTipRef.value, {
          width: 'calc(100vw - 50px)',
          maxWidth: '400px',
          height: `${totalHeight}px`,
          maxHeight: 'none',
          borderRadius: '20px',
          background: 'linear-gradient(180deg, #e0b6ff 0%, #ca93f2 100%)',
          padding: '12px 16px',
          opacity: '1'
        })
      }, 10)

      // 注意：设置已经移到 setTimeout 回调中

      // 显示内容元素
      if (contentRef.value) {
        setStyle(contentRef.value, {
          visibility: 'visible',
          opacity: '1',
          display: 'block',
          padding: '0 30px 0 0'
        })
      }

      if (titleRef.value) {
        setStyle(titleRef.value, {
          visibility: 'visible',
          opacity: '1',
          display: 'block',
          textAlign: 'left'
        })
      }

      if (collapseIconRef.value) {
        setStyle(collapseIconRef.value, {
          visibility: 'visible',
          opacity: '1',
          display: 'flex',
          position: 'relative'
        })
      }

      // 设置图标样式
      if (iconImgRef.value) {
        setStyle(iconImgRef.value, {
          width: '20px',
          height: '20px',
          position: 'static',
          transform: 'none'
        })
      }

      // 设置头部样式
      if (headerRef.value) {
        setStyle(headerRef.value, {
          padding: '0',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'flex-start',
          justifyContent: 'flex-start'
        })
      }
    }
  },
  { immediate: true }
)

// 监听提示显示状态
watch(
  () => chatUIStore.isShowAlert,
  (newVal) => {
    if (newVal) handleToggle()
  }
)

// 监听任务进度变化
watch(
  () => progress.value,
  () => {
    if (isShowMissionProgress.value && chatUIStore.isTaskTipCollapsed) {
      // 更新进度文本
      const progressTextElements = document.querySelectorAll('.progress-text')
      progressTextElements.forEach((el) => {
        el.setAttribute('data-content', `${progress.value}%`)
        el.textContent = `${progress.value}%`
      })
    }
  }
)

onMounted(async () => {
  // 初始状态设置
  if (chatUIStore.isTaskTipCollapsed) {
    if (taskTipRef.value) {
      setStyle(taskTipRef.value, {
        width: '46px',
        maxWidth: '46px',
        height: '46px',
        maxHeight: '46px',
        borderRadius: '23px',
        background: '#ca93f2',
        padding: '0'
      })
    }

    // 隐藏内容元素
    const hideStyles = { visibility: 'hidden', opacity: '0', display: 'none' }

    if (contentRef.value) setStyle(contentRef.value, hideStyles)
    if (titleRef.value) setStyle(titleRef.value, hideStyles)
    if (collapseIconRef.value) setStyle(collapseIconRef.value, hideStyles)

    // 设置图标样式
    if (iconImgRef.value) {
      setStyle(iconImgRef.value, {
        width: '30px',
        height: '30px',
        position: 'absolute',
        left: '50%',
        top: '50%',
        transform: 'translate(-50%, -50%)'
      })
    }

    // 设置头部样式
    if (headerRef.value) {
      setStyle(headerRef.value, {
        padding: '0',
        height: '100%',
        alignItems: 'center',
        justifyContent: 'center'
      })
    }
  } else {
    // 展开状态初始化
    // 动态计算展开后的高度
    // 先设置内容元素为可见，然后再计算高度
    if (contentRef.value) {
      setStyle(contentRef.value, {
        visibility: 'visible',
        display: 'block',
        height: 'auto',
        overflow: 'visible'
      })
    }

    if (titleRef.value) {
      setStyle(titleRef.value, {
        visibility: 'visible',
        display: 'block'
      })
    }

    // 使用 setTimeout 而不是 await，因为 onMounted 不是 async 函数
    setTimeout(() => {
      if (!taskTipRef.value) return

      const contentHeight = contentRef.value ? contentRef.value.scrollHeight : 0
      console.log('onMounted content height:', contentHeight) // 调试日志
      const skipButtonHeight = chatUIStore.isShowAlert ? 54 : 0
      const headerHeight = 30
      // 添加额外的间距，确保有足够的空间
      const totalHeight = headerHeight + contentHeight + skipButtonHeight + 40

      setStyle(taskTipRef.value, {
        width: 'calc(100vw - 50px)',
        maxWidth: '400px',
        height: `${totalHeight}px`,
        maxHeight: 'none',
        borderRadius: '20px',
        background: 'linear-gradient(180deg, #e0b6ff 0%, #ca93f2 100%)',
        padding: '12px 16px',
        opacity: '1'
      })
    }, 10)

    // 注意：设置已经移到 setTimeout 回调中

    if (headerRef.value) {
      setStyle(headerRef.value, {
        padding: '0',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'flex-start',
        justifyContent: 'flex-start'
      })
    }

    if (contentRef.value) {
      setStyle(contentRef.value, {
        padding: '0 30px 0 0',
        visibility: 'visible',
        opacity: '1',
        display: 'block'
      })
    }

    if (titleRef.value) {
      setStyle(titleRef.value, {
        visibility: 'visible',
        opacity: '1',
        display: 'block',
        textAlign: 'left'
      })
    }

    if (collapseIconRef.value) {
      setStyle(collapseIconRef.value, {
        visibility: 'visible',
        opacity: '1',
        display: 'flex',
        position: 'relative'
      })
    }

    if (iconImgRef.value) {
      setStyle(iconImgRef.value, {
        width: '20px',
        height: '20px',
        position: 'static',
        transform: 'none'
      })
    }
  }
})

// 导出组件方法
defineExpose({
  collapse: () => {
    collapseAnimation()
  }
})
</script>

<style lang="less" scoped>
.task-tip-wrapper {
  position: fixed;
  z-index: 100;
}

.task-tip {
  position: absolute;
  top: 0;
  right: 0;
  background: linear-gradient(180deg, #e0b6ff 0%, #ca93f2 100%);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 2px solid #1f0038;
  padding: 12px 16px;
  overflow: hidden;
  width: calc(100vw - 50px);
  max-width: 400px;
  will-change: transform, width, height, border-radius, background;
  transform-origin: right center;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  // 动画中状态样式
  &.animating {
    .task-description,
    .task-title,
    .collapse-icon,
    .skip-button {
      opacity: 0 !important;
      visibility: hidden !important;
      display: none !important;
    }
  }

  // 收起状态样式
  &.collapsed {
    width: 46px;
    max-width: 46px;
    height: 46px;
    max-height: 46px;
    border-radius: 23px;
    background: #ca93f2;
    padding: 0;

    .task-tip-header {
      padding: 0;
      height: 100%;
      align-items: center;
      justify-content: center;

      .task-icon {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 22px;
        height: 22px;
        margin: 0;
        z-index: 1;
        pointer-events: none;

        img {
          width: 30px;
          height: 30px;
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
        }

        .progress-text {
          position: absolute;
          margin: auto;
          height: fit-content;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #daff96;
          font-size: 14px;
          font-weight: 600;
          text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
          z-index: 1;
          &::before {
            content: attr(data-content);
            -webkit-text-stroke: 1px #542c74;
            position: absolute;
            left: 0;
            top: 0;
            z-index: -1;
          }
        }
      }

      .task-description,
      .task-title,
      .collapse-icon {
        display: none;
        visibility: hidden;
        opacity: 0;
      }
    }

    .skip-button {
      display: none;
      visibility: hidden;
      opacity: 0;
    }
  }
}

.task-tip-header {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 8px;
  cursor: pointer;
  box-sizing: border-box;
  flex: 1;

  .header-top {
    display: flex;
    align-items: center;
    gap: 5px;

    .task-title {
      flex: 1;
      font-size: 14px;
      font-weight: 600;
      color: #1f0038;
      line-height: 20px;
    }

    .collapse-icon {
      width: 20px;
      height: 20px;
      background: rgba(0, 0, 0, 0.2);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      color: rgba(255, 255, 255, 0.8);
      pointer-events: none;
      z-index: 2;
      :deep(svg) {
        width: 12px;
        height: 12px;
      }
    }
  }

  .task-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    margin: 0;
    z-index: 2;

    img {
      width: 20px;
      height: 20px;
    }
  }

  .task-description {
    font-size: 14px;
    font-weight: 400;
    color: #1f0038;
    line-height: 20px;
    position: relative;
    padding-right: 30px;

    &.with-progress {
      padding-bottom: 20px;
    }

    .description-content {
      word-break: break-word;
    }

    .progress-bar {
      position: absolute;
      left: 0;
      bottom: 0;
      height: 6px;
      background: #daff96;
      border-radius: 3px;
      transition: width 0.3s ease;
    }

    .progress-text {
      position: absolute;
      right: 5px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 15px;
      font-weight: 600;
      z-index: 2;
      color: #daff96;
      &::before {
        content: attr(data-content);
        -webkit-text-stroke: 1px #542c74;
        position: absolute;
        left: 0;
        top: 0;
        z-index: -1;
      }
    }
  }
}

.skip-button {
  margin-top: 10px;
  width: 100%;
  display: flex;
  justify-content: center;
  animation: fadeIn 0.3s ease;
}

.skip-action {
  border-radius: 26px;
  border-top: 2px solid #1f0038;
  border-right: 2px solid #1f0038;
  border-bottom: 6px solid #1f0038;
  border-left: 2px solid #1f0038;
  background: linear-gradient(180deg, #f5ffe2 0%, #daff96 100%);
  box-shadow: 0px 1.855px 11.13px 0px #daff96;
  height: 44px;
  color: #241d49;
  font-size: 15px;
  font-weight: 600;
  padding: 0 16px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>
