<template>
  <div class="actor-card-options">
    <div v-if="tooltip" class="tooltip-container">
      <div class="tooltip-content" v-text-stroke>{{ tooltip }}</div>
    </div>
    <div class="actor-cards-container" ref="cardsContainerRef">
      <button
        v-for="option in options"
        :key="option.actor_id"
        class="actor-card"
        :class="{ 'highlight-animation': option.is_highlight }"
        @click="handleSelect(option)"
        ref="cardRefs"
      >
        <div class="actor-avatar">
          <img :src="option.avatar_url" :alt="option.actor_name" />
        </div>
        <div class="actor-name" v-text-stroke>{{ option.actor_name }}</div>
        <div class="actor-diamond" v-if="option.coins > 0">
          <img src="https://cdn.magiclight.ai/assets/mobile/diamond.png" class="credit-icon" />
          <span class="actor-diamond-coins" v-if="!option.is_purchased">{{ option.coins }}</span>
          <span class="actor-diamond-coins" v-else>Unlocked</span>
        </div>
        <div class="actor-status">
          <div
            v-if="isActorCompleted(option.actor_name)"
            class="status-btn complete-btn"
            v-text-stroke
          >
            Complete
          </div>
          <div v-else class="status-btn explore-btn" v-text-stroke>Explore</div>
        </div>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, nextTick } from 'vue'
import { useChatResourcesStore } from '@/store/chat-resources'
import { useChatEventsStore } from '@/store/chat-events'
import type { ActorOptions } from '@/types/chat'
import { animate, stagger } from 'animejs'

defineProps<{
  tooltip?: string
}>()

const emit = defineEmits<{
  (e: 'select', option: ActorOptions): void
}>()

const chatResourcesStore = useChatResourcesStore()
const chatEventsStore = useChatEventsStore()
const cardsContainerRef = ref<HTMLElement | null>(null)
const cardRefs = ref<HTMLElement[]>([])

const options = computed(() => chatResourcesStore.chatActorOptions || [])

// 检查角色是否已完成任务
const isActorCompleted = (actorName: string) => {
  return chatEventsStore.completedMissionActors.includes(actorName)
}

const handleSelect = (option: ActorOptions) => {
  emit('select', option)
}

// 动画效果
const animateCards = async () => {
  if (!cardRefs.value.length) return

  // 重置卡片样式，确保动画正确播放
  cardRefs.value.forEach((card) => {
    card.style.opacity = '0'
    card.style.transform = 'scale(0.6) translateY(40px)'
  })

  await nextTick()

  // 使用 animate 创建容器的入场动画
  animate(cardsContainerRef.value, {
    opacity: [0, 1],
    duration: 400,
    easing: 'cubicBezier(0.4, 0.0, 0.2, 1)'
  })

  // 使用 animate 创建卡片的入场动画
  animate(cardRefs.value, {
    opacity: [0, 1],
    scale: [0.6, 1],
    translateY: [40, 0],
    rotateX: [30, 0],
    boxShadow: ['0 0 0 rgba(0, 0, 0, 0)', '0 4px 0px 0px rgba(133, 117, 146, 0.50)'],
    delay: stagger(120, { from: 'center' }),
    duration: 600,
    easing: 'cubicBezier(0.4, 0.0, 0.2, 1)'
  })

  // 使用 animate 创建卡片的弹跳效果
  animate(cardRefs.value, {
    translateY: [0, -8, 0],
    scale: [1, 1.05, 1],
    delay: stagger(80, { from: 'center' }),
    duration: 400,
    easing: 'spring(1, 80, 10, 0)'
  })
}

onMounted(async () => {
  // 确保DOM已更新后再执行动画
  await nextTick()
  animateCards()
})
</script>

<style lang="less" scoped>
.actor-card-options {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 16px;
  width: 100%;
  padding: 16px 0px;
  z-index: 11;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;

  .tooltip-container {
    position: absolute;
    top: -25px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    z-index: 12;
    pointer-events: none;
  }

  .tooltip-content {
    // background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 15px;
    font-weight: 700;
    // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    max-width: 90%;
    text-align: center;
    animation: fadeIn 0.3s ease-in-out;
  }
}

.actor-cards-container {
  display: flex;
  flex-wrap: nowrap;
  justify-content: center;
  gap: 6px; /* 减小间距 */
  perspective: 1000px; /* 3D效果 */
  overflow-x: auto;
  width: 100%;
  max-width: 100%;
  padding: 4px 0;
  margin: 0 auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */

  /* Hide scrollbar for Chrome, Safari and Opera */
  &::-webkit-scrollbar {
    display: none;
  }

  /* Center items when there's extra space */
  &::after {
    content: '';
    padding-right: 4px;
  }

  &::before {
    content: '';
    padding-left: 4px;
  }
}

.actor-card {
  position: relative;
  min-width: 80px;
  flex: 0 0 auto;
  height: fit-content;
  border-radius: 15px;
  background: rgba(76, 51, 96, 0.9);
  //overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0;
  cursor: pointer;
  transition: all 0.2s ease;
  transform-style: preserve-3d; /* 保持3D效果 */
  backface-visibility: hidden; /* 隐藏背面 */
  will-change: transform, box-shadow;
  border-radius: 15px;
  border: 2px solid #754f93;
  background: rgba(76, 51, 96, 0.9);
  box-shadow: 0px 4px 0px 0px rgba(133, 117, 146, 0.5);
  padding: 10px 6px;
  &:hover {
    box-shadow:
      0 8px 16px rgba(0, 0, 0, 0.3),
      0 0 15px rgba(138, 43, 226, 0.5);
  }

  &:active {
    transform: translateY(1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .actor-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    overflow: hidden;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(0deg, rgba(74, 42, 107, 0.3) 0%, rgba(74, 42, 107, 0) 50%);
      z-index: 1;
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .actor-name {
    width: 100%;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    font-weight: 700;
    padding: 0 2px;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    position: relative;
    margin-bottom: 6px;
  }

  .actor-status {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-top: 4px;
  }

  .status-btn {
    padding: 3px 8px;
    border-radius: 34px;
    font-size: 10px;
    font-weight: 600;
    text-align: center;
    min-width: 60px;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.2);
  }

  .explore-btn {
    background-color: #6c5b7b;
    color: #fff;
    animation: pulse 1.5s infinite;
  }

  .complete-btn {
    background: #daff96;
    color: #fff;
  }

  @keyframes pulse {
    0% {
      box-shadow: 0 0 0 0 rgba(108, 91, 123, 0.4);
    }
    70% {
      box-shadow: 0 0 0 6px rgba(108, 91, 123, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(108, 91, 123, 0);
    }
  }
  .actor-diamond {
    position: absolute;
    top: -4px;

    right: -2px;
    display: flex;
    width: fit-content;
    height: 15px;
    padding: 3px 7px;
    justify-content: center;
    align-items: center;
    gap: 2px;
    flex-shrink: 0;
    border-radius: 34px;
    border: 1px solid #daff96;
    background: #1f0038;
    img {
      width: 10px;
      height: 10px;
    }
    &-coins {
      color: #daff96;
      font-family: 'Work Sans';
      font-size: 9px;
      font-style: normal;
      font-weight: 600;
      line-height: normal;
    }
  }
  // 添加高亮效果
  &.highlight-animation {
    position: relative;
    overflow: visible;

    &::after {
      content: '';
      position: absolute;
      inset: -5px;
      border-radius: 18px;
      background: linear-gradient(
        90deg,
        rgba(255, 215, 0, 0.2) 10%,
        rgba(255, 215, 0, 0.7) 25%,
        rgba(255, 215, 0, 1) 40%,
        rgba(255, 180, 0, 1) 50%,
        rgba(255, 215, 0, 1) 60%,
        rgba(255, 215, 0, 0.7) 75%,
        rgba(255, 215, 0, 0.2) 90%
      );
      background-size: 70% 100%;
      background-repeat: no-repeat;
      mask:
        linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
      mask-composite: exclude;
      -webkit-mask:
        linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
      -webkit-mask-composite: xor;
      padding: 10px;
      animation: borderMove 1.5s linear infinite;
      pointer-events: none;
      z-index: -1;
    }
  }
}

@keyframes borderMove {
  0% {
    background-position: -100% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes floatAnimation {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

@keyframes glowPulse {
  0%,
  100% {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
  50% {
    box-shadow: 0 4px 20px rgba(138, 43, 226, 0.6);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
