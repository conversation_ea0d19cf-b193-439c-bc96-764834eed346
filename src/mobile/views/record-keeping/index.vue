<template>
  <div class="legal-page">
    <div class="header">
      <div class="back-button" @click="router.back()">
        <icon-left />
      </div>
      <h1>18 U.S.C. 2257 Compliance</h1>
    </div>

    <div class="content">
      <div class="section">
        <h2>18 U.S.C. 2257 Record-Keeping Requirements Compliance Statement</h2>
        <p class="last-updated">Last Updated: June 3, 2025</p>
      </div>

      <div class="section notice">
        <h3>Exemption Statement</h3>
        <p>
          All visual content displayed on {{ appName.toLowerCase() }}.ai is exempt from the
          record-keeping requirements under 18 U.S.C. §2257, 2257A, and 28 C.F.R. 75 for the
          following reasons: our website does not depict actual human beings engaged in real
          sexually explicit conduct.
        </p>
      </div>

      <div class="section">
        <h3>AI-Generated Content Declaration</h3>
        <ul>
          <li>
            All content created from our website is exclusively generated by artificial intelligence
            (AI) and created entirely through algorithmic processes.
          </li>
          <li>
            No real human models, actors, actresses, or other individuals appear in any AI-generated
            images on this website.
          </li>
          <li>
            Our website does not permit users from uploading images containing real human models,
            actors, actresses, or other individuals.
          </li>
        </ul>
      </div>

      <div class="section">
        <h3>Compliance Commitment</h3>
        <p>
          {{ appName.toLowerCase() }}.ai is committed to adhering to all applicable laws and
          regulations and only provides AI-generated content that meets strict ethical and
          compliance standards. The content created from this website contains only text generated
          based on AI and does not contain images or videos.
        </p>
      </div>

      <div class="section">
        <h3>Contact Information</h3>
        <p>
          For any inquiries or clarifications regarding our compliance with 2257 record-keeping
          requirements, please contact support@{{ appName.toLowerCase() }}.ai.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { IconLeft } from '@arco-design/web-vue/es/icon'
import { computed } from 'vue'

const router = useRouter()
const appName = computed(() => import.meta.env.VITE_APP_NAME || 'Playshot')
</script>

<style lang="less" scoped>
.legal-page {
  height: calc(var(--vh, 1vh) * 100);
  background: #1f0038;
  color: rgba(255, 255, 255, 0.9);
  // padding-bottom: 40px;
}

@import '@/assets/style/theme.less';

.header {
  position: sticky;
  top: 0;
  z-index: 100;
  padding: 16px 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  border-bottom: 1px solid var(--border-color);
  transition: background 0.3s ease;
  background: #1f0038;
  .back-button {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--text-secondary);
    transition: all 0.3s;
    background: var(--bg-tertiary);
    border-radius: 50%;

    &:hover {
      color: var(--text-primary);
      background: var(--bg-hover);
    }

    :deep(.arco-icon) {
      font-size: 18px;
    }
  }

  h1 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
  }
}

.content {
  margin: 0 auto;
  padding: 24px 20px;
  background: #1f0038;
  .section {
    margin-bottom: 32px;

    &.notice {
      background: rgba(202, 147, 242, 0.1);
      border-radius: 12px;
      padding: 20px;
      border: 1px solid rgba(202, 147, 242, 0.2);
    }

    h2 {
      font-size: 24px;
      font-weight: 600;
      margin: 0 0 16px;
      color: #ca93f2;
    }

    h3 {
      font-size: 18px;
      font-weight: 600;
      margin: 0 0 12px;
      color: #ca93f2;
    }

    p {
      margin: 0 0 16px;
      line-height: 1.6;
      color: rgba(255, 255, 255, 0.8);
      font-size: 15px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    ul {
      margin: 12px 0;
      padding-left: 20px;

      li {
        margin-bottom: 8px;
        color: rgba(255, 255, 255, 0.8);
        font-size: 15px;
        line-height: 1.6;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

.last-updated {
  font-style: italic;
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
  margin-top: 8px;
}
</style>
