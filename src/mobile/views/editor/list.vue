<template>
  <div class="creation-list">
    <div class="list-header">
      <div class="title">故事配置列表</div>
    </div>

    <div class="list-content">
      <div v-if="loading" class="loading-wrapper">
        <a-spin />
      </div>
      <template v-else>
        <div class="story-grid">
          <!-- Create Story Card -->
          <div class="create-story-card" @click="handleCreate">
            <div class="create-icon">
              <icon-plus />
            </div>
            <div class="create-text">创建故事</div>
          </div>

          <!-- Story Cards -->
          <div
            v-for="story in storyList"
            :key="story.project_id"
            class="story-card"
            @click="handleEdit(story)"
          >
            <div class="story-preview">
              <img v-if="story.preview_url" :src="story.preview_url" :alt="story.name" />
              <div v-else class="no-preview">
                <icon-file />
              </div>
            </div>
            <div class="story-info">
              <div class="story-name">{{ story.name }}</div>
              <div class="story-status">{{ story.status }}</div>
            </div>
            <div class="story-actions">
              <a-button type="text" status="danger" @click.stop="handleDelete(story)">
                <template #icon>
                  <icon-delete />
                </template>
              </a-button>
            </div>
          </div>
        </div>
      </template>
    </div>

    <ConfirmDialog
      v-model:visible="showDeleteConfirm"
      title="删除确认"
      content="确定要删除这个故事吗？删除后无法恢复。"
      confirmText="确定"
      cancelText="取消"
      @confirm="confirmDelete"
    />
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { storeToRefs } from 'pinia'
import { useRouter } from 'vue-router'
import { Message } from '@arco-design/web-vue'
import { useEditorStore } from '@/store/editor'
import type { StoryInfo } from '@/api/editor'
import { deleteStory } from '@/api/editor'
import ConfirmDialog from '@/mobile/components/ConfirmDialog.vue'

const router = useRouter()
const editorStore = useEditorStore()
const showDeleteConfirm = ref(false)
const storyToDelete = ref<StoryInfo | null>(null)

const { loading, storyList } = storeToRefs(editorStore)

const handleCreate = () => {
  router.push({
    path: '/pc/character-select',
    query: { storyId: 'new' }
  })
}

const handleEdit = (story: StoryInfo) => {
  router.push(`/pc/flow-editor/${story.project_id}`)
}

const handleDelete = (story: StoryInfo) => {
  storyToDelete.value = story
  showDeleteConfirm.value = true
}

const confirmDelete = async () => {
  if (!storyToDelete.value) return

  try {
    await deleteStory(storyToDelete.value.project_id)
    Message.success('删除成功')
    showDeleteConfirm.value = false
    storyToDelete.value = null
    // 重新获取列表
    editorStore.fetchStoryList()
  } catch (error) {
    Message.error('删除失败')
  }
}

onMounted(() => {
  editorStore.fetchStoryList()
})
</script>

<style lang="less" scoped>
.creation-list {
  min-height: 100vh;
  background-color: #1f0038;
  color: #fff;

  .list-header {
    position: sticky;
    top: 0;
    z-index: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px;
    background-color: #1f0038;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    .title {
      font-size: 20px;
      font-weight: 600;
    }
  }

  .list-content {
    max-width: 1440px;
    margin: 0 auto;
    padding: 24px;

    .loading-wrapper {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;
    }

    .story-grid {
      display: grid;
      gap: 24px;
      padding: 4px;

      // 响应式布局
      @media screen and (max-width: 767px) {
        grid-template-columns: repeat(2, 1fr);
      }

      @media screen and (min-width: 768px) and (max-width: 1023px) {
        grid-template-columns: repeat(3, 1fr);
      }

      @media screen and (min-width: 1024px) and (max-width: 1439px) {
        grid-template-columns: repeat(4, 1fr);
      }

      @media screen and (min-width: 1440px) {
        grid-template-columns: repeat(5, 1fr);
      }

      .create-story-card,
      .story-card {
        aspect-ratio: 0.75;
        background: rgba(204, 213, 255, 0.05);
        border-radius: 16px;
        overflow: hidden;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
          background: rgba(204, 213, 255, 0.08);
        }

        &:active {
          transform: translateY(0);
        }
      }

      .create-story-card {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        border: 2px dashed rgba(255, 255, 255, 0.2);

        .create-icon {
          font-size: 40px;
          margin-bottom: 12px;
          color: rgba(255, 255, 255, 0.6);
        }

        .create-text {
          font-size: 16px;
          color: rgba(255, 255, 255, 0.6);
        }

        &:hover {
          border-color: #ca93f2;
          .create-icon,
          .create-text {
            color: #ca93f2;
          }
        }
      }

      .story-card {
        position: relative;

        .story-preview {
          height: 75%;
          background: rgba(0, 0, 0, 0.2);

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .no-preview {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;
            color: rgba(255, 255, 255, 0.3);
            font-size: 40px;
          }
        }

        .story-info {
          padding: 12px;

          .story-name {
            font-size: 15px;
            font-weight: 500;
            margin-bottom: 4px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .story-status {
            font-size: 13px;
            color: rgba(255, 255, 255, 0.6);
          }
        }

        .story-actions {
          position: absolute;
          top: 12px;
          right: 12px;
          opacity: 0;
          transition: opacity 0.3s ease;

          .arco-btn {
            color: rgba(255, 255, 255, 0.8);
            background: rgba(0, 0, 0, 0.5);
            border-radius: 50%;
            width: 36px;
            height: 36px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(8px);

            &:hover {
              color: rgb(255, 77, 79);
              background: rgba(0, 0, 0, 0.7);
            }
          }
        }

        &:hover {
          .story-actions {
            opacity: 1;
          }
        }
      }
    }
  }
}
</style>
