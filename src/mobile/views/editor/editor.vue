<template>
  <div class="pc-editor" v-show="isDesktop">
    <PCEditor />
  </div>
  <div class="mobile-editor" v-show="!isDesktop">
    <StoryEditor />
    <ActionBar />
  </div>
</template>

<script setup lang="ts">
import { useWindowSize } from '@vueuse/core'
import { computed } from 'vue'
import StoryEditor from '@/mobile/components/Editor/StoryEditor.vue'
import ActionBar from '@/mobile/components/Editor/ActionBar.vue'
import PCEditor from '@/mobile/components/Editor/PCEditor.vue'

const { width } = useWindowSize()
const isDesktop = computed(() => width.value >= 768)
</script>

<style lang="less" scoped>
.mobile-editor {
  min-height: calc(var(--vh, 1vh) * 100);
  height: calc(var(--vh, 1vh) * 100);
  background-color: #1f0038;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  :deep(.story-editor) {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch; // 增强 iOS 滚动体验
  }
}
</style>
