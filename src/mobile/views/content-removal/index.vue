<template>
  <div class="legal-page">
    <div class="header">
      <div class="back-button" @click="router.back()">
        <icon-left />
      </div>
      <h1>Content Removal Policy</h1>
    </div>

    <div class="content">
      <div class="section">
        <h2>Content Removal Policy</h2>
        <p>
          At {{ appName }} AI, we are committed to maintaining a respectful and safe environment for
          all users. We understand that certain content may not adhere to our guidelines or may be
          deemed inappropriate. This Content Removal Policy outlines the procedures and
          circumstances under which content may be removed from our platform.
        </p>
      </div>

      <div class="section notice">
        <h3>Types of Removable Content</h3>
        <ul>
          <li>Content that violates our Community Guidelines.</li>
          <li>Content that infringes upon intellectual property rights.</li>
          <li>Content that is considered illegal, harmful, or abusive.</li>
          <li>Content that involves impersonation or unauthorized use of personal information.</li>
        </ul>
      </div>

      <div class="section">
        <h3>Reporting Mechanism</h3>
        <ul>
          <li>
            Users can report inappropriate content through our reporting system available on each
            content page.
          </li>
          <li>
            Reports can also be sent directly to our support team at support@{{
              appName.toLowerCase()
            }}.ai.
          </li>
        </ul>
      </div>

      <div class="section">
        <h3>Review Process</h3>
        <ul>
          <li>All reported content is reviewed by our moderation team within 24 hours.</li>
          <li>
            During the review, content may be temporarily removed or restricted until a final
            decision is made.
          </li>
        </ul>
      </div>

      <div class="section">
        <h3>Consequences of Violation</h3>
        <p>
          Repeated violations of our Content Removal Policy may result in account suspension or
          termination.
        </p>
      </div>

      <div class="section">
        <h3>Contact Information</h3>
        <p>
          For any questions or concerns regarding this policy, please contact us at support@{{
            appName.toLowerCase()
          }}.ai.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { IconLeft } from '@arco-design/web-vue/es/icon'
import { computed } from 'vue'

const router = useRouter()
const appName = computed(() => import.meta.env.VITE_APP_NAME || 'Playshot')
</script>

<style lang="less" scoped>
.legal-page {
  height: calc(var(--vh, 1vh) * 100);
  background: #1f0038;
  color: rgba(255, 255, 255, 0.9);
  padding-bottom: 40px;
}

.header {
  position: sticky;
  top: 0;
  z-index: 100;
  background: #1f0038;
  padding: 16px 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  .back-button {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: rgba(255, 255, 255, 0.6);
    transition: all 0.3s;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;

    &:hover {
      color: white;
      background: rgba(255, 255, 255, 0.15);
    }

    :deep(.arco-icon) {
      font-size: 18px;
    }
  }

  h1 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
  }
}

.content {
  margin: 0 auto;
  padding: 24px 20px;
  background: #1f0038;
  .section {
    margin-bottom: 32px;

    &.notice {
      background: rgba(202, 147, 242, 0.1);
      border-radius: 12px;
      padding: 20px;
      border: 1px solid rgba(202, 147, 242, 0.2);
    }

    h2 {
      font-size: 24px;
      font-weight: 600;
      margin: 0 0 16px;
      color: #ca93f2;
    }

    h3 {
      font-size: 18px;
      font-weight: 600;
      margin: 0 0 12px;
      color: #ca93f2;
    }

    p {
      margin: 0 0 16px;
      line-height: 1.6;
      color: rgba(255, 255, 255, 0.8);
      font-size: 15px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    ul {
      margin: 12px 0;
      padding-left: 20px;

      li {
        margin-bottom: 8px;
        color: rgba(255, 255, 255, 0.8);
        font-size: 15px;
        line-height: 1.6;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
</style>
