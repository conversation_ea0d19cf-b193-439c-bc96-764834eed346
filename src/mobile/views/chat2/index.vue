<template>
  <div class="chat-wrapper">
    <!-- 主聊天容器 -->
    <ChatContainerWrapper
      ref="chatContainerWrapperRef"
      :is-playing-video="isPlayingVideo"
      :background-video="backgroundVideo"
      :background-image="backgroundImage"
      :default-image="defaultBackgroundImage"
      :animated-images="chatResourcesStore.animatedImages"
      :content-ready="contentReady"
      :loading-progress="loadingProgress"
      :loading-text="loadingText"
      :should-show-resource-loader="shouldShowResourceLoader"
      :is-error="chatUIStore.isError"
      @container-click="handleContainerClick"
      @back-home="handleBackHome"
    >
      <!-- 顶部导航栏 -->
      <template #top-bar>
        <ChatTopBar
          v-if="!chatUIStore.overlay && contentReady && !chatResourcesStore.isPlayingVideo"
          :show-diamond-tip="chatEventsStore.inPaidScene"
          :show-credit-display="true"
          :amount="userStore.userInfo?.coins || 0"
          :show-add-button="true"
          @back="handleBackHome"
          @add="handleAddCredit('top')"
        />
      </template>

      <!-- 视频播放器 -->
      <template #video-player>
        <VideoPlayer
          ref="videoManager.videoPlayerRef"
          :storyId="storyStore.currentStory?.id || ''"
          :actorId="storyStore.currentActor?.id || ''"
          :video-url="chatResourcesStore.videoUrl"
          :is-playing="chatResourcesStore.isPlayingVideo"
          :muted="audioManager.isMuted"
          :loading-background="storyStore.currentActor?.preview_url"
          :force-loading="false"
          @update:is-playing="(val) => (chatResourcesStore.isPlayingVideo = val)"
          @update:muted="
            (val) => {
              audioManager.isMuted = val
              audioManager.updateAllAudioStates()
            }
          "
          @video-ended="videoManager.onVideoEnded"
          @video-loaded="videoManager.onVideoLoaded"
          @video-skipped="videoManager.handleVideoSkipped"
        />
      </template>

      <!-- 聊天UI控件 -->
      <template #chat-ui-controls>
        <ChatUIControls
          ref="chatUIControlsRef"
          v-if="!chatUIStore.overlay && contentReady"
          :show-share="!isAndroidWebView()"
          :is-muted="audioManager.isMuted"
          @toggle-mute="audioManager.toggleMute"
          @toggle-share="showShareModal = true"
          @telepathy-complete="handleTelepathyComplete"
        />
      </template>

      <!-- 聊天界面 -->
      <template #chat-interface>
        <ChatInterface
          ref="chatInterfaceRef"
          v-show="shouldShowChat"
          :content-ready="contentReady"
          :visible-content="visibleContent"
          :show-chat-options="showChatOptions"
          :is-telepathy-complete="isTelepathyComplete"
          @collapse-task-tip="handleCollapseTaskTip"
          @add-credit="handleAddCredit"
          @scroll-to-bottom="scrollToBottom"
          @toggle-history="handleToggleHistory"
          @continue-click="handleContinueClick"
          @option-select="handleOptionSelect"
          @telepathy-complete="handleTelepathyComplete"
        />
      </template>

      <!-- 覆盖层内容 -->
      <template #overlay>
        <ChatOverlay
          v-if="chatUIStore.overlay"
          :overlay="chatUIStore.overlay"
          :say-hi-text="sayHiText"
          @overlay-button-click="handleOverlayButton"
        />
      </template>

      <!-- 结束内容 -->
      <template #ending>
        <EndingContent
          v-if="chatUIStore.isEnding && chatUIStore.endings"
          :endings="chatUIStore.endings"
          @restart="handleRestart"
        />
      </template>
    </ChatContainerWrapper>

    <!-- Modals -->
    <LeaveConfirmModal
      v-model:visible="showLeaveConfirmModal"
      @confirm="handleLeaveConfirm"
      @cancel="handleLeaveCancel"
    />
    <AuthDrawer
      :isInLandingPage="!!props.characterId"
      v-model:visible="showAuthDrawer"
      @login="handleLoginSuccess"
      @register="handleRegisterSuccess"
    />
    <RatingModal
      v-model:visible="storyStore.isShowRatingModal"
      @close="storyStore.isShowRatingModal = false"
      @submit="handleRatingSubmit"
    />
    <TagRatingModal
      v-model:visible="storyStore.isShowTagRatingModal"
      @close="storyStore.isShowTagRatingModal = false"
    />
    <ShareModal v-model:visible="showShareModal" />
    <EndingShareModal />

    <!-- 付费场景提醒对话框 -->
    <ConfirmDialog
      v-model:visible="chatEventsStore.showPaidSceneDialog"
      title="Spicy Level"
      :title-style="{ color: '#FFF' }"
      :content-style="{ color: '#CA93F2', fontWeight: '600', textAlign: 'left' }"
      confirm-text="I Understand"
      :show-cancel="false"
      :show-icon="false"
      :close-on-click-overlay="false"
      @confirm="chatEventsStore.showPaidSceneDialog = false"
    >
      <template #content>
        <p>You have entered the spicy level. Enjoy your spicy time.</p>
        <p>
          Note: This level offers 5 minutes of free time, and the subsequent content is priced at 5
          diamonds per 5 minutes.
        </p>
      </template>
    </ConfirmDialog>
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  computed,
  onMounted,
  onBeforeMount,
  onBeforeUnmount,
  watch,
  provide,
  getCurrentInstance
} from 'vue'
import { useRoute, useRouter, onBeforeRouteLeave } from 'vue-router'
import { useChatEventsStore } from '@/store/chat-events'
import { useChatUIStore } from '@/store/chat-ui'
import { useChatMessagesStore } from '@/store/chat-messages'
import { useChatResourcesStore } from '@/store/chat-resources'
import { useChatTasksStore } from '@/store/chat-tasks'
import { useStoryStore } from '@/store/story'
import { useUserStore } from '@/store/user'
import { useRechargeStore } from '@/store/recharge'
import { useSkillStore } from '@/store/skill'
import { setDynamicSEO } from '@/router/seo-guard'
import { reportEvent } from '@/utils/report'
import { ReportEvent } from '@/interface'
import { ChatOptions } from '@/types/chat'
import { Message } from '@/mobile/components/Message'
import { useGameInitialization } from './composables/useGameInitialization'
import { useAudioManager } from '@/mobile/composables/useAudioManager'
import VideoPlayer from '@/mobile/components/VideoPlayer.vue'
import { useVideoManager } from '@/mobile/composables/useVideoManager'
import { useVideoCanvas } from '@/mobile/composables/useVideoCanvas'

// 新的组件导入
import ChatContainerWrapper from './components/ChatContainerWrapper/index.vue'
import ChatUIControls from './components/ChatUIControls/index.vue'
import ChatInterface from './components/ChatInterface/index.vue'
import ChatOverlay from './components/ChatOverlay/index.vue'
import EndingContent from './components/EndingContent/index.vue'

// 保留的组件导入
import ChatTopBar from '@/mobile/components/ChatTopBar.vue'
import ShareModal from '@/mobile/views/chat2/components/ShareModal/index.vue'
import LeaveConfirmModal from '@/mobile/components/LeaveConfirmModal.vue'
import AuthDrawer from '@/mobile/components/AuthDrawer.vue'
import RatingModal from '@/mobile/components/RatingModal.vue'
import TagRatingModal from '@/mobile/components/TagRatingModal.vue'
import EndingShareModal from './components/EndingShareModal/index.vue'
import ConfirmDialog from '@/components/ConfirmDialog.vue'
import ResourceLoader from '@/shared/components/ResourceLoader.vue'
import { isAndroidWebView } from '@/utils/isAndroidWebView'
// Props
const props = defineProps<{
  characterId?: string
  storyId?: string
}>()

// Store
const route = useRoute()
const router = useRouter()
const chatEventsStore = useChatEventsStore()
const chatUIStore = useChatUIStore()
const chatMessagesStore = useChatMessagesStore()
const chatResourcesStore = useChatResourcesStore()
const chatTasksStore = useChatTasksStore()
const storyStore = useStoryStore()
const userStore = useUserStore()
const rechargeStore = useRechargeStore()
const skillStore = useSkillStore()
// 动态SEO将通过路由守卫自动处理
// Constants
const sayHiText = 'Say Hi'

// State
const visibleContent = ref<Record<string, { text: string; mood?: number }>>({})
const showAuthDrawer = ref(false)
const showChatOptions = ref(false)
const contentReady = ref(false)
const showShareModal = ref(false)
const chatVisible = ref(true) // 聊天界面是否可见

// 组件引用
const chatContainerWrapperRef = ref()
const chatUIControlsRef = ref()
const chatInterfaceRef = ref()

// ResourceLoader相关状态
const loadingProgress = ref(0)
const loadingText = ref('Initializing...')
const logoUrl = computed(
  () => import.meta.env.VITE_LOGO_URL || 'https://cdn.magiclight.ai/assets/playshot/logo-v2.png'
)

// 首次加载标记 - 每次组件创建时都重置
const isFirstTimeLoading = ref(true)

// Composables
const { startGame, handleVisibilityChange, cleanup, initialized } = useGameInitialization()
const audioManager = useAudioManager()
const videoManager = useVideoManager()
const { captureLastFrame } = useVideoCanvas()

// 资源预加载进度管理
const updateLoadingProgress = (progress: number, text?: string) => {
  loadingProgress.value = Math.min(100, Math.max(0, progress))
  if (text) {
    loadingText.value = text
  }
}

// 提供组件引用给子组件
provide('chatUIControlsRef', {
  checkShareStatus: () => chatUIControlsRef.value?.checkShareStatus(),
  collapseTaskTip: () => chatUIControlsRef.value?.collapseTaskTip()
})

// 监听分享模态框关闭
watch(showShareModal, (newVal) => {
  if (!newVal) {
    // 模态框关闭时，检查分享状态
    chatUIControlsRef.value?.checkShareStatus()
  }
})

// 监听背景视频变化，预先捕获最后一帧
watch(
  () => chatResourcesStore.backgroundVideo,
  async (newVideoUrl, oldVideoUrl) => {
    if (newVideoUrl && newVideoUrl !== oldVideoUrl) {
      try {
        // 当背景视频变化时，预先捕获最后一帧
        const frameUrl = await captureLastFrame(newVideoUrl)
        videoFrameUrl.value = frameUrl
      } catch (error) {
        console.warn('Failed to pre-capture video frame:', error)
      }
    }
  }
)

// Computed
const isPlayingVideo = computed(() => chatResourcesStore.isPlayingVideo)
const backgroundVideo = computed(() => chatResourcesStore.backgroundVideo)
const backgroundImage = computed(() => chatResourcesStore.backgroundImage)
const defaultBackgroundImage = computed(() => storyStore.currentActor?.preview_url)
// 未使用的计算属性
// const isTypingComplete = computed(() => messageListRef.value?.isTypingComplete || false)
const isTelepathyComplete = computed(() => chatMessagesStore.heartValue >= 100)
const isLegacyVersion = computed(() => route.meta.version === 'legacy')

// 存储视频帧的 ref
const videoFrameUrl = ref<string | null>(null)

const loadingState = computed(() => {
  const isInitializing = chatUIStore.isInitializing
  const isResourceLoading = chatResourcesStore.isResourceLoading
  const isNotInitialized = !initialized.value

  return {
    // 基础加载状态
    isLoading: isInitializing || isResourceLoading,
    // 任何加载状态（包含初始化）
    isAnyLoading: isNotInitialized || isInitializing || isResourceLoading,
    // 首次加载显示ResourceLoader
    shouldShowResourceLoader:
      isFirstTimeLoading.value && (isNotInitialized || isInitializing || isResourceLoading)
  }
})

const shouldShowChat = computed(() => {
  // 只有在没有错误、已初始化、聊天可见且不在任何加载状态时才显示聊天界面
  return (
    !chatUIStore.isError &&
    initialized.value &&
    chatVisible.value &&
    !loadingState.value.isAnyLoading
  )
})

// 解构便于使用
const isAnyLoading = computed(() => loadingState.value.isAnyLoading)
const shouldShowResourceLoader = computed(() => loadingState.value.shouldShowResourceLoader)

// 监听初始化状态，更新加载进度
watch(
  () => chatUIStore.isInitializing,
  (isInitializing) => {
    if (isInitializing) {
      // 开始初始化时，确保进度从0开始
      if (isFirstTimeLoading.value) {
        updateLoadingProgress(0, 'Initializing...')

        // 模拟进度更新
        const progressInterval = setInterval(() => {
          if (!chatUIStore.isInitializing) {
            clearInterval(progressInterval)
            return
          }

          const currentProgress = loadingProgress.value
          if (currentProgress < 90) {
            // 渐进式增加进度，但不超过90%
            const increment = Math.max(1, Math.floor((90 - currentProgress) / 10))
            updateLoadingProgress(currentProgress + increment)
          }
        }, 200)

        // 监听资源加载状态
        const resourceInterval = setInterval(() => {
          if (!chatUIStore.isInitializing) {
            clearInterval(resourceInterval)
            return
          }

          if (chatResourcesStore.isResourceLoading) {
            updateLoadingProgress(Math.min(85, loadingProgress.value), 'Loading resources...')
          } else if (initialized.value) {
            updateLoadingProgress(Math.min(95, loadingProgress.value), 'Almost ready...')
          }
        }, 100)
      }
    } else {
      // 初始化完成，快速完成进度
      if (isFirstTimeLoading.value) {
        updateLoadingProgress(100, 'Ready!')
      }
    }
  }
)

// 简化的首次加载完成监听
watch(
  () => isAnyLoading.value,
  (loading) => {
    // 当所有加载都完成时，切换到后续模式
    if (!loading && isFirstTimeLoading.value && initialized.value) {
      setTimeout(() => {
        isFirstTimeLoading.value = false
      }, 800) // 给ResourceLoader足够时间显示"Ready!"
    }
  }
)

// Methods
const handleCollapseTaskTip = () => {
  chatUIControlsRef.value?.collapseTaskTip()
}
const handleRestart = async () => {
  reportEvent(ReportEvent.GameEndClickRestart, {
    storyId: (route.params?.storyId as string) || storyStore.currentStory?.id,
    actorId: props.characterId || route.params.actorId
  })
  // 清空聊天，调用各个 store 的清空方法
  chatEventsStore.clearChat()
  const actorId = (route.params.actorId as string) || props.characterId
  if (actorId) {
    chatResourcesStore.isFirstVideo = false
    await chatEventsStore.initializeChat(actorId)
  }
}
const handleContainerClick = async (e: MouseEvent) => {
  // 如果点击的是输入框区域，不处理
  const target = e.target as HTMLElement
  if (
    target.closest('.chat-input') ||
    target.closest('.task-tip-container') ||
    target.closest('.heart-value-container') ||
    target.closest('.audio-control') ||
    target.closest('.overlay-container') ||
    target.closest('.back-button') ||
    target.closest('.ending-content') ||
    target.closest('.video-controls') ||
    target.closest('.chat-option-button') ||
    target.closest('.back-scene-button') ||
    target.closest('.next-scene-button')
  ) {
    return
  }

  // 如果有消息正在打印，优先结束打字动画
  if (chatMessagesStore.messageTypingPromise) {
    // 通过 chatInterfaceRef 找到当前正在打字的消息组件并调用其 handleMessageClick 方法
    await chatInterfaceRef.value?.skipCurrentTyping()
    return
  }

  // 如果输入框存在且可见，显示提示动画
  chatInterfaceRef.value?.showAttentionAnimation()
}

const handleBackHome = () => {
  reportEvent(ReportEvent.BackHome, {
    userId: userStore.userInfo?.uuid,
    recentMessages: chatMessagesStore.messages.slice(-3),
    actorId: props.characterId || route.params.actorId
  })
  router.push('/')
}

const handleAddCredit = (actionType: 'pay_now' | 'option' | 'top') => {
  if (userStore.isGuest) {
    showAuthDrawer.value = true
  } else {
    rechargeStore.toggleRechargeModal()
  }
  reportEvent(ReportEvent.ClickAddCreditInChat, {
    userId: userStore.userInfo?.uuid,
    actorId: props.characterId || route.params.actorId,
    storyId: storyStore.currentStory?.id,
    actionType
  })
}

const handleLoginSuccess = async () => {
  await userStore.getUserInfo()
  showAuthDrawer.value = false
}

const handleRegisterSuccess = () => {
  showAuthDrawer.value = false
}

const scrollToBottom = () => {
  if (chatResourcesStore.isPlayingVideo) return
  setTimeout(() => {
    const messagesList = chatInterfaceRef.value?.messageListRef?.messagesListRef
    if (messagesList) {
      messagesList.scrollTo({
        top: messagesList.scrollHeight,
        behavior: 'smooth'
      })
    }
  }, 500)
}

const isMessagePlaying = (message: any) => {
  return message.content.audio_url && chatMessagesStore.currentPlayingMessageId === message.id
}

// 注意：此函数目前未使用，但保留以备将来使用
// const handlePlayTTS = (message: any) => {
//   if (!message.content.audio_url) return
//
//   // 直接使用audioManager，传递当前语音ID
//   audioManager.playTTSFromUrl(
//     message.content.audio_url,
//     message.id,
//     chatMessagesStore.voiceConfig.voice_id,
//     chatMessagesStore.voiceConfig.provider
//   )
// }

const handleOverlayButton = () => {
  if (chatUIStore.overlay?.button) {
    chatUIStore.handleOverlayButtonClick()
  }
}

const handleToggleHistory = () => {
  chatInterfaceRef.value?.toggleHistory()
}

// 添加选项选择处理方法
const handleOptionSelect = async (option: ChatOptions) => {
  if (
    option.paid_required &&
    !option.is_purchased &&
    userStore.userInfo?.coins < (option.coins || 0)
  ) {
    handleAddCredit('option')
    return
  }

  if (option.scene_id) {
    await chatEventsStore.sendMessage(option.text, option.option_id, 'text', true, option.scene_id)
  } else {
    await chatEventsStore.sendMessage(option.text, option.option_id)
  }
  reportEvent(ReportEvent.ClickChat2Option, {
    optionId: option.option_id,
    optionText: option.text,
    sceneId: option.scene_id
  })
  showChatOptions.value = false
  chatMessagesStore.heartValue = 0
  chatResourcesStore.chatCard2 = null
  handleCollapseTaskTip()
}

const handleTelepathyComplete = async () => {
  if (chatResourcesStore.chatCard2?.length) {
    showChatOptions.value = true
    return
  }
  await chatEventsStore.sendMessage('', null, 'text', true, null, 0)
  handleCollapseTaskTip()
}

// 处理"点击继续"按钮点击事件
const handleContinueClick = async () => {
  // 确保只有在心灵感应完成时才处理
  // if (!isTelepathyComplete.value) return

  // 如果有选项卡，显示选项
  // if (chatResourcesStore.chatCard2?.length) {
  //   showChatOptions.value = true
  //   return
  // }

  // 否则发送空消息继续对话
  await chatEventsStore.sendMessage('', null, 'text', true, null, 0)
  chatMessagesStore.heartValue = 0 // 重置心灵感应值
  handleCollapseTaskTip()
}

// Navigation Guards
let nextCallback: any = null

const showLeaveConfirmModal = ref(false)
const lastHiddenTime = ref(0)
const totalHiddenTime = ref(0)
const startTime = ref(0)

const handleLeaveConfirm = () => {
  showLeaveConfirmModal.value = false
  if (nextCallback) {
    chatEventsStore.clearChat()
    nextCallback()
    if (document.hidden && lastHiddenTime.value > 0) {
      totalHiddenTime.value += Date.now() - lastHiddenTime.value
    }

    const totalDuration = Date.now() - startTime.value
    const visibleDuration = Math.max(0, Math.floor((totalDuration - totalHiddenTime.value) / 1000))

    reportEvent(ReportEvent.ChatDuration, {
      characterId: props.characterId,
      duration: visibleDuration,
      userId: userStore.userInfo?.uuid,
      isInChat: !props.characterId,
      actorId: props.characterId || route.params.actorId
    })
  }
}

const handleLeaveCancel = () => {
  showLeaveConfirmModal.value = false
  nextCallback = null
}

onBeforeRouteLeave((to, _from, next) => {
  console.log(to)
  // 如果是前往登录页面、发生错误、需要充值或已经通过钻石用完模态框确认离开，则直接离开
  if (
    to.path === '/user/login' ||
    chatUIStore.isError ||
    storyStore.isNeedRecharge ||
    chatEventsStore.isConfirmedLeave
  ) {
    console.log(chatEventsStore.isConfirmedLeave)
    // 如果是通过钻石用完模态框确认离开，重置标记
    if (chatEventsStore.isConfirmedLeave) {
      chatEventsStore.isConfirmedLeave = false
    }
    next()
    return
  }
  nextCallback = next
  console.log('leave')
  showLeaveConfirmModal.value = true
})

const handleRatingSubmit = async (
  _ratings: { story: number; character: number; image: number },
  _comment: string
) => {
  try {
    // todo: 评分表单
    const isOk = true
    if (isOk) {
      storyStore.isShowRatingModal = false
      Message.success('Thank you for your feedback!')
    } else {
      Message.error('Failed to submit feedback')
    }
  } catch (error) {
    Message.error('Failed to submit feedback')
  }
}
// Lifecycle Hooks
onBeforeMount(async () => {
  if (isLegacyVersion.value) {
    chatUIStore.setLegacyVersion(true)
  }
})

onMounted(async () => {
  const { proxy } = getCurrentInstance()
  // @ts-ignore
  proxy?.$tracker?.setUserID(userStore.userInfo?.uuid)
  if (!props.characterId) {
    reportEvent(ReportEvent.StartChatFromIndexPage, {
      actorId: route.params.actorId
    })
  }

  const actorId = (route.params.actorId as string) || props.characterId
  const storyId = (route.params.storyId as string) || props.storyId
  if (actorId) {
    await startGame(actorId, storyId)
  }

  // 设置聊天页面SEO
  if (storyStore.currentStory) {
    const actorName = storyStore.currentActor?.name
    setDynamicSEO('Chat2', {
      story: storyStore.currentStory,
      actorName
    })
  }

  document.addEventListener('visibilitychange', handleVisibilityChange)

  // 尝试捕获当前背景视频的最后一帧
  if (chatResourcesStore.backgroundVideo) {
    try {
      const frameUrl = await captureLastFrame(chatResourcesStore.backgroundVideo)
      videoFrameUrl.value = frameUrl
    } catch (error) {
      console.warn('Failed to capture initial video frame:', error)
    }
  }
})

// 监听overlay的变化
watch(
  [
    () => chatUIStore.overlay,
    () => chatUIStore.isRedirectToChat,
    () => chatMessagesStore.chatHistory
  ],
  ([_overlay, isRedirectToChat, chatHistory]) => {
    if (isRedirectToChat || chatHistory.length >= 0) {
      contentReady.value = true
    }
    // 恢复聊天记录
    if (chatHistory.length > 0) {
      chatMessagesStore.messages = chatHistory
      chatResourcesStore.isFirstVideo = false
    }
  },
  { immediate: true }
)

onBeforeUnmount(() => {
  cleanup()
  skillStore.selectedSkills = []
  document.removeEventListener('visibilitychange', handleVisibilityChange)
})

defineOptions({
  name: 'Chat2View'
})
</script>

<style lang="less" scoped>
.chat-wrapper {
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
  position: relative;
  overflow: hidden;
}
</style>
