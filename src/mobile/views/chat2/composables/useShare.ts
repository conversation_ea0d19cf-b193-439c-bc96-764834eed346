import { ref, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { useShare } from '@vueuse/core'
import { getSharePoster, createShare, getShareInfo, getEndingSharePoster } from '@/api/share'
import { Message } from '@/mobile/components/Message'
import { useStoryStore } from '@/store/story'
import { useCache } from '@/composables/useCache'
import { useUserStore } from '@/store/user'
import { ReportEvent } from '@/interface/report'
import { reportEvent } from '@/utils/report'

// 生成缓存key
const generateCacheKey = (storyId: string, actorId: string) => `${storyId}-${actorId}`

// 图片预处理配置
const IMAGE_CONFIG = {
  maxWidth: 1200,
  maxHeight: 630, // 1.91:1 比例的高度
  quality: 0.9,
  type: 'image/jpeg',
  targetAspectRatio: 1.91 // 社交媒体推荐的宽高比
}

// 预处理图片
const preprocessImage = async (imageUrl: string): Promise<{ blob: Blob; url: string }> => {
  // 创建一个 canvas 元素
  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')
  if (!ctx) throw new Error('Failed to get canvas context')

  // 加载图片
  const img = new Image()
  img.crossOrigin = 'anonymous' // 处理跨域图片

  await new Promise((resolve, reject) => {
    img.onload = resolve
    img.onerror = reject
    img.src = imageUrl
  })

  // 获取原始尺寸
  const { width: originalWidth, height: originalHeight } = img

  // 计算目标尺寸
  let targetWidth = IMAGE_CONFIG.maxWidth
  let targetHeight = Math.round(targetWidth / IMAGE_CONFIG.targetAspectRatio)

  // 如果高度超过最大高度，则以高度为基准重新计算
  if (targetHeight > IMAGE_CONFIG.maxHeight) {
    targetHeight = IMAGE_CONFIG.maxHeight
    targetWidth = Math.round(targetHeight * IMAGE_CONFIG.targetAspectRatio)
  }

  // 设置 canvas 尺寸
  canvas.width = targetWidth
  canvas.height = targetHeight

  // 填充黑色背景
  ctx.fillStyle = '#1f0038'
  ctx.fillRect(0, 0, targetWidth, targetHeight)

  // 计算缩放比例，确保图片完整显示且不会被裁剪
  const scaleWidth = targetWidth / originalWidth
  const scaleHeight = targetHeight / originalHeight
  const scale = Math.min(scaleWidth, scaleHeight) // 使用较小的缩放比例，确保图片完全适应

  // 计算居中位置
  const scaledWidth = originalWidth * scale
  const scaledHeight = originalHeight * scale
  const x = (targetWidth - scaledWidth) / 2
  const y = (targetHeight - scaledHeight) / 2

  // 绘制图片（居中并保持完整）
  ctx.drawImage(img, x, y, scaledWidth, scaledHeight)

  // 转换为 blob
  const blob = await new Promise<Blob>((resolve) =>
    canvas.toBlob(
      (blob) => {
        resolve(blob as Blob)
      },
      IMAGE_CONFIG.type,
      IMAGE_CONFIG.quality
    )
  )

  // 创建新的 URL
  const url = URL.createObjectURL(blob)

  return { blob, url }
}

export const useShareFeature = () => {
  const route = useRoute()
  const storyStore = useStoryStore()
  const userStore = useUserStore()
  const { share, isSupported } = useShare()
  const posterUrl = ref('')
  const hasShared = ref(false)
  const isLoading = ref(false)

  // 延迟初始化缓存
  const cacheInstance = ref()
  const initCache = () => {
    if (!cacheInstance.value) {
      cacheInstance.value = useCache({
        maxSize: 20,
        ttl: 4 * 60 * 1000,
        persistKey: 'share-posters'
      })
    }
    return cacheInstance.value
  }

  const checkShareStatus = async () => {
    if (!storyStore.currentStory?.id || !storyStore.currentActor?.id) return
    try {
      const {
        data: { data }
      } = await getShareInfo(storyStore.currentStory.id, storyStore.currentActor.id)
      hasShared.value = data.is_rewarded
    } catch (error) {
      console.error('Failed to check share status:', error)
    }
  }

  const getPoster = async (isEnding = false) => {
    try {
      const storyId = storyStore.currentStory?.id
      const actorId = storyStore.currentActor?.id

      if (!storyId || !actorId) {
        console.error('Story or actor ID is missing')
        return null
      }

      // 如果是结局海报，直接获取不使用缓存
      if (isEnding) {
        isLoading.value = true
        const { data } = await getEndingSharePoster(storyId, actorId)
        const url = data.data.poster_url
        posterUrl.value = url
        return url
      }

      const cacheKey = generateCacheKey(storyId, actorId)
      const cache = initCache()

      // 检查缓存中是否存在
      const cachedUrl = cache.get(cacheKey)
      if (cachedUrl) {
        posterUrl.value = cachedUrl
        return cachedUrl
      }

      isLoading.value = true
      const { data } = await getSharePoster(storyId, actorId)
      const url = data.data.poster_url

      // 将结果存入缓存
      cache.set(cacheKey, url)
      posterUrl.value = url
      return url
    } catch (error) {
      console.error('Failed to get share poster:', error)
      return null
    } finally {
      isLoading.value = false
    }
  }

  const recordShareSuccess = async () => {
    if (!storyStore.currentStory?.id || !storyStore.currentActor?.id) return false

    try {
      // Check if already shared
      const {
        data: { data: shareInfo }
      } = await getShareInfo(storyStore.currentStory.id, storyStore.currentActor.id)
      if (shareInfo.is_rewarded) {
        Message.warning('Share successfully, only one reward per story')
        return false
      }

      // Create share record
      const {
        data: { data: createResult }
      } = await createShare(storyStore.currentStory.id, storyStore.currentActor.id)
      if (createResult?.user) {
        hasShared.value = true
        Message.success(`Share successfully, get ${shareInfo.coins} coins reward!`)
        // 更新用户信息
        userStore.setUserInfo(createResult.user)
        return true
      }
      return true
    } catch (error) {
      console.error('Failed to record share:', error)
      return false
    }
  }

  const downloadImage = async (isEnding = false) => {
    try {
      isLoading.value = true
      const url = await getPoster(isEnding)
      if (!url) {
        Message.error('Failed to get share image')
        return false
      }

      const response = await fetch(url)
      const blob = await response.blob()
      const downloadUrl = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = downloadUrl
      a.download = 'share-poster.png'
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(downloadUrl)
      document.body.removeChild(a)

      // Record successful share
      const success = await recordShareSuccess()
      if (!success) {
        Message.success('Image saved successfully!')
      }
      return success
    } catch (error) {
      console.error('Failed to download image:', error)
      Message.error('Failed to save image')
      return false
    } finally {
      isLoading.value = false
    }
  }

  const handleShare = async (isEnding = false) => {
    if (!isSupported.value) {
      Message.error('Your browser does not support sharing')
      return false
    }

    try {
      isLoading.value = true
      const url = await getPoster(isEnding)
      if (!url) {
        Message.error('Failed to get share image')
        return false
      }

      const description = `Not Just chatbot! Meet and feel your true love on Reelplay!`

      const shareData: ShareData = {
        title: 'ReelPlay - Interactive Stories',
        text: description,
        url: `https://reelplay.ai/?s=${storyStore.currentStory?.id}&a=${storyStore.currentActor?.id}`
      }

      // Try to fetch and share the image
      try {
        const response = await fetch(url)
        const originalBlob = await response.blob()

        const { blob: processedBlob, url: processedUrl } = await preprocessImage(url)

        // Add image to share data
        const shareFile = new File([processedBlob], 'share-poster.jpg', { type: IMAGE_CONFIG.type })
        shareData.files = [shareFile]

        // Add preview image and metadata
        if ('share' in navigator && 'canShare' in navigator) {
          const previewShare = {
            ...shareData,
            image: processedUrl,
            metadata: {
              'og:title': shareData.title,
              'og:description': description,
              'og:image': processedUrl,
              'og:url': shareData.url,
              'og:type': 'website',
              'og:site_name': 'ReelPlay',
              'twitter:card': 'summary_large_image',
              'twitter:title': shareData.title,
              'twitter:description': description,
              'twitter:image': processedUrl
            }
          }

          const canShareWithPreview = await navigator.canShare(previewShare)

          if (canShareWithPreview) {
            await share(previewShare)
            URL.revokeObjectURL(processedUrl)
            return true
          }
        }

        // Fallback to normal share
        const canShare = await navigator.canShare(shareData)

        if (canShare) {
          await share(shareData)
          URL.revokeObjectURL(processedUrl)
          return true
        }

        URL.revokeObjectURL(processedUrl)
        Message.error('Your device does not support sharing this content')
        return false
      } catch (error) {
        console.error('Failed to fetch share image:', error)
        // Try sharing without image if image sharing fails
        await share({
          title: shareData.title,
          text: shareData.text,
          url: shareData.url
        })
        return true
      }
    } catch (error) {
      console.error('Share failed:', error)
      // Message.error('Failed to share')
      return true
    } finally {
      isLoading.value = false
    }
  }

  const reportShareDataByLink = async () => {
    await nextTick()
    const { s, a } = route.query
    if (!s || !a) return
    try {
      await reportEvent(ReportEvent.VisitShareByLink, {
        storyId: s,
        actorId: a
      })
    } catch (error) {
      console.error('Failed to report share data:', error)
    }
  }

  return {
    handleShare,
    checkShareStatus,
    getPoster,
    posterUrl,
    hasShared,
    isLoading,
    isSupported,
    downloadImage,
    recordShareSuccess,
    reportShareDataByLink
  }
}
