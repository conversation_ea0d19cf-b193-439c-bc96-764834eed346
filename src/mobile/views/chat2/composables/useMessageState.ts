import { ref, computed, watch, onUnmounted } from 'vue'
import { useAudioManager } from '@/mobile/composables/useAudioManager'
import { useChatMessagesStore } from '@/store/chat-messages'
import { useChatUIStore } from '@/store/chat-ui'

export interface Message {
  id: string
  msg_type: 'text' | 'image' | 'video' | 'config'
  sender_type: 'user' | 'actor' | 'system' | 'tips'
  content: {
    text?: string
    media_url?: string
    html?: string
    audio_url?: string
  }
  create_time: string
  sender: {
    id?: string
    name: string
    avatar_url?: string
  }
}

export function useMessageState() {
  const chatMessagesStore = useChatMessagesStore()
  const chatUIStore = useChatUIStore()
  const audioManager = useAudioManager()

  const currentMessageIndex = ref(0)
  const showUserMessage = ref(true)
  const isTypingComplete = ref(true)
  const isTyping = ref(false)
  const showThinkingState = ref(false) // 控制思考状态的显示
  const actualThinkingState = ref(false) // 实际的思考状态
  const thinkingTimer = ref<number | null>(null) // 用于防抖的定时器

  // 监听实际思考状态的变化
  watch(
    () => {
      return (
        chatUIStore.streaming || chatMessagesStore.isActorThinking || audioManager.state.ttsLoading
      )
    },
    (newValue: boolean) => {
      actualThinkingState.value = Boolean(newValue)

      // 如果变为true，立即更新
      if (newValue) {
        if (thinkingTimer.value) {
          clearTimeout(thinkingTimer.value)
          thinkingTimer.value = null
        }
      }
      // 如果变为false，延迟更新，防止状态频繁切换
      else {
        if (!thinkingTimer.value) {
          thinkingTimer.value = window.setTimeout(() => {
            // 确保500ms后仍然是false才更新
            if (!actualThinkingState.value) {
              actualThinkingState.value = false
            }
            thinkingTimer.value = null
          }, 500) // 500ms的延迟，可以根据需要调整
        }
      }
    }
  )

  const isThinking = computed(() => {
    return showThinkingState.value && actualThinkingState.value
  })

  const handleNewMessage = (message: Message) => {
    // 重置思考状态显示
    showThinkingState.value = false

    if (message.sender_type === 'user') {
      showUserMessage.value = true
      // 2秒后隐藏用户消息并显示思考状态
      setTimeout(() => {
        showUserMessage.value = false
        showThinkingState.value = true
      }, 2000)
    } else {
      // 非用户消息立即显示思考状态
      showThinkingState.value = true
    }

    // 无论是什么类型的消息，都先设置为未完成状态
    isTypingComplete.value = false

    if (message.sender_type === 'actor') {
      isTyping.value = true
    } else {
      // 非actor消息（比如system消息）立即设置为完成状态
      setTimeout(() => {
        isTypingComplete.value = true
        isTyping.value = false
      }, 0)
    }
  }

  const handleTypingComplete = () => {
    isTypingComplete.value = true
    isTyping.value = false
    // 打字完成后不需要再显示思考状态
    showThinkingState.value = false
  }

  const shouldShowMessage = (message: Message) => {
    if (!message) return false

    // 用户消息显示受 showUserMessage 控制
    if (message.sender_type === 'user') {
      return showUserMessage.value
    }

    // 机器人消息在打字过程中或完成后显示
    if (message.sender_type === 'actor') {
      return !isThinking.value || isTypingComplete.value || isTyping.value
    }

    // 系统消息始终显示
    return true
  }

  // 清理定时器
  onUnmounted(() => {
    if (thinkingTimer.value) {
      clearTimeout(thinkingTimer.value)
      thinkingTimer.value = null
    }
  })

  return {
    currentMessageIndex,
    isTypingComplete,
    isTyping,
    showUserMessage,
    isThinking,
    showThinkingState,
    actualThinkingState,
    handleNewMessage,
    handleTypingComplete,
    shouldShowMessage
  }
}
