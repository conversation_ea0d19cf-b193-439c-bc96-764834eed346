import { ref } from 'vue'

export interface TypewriterOptions {
  typingSpeed?: number
  punctuationDelay?: number
  initialDelay?: number
}

export function useTypewriter(options: TypewriterOptions = {}) {
  const { typingSpeed = 50, punctuationDelay = 300, initialDelay = 200 } = options

  const displayText = ref('')
  const isTyping = ref(false)
  let currentText = '' // 保存完整的文本内容
  let skipRequested = false // 标记是否请求跳过动画
  let currentTextVersion = 0 // 添加版本控制

  const clearQueue = () => {
    displayText.value = ''
    isTyping.value = false
    currentText = ''
    skipRequested = false
    currentTextVersion++ // 增加版本号
  }

  const skipTyping = () => {
    if (isTyping.value && currentText) {
      skipRequested = true
      displayText.value = currentText
      isTyping.value = false
      currentTextVersion++ // 增加版本号
      return true
    }
    return false
  }

  const typeMessage = async (text: string, forceUpdate = false) => {
    // 如果是强制更新，直接设置文本
    if (forceUpdate) {
      displayText.value = text
      isTyping.value = false
      return true
    }

    // 记录当前版本
    const thisVersion = ++currentTextVersion

    // 保存完整文本
    currentText = text

    // 清空显示状态
    displayText.value = ''
    isTyping.value = false
    skipRequested = false

    if (!currentText) {
      return true
    }

    isTyping.value = true
    let currentDisplayText = ''

    // 等待初始延迟
    if (!skipRequested && thisVersion === currentTextVersion) {
      await new Promise((resolve) => setTimeout(resolve, initialDelay))
    }

    // 使用 currentText 而不是 text 参数来进行打字效果
    for (let i = 0; i < currentText.length; i++) {
      // 检查版本是否仍然匹配
      if (thisVersion !== currentTextVersion) {
        return false
      }

      // 如果请求跳过，直接显示完整文本并退出
      if (skipRequested) {
        displayText.value = currentText
        isTyping.value = false
        skipRequested = false
        return true
      }

      const char = currentText[i]
      const nextChar = currentText[i + 1]

      try {
        await new Promise((resolve) => {
          const delay = /[,.!?]/.test(char) && nextChar === ' ' ? punctuationDelay : typingSpeed
          setTimeout(() => {
            // 再次检查版本是否匹配
            if (thisVersion === currentTextVersion) {
              currentDisplayText += char
              displayText.value = currentDisplayText
            }
            resolve(true)
          }, delay)
        })
      } catch (error) {
        console.error('Typing error:', error)
        isTyping.value = false
        return false
      }
    }

    // 最后一次检查版本
    if (thisVersion === currentTextVersion) {
      isTyping.value = false
      return true
    }
    return false
  }

  return {
    displayText,
    isTyping,
    typeMessage,
    clearQueue,
    skipTyping,
    skipRequested
  }
}
