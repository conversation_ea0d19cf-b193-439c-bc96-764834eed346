import { ref, computed } from 'vue'
import type { Message } from './useMessageState'

export function useHistoryState() {
  const showScrollBottom = ref(false)
  const messagesContainer = ref<HTMLElement | null>(null)

  const handleScroll = () => {
    if (!messagesContainer.value) return

    const { scrollTop, scrollHeight, clientHeight } = messagesContainer.value
    const scrolledUp = scrollHeight - scrollTop - clientHeight > 100
    showScrollBottom.value = scrolledUp
  }

  const scrollToBottom = () => {
    if (messagesContainer.value) {
      // Force a reflow to ensure scrollHeight is accurate
      void messagesContainer.value.offsetHeight

      messagesContainer.value.scrollTo({
        top: messagesContainer.value.scrollHeight,
        behavior: 'smooth'
      })
    }
  }

  return {
    showScrollBottom,
    messagesContainer,
    handleScroll,
    scrollToBottom
  }
}
