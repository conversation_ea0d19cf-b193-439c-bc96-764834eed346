import { computed, ref, watchEffect } from 'vue'

interface MessageContent {
  text?: string
  media_url?: string
  html?: string
  audio_url?: string
}

export function useMessageActions(content: MessageContent) {
  const currentText = ref<string>('')

  watchEffect(() => {
    currentText.value = content.text || ''
  })

  const actionText = computed(() => {
    if (!currentText.value) return ''
    const matches = currentText.value.match(/\*(.*?)\*/g)
    if (!matches) return ''
    return matches.map((match) => match.replace(/^\*|\*$/g, '')).join(' ')
  })

  const dialogText = computed(() => {
    if (!currentText.value) return ''
    return currentText.value.replace(/\*(.*?)\*/g, '').trim()
  })

  const historyDialogText = computed(() => {
    if (!currentText.value) return ''
    return currentText.value.replace(
      /\*(.*?)\*/g,
      (_, action) => `<span class="history-action">(${action})</span>`
    )
  })

  const hasAction = computed(() => {
    return actionText.value !== ''
  })

  const hasAudio = computed(() => {
    return !!content.audio_url
  })

  return {
    actionText,
    dialogText,
    historyDialogText,
    hasAction,
    hasAudio
  }
}
