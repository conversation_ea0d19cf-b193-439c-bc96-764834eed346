import { computed } from 'vue'

export interface MoodIndicatorOptions {
  mood?: number
  labels?: {
    negative: string
    neutral: string
    positive: string
  }
}

const defaultLabels = {
  negative: 'Negative',
  neutral: 'Neutral',
  positive: 'Positive'
}

export function useMoodIndicator(options: MoodIndicatorOptions = {}) {
  const { mood, labels = defaultLabels } = options

  const hasMood = computed(() => {
    return typeof mood !== 'undefined'
  })

  const moodValue = computed(() => {
    if (typeof mood === 'undefined') return 50
    return (mood + 1) * 50
  })

  const moodLabels = computed(() => {
    return [labels.negative, labels.neutral, labels.positive]
  })

  return {
    hasMood,
    moodValue,
    moodLabels
  }
}
