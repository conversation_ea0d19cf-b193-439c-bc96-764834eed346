import { ref } from 'vue'
// 导入新的分离后的 store
import { useChatEventsStore } from '@/store/chat-events'
import { useChatMessagesStore } from '@/store/chat-messages'
import { useChatResourcesStore } from '@/store/chat-resources'
import { useChatUIStore } from '@/store/chat-ui'
import { useStoryStore } from '@/store/story'
import { useUserStore } from '@/store/user'
import { useVideoManager } from '@/mobile/composables/useVideoManager'
import { useAudioManager } from '@/mobile/composables/useAudioManager'
import { reportEvent } from '@/utils/report'
import { ReportEvent } from '@/interface'
import { sendUserPlayedGameEvent } from '@/utils/googleAnalytics'
import { getUserChatHistory } from '@/api/chat'
import type { Message } from '@/types/chat'

// 不再需要这个接口
// interface SSEEvent {
//   event_type: string
//   timestamp: number
//   data: Record<string, any>
// }

export const useGameInitialization = () => {
  // 使用新的分离后的 store
  const chatEventsStore = useChatEventsStore()
  const chatMessagesStore = useChatMessagesStore()
  const chatResourcesStore = useChatResourcesStore()
  const chatUIStore = useChatUIStore()
  const storyStore = useStoryStore()
  const userStore = useUserStore()
  const videoManager = useVideoManager()
  const audioManager = useAudioManager()

  const startTime = ref<number>(0)
  const totalHiddenTime = ref<number>(0)
  const lastHiddenTime = ref<number>(0)
  const initialized = ref(false)
  const hasLoadedHistory = ref(false)
  const formatChatHistory = (history: any[]): Message[] => {
    return history.map((item) => {
      const isUserMessage = 'user' in item
      return {
        id: crypto.randomUUID(), // Generate a unique ID for each message
        msg_type: 'text',
        sender_type: isUserMessage ? 'user' : 'actor',
        content: {
          text: isUserMessage ? item.user : item.actor
        },
        create_time: new Date().toISOString(), // Since no timestamp is provided in original data
        sender: {
          name: isUserMessage ? 'You' : storyStore.currentActor?.name || 'Actor',
          avatar_url: isUserMessage
            ? userStore.userInfo?.avatar_url || ''
            : storyStore.currentActor?.avatar_url || ''
        }
      }
    })
  }

  const loadChatHistory = async (storyId?: string, actorId?: string) => {
    try {
      // 优先使用传入的参数，然后回退到 store 中的值
      const finalStoryId = storyId || storyStore.currentStory?.id
      const finalActorId = actorId || storyStore.currentActor?.id
      const storyVersion = storyStore.currentStory?.version

      if (!finalStoryId || !finalActorId) {
        console.warn('Missing storyId or actorId for loading chat history')
        return false
      }

      const { data } = await getUserChatHistory(finalStoryId, finalActorId, storyVersion)
      if (data.isOk && data.data.history?.length > 0) {
        hasLoadedHistory.value = true
        chatMessagesStore.chatHistory = formatChatHistory(data.data.history)
        return true
      }
      return false
    } catch (error) {
      console.error('Failed to load chat history:', error)
      return false
    }
  }

  const startGame = async (actorId: string, storyId: string) => {
    if (initialized.value) return

    // 发送 GA4 事件
    sendUserPlayedGameEvent()
    try {
      // 优化：并行执行多个初始化任务
      const initTasks = []

      // 任务1：检查聊天历史（如果需要）
      if (!chatEventsStore.isShouldRestart) {
        initTasks.push(
          loadChatHistory(storyId, actorId).then((hasHistory) => {
            if (!hasHistory) {
              chatEventsStore.setShouldRestart(true)
            }
          })
        )
      }

      // 任务2：获取故事详情
      initTasks.push(storyStore.getStoreDetail(storyId))

      // 任务3：初始化音频管理器
      initTasks.push(Promise.resolve(audioManager.init()))

      // 并行执行所有准备任务
      await Promise.allSettled(initTasks)

      // 初始化聊天连接（这个必须在其他任务完成后执行）
      await chatEventsStore.initializeChat(actorId, storyId)

      startTime.value = Date.now()
      // 使用 audioManager 播放背景音乐
      audioManager.playBgm()
      initialized.value = true

      if (videoManager.videoPlayerRef.value) {
        videoManager.setVideoRef(videoManager.videoPlayerRef.value)
      }
    } catch (error) {
      console.error('Failed to initialize game:', error)
      chatUIStore.setError(true)
    }
  }

  const handleVisibilityChange = () => {
    if (document.hidden) {
      lastHiddenTime.value = Date.now()
    } else {
      if (lastHiddenTime.value > 0) {
        totalHiddenTime.value += Date.now() - lastHiddenTime.value
        lastHiddenTime.value = 0
      }
    }
  }

  const cleanup = (actorId?: string) => {
    if (document.hidden && lastHiddenTime.value > 0) {
      totalHiddenTime.value += Date.now() - lastHiddenTime.value
    }

    const totalDuration = Date.now() - startTime.value
    const visibleDuration = Math.max(0, Math.floor((totalDuration - totalHiddenTime.value) / 1000))

    // 上报游戏时长
    reportEvent(ReportEvent.ChatDuration, {
      duration: visibleDuration,
      userId: userStore.userInfo?.uuid,
      isInChat: true,
      actorId: actorId || ''
    })

    // 清理状态
    // 重置视频元素引用
    chatResourcesStore.videoElementRef = null
    // 清理各个 store 的状态
    chatEventsStore.clearChat()
    chatMessagesStore.clearMessages()
    chatResourcesStore.clearResources()
    chatUIStore.clearUIState()
    // 暂停背景音乐
    audioManager.pauseBgm()
    audioManager.cleanup()
    initialized.value = false
  }

  return {
    startGame,
    handleVisibilityChange,
    cleanup,
    initialized,
    hasLoadedHistory
  }
}
