<template>
  <div class="messages-wrapper" ref="messagesListRef">
    <!-- <div class="action-area">
      <div class="banner-container" v-show="!isTelepathyComplete">
        <ActionBanner v-show="currentMessage?.sender_type === 'actor'" :text="currentActionText" />
      </div>
    </div> -->
    <div class="current-message">
      <!-- 思考状态显示 -->
      <div v-if="isThinking" class="thinking-state">
        <div class="actor-avatar">
          <img
            :src="chatResourcesStore.currentActorAvatarUrl || storyStore.currentActor?.avatar_url"
            :alt="storyStore.currentActor?.name"
          />
        </div>
        <div class="thinking-dots">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
      <div v-else-if="shouldShowMessage(currentMessage)" class="message-item">
        <template v-if="currentMessage.sender_type === 'user'">
          <UserMessage
            :message="currentMessage"
            :visible-content="visibleContent[currentMessage.id]"
            :is-in-history="false"
          />
        </template>
        <template
          v-else-if="
            currentMessage.content.text &&
            ['video', 'image', 'text'].includes(currentMessage.msg_type)
          "
        >
          <!-- <SystemMessage
            v-if="currentMessage.msg_type === 'system'"
            :message="currentMessage"
            :visible-content="visibleContent[currentMessage.uuid]"
          /> -->
          <!--  v-else-if="currentMessage.sender_type === 'actor'" -->
          <ActorMessage
            ref="actorMessageRef"
            :message="currentMessage"
            :visible-content="visibleContent[currentMessage.id]"
            :is-playing="isMessagePlaying(currentMessage)"
            :is-in-history="false"
            :os-text="currentActionText"
            @play-tts="$emit('play-tts', currentMessage)"
            @typed="handleTyped"
            @action-text="handleActionText"
            @next-event-click="handleNextEventClick"
          />
        </template>
      </div>
      <!-- Tap to continue 按钮 -->
      <div
        class="tap-to-continue-button"
        v-if="
          shouldShowContinueButton &&
          currentMessage &&
          currentMessage.sender_type === 'actor' &&
          chatUIStore.hideInput &&
          chatMessagesStore.heartValue < 100 &&
          !chatMessagesStore.hasNextMessage &&
          !chatMessagesStore.hasNextVideo &&
          !justClickedNextEvent &&
          !isProcessingConsecutiveMessages
        "
        @click="handleContinueClick"
      >
        <div class="button-content">
          <span class="button-text" v-text-stroke="{ color: '#1F0038' }">Tap to continue</span>
        </div>
      </div>
    </div>

    <!-- History Drawer -->
    <ChatHistory
      v-model:visible="showHistoryDrawer"
      :messages="messages"
      :visible-content="visibleContent"
      :is-message-playing="isMessagePlaying"
      @play-tts="$emit('play-tts', $event)"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onUnmounted } from 'vue'
import { useChatMessagesStore } from '@/store/chat-messages'
import { useStoryStore } from '@/store/story'
import { useChatResourcesStore } from '@/store/chat-resources'
import { useChatUIStore } from '@/store/chat-ui'
import { useMessageState, Message } from '../../composables/useMessageState'
import UserMessage from '../UserMessage.vue'
import ActorMessage from '../ActorMessage.vue'
import ChatHistory from '../ChatHistory/index.vue'

interface Props {
  messages: Message[]
  visibleContent?: Record<string, { text: string; mood?: number }>
  isMessagePlaying: (message: Message) => boolean
}

const props = defineProps<Props>()
const emit = defineEmits([
  'play-tts',
  'add-credit',
  'action-text',
  'typed',
  'telepathy-complete',
  'thinking-change',
  'continue-click'
])

const chatMessagesStore = useChatMessagesStore()
const chatResourcesStore = useChatResourcesStore()
const storyStore = useStoryStore()
const chatUIStore = useChatUIStore()

const messagesListRef = ref<HTMLElement | null>(null)
const actorMessageRef = ref<InstanceType<typeof ActorMessage> | null>(null)
const showHistoryDrawer = ref(false)
const currentActionText = ref('')
const shouldShowContinueButton = ref(false)
const continueButtonTimer = ref<number | null>(null)
// 添加一个状态来跟踪是否刚刚点击了"下一个事件"按钮
const justClickedNextEvent = ref(false)
// 添加一个定时器来重置justClickedNextEvent状态
const nextEventClickTimer = ref<number | null>(null)
// 添加一个状态来跟踪是否正在处理连续消息
const isProcessingConsecutiveMessages = ref(false)
// 添加一个定时器来重置连续消息状态
const consecutiveMessagesTimer = ref<number | null>(null)

const {
  currentMessageIndex,
  isTypingComplete,
  isThinking,
  actualThinkingState,
  handleNewMessage,
  handleTypingComplete,
  shouldShowMessage
} = useMessageState()

// Watch for changes in isThinking and emit events to parent
watch(
  () => actualThinkingState.value,
  (newValue) => {
    emit('thinking-change', newValue)
  }
)

// 监听打字完成状态，延迟600ms后显示"继续"按钮
watch(
  () => isTypingComplete.value,
  (newValue) => {
    // 清除之前的定时器
    if (continueButtonTimer.value) {
      clearTimeout(continueButtonTimer.value)
      continueButtonTimer.value = null
    }

    // 如果打字完成
    if (newValue) {
      shouldShowContinueButton.value = true
      continueButtonTimer.value = null
    } else {
      // 如果打字未完成，立即隐藏按钮
      shouldShowContinueButton.value = false
    }
  }
)

// 获取当前最新的消息
const currentMessage = computed(() => {
  if (props.messages.length === 0) return null
  // 有历史记录，则返回历史记录的最后一项
  if (chatMessagesStore.chatHistory.length > 0) {
    return props.messages[props.messages.length - 1]
  }
  return props.messages[currentMessageIndex.value]
})

// 监听消息变化
watch(
  () => props.messages.length,
  (newLength, oldLength) => {
    if (newLength > oldLength) {
      const latestMessage = props.messages[newLength - 1]
      currentMessageIndex.value = newLength - 1
      handleNewMessage(latestMessage)
      // 设置标志表示正在处理消息
      isProcessingConsecutiveMessages.value = true

      // 清除之前的定时器
      if (consecutiveMessagesTimer.value) {
        clearTimeout(consecutiveMessagesTimer.value)
      }

      // 设置定时器，延迟后重置标志
      // 这是为了确保在没有连续消息的情况下也能重置标志
      consecutiveMessagesTimer.value = window.setTimeout(() => {
        // 只有当没有下一条消息或视频时才重置标志
        if (!chatMessagesStore.hasNextMessage && !chatMessagesStore.hasNextVideo) {
          isProcessingConsecutiveMessages.value = false
        }
        consecutiveMessagesTimer.value = null
      }, 1500)
    }
  }
)

// 监听hasNextMessage和hasNextVideo的变化
watch(
  [() => chatMessagesStore.hasNextMessage, () => chatMessagesStore.hasNextVideo],
  ([hasNextMessage, hasNextVideo]) => {
    // 如果有下一条消息或视频，说明正在处理连续消息
    if (hasNextMessage || hasNextVideo) {
      isProcessingConsecutiveMessages.value = true

      // 清除之前的定时器
      if (consecutiveMessagesTimer.value) {
        clearTimeout(consecutiveMessagesTimer.value)
      }
    } else {
      // 如果没有下一条消息或视频，设置一个延迟再重置标志
      // 这是为了防止在消息之间的短暂间隙中错误地显示"Tap to continue"按钮
      if (consecutiveMessagesTimer.value) {
        clearTimeout(consecutiveMessagesTimer.value)
      }

      consecutiveMessagesTimer.value = window.setTimeout(() => {
        isProcessingConsecutiveMessages.value = false
        consecutiveMessagesTimer.value = null
      }, 1500) // 延迟时间1.5秒
    }
  }
)

// 处理打字完成事件
const handleTyped = () => {
  handleTypingComplete()
  emit('typed')
}

const handleActionText = (text: string) => {
  currentActionText.value = text
}

const toggleHistory = () => {
  showHistoryDrawer.value = !showHistoryDrawer.value
}

// 跳过当前正在打字的消息
const skipCurrentTyping = async () => {
  actorMessageRef.value?.handleMessageClick()
}

// const handleTelepathyClick = () => {
//   if (isTelepathyComplete.value) {
//     // 触发telepathy完成事件
//     emit('telepathy-complete')
//   }
// }

// 处理"下一个事件"按钮点击事件
const handleNextEventClick = () => {
  // 设置标志，表示刚刚点击了"下一个事件"按钮
  justClickedNextEvent.value = true

  // 同时设置连续消息处理标志
  isProcessingConsecutiveMessages.value = true

  // 清除之前的定时器
  if (nextEventClickTimer.value) {
    clearTimeout(nextEventClickTimer.value)
  }

  if (consecutiveMessagesTimer.value) {
    clearTimeout(consecutiveMessagesTimer.value)
  }

  // 设置定时器，2秒后重置标志
  nextEventClickTimer.value = window.setTimeout(() => {
    justClickedNextEvent.value = false
    nextEventClickTimer.value = null

    // 如果此时没有下一条消息，也重置连续消息标志
    if (!chatMessagesStore.hasNextMessage && !chatMessagesStore.hasNextVideo) {
      isProcessingConsecutiveMessages.value = false
    }
  }, 2000)
}

const handleContinueClick = () => {
  // 触发事件通知父组件
  emit('continue-click')
}

// 组件卸载时清除所有定时器
onUnmounted(() => {
  if (continueButtonTimer.value) {
    clearTimeout(continueButtonTimer.value)
    continueButtonTimer.value = null
  }

  if (nextEventClickTimer.value) {
    clearTimeout(nextEventClickTimer.value)
    nextEventClickTimer.value = null
  }

  if (consecutiveMessagesTimer.value) {
    clearTimeout(consecutiveMessagesTimer.value)
    consecutiveMessagesTimer.value = null
  }
})

defineExpose({
  messagesListRef,
  toggleHistory,
  isTypingComplete,
  skipCurrentTyping,
  isThinking
})
</script>

<style lang="less" scoped>
.messages-wrapper {
  // position: absolute;
  // bottom: 80px;
  // overflow-y: auto;
  padding: 16px;
  -webkit-overflow-scrolling: touch;
  transition: all 0.3s ease;
  width: 100%;
}

.action-area {
  position: relative;
  width: 100%;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10px;
}

.current-message {
  margin-top: 10px;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .message-item {
    width: 100%;
    animation: slideUp 0.3s ease;

    :deep(.actor-message) {
      justify-content: flex-start;
      display: flex;
      gap: 12px;
      align-items: flex-start;
      padding: 0 16px;

      .actor-avatar {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        overflow: hidden;
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .message-content {
        background: transparent;
        color: #fff;
        box-shadow: none;
        text-align: left;
        padding: 0;
        margin: 0;
        max-width: calc(100% - 44px);
      }
    }

    :deep(.user-message) {
      justify-content: center;
    }
  }

  .tap-to-continue-button {
    margin-top: 20px;
    pointer-events: auto;
    cursor: pointer;
    animation: breathe 2s ease-in-out infinite;
    transition: transform 0.2s ease;

    .button-content {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 8px 16px;
      border-radius: 20px;
    }

    .button-text {
      color: #ca93f2;
      font-size: 12px;
      font-weight: 600;
      position: relative;
      padding-right: 24px;
      display: flex;
      align-items: center;

      &::after {
        content: '';
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 20px;
        height: 14px;
        background-image: url('@/assets/icon/triangle.svg'), url('@/assets/icon/triangle.svg');
        background-repeat: no-repeat, no-repeat;
        background-position:
          0 center,
          8px center;
        background-size:
          9px 14px,
          9px 14px;
      }
    }

    &:hover {
      transform: scale(1.05);
    }

    &:active {
      transform: scale(0.95);
    }
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.history-drawer-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 0;

  .drawer-header {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px;
    position: relative;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    h3 {
      color: #fff;
      font-size: 16px;
      font-weight: 500;
      margin: 0;
    }

    .close-button {
      position: absolute;
      right: 16px;
      top: 50%;
      transform: translateY(-50%);
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      border: none;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
      }

      :deep(svg) {
        width: 16px;
        height: 16px;
        color: #fff;
      }
    }
  }

  .messages-history {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    padding: 16px;

    .message-item {
      display: flex;
      gap: 12px;
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }

      .message-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        overflow: hidden;
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .message-content-wrapper {
        flex: 1;

        .sender-name {
          color: rgba(255, 255, 255, 0.6);
          font-size: 14px;
          margin-bottom: 8px;
        }

        :deep(.message-content) {
          background: transparent;
          border: none;
          backdrop-filter: none;
          color: #fff;
          padding: 0;
          font-size: 15px;
          line-height: 1.5;

          .text {
            white-space: pre-wrap;
          }
        }

        :deep(.user-message .message-content) {
          background: transparent;
          border: none;
        }

        :deep(.system-message .message-content) {
          background: transparent;
          border: none;
          color: rgba(255, 255, 255, 0.6);
          padding: 0;
        }
      }

      &::after {
        content: '';
        display: block;
        height: 1px;
        background: rgba(255, 255, 255, 0.1);
        margin-top: 24px;
      }

      &:last-child {
        margin-bottom: 0;

        &::after {
          display: none;
        }
      }
    }
  }
}

.thinking-state {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  gap: 12px;
  padding: 0 16px;
  animation: slideUp 0.3s ease;

  .actor-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .thinking-dots {
    display: flex;
    gap: 4px;
    align-items: center;

    span {
      width: 8px;
      height: 8px;
      background: #fff;
      border-radius: 50%;
      opacity: 0.6;
      animation: thinking 1.4s infinite;

      &:nth-child(2) {
        animation-delay: 0.2s;
      }

      &:nth-child(3) {
        animation-delay: 0.4s;
      }
    }
  }
}

@keyframes thinking {
  0%,
  100% {
    transform: translateY(0);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-6px);
    opacity: 1;
  }
}

@keyframes breathe {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.9;
  }
  50% {
    transform: scale(1.05);
    opacity: 1;
  }
}
.banner-container {
  display: flex;
  align-items: center;
  justify-content: center;
  // padding-right: 76px; // Increased padding to accommodate heart value
  width: 100%;
  height: 100%;
  position: relative;

  :deep(.action-banner) {
    width: auto;
    max-width: none;
    // padding-right: 86px; // Space for heart value
    background: linear-gradient(
      90deg,
      rgba(31, 0, 56, 0) 0%,
      #1f0038 50%,
      #1f0038 calc(100% - 60px),
      rgba(31, 0, 56, 0) 100%
    );
  }
}
</style>
