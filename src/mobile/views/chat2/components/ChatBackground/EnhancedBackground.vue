<template>
  <div class="enhanced-background">
    <!-- 视频背景 -->
    <div
      v-show="currentType === 'video'"
      ref="videoContainerRef"
      class="video-container"
      :class="{
        'background-blurred': isBackgroundBlurred
      }"
    >
      <VideoBackground
        v-if="currentVideoUrl"
        ref="videoBackgroundRef"
        :video-url="currentVideoUrl"
        :muted="true"
        :loop="shouldLoop"
        :autoplay="true"
        @video-loaded="handleVideoLoaded"
        class="video-background-component"
      />
    </div>

    <!-- 动画图片序列 -->
    <div
      v-show="currentType === 'animated-images'"
      ref="animatedImagesRef"
      class="animated-images-container"
      @click="handleAnimatedImageClick"
    >
      <img
        v-if="showTapToContinue"
        class="tap2continue"
        src="https://cdn.magiclight.ai/assets/playshot/tap2continue-v2.png"
        alt="tap2continue"
      />
      <div class="images-wrapper">
        <img
          v-for="(image, index) in currentAnimatedImages"
          :key="image"
          :src="image"
          :class="{
            'animated-image': true,
            active: index === currentImageIndex,
            previous: index === previousImageIndex
          }"
          alt="Animated Image"
          @error="(e: Event) => ((e.target as HTMLImageElement).style.display = 'none')"
        />
      </div>
    </div>

    <!-- 静态背景图片 -->
    <div
      v-show="currentType === 'image'"
      ref="staticBackgroundRef"
      class="background-image"
      :class="{
        'background-blurred': isBackgroundBlurred
      }"
      :style="{
        backgroundImage: currentImageUrl ? `url(${currentImageUrl})` : 'none'
      }"
    />

    <!-- 缓冲背景层 -->
    <div
      v-if="showBuffer"
      ref="bufferBackgroundRef"
      class="buffer-background"
      :style="bufferBackgroundStyle"
    />

    <!-- 加载指示器 -->
    <div v-if="isLoading" class="background-loading-indicator">
      <div class="loading-spinner"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onBeforeUnmount } from 'vue'
import { animate } from 'animejs'
import VideoBackground from '@/mobile/components/VideoBackground.vue'
import {
  useBackgroundTransitionManager,
  TransitionMode
} from '@/mobile/composables/useBackgroundTransitionManager'
import { useStoryStore } from '@/store/story'
import { useChatResourcesStore } from '@/store/chat-resources'

// 组件属性
interface Props {
  backgroundVideo?: string
  backgroundImage?: string
  defaultImage?: string
  animatedImages?: string[]
  transitionMode?: TransitionMode
}

const props = withDefaults(defineProps<Props>(), {
  backgroundVideo: '',
  backgroundImage: '',
  defaultImage: '',
  animatedImages: () => [],
  transitionMode: 'fade'
})

// 组件事件
const emit = defineEmits<{
  (e: 'resource-loading', loading: boolean): void
  (e: 'transition-complete'): void
  (e: 'animated-image-complete'): void
}>()

// 引用DOM元素
const videoContainerRef = ref<HTMLElement | null>(null)
const videoBackgroundRef = ref<InstanceType<typeof VideoBackground> | null>(null)
const animatedImagesRef = ref<HTMLElement | null>(null)
const staticBackgroundRef = ref<HTMLElement | null>(null)
const bufferBackgroundRef = ref<HTMLElement | null>(null)

// 状态
const storyStore = useStoryStore()
const chatResourcesStore = useChatResourcesStore()
const currentImageIndex = ref(0)
const previousImageIndex = ref(-1)
const showTapToContinue = ref(true)

// 模糊状态计算属性
const isBackgroundBlurred = computed(() => {
  const isBlurred = chatResourcesStore.backgroundBlurState.isBlurred
  console.log('EnhancedBackground blur state:', {
    isBlurred,
    tag: chatResourcesStore.backgroundBlurState.tag,
    isBlurRequired: chatResourcesStore.backgroundBlurState.isBlurRequired
  })
  return isBlurred
})

// 初始化背景过渡管理器
const {
  isTransitioning,
  isLoading,
  currentResource,
  bufferResource,
  transitionTo,
  applyTransitionToElements
} = useBackgroundTransitionManager({
  defaultTransition: {
    mode: props.transitionMode,
    duration: 600,
    easing: 'easeOutCubic',
    delay: 0
  }
})

// 计算属性
const currentType = computed(() => currentResource.value?.type || 'image')

const currentVideoUrl = computed(() => {
  if (currentType.value !== 'video') return null
  return currentResource.value?.url as string
})

const currentImageUrl = computed(() => {
  if (currentType.value !== 'image') return null
  return (currentResource.value?.url as string) || props.defaultImage
})

const currentAnimatedImages = computed(() => {
  if (currentType.value !== 'animated-images') return []
  return (currentResource.value?.url as string[]) || []
})

const showBuffer = computed(() => {
  return isTransitioning.value || isLoading.value
})

// 根据 chat-resources.ts 中的 handlePlayVideoEvent 逻辑确定是否循环播放视频
const shouldLoop = computed(() => {
  // 获取当前视频 URL
  const videoUrl = currentVideoUrl.value
  if (!videoUrl) return true

  const notLoop =
    chatResourcesStore.backgroundVideoMap[chatResourcesStore.currentActorId || '']?.notLoop || false
  // 如果是背景视频且设置了不循环标志，则不循环播放
  return !notLoop
})

const bufferBackgroundStyle = computed(() => {
  if (!bufferResource.value) return {}

  if (bufferResource.value.type === 'image') {
    return {
      backgroundImage: `url(${bufferResource.value.url})`,
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      opacity: showBuffer.value ? 1 : 0
    }
  } else if (bufferResource.value.type === 'animated-images') {
    const lastImage = (bufferResource.value.url as string[])[0] || ''
    return {
      backgroundImage: lastImage ? `url(${lastImage})` : 'none',
      backgroundSize: 'cover',
      backgroundPosition: 'center',
      opacity: showBuffer.value ? 1 : 0
    }
  }

  return {
    opacity: 0
  }
})

// 处理视频加载完成
const handleVideoLoaded = () => {
  console.log('Video loaded successfully')
}

// 处理动画图片点击
const handleAnimatedImageClick = () => {
  // 如果已经是最后一张图片，则切换到静态背景
  if (currentImageIndex.value >= currentAnimatedImages.value.length - 1) {
    // 获取最后一张图片作为静态背景
    const lastImage = currentAnimatedImages.value[currentAnimatedImages.value.length - 1]

    // 更新store中的状态
    const { currentActor } = storyStore
    const actorId = currentActor?.id

    if (actorId) {
      chatResourcesStore.backgroundVideoMap[chatResourcesStore.currentActorId || ''].url = ''
      chatResourcesStore.backgroundImageCouldBeFullScreen = true
      chatResourcesStore.backgroundImageMap[actorId] = lastImage || ''
      chatResourcesStore.animatedImagesMap[actorId] = []
    }

    // 切换到静态背景
    transitionTo(lastImage, 'image')
      .then(() => {
        if (staticBackgroundRef.value) {
          applyTransitionToElements(staticBackgroundRef.value, animatedImagesRef.value)
        }
      })
      .catch(console.error)

    // 重置索引
    currentImageIndex.value = 0
    previousImageIndex.value = -1

    // 通知父组件动画序列已完成
    emit('animated-image-complete')

    return
  }

  // 否则显示下一张图片
  previousImageIndex.value = currentImageIndex.value
  currentImageIndex.value++

  // 如果是最后一张图片，隐藏点击提示
  if (currentImageIndex.value >= currentAnimatedImages.value.length - 1) {
    showTapToContinue.value = false
  }

  // 创建图片切换动画
  const images = document.querySelectorAll('.animated-image')
  if (images.length > currentImageIndex.value) {
    const currentImage = images[currentImageIndex.value] as HTMLElement
    currentImage.style.opacity = '0'

    // 使用requestAnimationFrame确保DOM更新
    requestAnimationFrame(() => {
      // 创建动画
      animate(currentImage, {
        opacity: [0, 1],
        scale: [1.05, 1],
        duration: 500,
        easing: 'easeOutQuad'
      })
    })
  }
}

// 监听属性变化 - 优化版本，支持防抖和智能预加载
let transitionTimeout: ReturnType<typeof setTimeout> | null = null

// 清理定时器
onBeforeUnmount(() => {
  if (transitionTimeout) {
    clearTimeout(transitionTimeout)
    transitionTimeout = null
  }
})

// 心值变化监听已移至 chat4/index.vue 中处理

watch(
  [() => props.backgroundVideo, () => props.backgroundImage, () => props.animatedImages],
  async ([newVideo, newImage, newAnimatedImages]) => {
    // 清除之前的防抖定时器
    if (transitionTimeout) {
      clearTimeout(transitionTimeout)
      transitionTimeout = null
    }

    // 避免不必要的更新
    // 如果有视频，优先使用视频
    if (newVideo) {
      // 如果当前已经是这个视频，则不需要更新
      if (currentType.value === 'video' && newVideo === currentVideoUrl.value) {
        return
      }
    } else if (newAnimatedImages && newAnimatedImages.length > 0) {
      // 如果没有视频但有动画图片，使用动画图片
      if (
        currentType.value === 'animated-images' &&
        JSON.stringify(newAnimatedImages) === JSON.stringify(currentAnimatedImages.value)
      ) {
        return
      }
    } else if (newImage) {
      // 如果没有视频和动画图片，使用静态图片
      if (currentType.value === 'image' && newImage === currentImageUrl.value) {
        return
      }
    } else {
      // 如果都没有，不需要更新
      return
    }

    // 优化：快速响应背景切换，实现视频到背景的无缝衔接
    transitionTimeout = setTimeout(async () => {
      // 通知父组件资源开始加载
      emit('resource-loading', true)

      // 设置背景过渡状态为 true
      chatResourcesStore.setBackgroundTransitioning(true)

      try {
        // 确定要加载的资源类型和优先级
        if (newVideo) {
          // 视频优先级最高
          await transitionTo(newVideo, 'video')

          // 应用过渡动画，使用更快的过渡
          if (videoContainerRef.value) {
            await applyTransitionToElements(videoContainerRef.value, bufferBackgroundRef.value)
          }
        } else if (newAnimatedImages && newAnimatedImages.length > 0) {
          // 动画图片序列优先级第二 - 快速切换以实现无缝衔接
          await transitionTo(newAnimatedImages, 'animated-images')

          // 重置动画图片索引
          currentImageIndex.value = 0
          previousImageIndex.value = -1
          showTapToContinue.value = true

          // 应用过渡动画，使用更快的过渡时间
          if (animatedImagesRef.value) {
            await applyTransitionToElements(animatedImagesRef.value, bufferBackgroundRef.value)
          }
        } else if (newImage) {
          // 静态图片优先级最低 - 快速切换以实现无缝衔接
          await transitionTo(newImage, 'image')

          // 应用过渡动画，使用更快的过渡时间
          if (staticBackgroundRef.value) {
            await applyTransitionToElements(staticBackgroundRef.value, bufferBackgroundRef.value)
          }
        } else if (props.defaultImage) {
          // 如果没有其他资源，使用默认图片
          await transitionTo(props.defaultImage, 'image')

          if (staticBackgroundRef.value) {
            await applyTransitionToElements(staticBackgroundRef.value, bufferBackgroundRef.value)
          }
        }

        // 通知父组件资源加载完成
        emit('resource-loading', false)
        emit('transition-complete')
      } catch (error) {
        console.error('Background transition error:', error)
        // 通知父组件资源加载完成（即使有错误）
        emit('resource-loading', false)
      } finally {
        // 设置背景过渡状态为 false
        chatResourcesStore.setBackgroundTransitioning(false)
      }
    }, 10) // 优化：进一步减少防抖延迟，实现快速响应
  },
  { immediate: true } // 确保组件挂载时立即执行
)
</script>

<style lang="less" scoped>
.enhanced-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  overflow: hidden;

  .background-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    will-change: transform, opacity;
    transform: translate(var(--pan-x, 0), var(--pan-y, 0)) scale(var(--zoom-level, 1));
    transform-origin: center center;
    transition: transform 0.3s ease;
  }

  .buffer-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1.5;
    will-change: opacity;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }

  .video-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    overflow: hidden;
    will-change: transform, opacity;
  }

  .animated-images-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
    will-change: transform, opacity;
    transform: translate(var(--pan-x, 0), var(--pan-y, 0)) scale(var(--zoom-level, 1));
    transform-origin: center center;
    transition: transform 0.3s ease;

    .images-wrapper {
      position: relative;
      width: 100%;
      height: 100%;
    }

    .tap2continue {
      position: absolute;
      top: 60%;
      left: 70%;
      z-index: 3;
      width: 120px;
      animation: bounce 1s infinite;
    }

    .animated-image {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
      opacity: 0;
      transition:
        opacity 0.5s cubic-bezier(0.4, 0, 0.2, 1),
        transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
      pointer-events: none;
      will-change: transform, opacity;

      &.active {
        opacity: 1;
        z-index: 2;
      }

      &.previous {
        opacity: 0;
        z-index: 1;
      }
    }
  }

  .background-loading-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 2.5;
  }

  .loading-spinner {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top-color: #fff;
    animation: spin 1s linear infinite;
  }

  // 模糊效果样式
  .background-blurred {
    filter: blur(10px);
    transition: filter 0.5s ease-in-out;
  }

  // 确保视频背景组件也能应用模糊效果
  .video-container.background-blurred .video-background-component {
    filter: blur(10px);
    transition: filter 0.5s ease-in-out;
  }
}

@keyframes bounce {
  0%,
  100% {
    transform: translateX(-50%) translateY(0);
  }
  50% {
    transform: translateX(-50%) translateY(-10px);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
