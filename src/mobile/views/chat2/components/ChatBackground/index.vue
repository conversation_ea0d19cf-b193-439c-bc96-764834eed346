<template>
  <EnhancedBackground
    :background-video="backgroundVideo"
    :background-image="currentBgImage || defaultImage"
    :default-image="defaultImage"
    :animated-images="filteredAnimatedImages"
    :transition-mode="transitionMode"
    @resource-loading="handleResourceLoading"
    @transition-complete="handleTransitionComplete"
    @animated-image-complete="handleAnimatedImageComplete"
    class="enhanced-background"
  />
</template>

<script setup lang="ts">
import { computed, toRef } from 'vue'
import EnhancedBackground from './EnhancedBackground.vue'
import { useBackgroundState } from '../../composables/useBackgroundState'
import { useChatStore } from '@/store/chat'
import { useStoryStore } from '@/store/story'
import { TransitionMode } from '@/mobile/composables/useBackgroundTransitionManager'
import { useChatEventsStore } from '@/store/chat-events'
import { useChatResourcesStore } from '@/store/chat-resources'

interface Props {
  backgroundVideo?: string
  backgroundImage?: string
  defaultImage?: string
  animatedImages?: string[]
  transitionMode?: TransitionMode
}

const props = withDefaults(defineProps<Props>(), {
  backgroundVideo: '',
  backgroundImage: '',
  defaultImage: '',
  animatedImages: () => [],
  transitionMode: 'fade'
})

const { currentBgImage } = useBackgroundState({
  initialImage: toRef(props, 'backgroundImage')
})

const chatEventsStore = useChatEventsStore()
const storyStore = useStoryStore()
const chatResourcesStore = useChatResourcesStore()

// 定义事件
const emit = defineEmits<{
  (e: 'resource-loading', loading: boolean): void
}>()

// 计算属性用于处理背景视频
const backgroundVideo = computed(() => props.backgroundVideo)

// 根据条件决定是否使用动画序列的最后一张图片作为背景
const shouldUseLastAnimatedImage = computed(() => {
  return props.animatedImages.length > 0 && !chatEventsStore.isShouldRestart
})

const lastAnimatedImage = computed(() => {
  if (shouldUseLastAnimatedImage.value) {
    return props.animatedImages[props.animatedImages.length - 1]
  }
  return null
})

// 修改计算属性以处理过滤后的动画图片
const filteredAnimatedImages = computed(() => {
  if (shouldUseLastAnimatedImage.value) {
    return [lastAnimatedImage.value].filter(Boolean)
  }
  return props.animatedImages.filter((image) => image)
})

// 处理资源加载状态变化
const handleResourceLoading = (loading: boolean) => {
  emit('resource-loading', loading)
}

// 处理过渡完成
const handleTransitionComplete = () => {
  // 可以在这里添加额外的逻辑
}

// 处理动画图片序列完成
const handleAnimatedImageComplete = () => {
  const { currentActor } = storyStore
  const actorId = currentActor?.id

  if (!actorId) return

  // 更新store中的状态
  chatResourcesStore.backgroundVideoMap[chatResourcesStore.currentActorId || ''].url = ''
  const actorImages = chatResourcesStore.animatedImagesMap[actorId] || []
  currentBgImage.value = actorImages[actorImages.length - 1] || ''
  chatResourcesStore.backgroundImageCouldBeFullScreen = true
  chatResourcesStore.backgroundImageMap[actorId] = actorImages[actorImages.length - 1] || ''
  chatResourcesStore.animatedImagesMap[actorId] = []
}
</script>

<style lang="less" scoped>
.enhanced-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  overflow: hidden;
}
</style>
