<template>
  <div class="action-banner-container">
    <transition name="fade">
      <div class="action-banner" v-if="text">
        <span>{{ text }}</span>
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  text: string
}>()
</script>

<style lang="less" scoped>
.action-banner-container {
  z-index: 100;
  pointer-events: none;
  display: flex;
  justify-content: center;
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
}

.action-banner {
  background: linear-gradient(90deg, rgba(31, 0, 56, 0) 0%, #1f0038 50%, #1f0038 100%);
  padding: 8px 12px;
  width: 100%;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;

  span {
    font-size: 12px;
    color: #fff;
    overflow: hidden;
    display: block;
    text-align: center;
  }
}

.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(10px);
}
</style>
