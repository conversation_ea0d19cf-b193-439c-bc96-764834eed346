<template>
  <div class="chat-ui-controls">
    <!-- 任务提示 -->
    <div class="task-tip-container" v-if="chatTasksStore.tasks.length">
      <TaskTip ref="taskTipRef" :description="chatTasksStore.tasks[0].description" />
    </div>

    <!-- 音频控制 -->
    <div class="audio-control" v-if="showAudioControl">
      <button class="audio-button" @click.stop="$emit('toggle-mute')">
        <icon-volume-notice v-if="!isMuted" />
        <icon-volume-mute v-else />
      </button>
    </div>

    <!-- 分享按钮 -->
    <div class="share-button-container" v-if="showShare">
      <ShareButton ref="shareButtonRef" @click="$emit('toggle-share')" />
    </div>

    <!-- 心灵感应值 -->
    <div class="heart-value-container" v-if="showHeartValue">
      <HeartValue :value="chatMessagesStore.heartValue" @click="$emit('telepathy-complete')" />
    </div>

    <!-- 技能模态框 -->
    <div class="skill-container" v-if="showSkillModal">
      <SkillModal />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useChatTasksStore } from '@/store/chat-tasks'
import { useChatMessagesStore } from '@/store/chat-messages'
import { useChatUIStore } from '@/store/chat-ui'
import { useStoryStore } from '@/store/story'
import TaskTip from '../TaskTip.vue'
import ShareButton from '../ShareButton/index.vue'
import HeartValue from '../HeartValue/index.vue'
import SkillModal from '../SkillModal/index.vue'
import IconVolumeNotice from '@/assets/icon/voice-notice-white.svg'
import IconVolumeMute from '@/assets/icon/voice-mute-white.svg'

interface Props {
  showShare?: boolean
  isMuted?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showShare: true,
  isMuted: false
})

const emit = defineEmits<{
  'toggle-mute': []
  'toggle-share': []
  'telepathy-complete': []
}>()

// Store
const chatTasksStore = useChatTasksStore()
const chatMessagesStore = useChatMessagesStore()
const chatUIStore = useChatUIStore()
const storyStore = useStoryStore()

// Refs
const taskTipRef = ref()
const shareButtonRef = ref()

// 计算属性
const showAudioControl = computed(() => true)

const showHeartValue = computed(() => {
  return !chatUIStore.isLegacyVersion && !chatUIStore.isEnding
})

const showSkillModal = computed(() => {
  return !chatUIStore.isLegacyVersion && storyStore.currentStory?.is_active_skill
})

// 暴露方法给父组件
defineExpose({
  collapseTaskTip: () => taskTipRef.value?.collapse(),
  checkShareStatus: () => shareButtonRef.value?.checkShareStatus()
})
</script>

<style lang="less" scoped>
.chat-ui-controls {
  position: relative;
  width: 100%;
  height: 100%;
  pointer-events: none; // 允许点击穿透，具体按钮会重新启用pointer-events

  > * {
    pointer-events: auto; // 子元素恢复点击事件
  }
}

.task-tip-container {
  position: absolute;
  top: 242px;
  right: 16px;
  z-index: 3;
}

.audio-control {
  position: absolute;
  top: 80px;
  right: 16px;
  z-index: 2;

  .audio-button {
    background: rgba(31, 0, 56, 0.5);
    border: none;
    border-radius: 50%;
    width: 46px;
    height: 46px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;

    :deep(svg) {
      width: 20px;
      height: 20px;
      color: #333;
    }
  }
}

.share-button-container {
  position: absolute;
  top: 134px;
  right: 16px;
  z-index: 2;
}

.heart-value-container {
  position: absolute;
  right: 16px;
  top: 60%;
  transform: translateY(-50%);
  z-index: 1;
}

.skill-container {
  position: absolute;
  right: 16px;
  top: 192px;
  z-index: 101;
}
</style>
