<template>
  <div class="chat-input">
    <div class="input-container" :class="{ attention: showAttention }">
      <button class="history-button" @click="toggleHistory">
        <icon-history />
      </button>
      <div class="text-input" v-show="!chatUIStore.hideInput">
        <textarea
          ref="inputRef"
          v-model="inputText"
          :placeholder="placeholder"
          :disabled="isDisabled"
          @keydown.enter.prevent="handleEnter"
          @input="handleInput"
        />
        <button
          class="send-button"
          :class="{ disabled: !canSend }"
          :disabled="!canSend"
          @click="handleSend"
        >
          <icon-send />
        </button>
      </div>
      <!-- 动作选项 -->
      <div v-if="chatResourcesStore.actionOptions?.length" class="action-options">
        <button
          v-for="option in chatResourcesStore.actionOptions"
          :key="option.option_id"
          class="action-option-button"
          @click="handleActionClick(option as ActionOption)"
        >
          <img
            v-if="(option as ActionOption).image_url"
            :src="(option as ActionOption).image_url"
            :alt="option.text"
            class="action-icon"
          />
          <span class="action-text">{{ option.text }}</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onBeforeUnmount, watch } from 'vue'
import { useChatEventsStore } from '@/store/chat-events'
import { useChatUIStore } from '@/store/chat-ui'
import { useChatMessagesStore } from '@/store/chat-messages'
import { useChatResourcesStore } from '@/store/chat-resources'
import { useUserStore } from '@/store/user'
import { Message } from '@/mobile/components/Message'
import { ChatOptions } from '@/types/chat'
import IconHistory from '@/assets/icon/history.svg'
import IconSend from '@/assets/icon/send-message.svg'
import { reportEvent } from '@/utils/report'
import { ReportEvent } from '@/interface'

interface ActionOption extends ChatOptions {
  image_url?: string
}

defineProps<{
  collapseTaskTip?: () => void
}>()

const chatEventsStore = useChatEventsStore()
const chatUIStore = useChatUIStore() // 暂时不使用
const chatMessagesStore = useChatMessagesStore()
const chatResourcesStore = useChatResourcesStore()
const userStore = useUserStore()

const inputRef = ref<HTMLTextAreaElement | null>(null)
const inputText = ref('')
const remainingTime = ref(0)

const emit = defineEmits(['add-credit', 'scroll-to-bottom', 'toggle-history', 'collapse-task-tip'])

// 计算属性
const isDisabled = computed(() => {
  // 打印调试信息
  // console.log('isDisabled 检查 - messageTypingPromise:', chatMessagesStore.messageTypingPromise)
  // console.log('isDisabled 检查 - remainingTime:', remainingTime.value)
  // console.log('isDisabled 检查 - isActorThinking:', chatMessagesStore.isActorThinking)

  // 定义禁用条件
  const isTyping = chatMessagesStore.messageTypingPromise !== null
  const isWaiting = remainingTime.value > 0
  const isThinking = chatMessagesStore.isActorThinking
  const hasOptions = chatResourcesStore.actionOptions?.length > 0

  return isTyping || isWaiting || isThinking || hasOptions
})

const canSend = computed(() => {
  return (
    !isDisabled.value && inputText.value.trim().length > 0 && inputText.value.trim().length <= 500
  )
})

// 添加倒计时逻辑
let countdownInterval: ReturnType<typeof setInterval> | null = null

// 注意：这个函数在其他地方被调用，不要删除
function startCountdown(initialTime: number) {
  clearCountdown()
  remainingTime.value = initialTime
  countdownInterval = setInterval(() => {
    if (remainingTime.value > 0) {
      remainingTime.value -= 1000
    } else {
      clearCountdown()
    }
  }, 1000)
}

const clearCountdown = () => {
  if (countdownInterval) {
    clearInterval(countdownInterval)
    countdownInterval = null
  }
  remainingTime.value = 0
}

// 监听 messageTypingPromise 的变化
watch(
  () => chatMessagesStore.messageTypingPromise,
  (newPromise) => {
    if (!newPromise) {
      // 打字动画完成，如果有下一个视频或消息，则开始倒计时
      if (chatMessagesStore.hasNextVideo || chatMessagesStore.hasNextMessage) {
        startCountdown(chatMessagesStore.messageDelayTime)
      }
    } else {
      // 打字动画开始，清除倒计时
      clearCountdown()
    }
  }
)

const placeholder = computed(() => {
  // 定义不同状态的提示文本
  const typingPrompt = 'Please wait...'
  const thinkingPrompt = 'Actor is thinking...'
  const optionsPrompt = 'Please select an option'
  const defaultPrompt = 'Type your message...'

  // 根据状态返回相应的提示文本
  if (chatMessagesStore.messageTypingPromise !== null) {
    // 如果正在打字，显示正在等待提示
    return typingPrompt
  }

  if (chatMessagesStore.isActorThinking) {
    return thinkingPrompt
  }

  // 打字动画完成后，如果有倒计时，显示倒计时信息
  if (remainingTime.value > 0) {
    if (chatMessagesStore.hasNextVideo) {
      return `Video will auto-play in ${Math.ceil(remainingTime.value / 1000)}s...`
    }
    if (chatMessagesStore.hasNextMessage) {
      return `Next message in ${Math.ceil(remainingTime.value / 1000)}s...`
    }
    return `Please wait ${Math.ceil(remainingTime.value / 1000)}s...`
  }

  if (chatResourcesStore.actionOptions?.length) {
    return optionsPrompt
  }

  return defaultPrompt
})

const showAttention = ref(false)
let attentionTimer: ReturnType<typeof setTimeout> | null = null

// 显示提示动画
const showAttentionAnimation = () => {
  if (attentionTimer) {
    clearTimeout(attentionTimer)
  }
  showAttention.value = true
  attentionTimer = setTimeout(() => {
    showAttention.value = false
  }, 1000)
}

// 方法
const toggleHistory = () => {
  reportEvent(ReportEvent.ClickChat2HistoryButton)
  emit('toggle-history')
}

const handleInput = () => {
  if (!inputRef.value) return

  // 重置高度到一行
  inputRef.value.style.height = '20px'

  // 如果内容为空，保持一行高度
  if (!inputText.value.trim()) {
    inputRef.value.style.height = '20px'
    return
  }

  // 如果内容超过一行，则自动增长
  const scrollHeight = inputRef.value.scrollHeight
  if (scrollHeight > 20) {
    inputRef.value.style.height = `${scrollHeight}px`
  }

  emit('scroll-to-bottom')
}

const handleEnter = (e: KeyboardEvent) => {
  if (e.shiftKey) return
  handleSend()
}

const handleSend = async () => {
  if (!canSend.value) return
  // if (!userStore.userInfo?.coins) {
  //   emit('add-credit', 'option')
  //   return
  // }

  const text = inputText.value.trim()
  inputText.value = ''

  // 发送后重置输入框高度
  if (inputRef.value) {
    inputRef.value.style.height = '20px'
  }

  try {
    await chatEventsStore.sendMessage(text, null, 'text', false, null, 2000)
    emit('collapse-task-tip')
  } catch (error) {
    Message.error('Failed to send message')
  }
}

const handleActionClick = async (option: ActionOption) => {
  if (!userStore.userInfo?.coins) {
    emit('add-credit', 'option')
    return
  }

  try {
    await chatEventsStore.sendMessage(option.text)
    emit('collapse-task-tip')
  } catch (error) {
    Message.error('Failed to select action')
  }
}

// 暴露方法给父组件
defineExpose({
  showAttentionAnimation,
  startCountdown // 导出这个函数以便其他组件可以调用
})

onBeforeUnmount(() => {
  if (attentionTimer) {
    clearTimeout(attentionTimer)
  }
  clearCountdown()
})
</script>

<style lang="less" scoped>
.chat-input {
  width: 100%;
  position: relative;
  bottom: 0;
  padding: 8px 16px;
  background: transparent;

  .input-container {
    display: flex;
    gap: 8px;
    align-items: center;
    transform-origin: center bottom;

    &.attention {
      animation: bounce 0.5s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;

      .text-input {
        animation: glow 1s ease-in-out;
      }
    }

    .history-button {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      background: #ca93f2;
      border: none;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;
      color: rgba(255, 255, 255, 0.6);
      flex-shrink: 0;

      &:hover {
        color: rgba(255, 255, 255, 0.9);
      }

      :deep(svg) {
        flex-shrink: 0;
        width: 16px;
        height: 16px;
      }
    }

    .text-input {
      flex: 1;
      display: flex;
      gap: 8px;
      align-items: center;
      background: rgba(31, 0, 56, 0.8);
      border-radius: 30px;
      padding: 4px;
      border: 1px solid rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;

      textarea {
        flex: 1;
        background: transparent;
        border: none;
        padding: 0 12px;
        color: #fff;
        font-size: 14px;
        line-height: 20px;
        height: 20px;
        resize: none;
        max-height: 120px;
        transition: all 0.3s ease;
        overflow-y: hidden;

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        &::placeholder {
          color: rgba(255, 255, 255, 0.4);
        }

        &:focus {
          outline: none;
        }
      }

      .send-button {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background: #ca93f2;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
        flex-shrink: 0;

        &:hover:not(.disabled) {
          background: #b48ded;
        }

        &.disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        :deep(svg) {
          flex-shrink: 0;
          width: 36px;
          height: 36px;
        }
      }
    }

    .chat-options {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .chat-option-button {
        background: rgba(255, 255, 255, 0.9);
        border: none;
        border-radius: 8px;
        padding: 12px;
        color: #333;
        font-size: 15px;
        text-align: left;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          background: #fff;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
      }
    }

    .action-options {
      display: flex;
      gap: 12px;
      overflow-x: auto;
      padding: 4px 0;
      -webkit-overflow-scrolling: touch;

      .action-option-button {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 4px;
        background: none;
        border: none;
        cursor: pointer;
        min-width: 64px;

        .action-icon {
          width: 48px;
          height: 48px;
          border-radius: 12px;
          object-fit: cover;
        }

        .action-text {
          color: #fff;
          font-size: 12px;
          text-align: center;
        }
      }
    }
  }
}

@keyframes bounce {
  0%,
  100% {
    transform: translateY(0);
  }
  20% {
    transform: translateY(-6px);
  }
  40% {
    transform: translateY(4px);
  }
  60% {
    transform: translateY(-2px);
  }
  80% {
    transform: translateY(1px);
  }
}

@keyframes glow {
  0%,
  100% {
    border-color: rgba(255, 255, 255, 0.1);
    box-shadow: none;
  }
  50% {
    border-color: #ca93f2;
    box-shadow: 0 0 20px rgba(202, 147, 242, 0.6);
  }
}
</style>
