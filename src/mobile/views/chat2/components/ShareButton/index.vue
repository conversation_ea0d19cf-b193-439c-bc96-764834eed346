<template>
  <button class="share-button" @click="handleClick">
    <icon-share />
    <span class="red-dot" v-if="!hasShared"></span>
  </button>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { getShareInfo } from '@/api/share'
import { useStoryStore } from '@/store/story'
import IconShare from '@/assets/icon/share.svg'

const props = defineProps<{
  modelValue?: boolean
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void
}>()

const storyStore = useStoryStore()
const hasShared = ref(true)

const checkShareStatus = async () => {
  if (!storyStore.currentStory?.id || !storyStore.currentActor?.id) return
  try {
    const {
      data: { data }
    } = await getShareInfo(storyStore.currentStory.id, storyStore.currentActor.id)
    hasShared.value = data.is_rewarded
  } catch (error) {
    console.error('Failed to check share status:', error)
  }
}

// 监听故事ID和角色ID的变化
watch(
  [() => storyStore.currentStory?.id, () => storyStore.currentActor?.id],
  ([newStoryId, newActorId]) => {
    if (newStoryId && newActorId) {
      checkShareStatus()
    }
  }
)

// 暴露方法给父组件
defineExpose({
  checkShareStatus
})

const handleClick = () => {
  emit('update:modelValue', true)
}

onMounted(() => {
  checkShareStatus()
})
</script>

<style lang="less" scoped>
.share-button {
  background: rgba(31, 0, 56, 0.5);
  border: none;
  border-radius: 50%;
  width: 46px;
  height: 46px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  position: relative;

  :deep(svg) {
    width: 20px;
    height: 20px;
    color: #333;
  }

  .red-dot {
    position: absolute;
    top: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    background: #ff4d4f;
    border-radius: 50%;
    border: 2px solid rgba(31, 0, 56, 0.5);
    transform: translate(25%, -25%);
  }

  &:hover {
    transform: scale(1.05);
  }

  &:active {
    transform: scale(0.95);
  }
}
</style>
