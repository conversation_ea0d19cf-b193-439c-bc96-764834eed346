<template>
  <div class="skill-icon" @click="showSkillDetail">
    <img
      v-if="selectedSkill?.image_url"
      :src="selectedSkill.image_url"
      :alt="selectedSkill?.name"
    />
  </div>

  <ConfirmDialog
    v-model:visible="isVisible"
    :show-icon="false"
    :show-cancel="false"
    :confirm-text="'Ok, I get it.'"
    :close-on-click-overlay="true"
    :confirm-button-style="{
      borderRadius: '26px',
      borderTop: '2px solid #1f0038',
      borderRight: '2px solid #1f0038',
      borderBottom: '6px solid #1f0038',
      borderLeft: '2px solid #1f0038',
      background: 'linear-gradient(180deg, #d6cafd 0%, #ca93f2 100%)',
      boxShadow: '0px 1.855px 11.13px 0px #b098ff'
    }"
  >
    <template #default>
      <span class="skill-title">Your Talent</span>
    </template>

    <template #content>
      <div class="skill-detail">
        <img
          v-if="selectedSkill?.image_url"
          :src="selectedSkill.image_url"
          :alt="selectedSkill?.name"
        />
      </div>
    </template>
  </ConfirmDialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useSkillStore } from '@/store/skill'
import ConfirmDialog from '@/components/ConfirmDialog.vue'

const skillStore = useSkillStore()
const isVisible = ref(false)

const selectedSkill = computed(() => {
  return skillStore.selectedSkills[0]
})

const showSkillDetail = () => {
  if (selectedSkill.value) {
    isVisible.value = true
  }
}
</script>

<style lang="less" scoped>
.skill-icon {
  width: 46px;
  height: 46px;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.2s ease;
  background: rgba(31, 0, 56, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  &:hover {
    transform: scale(1.05);
  }

  &:active {
    transform: scale(0.95);
  }

  img {
    width: 30px;
    height: 30px;
    object-fit: cover;
  }
}

.skill-detail {
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.skill-description {
  color: rgba(255, 255, 255, 0.8);
  font-size: 15px;
  line-height: 1.5;
  text-align: center;
  margin-top: 8px;
}

.skill-title {
  font-size: 16px;
  font-weight: 600;
  color: #fff;
}
</style>
