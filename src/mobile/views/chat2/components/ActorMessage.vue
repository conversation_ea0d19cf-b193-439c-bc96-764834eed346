<template>
  <div class="actor-message-container">
    <div class="actor-message">
      <div class="thought-bubble" v-if="osText && chatMessagesStore.heartValue < 100">
        <div class="thought-content">
          <span
            class="thought-text"
            :data-content="processedOsText"
            v-html="processedOsText"
          ></span>
        </div>
        <img
          class="thought-bg"
          src="https://static.playshot.ai/static/images/chat/thought-box.png"
          alt="thought box"
        />
      </div>
      <div
        class="message-wrapper"
        v-show="props.message.content.text?.replace(/\*(.*?)\*/g, '').trim()"
      >
        <div class="actor-avatar" v-if="!isInHistory">
          <img
            :src="props.message.sender?.avatar_url || storyStore.currentActor?.avatar_url"
            :alt="storyStore.currentActor?.name"
          />
        </div>
        <div
          class="message-content"
          :class="{
            playing: isPlaying,
            'has-mood': hasMood,
            typing: isTyping,
            'has-action': hasAction
          }"
          @click="handleMessageClick"
        >
          <div
            :class="['text', { 'stroke-text': !isInHistory }]"
            ref="textRef"
            v-html="isInHistory ? historyDialogText : displayText"
            :data-content="isInHistory ? historyDialogText : displayText"
          ></div>
          <div v-if="hasMood" class="mood-indicator">
            <div class="mood-bar">
              <div class="mood-value" :style="{ width: `${moodValue}%` }" />
            </div>
            <div class="mood-labels">
              <span v-for="label in moodLabels" :key="label">{{ label }}</span>
            </div>
          </div>
          <div class="play-button" v-if="hasAudio" @click="handlePlayClick">
            <icon-volume-notice v-if="!isPlaying" />
            <icon-pause-circle-fill v-else />
          </div>
        </div>
      </div>
    </div>
    <!-- 快捷回复选项 -->
    <div class="quick-replies" v-if="showQuickReplies && quickReplies.length > 0">
      <button
        v-for="(reply, index) in quickReplies"
        :key="index"
        class="quick-reply-button"
        :class="{
          'first-option': index === 0,
          'second-option': index === 1,
          'third-option': index === 2
        }"
        @click="handleQuickReplyClick(reply)"
      >
        <span v-html="reply.text"></span>
      </button>
    </div>
  </div>
  <teleport to="body" v-if="!isInHistory">
    <div
      class="next-event-indicator-container"
      v-if="chatMessagesStore.hasNextMessage || chatMessagesStore.hasNextVideo"
      :style="indicatorPosition"
    >
      <div class="indicator-icon" v-if="chatMessagesStore.hasNextMessage" @click="handleNextEvent">
        <icon-message-notice />
      </div>
      <div class="indicator-icon" v-if="chatMessagesStore.hasNextVideo" @click="handleNextEvent">
        <icon-video-camera />
      </div>
    </div>
  </teleport>
</template>

<script setup lang="ts">
import { watch, onBeforeUnmount, ref, computed } from 'vue'
import IconVolumeNotice from '@/assets/icon/volume-notice.svg'
import IconPauseCircleFill from '@/assets/icon/pause-circle-fill.svg'
import IconMessageNotice from '@/assets/icon/message-notice.svg'
import IconVideoCamera from '@/assets/icon/video-camera.svg'
import { useStoryStore } from '@/store/story'
// 导入新的分离后的 store
import { useChatEventsStore } from '@/store/chat-events'
import { useChatMessagesStore } from '@/store/chat-messages'
import { useChatResourcesStore } from '@/store/chat-resources'
import { useTypewriter } from '../composables/useTypewriter'
import { useMessageActions } from '../composables/useMessageActions'
import { useMoodIndicator } from '../composables/useMoodIndicator'
import { useAudioManager } from '@/mobile/composables/useAudioManager'
import type { Message } from '../composables/useMessageState'

interface Props {
  message: Message
  visibleContent?: {
    text: string
    mood?: number
  }
  isInHistory?: boolean
  osText?: string
}

// 快捷回复选项接口
interface QuickReply {
  id: string
  text: string
}

const props = withDefaults(defineProps<Props>(), {
  isInHistory: false
})

const storyStore = useStoryStore()
const chatEventsStore = useChatEventsStore()
const chatMessagesStore = useChatMessagesStore()
const chatResourcesStore = useChatResourcesStore()
const audioManager = useAudioManager()

const isPlaying = defineModel<boolean>('isPlaying', { default: false })
const hasAction = ref(false)
const historyDialogText = computed(() => {
  if (!props.message.content.text) return ''

  // 先提取快捷回复选项
  const replies = extractQuickReplies(props.message.content.text)

  // 从消息文本中移除快捷回复选项部分
  let cleanedText = props.message.content.text
  if (replies.length > 0) {
    // 使用正则表达式移除所有 $xxx$ 格式的文本
    cleanedText = props.message.content.text.replace(/\$[^$]+\$/g, '')
    // 移除可能的空行
    cleanedText = cleanedText.replace(/\n\s*\n/g, '\n').trim()
  }

  // 处理动作文本和高亮文本
  return cleanedText
    .replace(/\*(.*?)\*/g, (_, action) => `<span class="history-action">(${action})</span>`)
    .replace(/_([^_]+)_/g, (_, highlight) => `<span class="highlight">${highlight}</span>`)
})

const { displayText, isTyping, typeMessage, clearQueue, skipTyping, skipRequested } = useTypewriter(
  {
    typingSpeed: 50,
    punctuationDelay: 300,
    initialDelay: 200
  }
)

const { hasAudio } = useMessageActions(props.message.content)

const { hasMood, moodValue, moodLabels } = useMoodIndicator({
  mood: props.visibleContent?.mood
})

const processedOsText = computed(() => {
  if (!props.osText) return ''
  return props.osText.replace(/_([^_]+)_/g, '<span class="highlight">$1</span>')
})

// 快捷回复选项相关状态
const quickReplies = ref<QuickReply[]>([])
const showQuickReplies = ref(false)

// 从消息文本中提取快捷回复选项
const extractQuickReplies = (text: string): QuickReply[] => {
  if (!text) return []

  // 使用正则表达式匹配$$之间的内容作为快捷回复选项
  const replyRegex = /\$([^$]+)\$/g
  const replies: QuickReply[] = []
  let match: RegExpExecArray | null
  let id = 1

  while ((match = replyRegex.exec(text)) !== null) {
    replies.push({
      id: id.toString(),
      text: match[1].trim()
    })
    id++
  }

  return replies
}

// 处理快捷回复点击
const handleQuickReplyClick = (reply: QuickReply) => {
  // 隐藏快捷回复选项
  showQuickReplies.value = false

  // 发送选中的回复作为用户消息
  chatEventsStore.sendMessage(reply.text)

  // 清空快捷回复选项
  quickReplies.value = []
}

const emit = defineEmits(['play-tts', 'typed', 'action-text', 'next-event-click'])

// 监听消息内容变化
watch(
  () => props.message.content.text,
  async (newText) => {
    if (newText && !props.isInHistory) {
      // 隐藏之前可能显示的快捷回复选项
      showQuickReplies.value = false
      quickReplies.value = []

      // 提取快捷回复选项
      const replies = extractQuickReplies(newText)

      // 从消息文本中移除快捷回复选项部分
      let cleanedText = newText
      if (replies.length > 0) {
        // 使用正则表达式移除所有 $xxx$ 格式的文本
        cleanedText = newText.replace(/\$[^$]+\$/g, '')
        // 移除可能的空行
        cleanedText = cleanedText.replace(/\n\s*\n/g, '\n').trim()
      }

      // 处理动作文本
      const actionMatches = cleanedText.match(/\*(.*?)\*/g)
      const actionText = actionMatches
        ? actionMatches.map((match) => match.replace(/^\*|\*$/g, '')).join(' ')
        : ''

      // 更新动作状态
      hasAction.value = !!actionText

      // 处理对话文本 - 移除动作文本部分
      const processedText = cleanedText.replace(/\*(.*?)\*/g, '').trim()

      // 如果有动作文本，发送事件
      emit('action-text', actionText)

      // 等待前一个打字机效果完成
      if (isTyping.value) {
        await new Promise<void>((resolve) => {
          const checkInterval = setInterval(() => {
            if (!isTyping.value || skipRequested) {
              clearInterval(checkInterval)
              resolve()
            }
          }, 50)
        })
      }

      // 重置打字机状态
      clearQueue()

      // 在打字效果开始前尝试播放 TTS
      if (props.message.id && processedText) {
        // 立即播放 TTS，与打字效果同步
        audioManager.playTTS(processedText, props.message.id)
      }

      // 如果 TTS 准备失败或者不需要 TTS，也要开始打字效果
      const isComplete = await typeMessage(processedText)

      if (isComplete) {
        emit('typed')

        // 在打字完成后显示快捷回复选项
        if (replies.length > 0) {
          quickReplies.value = replies
          // 延迟显示快捷回复选项，给用户一些时间阅读消息
          setTimeout(() => {
            showQuickReplies.value = true
          }, 500)
        }

        // 通知消息打字完成
        if (chatMessagesStore.messageTypingResolve) {
          // 使用 store 中的方法完成打字动画
          await chatMessagesStore.completeMessageTyping(skipRequested ? 0 : 300, skipRequested)
        } else {
          // 即使没有 resolve 函数，也要确保重置 promise
          chatMessagesStore.messageTypingPromise = null
        }
      }
    }
  },
  { immediate: true }
)

const handlePlayClick = () => {
  if (props.message.content.audio_url) {
    emit('play-tts', props.message)
  }
}

// 创建一个共享函数来处理快捷回复选项的显示
const showQuickRepliesIfAvailable = () => {
  // 检查当前消息是否有快捷回复选项
  if (props.message.content.text) {
    const replies = extractQuickReplies(props.message.content.text)
    if (replies.length > 0) {
      quickReplies.value = replies
      // 延迟显示快捷回复选项，给用户一些时间阅读消息
      setTimeout(() => {
        showQuickReplies.value = true
      }, 500)
    }
  }
}

const handleMessageClick = async () => {
  if (isTyping.value) {
    // 尝试跳过打字动画
    const skipped = skipTyping()
    if (skipped) {
      // 停止TTS播放，确保音频和打字效果同步
      audioManager.stopTTS()
      emit('typed')

      // 在跳过打字动画后显示快捷回复选项
      showQuickRepliesIfAvailable()

      if (chatMessagesStore.messageTypingResolve) {
        chatMessagesStore.completeMessageTyping(0, true)
      } else {
        // 即使没有 resolve 函数，也要确保重置 promise
        chatMessagesStore.messageTypingPromise = null
      }
    }
  } else if (showQuickReplies.value) {
    // 如果已经显示了快捷回复选项，点击消息时隐藏它们
    showQuickReplies.value = false
  }
}

const handleNextEvent = async () => {
  // 通知父组件已点击下一个事件按钮
  emit('next-event-click')
  // 隐藏快捷回复选项
  showQuickReplies.value = false

  // 确保在点击后立即更新状态，防止闪现"Tap to continue"按钮

  // 停止当前消息的打字效果
  skipTyping()
  emit('typed')

  // 直接处理打字完成
  if (chatMessagesStore.messageTypingResolve) {
    console.log('调用 completeMessageTyping 完成打字')
    chatMessagesStore.completeMessageTyping(0, true)
  } else {
    console.log('没有 messageTypingResolve，手动重置 messageTypingPromise')
    // 即使没有 resolve 函数，也要确保重置 promise
    chatMessagesStore.messageTypingPromise = null
  }

  // 停止当前TTS播放
  audioManager.stopTTS()

  // 查找下一个事件
  if (!chatEventsStore.messageQueue.length) return

  // 从队列中寻找下一条消息或视频事件
  const nextIndex = chatEventsStore.messageQueue.findIndex(
    (evt) =>
      evt.event_type === 'message' || (evt.event_type === 'play_video' && !evt.data.is_background)
  )

  if (nextIndex === -1) return

  const nextEvent = chatEventsStore.messageQueue[nextIndex]

  // 修改队列处理逻辑
  chatEventsStore.messageQueue.splice(0, nextIndex + 1)

  // 重置消息队列的状态
  chatMessagesStore.messageDelayTime = 0
  chatMessagesStore.hasNextMessage = false
  chatMessagesStore.hasNextVideo = false

  // 手动调用 store 中的事件处理方法
  if (nextEvent.event_type === 'message') {
    // 直接调用 chatMessagesStore 中的方法
    await chatMessagesStore.handleMessageEvent(nextEvent)
  } else if (nextEvent.event_type === 'play_video' && !nextEvent.data.is_background) {
    // 尝试手动触发视频播放逻辑
    chatResourcesStore.videoUrl = nextEvent.data.url
    chatResourcesStore.isPlayingVideo = true
  }
}

// 组件卸载时清空队列
onBeforeUnmount(() => {
  clearQueue()
})

const textRef = ref<HTMLElement | null>(null)

// 计算指示器的位置
const indicatorPosition = computed(() => {
  if (!textRef.value) return {}

  const rect = textRef.value.getBoundingClientRect()
  return {
    position: 'fixed',
    top: `${rect.top + rect.height / 2}px`,
    left: `${rect.right + 0}px`,
    transform: 'translateY(-50%)'
  } as const
})

defineExpose({
  handleMessageClick
})
</script>

<style lang="less" scoped>
.actor-message-container {
  display: flex;
  flex-direction: column;
  margin-bottom: 8px;
  position: relative;

  .actor-message {
    display: flex;
    flex-direction: column;
    position: relative;

    .thought-bubble {
      align-self: flex-start;
      // margin-left: 30px;
      // margin-bottom: 24px;
      min-width: 80px;
      // max-width: calc(100% - 60px);
      z-index: 1;
      display: inline-block;
      position: relative;
      padding: 20px;

      .thought-content {
        position: relative;
        z-index: 2;
        padding: 8px;
      }

      .thought-bg {
        position: absolute;
        inset: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
        object-fit: fill;
      }

      .thought-text {
        font-size: 13px;
        color: #1f0038;
        white-space: pre-wrap;
        word-break: break-word;
        text-align: center;
        display: block;
        position: relative;
        font-weight: 600;
        z-index: 2;
        transform: translateY(5px);

        :deep(.highlight) {
          color: #ed2a2e;
          font-weight: 700;
        }
      }
    }

    .message-wrapper {
      width: 100%;
      display: flex;
      align-items: flex-start;
      gap: 8px;
    }

    .actor-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      overflow: hidden;
      flex-shrink: 0;
      position: relative;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .message-content {
      background: rgba(255, 255, 255, 0.9);
      border-radius: 4px 16px 16px 16px;
      padding: 12px 16px;
      color: #333;
      font-size: 15px;
      line-height: 1.4;
      word-break: break-word;
      font-weight: 700;
      position: relative;
      transition: all 0.3s ease;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      flex: 1;
      cursor: pointer;

      .text {
        white-space: pre-wrap;
        position: relative;
        display: block;

        &.stroke-text {
          &::after {
            content: attr(data-content);
            -webkit-text-stroke: 1px #1f0038;
            position: absolute;
            left: 0;
            top: 0;
            right: 0;
            z-index: -1;
            white-space: pre-wrap;
          }
        }
      }
      &.has-action {
        background: rgba(255, 255, 255, 0.95);
      }

      .current-message & {
        background: rgba(255, 255, 255, 0.95);
        max-width: 90%;
        margin: 0 auto;
        border-radius: 16px;
        text-align: center;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
      }

      &.playing {
        background: rgba(255, 255, 255, 0.95);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      &.has-mood {
        padding-bottom: 40px;
      }

      .mood-indicator {
        position: absolute;
        bottom: 12px;
        left: 16px;
        right: 16px;

        .mood-bar {
          height: 4px;
          background: rgba(0, 0, 0, 0.1);
          border-radius: 2px;
          overflow: hidden;
          margin-bottom: 4px;

          .mood-value {
            height: 100%;
            background: linear-gradient(90deg, #ff70df 0%, #f64c9c 100%);
            transition: width 0.3s ease;
          }
        }

        .mood-labels {
          display: flex;
          justify-content: space-between;
          font-size: 10px;
          color: rgba(0, 0, 0, 0.4);
        }
      }

      .play-button {
        position: absolute;
        right: -70px;
        top: 50%;
        transform: translateY(-50%);
        width: 24px;
        height: 24px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);

        &:hover {
          background: rgba(255, 255, 255, 1);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        :deep(svg) {
          width: 16px;
          height: 16px;
          color: #f64c9c;
        }
      }

      :deep(.history-action) {
        color: #ca93f2;
        font-style: italic;
      }

      :deep(.highlight) {
        color: #ed2a2e;
        font-weight: 700;
      }
    }
  }

  // 快捷回复选项样式
  .quick-replies {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 16px;
    margin-left: 48px; // 与消息内容对齐
    animation: fadeIn 0.3s ease-in-out;
    max-width: 80%;

    .quick-reply-button {
      position: relative;
      padding: 12px 16px;
      border-radius: 16px;
      background: linear-gradient(151deg, #ffdbd3 0%, #ffb762 96.71%);
      border: 2px solid #1f0038;
      color: #1f0038;
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      transition: all 0.2s ease;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden; // 确保背景图片不会溢出圆角

      // 添加背景图片
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: url('https://static.playshot.ai/static/images/chat/chat-option-bg.png');
        background-repeat: repeat;
        background-size: 18px;
        z-index: 0;
      }

      // 确保内容在背景之上
      span {
        position: relative;
        z-index: 1;
        flex: 1;
        line-height: 1.4;
      }

      .diamond-icon {
        width: 20px;
        height: 20px;
        margin-right: 8px;
        z-index: 1;
      }

      &.second-option {
        background: linear-gradient(151deg, #ca93f2 0%, #b168e6 96.71%);
      }

      &.third-option {
        background: linear-gradient(151deg, #bcc2ff 0%, #68a7ff 96.71%);
      }

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        filter: brightness(1.05);
      }

      &:active {
        transform: translateY(1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
    }
  }
}

.next-event-indicator-container {
  position: fixed;
  display: flex;
  flex-direction: column;
  gap: 8px;
  z-index: 1000;

  .indicator-icon {
    width: 24px;
    height: 24px;
    animation: bounce 1s ease-in-out infinite;
    cursor: pointer;
    transition: all 0.3s ease;

    &:nth-child(2) {
      animation-delay: 0.2s;
    }

    :deep(svg) {
      width: 24px;
      height: 24px;
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-6px);
  }
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
}

@keyframes blink {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}
</style>
