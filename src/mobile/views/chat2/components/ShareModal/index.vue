<template>
  <div class="share-modal" v-if="visible" @click="handleClose">
    <div class="share-content" @click.stop>
      <div class="share-header">
        <div class="share-title">
          Share to claim your reward
          <span class="reward-amount">
            <img src="https://cdn.magiclight.ai/assets/mobile/diamond.png" alt="diamond" />
          </span>
        </div>
        <div class="close-button" @click="handleClose">
          <icon-close />
        </div>
      </div>

      <div class="share-body">
        <div class="poster-container" v-if="posterUrl">
          <img :src="posterUrl" alt="Share Poster" class="poster-image" />
          <!-- <div class="save-tip" v-if="!isSupported"></div> -->
        </div>
        <div v-else class="poster-loading">
          {{ posterUrl }}
          <a-spin />
        </div>

        <template v-if="!showConfirm">
          <button class="share-button" :disabled="isLoading" @click="handleShareClick">
            {{ isSupported ? 'Share to' : 'Save Image' }}
          </button>
        </template>
        <template v-else-if="isProcessing">
          <button class="share-button processing">
            <a-spin :size="20" />
            <span>Processing...</span>
          </button>
        </template>
        <template v-else>
          <button class="confirm-button" @click="handleClose">
            {{ confirmButtonText }}
            <span class="reward-amount" v-if="shareSuccess">
              <img src="https://cdn.magiclight.ai/assets/mobile/diamond.png" alt="diamond" />{{ 5 }}
            </span>
          </button>
        </template>

        <div class="share-tip" v-if="!isSupported">
          Your browser doesn't support direct sharing. Please save the image and share it manually.
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, onUnmounted, inject } from 'vue'
import { useShareFeature } from '../../composables/useShare'
import IconClose from '@/assets/icon/close.svg'
import IconShare from '@/assets/icon/share.svg'
import { reportEvent } from '@/utils/report'
import { ReportEvent } from '@/interface/report'
import { useStoryStore } from '@/store/story'

const storyStore = useStoryStore()

const visible = defineModel<boolean>('visible', { required: true })

const emit = defineEmits<{
  (e: 'share-success'): void
  (e: 'update:visible', value: boolean): void
}>()

const shareButtonRef = inject<{ checkShareStatus: () => Promise<void> }>('shareButtonRef')

const {
  handleShare,
  getPoster,
  posterUrl,
  isLoading,
  isSupported,
  downloadImage,
  recordShareSuccess
} = useShareFeature()

const handleClose = () => {
  visible.value = false
}

let shareStartTime = 0
const MIN_SHARE_TIME = 500 // 最小分享时间
const AUTO_CLOSE_DELAY = 1000 // 自动关闭延时

// 显示确认按钮的状态
const showConfirm = ref(false)
const confirmTimer = ref<number>()
const confirmButtonText = ref('Confirm Share Success')
const shareSuccess = ref(false)
const isProcessing = ref(false)

const handleShareSuccess = async () => {
  const success = await recordShareSuccess()
  if (success) {
    emit('share-success')
    handleClose()
  }
}

const handleVisibilityChange = async () => {
  if (document.visibilityState === 'visible' && shareStartTime > 0) {
    const timePassed = Date.now() - shareStartTime
    if (timePassed >= MIN_SHARE_TIME) {
      await handleShareSuccess()
    }
    shareStartTime = 0
  }
}

onMounted(() => {
  document.addEventListener('visibilitychange', handleVisibilityChange)
})

onUnmounted(() => {
  document.removeEventListener('visibilitychange', handleVisibilityChange)
  if (confirmTimer.value) {
    clearTimeout(confirmTimer.value)
  }
})

const handleShareClick = async () => {
  reportEvent(ReportEvent.ShareButtonClick, {
    story_id: storyStore.currentStory?.id,
    actor_id: storyStore.currentActor?.id
  })
  if (isSupported.value) {
    shareStartTime = Date.now()
    const success = await handleShare()

    if (success) {
      isProcessing.value = true // 开始处理时显示加载状态
      showConfirm.value = false // 确保不显示确认按钮
      // 设置定时器，定时器结束后自动尝试记录分享
      confirmTimer.value = window.setTimeout(async () => {
        shareSuccess.value = await recordShareSuccess()
        isProcessing.value = false // 处理完成后隐藏加载状态
        showConfirm.value = true
        confirmButtonText.value = shareSuccess.value ? 'Reward Claimed' : 'OK'
        // 更新分享按钮状态
        if (shareSuccess.value) {
          shareButtonRef?.checkShareStatus?.()
        }
      }, AUTO_CLOSE_DELAY)
    }
  } else {
    // If sharing is not supported, trigger download
    const success = await downloadImage()
    if (success) {
      emit('share-success')
      handleClose()
    }
  }
}

watch(
  () => visible.value,
  async (newVisible) => {
    if (newVisible) {
      await getPoster(false)
      showConfirm.value = false
      isProcessing.value = false
      shareSuccess.value = false
    }
  }
)
</script>

<style lang="less" scoped>
.share-modal {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;

  .share-content {
    width: 90%;
    max-width: 375px;
    height: auto;
    max-height: calc(var(--vh, 70vh) * 100);
    background: #1f0038;
    border-radius: 24px;
    position: relative;
    display: flex;
    flex-direction: column;
    padding: 16px;
    box-sizing: border-box;
  }

  .share-header {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    position: relative;
    height: 32px;

    .share-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 18px;
      font-weight: 600;
      color: #fff;
      .reward-amount {
        display: flex;
        align-items: center;
        img {
          width: 26px;
          height: 26px;
        }
      }
    }

    .close-button {
      position: absolute;
      right: -14px;
      top: -14px;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius: 50%;
      background: rgba(31, 0, 56, 0.05);
      transition: background 0.3s;

      &:hover {
        background: rgba(31, 0, 56, 0.1);
      }

      :deep(svg) {
        width: 24px;
        height: 24px;
        color: #1f0038;
      }
    }
  }

  .share-body {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    flex: 1;
    gap: 16px;

    .poster-container {
      position: relative;
      width: 100%;
      flex: 1;
      min-height: 0;
      border-radius: 16px;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #1f0038;

      .poster-image {
        max-width: 100%;
        max-height: 100%;
        width: auto;
        height: auto;
        object-fit: contain;
        display: block;
        user-select: all;
        pointer-events: all;
      }

      .save-tip {
        position: absolute;
        bottom: 16px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0, 0, 0, 0.7);
        color: #fff;
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 14px;
        pointer-events: none;
        animation: fadeIn 0.3s ease;
      }
    }

    .poster-loading {
      width: 100%;
      flex: 1;
      min-height: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #1f0038;
      border-radius: 16px;
    }

    .share-button {
      width: 100%;
      height: 50px;
      border-radius: 0.26rem;
      border-top: 2px solid #1f0038;
      border-right: 2px solid #1f0038;
      border-bottom: 6px solid #1f0038;
      border-left: 2px solid #1f0038;
      background: linear-gradient(180deg, #d6cafe 0%, #ca93f2 100%);
      box-shadow: 0px 1.855px 11.13px 0px #b098ff;
      color: #241d49;
      font-size: 15px;
      font-weight: 600;
      flex-shrink: 0;

      &:hover {
        opacity: 0.9;
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      &.processing {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        cursor: not-allowed;
        opacity: 0.8;

        :deep(.arco-spin) {
          color: #241d49;
        }
      }
    }

    .share-tip {
      font-size: 14px;
      color: #666;
      text-align: center;
      line-height: 1.4;
      flex-shrink: 0;
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, 10px);
  }
  to {
    opacity: 1;
    transform: translate(-50%, 0);
  }
}

.confirm-button {
  width: 100%;
  height: 50px;
  border-radius: 26px;
  border-top: 2px solid #1f0038;
  border-right: 2px solid #1f0038;
  border-bottom: 6px solid #1f0038;
  border-left: 2px solid #1f0038;
  background: linear-gradient(180deg, #f5ffe2 0%, #daff96 100%);
  box-shadow: 0px 1.855px 11.13px 0px #daff96;
  color: #1f0038;
  font-size: 15px;
  font-weight: 600;
  flex-shrink: 0;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  &:hover {
    opacity: 0.9;
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(1px);
  }

  .reward-amount {
    display: flex;
    align-items: center;
    gap: 4px;
    img {
      width: 16px;
      height: 16px;
    }
  }
}
</style>
