<template>
  <div class="nsfw-banner">
    <div class="nsfw-tag">NSFW</div>

    <div class="banner-text">Best with headphones</div>
    <div class="headphone-icon">
      <IconEarphone />
    </div>
  </div>
</template>

<script setup lang="ts">
import IconEarphone from '@/assets/icon/earphone.svg'

defineOptions({
  name: 'NsfwBanner'
})
</script>

<style lang="less" scoped>
.nsfw-banner {
  margin-top: 8px;
  padding: 8px 20px;
  position: relative;
  width: 100%;
  background: #0b011a;
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  gap: 12px;
  border-bottom: 1px solid rgba(202, 147, 242, 0.1);
  position: relative;
  z-index: 1000;
  height: 50px;
}

.nsfw-tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: #ff2d2d;
  color: white;
  height: 34px;
  font-weight: 700;
  padding: 5px 10px;
  border-radius: 2px;
  box-shadow: 0 2px 6px rgba(255, 71, 87, 0.2);
  color: #fff;
  text-align: center;
  text-shadow: 0px 1px 0px #1f0038;
  font-size: 20px;
  font-style: normal;
  font-weight: 700;
  line-height: 168.018%;
  min-width: 100px;
  text-align: center;
}

.headphone-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 20px;
    height: 20px;
    opacity: 0.9;
  }
}

.banner-text {
  color: #fff;
  flex: 1;
  text-shadow: 0px 1px 0px #1f0038;
  -webkit-text-stroke-width: 1;
  -webkit-text-stroke-color: #1f0038;
  font-family: 'Work Sans';
  font-size: 17px;
  font-style: normal;
  font-weight: 600;
  line-height: 168.018%; /* 28.563px */
}
</style>
