<template>
  <div class="social-callback">
    <div class="loading-container">
      <a-spin :loading="true" tip="Processing login..." />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Message } from '@/mobile/components/Message'
import { useUserStore } from '@/store/user'
import { exchangeSocialLogin } from '@/api/social-login'
import type { SocialLoginType } from '@/api/social-login'
import { reportEvent } from '@/utils'
import { ReportEvent } from '@/interface'

const router = useRouter()
const userStore = useUserStore()

onMounted(async () => {
  try {
    // Get URL search params
    const urlParams = new URLSearchParams(window.location.search)
    const code = urlParams.get('code')
    const error = urlParams.get('error')
    const login_type = urlParams.get('login_type') as SocialLoginType
    const redirect = urlParams.get('redirect') || sessionStorage.getItem('login_redirect')

    // Handle login cancellation or error
    if (error || !code || !login_type) {
      // User cancelled the login or there was an error
      router.replace('/user/login')
      return
    }

    // Exchange code for user data
    const response = await exchangeSocialLogin(login_type, code, userStore.userInfo?.uuid)
    if (!response.data.isOk) {
      throw new Error(response.data.message || 'Failed to exchange social login code')
    }

    // Update store with user data using the new action
    await userStore.handleSocialLogin(response.data.data)

    reportEvent(ReportEvent.LoginSuccess, {
      userId: userStore.userInfo?.uuid,
      type: login_type
    })

    Message.success('Login successful')

    // 清除存储的重定向路径
    sessionStorage.removeItem('login_redirect')

    // 处理重定向URL，只保留路径部分
    let redirectPath = '/'
    if (redirect) {
      try {
        const url = new URL(decodeURIComponent(redirect))
        redirectPath = url.pathname + url.search + url.hash
        console.log('redirectPath', redirectPath)
      } catch {
        // 如果解析URL失败，说明可能是相对路径，直接使用
        redirectPath = decodeURIComponent(redirect)
      }
    }

    // 重定向到指定页面或默认页面
    router.replace(redirectPath)
  } catch (error: any) {
    console.error('Social login callback error:', error)
    Message.error(error.message || 'Failed to complete social login')
    router.replace('/user/login')
  }
})
</script>

<style lang="less" scoped>
.social-callback {
  height: calc(var(--vh, 1vh) * 100);
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(180deg, #2b1b2f 0%, #1a1021 100%);

  .loading-container {
    text-align: center;
    padding: 24px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    backdrop-filter: blur(10px);
  }
}
</style>
