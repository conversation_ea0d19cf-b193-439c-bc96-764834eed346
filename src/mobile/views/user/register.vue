<template>
  <div class="register-container">
    <div class="register-card">
      <div class="close-button" @click="router.push('/')">
        <icon-close />
      </div>

      <div class="logo-wrapper">
        <img src="https://cdn.magiclight.ai/assets/playshot/logo-v2.png" alt="Playshot" />
        <h2>Create Account</h2>
      </div>

      <div class="form-wrapper">
        <div class="input-group">
          <label>Email Address</label>
          <input
            v-model="form.email"
            type="email"
            placeholder="Enter"
            :class="{ error: errors.email }"
          />
          <span v-if="errors.email" class="error-text">{{ errors.email }}</span>
        </div>

        <div class="input-group">
          <label>Name</label>
          <input
            v-model="form.name"
            type="text"
            placeholder="Enter"
            :class="{ error: errors.name }"
          />
          <span v-if="errors.name" class="error-text">{{ errors.name }}</span>
        </div>

        <div class="input-group">
          <label>Password</label>
          <input
            v-model="form.password"
            type="password"
            placeholder="Enter"
            :class="{ error: errors.password }"
          />
          <span v-if="errors.password" class="error-text">{{ errors.password }}</span>
        </div>

        <div class="input-group">
          <label>Confirm Password</label>
          <input
            v-model="form.confirmPassword"
            type="password"
            placeholder="Enter"
            :class="{ error: errors.confirmPassword }"
          />
          <span v-if="errors.confirmPassword" class="error-text">{{ errors.confirmPassword }}</span>
        </div>

        <button class="continue-button" :disabled="loading" @click="handleRegister">
          <span v-if="!loading">❤️ Continue</span>
          <a-spin v-else />
        </button>

        <div class="login-link" @click="router.push('/user/login')">
          Already have an account? <span class="sign-in">Sign in</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { Message } from '@/mobile/components/Message'
import { useUserStore } from '@/store/user'

interface FormState {
  email: string
  name: string
  password: string
  confirmPassword: string
}

interface FormErrors {
  email?: string
  name?: string
  password?: string
  confirmPassword?: string
}

const router = useRouter()
const userStore = useUserStore()
const loading = ref(false)

const form = reactive<FormState>({
  email: '',
  name: '',
  password: '',
  confirmPassword: ''
})

const errors = reactive<FormErrors>({})

const validateForm = (): boolean => {
  let isValid = true
  errors.email = ''
  errors.name = ''
  errors.password = ''
  errors.confirmPassword = ''

  if (!form.email) {
    errors.email = 'Email is required'
    isValid = false
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email)) {
    errors.email = 'Invalid email format'
    isValid = false
  }

  if (!form.name) {
    errors.name = 'Name is required'
    isValid = false
  } else if (form.name.length < 2) {
    errors.name = 'Name must be at least 2 characters'
    isValid = false
  }

  if (!form.password) {
    errors.password = 'Password is required'
    isValid = false
  } else if (form.password.length < 6) {
    errors.password = 'Password must be at least 6 characters'
    isValid = false
  }

  if (!form.confirmPassword) {
    errors.confirmPassword = 'Please confirm your password'
    isValid = false
  } else if (form.password !== form.confirmPassword) {
    errors.confirmPassword = 'Passwords do not match'
    isValid = false
  }

  return isValid
}

const handleRegister = async () => {
  if (!validateForm()) return

  loading.value = true
  try {
    const isRegister = await userStore.register(form)
    if (!isRegister) {
      loading.value = false
      return false
    }
    Message.success('Registration successful')
    router.push('/user/login')
  } catch (error: any) {
    console.error(error)
    Message.error(error.message || 'Registration failed')
  } finally {
    loading.value = false
  }
}

defineOptions({
  name: 'RegisterView'
})
</script>

<style lang="less" scoped>
.register-container {
  height: calc(var(--vh, 1vh) * 100);
  background: linear-gradient(180deg, #2b1b2f 0%, #1a1021 100%);
  padding: 20px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: url('https://cdn.magiclight.ai/assets/playshot/login-bg.png');
  background-size: cover;
  background-position: center;
}

.register-card {
  width: 100%;
  max-width: 320px;
  background: #1f0038;
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 24px;
  position: relative;
  text-align: center;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.2);
}

.close-button {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: rgba(255, 255, 255, 0.6);
  transition: color 0.2s;

  &:hover {
    color: white;
  }

  :deep(.arco-icon) {
    font-size: 18px;
  }
}

.logo-wrapper {
  margin-bottom: 32px;

  img {
    height: 28px;
    margin-bottom: 12px;
  }

  h2 {
    color: rgba(255, 255, 255, 0.8);
    font-size: 18px;
    font-weight: 500;
    margin: 0;
  }
}

.form-wrapper {
  .input-group {
    margin-bottom: 20px;
    text-align: left;

    label {
      display: block;
      color: rgba(255, 255, 255, 0.6);
      font-size: 14px;
      margin-bottom: 8px;
      margin-left: 4px;
    }

    input {
      width: 100%;
      height: 44px;
      background: rgba(255, 255, 255, 0.06);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 24px;
      padding: 0 20px;
      color: white;
      font-size: 15px;
      transition: all 0.3s;

      &::placeholder {
        color: rgba(255, 255, 255, 0.3);
      }

      &:focus {
        outline: none;
        border-color: rgba(255, 255, 255, 0.2);
        background: rgba(255, 255, 255, 0.08);
      }

      &.error {
        border-color: rgba(255, 77, 77, 0.5);
      }
    }

    .error-text {
      color: rgba(255, 77, 77, 0.9);
      font-size: 12px;
      margin-top: 6px;
      margin-left: 4px;
      display: block;
    }
  }

  .continue-button {
    width: 100%;
    height: 44px;
    border: none;
    border-radius: 22px;
    font-size: 15px;
    font-weight: 500;
    color: #000;
    background: #ca93f2;
    cursor: pointer;
    transition: all 0.3s;
    margin-top: 8px;

    &:disabled {
      opacity: 0.7;
      cursor: not-allowed;
    }

    &:not(:disabled):active {
      transform: scale(0.98);
    }
  }

  .login-link {
    margin-top: 16px;
    color: rgba(255, 255, 255, 0.6);
    font-size: 14px;
    cursor: pointer;
    transition: color 0.2s;
    padding: 8px;

    .sign-in {
      color: #ca93f2;
      font-weight: 500;
      margin-left: 4px;
    }

    &:active {
      opacity: 0.8;
    }
  }
}
</style>
