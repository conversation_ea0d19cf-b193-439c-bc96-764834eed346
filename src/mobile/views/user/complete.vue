<template>
  <div class="complete-info-container">
    <div class="modal-card">
      <div class="close-btn" @click="handleClose">
        <icon-close />
      </div>

      <template v-if="userStore.userInfo?.plan === 'free'">
        <div class="loading-wrapper">
          <a-spin v-if="!showTimeoutMessage" dot :size="40" />
          <div class="loading-container">
            <template v-if="!showTimeoutMessage">
              <p class="loading-text">Processing your registration...</p>
              <p class="loading-tip">Please wait, this may take a few seconds</p>
            </template>
            <template v-else>
              <p class="loading-text">Registration timeout</p>
              <p class="support-email">
                Need help? Contact us at:
                <a href="mailto:<EMAIL>"><EMAIL></a>
              </p>
            </template>
          </div>
        </div>
      </template>

      <template v-else>
        <div class="success-wrapper">
          <div class="success-icon">
            <icon-check-circle-fill />
          </div>
          <div class="info-section">
            <div class="info-label">Your Email Address</div>
            <div class="info-value">{{ userStore.userInfo.email }}</div>
          </div>
          <div class="info-section">
            <div class="info-label">Default Password</div>
            <div class="info-value">123456</div>
          </div>
          <div class="tip-text">
            For your account's safety,<br />
            please update your password promptly.
          </div>
          <a-button type="primary" class="enter-btn" @click="handleEnter">Get Started</a-button>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store/user'
import { useTimeoutPoll } from '@vueuse/core'
import { reportEvent } from '@/utils'
import { ReportEvent } from '@/interface'
import { Message } from '@/mobile/components/Message'
const router = useRouter()
const userStore = useUserStore()
const isPolling = ref(true)
const showTimeoutMessage = ref(false)

const handleEnter = () => {
  router.push('/chat')
}

const handleClose = () => {
  router.push('/chat')
}

const startPolling = () => {
  const { resume, pause } = useTimeoutPoll(async () => {
    if (!isPolling.value) {
      pause()
      return
    }

    try {
      await userStore.getUserInfo()
      if (userStore.userInfo?.plan === 'basic') {
        // @ts-ignore
        if (window.fbq) {
          // @ts-ignore
          window.fbq('trackCustom', 'payment_success')
        }
        reportEvent(ReportEvent.PaymentSuccess, {
          userId: userStore.userInfo?.uuid
        })
        isPolling.value = false
        pause()
      }
    } catch (error) {
      console.error('Polling error:', error)
    }
  }, 10000)

  userStore.getUserInfo()
  resume()

  // Handle timeout separately
  setTimeout(() => {
    if (userStore.userInfo?.plan !== 'basic') {
      isPolling.value = false
      pause()
      Message.warning({
        content: 'Registration timeout, please refresh and try again',
        duration: 3000
      })
      showTimeoutMessage.value = true
      reportEvent(ReportEvent.GetPaymentSuccessInfoTimeout, {
        userId: userStore.userInfo?.uuid
      })
    }
  }, 60000)
}

onMounted(() => {
  startPolling()
})
</script>

<style lang="less" scoped>
.complete-info-container {
  height: calc(var(--vh, 1vh) * 100);
  display: flex;
  align-items: center;
  justify-content: center;
  background: #1d1e22;
  padding: 20px;
  background-image: url('https://cdn.magiclight.ai/assets/playshot/login-bg.png');
  background-size: cover;
  background-position: center;

  .modal-card {
    width: 100%;
    max-width: 335px;
    min-height: 335px;
    background: #180430;
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;

    .close-btn {
      position: absolute;
      top: 16px;
      right: 16px;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: rgba(255, 255, 255, 0.6);
      cursor: pointer;
      transition: all 0.3s ease;
      z-index: 1;

      &:hover {
        color: rgba(255, 255, 255, 0.9);
      }
    }

    .loading-wrapper {
      text-align: center;

      :deep(.arco-spin) {
        margin-bottom: 24px;
        .arco-spin-dot {
          color: #42c84b;
        }
      }

      .loading-text {
        color: #fff;
        font-size: 16px;
        margin-bottom: 8px;
      }

      .loading-tip {
        color: rgba(255, 255, 255, 0.5);
        font-size: 14px;
      }
    }

    .success-wrapper {
      text-align: center;

      .success-icon {
        font-size: 32px;
        color: #42c84b;
        margin-bottom: 24px;
        animation: scaleIn 0.3s ease;
      }

      .info-section {
        background: #3d2f54;
        border-radius: 12px;
        padding: 12px 16px;
        margin-bottom: 16px;
        text-align: left;

        .info-label {
          color: rgba(255, 255, 255, 0.5);
          font-size: 12px;
          margin-bottom: 4px;
        }

        .info-value {
          color: #fff;
          font-size: 14px;
          font-weight: 500;
        }
      }

      .tip-text {
        color: rgba(255, 255, 255, 0.5);
        font-size: 12px;
        line-height: 1.5;
        margin: 16px 0 24px;
      }

      .enter-btn {
        width: 100%;
        height: 44px;
        border-radius: 22px;
        font-size: 16px;
        font-weight: 500;
        background: linear-gradient(135deg, #6f42c1 0%, #4b367c 100%);
        border: 1px solid rgba(255, 255, 255, 0.1);
        color: #fff;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);

        &:hover {
          transform: translateY(-2px);
          background: linear-gradient(135deg, #7b4dd4 0%, #533c89 100%);
          box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
        }

        &:active {
          transform: translateY(0);
          background: linear-gradient(135deg, #633ab0 0%, #432f6f 100%);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }
      }
    }
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.5);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.support-email {
  margin-top: 16px;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.6);
  text-align: center;

  a {
    color: #ca93f2;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}
</style>
