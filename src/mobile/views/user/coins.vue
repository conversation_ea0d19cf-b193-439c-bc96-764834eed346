<template>
  <div class="coins-page">
    <div class="header">
      <div class="back-button" @click="router.back()">
        <icon-left />
      </div>
      <h1>Coins Details</h1>
    </div>

    <div class="coins-display">
      <img src="https://cdn.magiclight.ai/assets/mobile/diamond.png" class="diamond-icon" />
      <span class="amount">{{ userStore.userInfo?.coins || 0 }}</span>
    </div>

    <div class="history-list">
      <div v-for="item in historyList" :key="item.action_time" class="history-item">
        <div class="left">
          <div class="type">{{ formatActionType(item.action_type) }}</div>
          <div class="time">{{ formatTime(item.action_time) }}</div>
        </div>
        <div
          class="amount"
          :class="{
            positive: item.coin_delta > 0,
            neutral: item.coin_delta === 0,
            negative: item.coin_delta < 0
          }"
        >
          {{ item.coin_delta > 0 ? '+' : '' }}{{ item.coin_delta }}
        </div>
      </div>
      <div v-if="loading" class="loading">
        <a-spin />
      </div>
      <div v-if="!loading && historyList.length === 0" class="empty">
        No coin history available yet.
      </div>
      <div v-if="!loading && historyList.length > 0" class="no-more">No more history</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/store'
import { IconLeft } from '@arco-design/web-vue/es/icon'
import { getCoinHistory, type CoinHistoryItem } from '@/api/coins'
import dayjs from 'dayjs'

const router = useRouter()
const userStore = useUserStore()

const historyList = ref<CoinHistoryItem[]>([])
const loading = ref(false)

const loadHistory = async () => {
  try {
    loading.value = true
    const { data: response } = await getCoinHistory()
    if (response.code === '0') {
      historyList.value = response.data
    }
  } catch (error) {
    console.error('Failed to load coin history:', error)
  } finally {
    loading.value = false
  }
}

const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm')
}

const formatActionType = (type: string) => {
  const typeMap: Record<string, string> = {
    signup: 'Sign Up Bonus',
    purchase: 'Purchase',
    chat: 'Chat Consumption',
    story: 'Story Creation',
    refund: 'Refund',
    discord: 'Discord Sign Up Bonus',
    google: 'Google Sign Up Bonus',
    apple: 'Apple Sign Up Bonus'
  }
  return (
    typeMap[type] ||
    type
      .split('_')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ')
  )
}

onMounted(() => {
  loadHistory()
})
</script>

<style lang="less" scoped>
.coins-page {
  height: calc(var(--vh, 1vh) * 100);
  background: #1f0038;
  color: white;
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.header {
  padding: 20px 16px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  margin-bottom: 0;

  .back-button {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    margin-right: 16px;
    cursor: pointer;

    &:active {
      background: rgba(255, 255, 255, 0.2);
    }
  }

  h1 {
    font-size: 24px;
    font-weight: 600;
    margin: 0;
  }
}

.coins-display {
  padding: 0 16px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 32px;

  .diamond-icon {
    width: 32px;
    height: 32px;
  }

  .amount {
    font-size: 32px;
    font-weight: 600;
  }
}

.history-list {
  flex: 1;
  overflow-y: auto;
  padding: 0 16px;
  padding-bottom: calc(20px + 56px);
  -webkit-overflow-scrolling: touch;

  .history-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 16px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    .left {
      .type {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 4px;
      }

      .time {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.5);
      }
    }

    .amount {
      font-size: 16px;
      font-weight: 600;
      color: #ff4d4f;

      &.positive {
        color: #b1ff8f;
      }

      &.neutral {
        color: rgba(255, 255, 255, 0.8);
      }

      &.negative {
        color: #ff4d4f;
      }
    }
  }

  .loading {
    text-align: center;
    padding: 32px 0;
  }

  .empty {
    text-align: center;
    padding: 32px 0;
    color: rgba(255, 255, 255, 0.5);
    font-size: 14px;
  }

  .no-more {
    text-align: center;
    padding: 16px 0;
    color: rgba(255, 255, 255, 0.5);
    font-size: 12px;
    font-weight: 400;
  }
}
</style>
