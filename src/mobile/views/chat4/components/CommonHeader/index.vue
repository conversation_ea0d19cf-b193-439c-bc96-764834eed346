<template>
  <div class="common-header">
    <!-- 左侧：返回按钮和角色信息 -->
    <div class="header-left">
      <!-- 返回按钮 -->
      <div class="back-button" @click="handleBackClick">
        <slot name="back-button">
          <span class="back-icon"><icon-left /></span>
        </slot>
      </div>

      <!-- 角色信息 -->
      <div class="character-info">
        <div class="avatar">
          <img :src="characterAvatar || '/default-avatar.png'" :alt="characterName" />
        </div>
        <div class="character-details">
          <div class="character-name">{{ characterName }}</div>
          <!-- 直播场景显示热度信息 -->
          <div v-if="showViewerCount" class="live-info">
            <div class="fire-icon">🔥</div>
            <span class="viewer-count">{{ formatViewerCount(dynamicViewerCount) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧：钻石积分显示或其他内容 -->
    <div class="header-right">
      <slot name="header-right">
        <!-- 默认显示钻石积分 -->
        <div class="coin-display">
          <slot name="coin-display"></slot>
        </div>
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'

interface Props {
  characterName?: string
  characterAvatar?: string
  showViewerCount?: boolean
  viewerCount?: number
  isLive?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  characterName: 'Character',
  showViewerCount: false,
  viewerCount: 0,
  isLive: false
})

const emit = defineEmits<{
  'back-click': []
}>()

// 动态观看人数管理
const dynamicViewerCount = ref(props.viewerCount)

// 格式化观看人数
const formatViewerCount = (count: number): string => {
  if (count >= 1000) {
    return `${Math.floor(count / 1000)},${String(count % 1000).padStart(3, '0')}`
  }
  return count.toString()
}

// 模拟真实直播间观看人数波动
const simulateViewerCountFluctuation = () => {
  // 随机增减范围：-3 到 +8 (更倾向于增加，模拟直播间热度上升)
  const changeRange = Math.random()
  let change = 0

  if (changeRange < 0.1) {
    // 10% 概率大幅减少 (-5 到 -3)
    change = -Math.floor(Math.random() * 3) - 3
  } else if (changeRange < 0.3) {
    // 20% 概率小幅减少 (-2 到 -1)
    change = -Math.floor(Math.random() * 2) - 1
  } else if (changeRange < 0.5) {
    // 20% 概率保持不变
    change = 0
  } else if (changeRange < 0.8) {
    // 30% 概率小幅增加 (1 到 3)
    change = Math.floor(Math.random() * 3) + 1
  } else {
    // 20% 概率大幅增加 (4 到 8)
    change = Math.floor(Math.random() * 5) + 4
  }

  // 确保观看人数不会低于初始值的70%
  const minCount = Math.floor(props.viewerCount * 0.7)
  const newCount = Math.max(minCount, dynamicViewerCount.value + change)

  // 也不要让观看人数增长过快，限制在初始值的150%
  const maxCount = Math.floor(props.viewerCount * 1.5)
  dynamicViewerCount.value = Math.min(maxCount, newCount)
}

// 定时器管理
let viewerCountTimer: ReturnType<typeof setInterval> | null = null

// 启动观看人数波动
const startViewerCountFluctuation = () => {
  if (props.showViewerCount && props.isLive) {
    // 每3-8秒随机变化一次
    const scheduleNext = () => {
      const delay = Math.random() * 5000 + 3000 // 3-8秒
      viewerCountTimer = setTimeout(() => {
        simulateViewerCountFluctuation()
        scheduleNext() // 递归调度下一次变化
      }, delay)
    }
    scheduleNext()
  }
}

// 停止观看人数波动
const stopViewerCountFluctuation = () => {
  if (viewerCountTimer) {
    clearTimeout(viewerCountTimer)
    viewerCountTimer = null
  }
}

// 处理返回按钮点击
const handleBackClick = () => {
  emit('back-click')
}

// 生命周期管理
onMounted(() => {
  // 初始化动态观看人数
  dynamicViewerCount.value = props.viewerCount
  // 启动观看人数波动
  startViewerCountFluctuation()
})

onBeforeUnmount(() => {
  // 清理定时器
  stopViewerCountFluctuation()
})

// 监听props变化
watch(
  () => props.viewerCount,
  (newCount) => {
    dynamicViewerCount.value = newCount
  }
)

watch(
  () => [props.showViewerCount, props.isLive],
  () => {
    stopViewerCountFluctuation()
    if (props.showViewerCount && props.isLive) {
      startViewerCountFluctuation()
    }
  }
)
</script>

<style lang="less" scoped>
.common-header {
  position: absolute;
  top: 25px;
  left: 7px;
  right: 7px;
  z-index: 100;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 9px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 9px;

  .back-button {
    width: 36px;
    height: 36px;
    border-radius: 22.5px;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(20.45px);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    flex-shrink: 0;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: scale(1.05);
    }

    &:active {
      transform: scale(0.95);
    }

    .back-icon {
      font-weight: 400;
      line-height: 1.19;
      color: #ffffff;
      text-align: center;
      svg {
        color: #ffffff;
      }
    }
  }

  .character-info {
    display: flex;
    align-items: center;
    gap: 9px;

    .avatar {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      overflow: hidden;
      flex-shrink: 0;
      border: 1.67px solid #fffcde;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .character-details {
      display: flex;
      flex-direction: column;
      gap: 2px;

      .character-name {
        font-family: 'Work Sans', sans-serif;
        font-weight: 600;
        font-size: 13px;
        line-height: 1.17;
        color: #ffffff;
        text-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
        white-space: nowrap;
      }

      .live-info {
        display: flex;
        align-items: center;
        gap: 4px;

        .fire-icon {
          width: 10px;
          height: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 8px;
        }

        .viewer-count {
          font-family: 'Work Sans', sans-serif;
          font-size: 11px;
          font-weight: 500;
          line-height: 1.17;
          color: white;
          text-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
        }
      }
    }
  }
}

.header-right {
  flex-shrink: 0;

  .coin-display {
    // 钻石积分显示样式由父组件的插槽内容决定
  }
}

/* 响应式适配 */
@media (min-width: 768px) {
  .common-header {
    top: 30px;
    left: 15px;
    right: 15px;
  }

  .header-left {
    gap: 12px;

    .back-button {
      width: 40px;
      height: 40px;
      border-radius: 25px;
    }

    .character-info {
      gap: 12px;

      .avatar {
        width: 40px;
        height: 40px;
      }

      .character-details {
        .character-name {
          font-size: 15px;
        }

        .live-info {
          .fire-icon {
            width: 12px;
            height: 12px;
            font-size: 10px;
          }

          .viewer-count {
            font-size: 12px;
          }
        }
      }
    }
  }
}
</style>
