<template>
  <Teleport to="body">
    <Transition name="fade">
      <div v-if="visible" class="friend-request-modal-overlay" @click.stop="handleOverlayClick">
        <div class="friend-request-modal" @click.stop>
          <div class="modal-content">
            <p class="content-text">Friend request accepted!</p>
            <p class="content-text">Continue the stream or start chatting?</p>
          </div>

          <div class="modal-buttons">
            <button class="keep-watching-btn" @click="handleKeepWatching">Keep Watching</button>
            <button class="start-chatting-btn" @click="handleStartChatting">Start Chatting</button>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, withDefaults } from 'vue'

interface Props {
  visible: boolean
  closeOnClickOverlay?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  closeOnClickOverlay: false
})

const emit = defineEmits<{
  'update:visible': [value: boolean]
  'keep-watching': []
  'start-chatting': []
}>()

const handleKeepWatching = () => {
  emit('keep-watching')
  emit('update:visible', false)
}

const handleStartChatting = () => {
  emit('start-chatting')
  emit('update:visible', false)
}

const handleOverlayClick = () => {
  if (props.closeOnClickOverlay) {
    emit('update:visible', false)
  }
}
</script>

<style lang="less" scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.friend-request-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.friend-request-modal {
  width: 320px;
  height: 180px;
  background: #1f0038;
  border-radius: 17px;
  padding: 0;
  display: flex;
  flex-direction: column;
  position: relative;
  backdrop-filter: blur(20px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-content {
  padding: 24px 14px 0 14px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

.content-text {
  font-family: 'Work Sans', sans-serif;
  font-weight: 700;
  font-size: 15px;
  line-height: 1.173;
  color: #ffffff;
  margin: 0;
  text-align: left;
}

.modal-buttons {
  padding: 0 14px 20px 14px;
  display: flex;
  gap: 20px;
  align-items: center;
  justify-content: space-between;
}

.keep-watching-btn,
.start-chatting-btn {
  width: 136px;
  height: 42px;
  border-radius: 26px;
  border: none;
  font-family: 'Work Sans', sans-serif;
  font-weight: 600;
  font-size: 15px;
  line-height: 1.173;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:active {
    transform: scale(0.98);
  }
}

.keep-watching-btn {
  background: linear-gradient(180deg, #d6cafe 0%, #ca93f2 100%);
  color: #241d49;
  border: 2px solid #1f0038;
  border-bottom: 6px solid #1f0038;
  box-shadow: 0px 2px 12px 0px rgba(176, 152, 255, 1);
}

.start-chatting-btn {
  background: linear-gradient(180deg, #f5ffe2 0%, #daff96 100%);
  color: #241d49;
  border: 2px solid #1f0038;
  border-bottom: 6px solid #1f0038;
  box-shadow: 0px 1.855px 11.13px 0px rgba(218, 255, 150, 1);
}
</style>
