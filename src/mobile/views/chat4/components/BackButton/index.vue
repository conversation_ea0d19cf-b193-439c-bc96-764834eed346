<template>
  <button class="back-button" @click="$emit('click')">
    <span class="back-icon"><icon-left /></span>
  </button>
</template>

<script setup lang="ts">
const emit = defineEmits<{
  click: []
}>()
</script>

<style lang="less" scoped>
.back-button {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 36px;
  height: 36px;
  border-radius: 22.5px;
  border: none;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(20.45px);
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(0, 0, 0, 0.4);
  }

  &:active {
    transform: scale(0.95);
  }

  .back-icon {
    font-family: 'SF Pro', sans-serif;
    font-size: 24.55px;
    font-weight: 400;
    line-height: 1.19;
    color: white;
    text-align: center;
  }
}
</style>
