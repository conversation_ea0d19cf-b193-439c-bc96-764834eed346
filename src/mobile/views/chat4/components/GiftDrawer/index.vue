<template>
  <div v-if="visible" class="gift-drawer-overlay" @click="handleClose">
    <div class="gift-drawer" @click.stop>
      <!-- 头部 -->
      <div class="drawer-header">
        <h3 class="drawer-title">Send Gift</h3>
        <button class="close-button" @click="handleClose">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path
              d="M18 6L6 18M6 6l12 12"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
      </div>

      <!-- 礼物网格 -->
      <div v-if="!loading && !error" class="gift-grid">
        <div
          v-for="gift in presents"
          :key="gift.id"
          class="gift-item"
          :class="{ selected: selectedGift?.id === gift.id }"
          @click="selectGift(gift)"
        >
          <div class="gift-image">
            <img :src="gift.image_url" :alt="gift.title" />
          </div>
          <div class="gift-price">
            <img
              src="https://cdn.magiclight.ai/assets/mobile/diamond.png"
              alt="Diamond"
              class="diamond-icon"
            />
            <span class="price-text">{{ gift.coins }}</span>
          </div>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <div class="loading-spinner"></div>
        <div class="loading-text">Loading gifts...</div>
      </div>

      <!-- 错误状态 -->
      <div v-if="error" class="error-state">
        <div class="error-text">{{ error }}</div>
        <button class="retry-button" @click="fetchPresents">Retry</button>
      </div>

      <!-- 底部 -->
      <div class="drawer-footer">
        <div class="diamond-balance">
          <div class="balance-info">
            <span class="balance-label">Balance:</span>
            <div class="balance-amount">
              <img
                src="https://cdn.magiclight.ai/assets/mobile/diamond.png"
                alt="Diamond"
                class="diamond-icon"
              />
              <span class="balance-text">{{ userBalance }}</span>
            </div>
          </div>
        </div>
        <button
          class="buy-button"
          :class="{ 'insufficient-balance': !canBuySelectedGift }"
          :disabled="!selectedGift || sending || !canBuySelectedGift"
          @click="handleSendGift"
        >
          {{ sending ? 'Sending...' : 'Send' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import { useUserStore } from '@/store/user'
import { useStoryStore } from '@/store/story'
import { useChatEventsStore } from '@/store/chat-events'
import { getPresentsListAPI, type Present } from '@/api/chat-multivariate'

// Props
interface Props {
  visible: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  'gift-sent': [gift: Present]
}>()

// Store
const userStore = useUserStore()
const storyStore = useStoryStore()
const chatEventsStore = useChatEventsStore()

// 状态
const presents = ref<Present[]>([])
const selectedGift = ref<Present | null>(null)
const loading = ref(false)
const error = ref('')
const sending = ref(false)
// 用户钻石余额 - 使用真实数据
const userBalance = computed(() => userStore.userInfo?.coins || 0)

// 检查是否可以购买选中的礼物
const canBuySelectedGift = computed(() => {
  if (!selectedGift.value) return false
  return userBalance.value >= selectedGift.value.coins
})

// 获取礼物列表
const fetchPresents = async () => {
  loading.value = true
  error.value = ''

  try {
    console.log('Fetching presents list from API...')
    const response = await getPresentsListAPI()

    if (response.data.data?.present_list) {
      presents.value = response.data.data.present_list
      console.log('Presents loaded successfully:', presents.value.length, 'items')
    } else {
      throw new Error(response.data.message || 'Failed to load presents')
    }
  } catch (err: any) {
    console.error('Failed to fetch presents:', err)
    error.value = err.response?.data?.message || err.message || 'Failed to load gifts'
  } finally {
    loading.value = false
  }
}

// 选择礼物
const selectGift = (gift: Present) => {
  selectedGift.value = gift
}

// 发送礼物
const handleSendGift = async () => {
  if (!selectedGift.value || sending.value) return

  if (!canBuySelectedGift.value) {
    Message.error('Insufficient balance')
    return
  }

  try {
    sending.value = true

    console.log('Sending gift:', {
      presentId: selectedGift.value.id,
      title: selectedGift.value.title
    })

    // 使用 sendMessage 发送包含 present_id 的消息
    // 这会调用 sendMessageSSE 并在 content.data 中传递 present_id
    await chatEventsStore.sendMessage(
      `🎁 Sent ${selectedGift.value.title}`, // 消息文本
      null, // optionId
      'text', // msgType
      false, // isTelepathyComplete
      null, // sceneId
      800, // delay
      false, // isJump
      { present_id: selectedGift.value.id } // extraData - present_id 会被添加到 data 中
    )

    // 触发直播间内的礼物动画
    emit('gift-sent', selectedGift.value)

    // 更新用户余额信息
    try {
      await userStore.getUserInfo()
    } catch (error) {
      console.error('Failed to update user balance:', error)
      // 即使更新余额失败，也不影响礼物发送的成功状态
    }

    handleClose()
  } catch (err: any) {
    console.error('Failed to send gift:', err)
    const errorMessage = err.message || 'Network error, please try again'
    Message.error(errorMessage)
  } finally {
    sending.value = false
  }
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
  selectedGift.value = null
}

// 监听visible变化
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible && presents.value.length === 0) {
      fetchPresents()
    }
  }
)

// 组件挂载时获取礼物列表
onMounted(() => {
  if (props.visible) {
    fetchPresents()
  }
})
</script>

<style scoped lang="less">
.gift-drawer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.gift-drawer {
  background: linear-gradient(180deg, #4c3c59 0%, #2d1b3d 100%);
  border-radius: 20px 20px 0 0;
  width: 100%;
  max-width: 500px;
  max-height: 70vh;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
  border: 2px solid rgba(218, 255, 150, 0.2);
}

.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.14);

  .drawer-title {
    color: #ffffff;
    font-family: 'Work Sans', sans-serif;
    font-weight: 600;
    font-size: 18px;
    margin: 0;
  }

  .close-button {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;

    &:hover {
      color: #ffffff;
      background: rgba(255, 255, 255, 0.1);
    }
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.gift-grid {
  padding: 20px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(90px, 1fr));
  gap: 12px;
  max-height: 400px;
  overflow-y: auto;
  justify-items: center;

  /* 确保在手机端有合适的列数 */
  @media (max-width: 480px) {
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
    padding: 16px;
  }

  /* 平板端 */
  @media (min-width: 481px) and (max-width: 768px) {
    grid-template-columns: repeat(5, 1fr);
  }

  /* 桌面端 */
  @media (min-width: 769px) {
    grid-template-columns: repeat(6, 1fr);
  }
}

.gift-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
  max-width: 90px;

  &.selected .gift-image {
    border: 2px solid #daff96;
  }

  .gift-image {
    width: 100%;
    aspect-ratio: 1;
    max-width: 80px;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 8px;
    border: 2px solid transparent;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .gift-price {
    display: flex;
    align-items: center;
    gap: 4px;

    .diamond-icon {
      width: 18px;
      height: 18px;
    }

    .price-text {
      font-family: 'Work Sans', sans-serif;
      font-weight: 600;
      font-size: 12px;
      color: #daff96;
    }
  }
}

.loading-state,
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  gap: 16px;

  .loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid rgba(255, 255, 255, 0.1);
    border-top: 3px solid #daff96;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .loading-text,
  .error-text {
    font-family: 'Work Sans', sans-serif;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
  }

  .retry-button {
    padding: 8px 16px;
    background: #daff96;
    border: none;
    border-radius: 8px;
    color: #1f0038;
    font-family: 'Work Sans', sans-serif;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: #c5e885;
    }
  }
}

.drawer-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.14);
  height: 80px;

  .diamond-balance {
    .balance-info {
      display: flex;
      gap: 7px;

      .balance-label {
        font-family: 'Work Sans', sans-serif;
        font-weight: 500;
        font-size: 15px;
        color: #ffffff;
      }

      .balance-amount {
        display: flex;
        align-items: center;
        gap: 4px;

        .diamond-icon {
          width: 18px;
          height: 18px;
        }

        .balance-text {
          font-family: 'Work Sans', sans-serif;
          font-weight: 600;
          font-size: 12px;
          color: #daff96;
        }
      }
    }
  }

  .buy-button {
    width: 80px;
    height: 36px;
    background: linear-gradient(180deg, #d6cafe 0%, #ca93f2 100%);
    border: 2px solid #1f0038;
    border-bottom: 6px solid #1f0038;
    border-radius: 26px;
    box-shadow: 0px 2px 12px 0px rgba(176, 152, 255, 1);
    color: #241d49;
    font-family: 'Work Sans', sans-serif;
    font-weight: 600;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover:not(:disabled) {
      transform: translateY(-1px);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    &.insufficient-balance {
      background: linear-gradient(180deg, #ffe2e2 0%, #ff9696 100%);
      color: #8b0000;

      &:hover:not(:disabled) {
        background: linear-gradient(180deg, #ffd0d0 0%, #ff8080 100%);
      }
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
