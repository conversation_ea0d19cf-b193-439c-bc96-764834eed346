<template>
  <div class="danmaku-options">
    <div
      class="options-container"
      ref="optionsContainer"
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
      @scroll="handleScroll"
    >
      <button
        v-for="option in options"
        :key="option.id"
        class="option-button"
        :class="{ active: option.id === selectedOptionId }"
        @click="handleOptionClick(option)"
      >
        {{ option.text }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'

interface DanmakuOption {
  id: string
  text: string
  type?: 'normal' | 'special'
}

interface Props {
  options: DanmakuOption[]
  selectedOptionId?: string
}

const props = withDefaults(defineProps<Props>(), {
  options: () => [
    { id: '1', text: 'You are wonderful！', type: 'normal' },
    { id: '2', text: 'You are the best in my heart！', type: 'normal' },
    { id: '3', text: 'I will always support you！', type: 'normal' },
    { id: '4', text: 'No one is more dazzling than you！', type: 'normal' },
    { id: '5', text: 'Awesome! Lighting up for you!', type: 'normal' },
    { id: '6', text: 'Perfect! Shouting for you!', type: 'normal' },
    { id: '7', text: "You're my light! Love you to the core!", type: 'normal' },
    { id: '8', text: 'Help! Freaking out over you!', type: 'special' },
    { id: '9', text: 'On fire! Gotta give you all the hype!', type: 'normal' }
  ]
})

const emit = defineEmits<{
  'option-selected': [option: DanmakuOption]
}>()

// 引用
const optionsContainer = ref<HTMLElement>()

// 触摸相关状态
const touchState = ref({
  startX: 0,
  startY: 0,
  isDragging: false,
  startTime: 0
})

// 处理选项点击
const handleOptionClick = (option: DanmakuOption) => {
  emit('option-selected', option)
}

// 触摸开始
const handleTouchStart = (e: TouchEvent) => {
  const touch = e.touches[0]
  touchState.value = {
    startX: touch.clientX,
    startY: touch.clientY,
    isDragging: false,
    startTime: Date.now()
  }
}

// 触摸移动
const handleTouchMove = (e: TouchEvent) => {
  if (!touchState.value.startX) return

  const touch = e.touches[0]
  const deltaX = Math.abs(touch.clientX - touchState.value.startX)
  const deltaY = Math.abs(touch.clientY - touchState.value.startY)

  // 如果水平移动距离大于垂直移动距离，认为是横向滑动
  if (deltaX > deltaY && deltaX > 10) {
    touchState.value.isDragging = true
    e.preventDefault() // 阻止页面滚动
  }
}

// 触摸结束
const handleTouchEnd = (e: TouchEvent) => {
  touchState.value = {
    startX: 0,
    startY: 0,
    isDragging: false,
    startTime: 0
  }
}

// 滚动处理
const handleScroll = (e: Event) => {
  // 可以在这里添加滚动相关的逻辑
}

// 自动滚动到指定选项
const scrollToOption = (optionId: string) => {
  if (!optionsContainer.value) return

  const optionElement = optionsContainer.value.querySelector(`[data-option-id="${optionId}"]`)
  if (optionElement) {
    optionElement.scrollIntoView({
      behavior: 'smooth',
      block: 'nearest',
      inline: 'center'
    })
  }
}

// 暴露方法
defineExpose({
  scrollToOption
})

onMounted(() => {
  // 确保容器可以横向滚动
  nextTick(() => {
    if (optionsContainer.value) {
      // 设置初始滚动位置
      optionsContainer.value.scrollLeft = 0
    }
  })
})
</script>

<style lang="less" scoped>
.danmaku-options {
  width: 100%;
  height: 52px; // 根据Figma设计的高度
  position: relative;
}

.options-container {
  display: flex;
  align-items: center;
  gap: 13px;
  height: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  padding: 0 16px;
  scroll-behavior: smooth;

  // 隐藏滚动条但保持滚动功能
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */

  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }

  // 触摸滚动优化
  -webkit-overflow-scrolling: touch;
  scroll-snap-type: x proximity;
}

.option-button {
  flex-shrink: 0;
  background: rgba(46, 23, 65, 0.58);
  border: none;
  border-radius: 68px;
  padding: 11px 17px; // 增加1px来补偿边框
  backdrop-filter: blur(8px);
  cursor: pointer;
  transition: all 0.3s ease;
  scroll-snap-align: center;
  position: relative;

  // 使用伪元素实现渐变边框
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #ffffff 0%, #ff95d8 36%, #b476ff 74%, #ffe6e6 100%);
    border-radius: 68px;
    z-index: -1;
    padding: 1px; // 边框宽度
  }

  // 使用另一个伪元素创建内部背景
  &::after {
    content: '';
    position: absolute;
    top: 1px;
    left: 1px;
    right: 1px;
    bottom: 1px;
    background: rgba(46, 23, 65, 0.58);
    border-radius: 67px; // 稍微小一点以适应边框
    backdrop-filter: blur(8px);
    z-index: -1;
  }

  // 文字样式
  font-family: 'Work Sans', sans-serif;
  font-weight: 500;
  font-size: 12px;
  line-height: 1.17;
  color: #ffffff;
  text-align: center;
  white-space: nowrap;
  position: relative;
  z-index: 1; // 确保文字在伪元素之上

  // 悬停效果
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0px 4px 12px rgba(255, 255, 255, 0.3);
  }

  // 激活状态
  &:active {
    transform: translateY(0);
    box-shadow: 0px 0px 8px rgba(255, 255, 255, 0.4);
  }

  // 选中状态
  &.active {
    background: linear-gradient(135deg, #8574f3 0%, #df7aec 100%);
    border: 1px solid #ffed74;
    box-shadow: 0px 0px 11px 0px rgba(255, 255, 255, 0.6);
  }
}

// 响应式设计
@media (max-width: 375px) {
  .option-button {
    padding: 8px 12px;
    font-size: 9px;
  }

  .options-container {
    gap: 10px;
    padding: 0 12px;
  }
}

// 添加渐变遮罩效果，提示可以滚动
.danmaku-options::before,
.danmaku-options::after {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  width: 20px;
  pointer-events: none;
  z-index: 1;
}

.danmaku-options::before {
  left: 0;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.1), transparent);
}

.danmaku-options::after {
  right: 0;
  background: linear-gradient(to left, rgba(0, 0, 0, 0.1), transparent);
}
</style>
