<template>
  <div
    class="danmaku-bubble"
    :class="{
      normal: danmaku.type === 'normal',
      special: danmaku.type === 'special',
      system: danmaku.type === 'system'
    }"
  >
    <!-- 普通弹幕 -->
    <div v-if="danmaku.type === 'normal'" class="normal-content">
      <span class="danmaku-text">{{ danmaku.content }}</span>
    </div>

    <!-- 特殊弹幕（带头像） -->
    <div v-else-if="danmaku.type === 'special'" class="special-content">
      <div v-if="danmaku.avatar" class="avatar-container">
        <img :src="danmaku.avatar" :alt="'User avatar'" class="avatar" />
      </div>
      <span class="danmaku-text">{{ danmaku.content }}</span>
    </div>

    <!-- 系统弹幕 -->
    <div v-else class="system-content">
      <span class="danmaku-text">{{ danmaku.content }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'

interface Danmaku {
  id: string
  content: string
  type: 'normal' | 'special' | 'system'
  avatar?: string
  timestamp: number
}

interface Props {
  danmaku: Danmaku
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'animation-complete': [danmakuId: string]
}>()

onMounted(() => {
  // 弹幕不再自动触发完成事件，由父组件的定时器控制
})
</script>

<style lang="less" scoped>
.danmaku-bubble {
  width: fit-content;
  max-width: calc(100vw - 32px); // 考虑左右padding
  animation: slideInDanmaku 0.4s ease-out;

  // 普通弹幕样式 - 根据Figma设计
  &.normal {
    .normal-content {
      background: transparent; // 透明背景，让伪元素处理
      border: none;
      border-radius: 68px;
      padding: 11px 17px; // 增加1px来补偿边框
      backdrop-filter: blur(8px);
      position: relative;

      // 使用伪元素实现渐变边框
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, #ffffff 0%, #ff95d8 36%, #b476ff 74%, #ffe6e6 100%);
        border-radius: 68px;
        z-index: -1;
      }

      // 使用另一个伪元素创建内部背景
      &::after {
        content: '';
        position: absolute;
        top: 1px;
        left: 1px;
        right: 1px;
        bottom: 1px;
        background: rgba(46, 23, 65, 0.58);
        border-radius: 67px; // 稍微小一点以适应边框
        backdrop-filter: blur(8px);
        z-index: -1;
      }

      .danmaku-text {
        font-family: 'Work Sans', sans-serif;
        font-weight: 500;
        font-size: 10px;
        line-height: 1.17;
        color: #ffffff;
        text-align: center;
        position: relative;
        z-index: 1; // 确保文字在伪元素之上
      }
    }
  }

  // 特殊弹幕样式 - 渐变背景带头像
  &.special {
    .special-content {
      background: linear-gradient(135deg, #8574f3 0%, #df7aec 100%);
      border: 1px solid #ffed74;
      border-radius: 68px;
      padding: 10px 5px;
      display: flex;
      align-items: center;
      gap: 4px;
      box-shadow: 0px 0px 11px 0px rgba(255, 255, 255, 0.6);
      backdrop-filter: blur(8px);

      .avatar-container {
        width: 22px;
        height: 22px;
        border-radius: 50%;
        overflow: hidden;
        flex-shrink: 0;

        .avatar {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .danmaku-text {
        font-family: 'Work Sans', sans-serif;
        font-weight: 500;
        font-size: 10px;
        line-height: 1.17;
        color: #ffffff;
        text-align: center;
        padding-right: 11px; // 右侧padding
      }
    }
  }

  // 系统弹幕样式
  &.system {
    .system-content {
      background: rgba(218, 255, 150, 0.2);
      border: 1px solid rgba(218, 255, 150, 0.5);
      border-radius: 68px;
      padding: 10px 16px;
      backdrop-filter: blur(8px);

      .danmaku-text {
        font-family: 'Work Sans', sans-serif;
        font-weight: 500;
        font-size: 10px;
        line-height: 1.17;
        color: #daff96;
        text-align: center;
      }
    }
  }
}

@keyframes slideInDanmaku {
  from {
    opacity: 0;
    transform: translateX(-30px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

// 响应式设计
@media (max-width: 375px) {
  .danmaku-bubble {
    max-width: calc(100vw - 24px);

    &.normal .normal-content,
    &.special .special-content,
    &.system .system-content {
      padding: 8px 12px;

      .danmaku-text {
        font-size: 9px;
      }
    }

    &.special .special-content {
      .avatar-container {
        width: 20px;
        height: 20px;
      }

      .danmaku-text {
        padding-right: 8px;
      }
    }
  }
}
</style>
