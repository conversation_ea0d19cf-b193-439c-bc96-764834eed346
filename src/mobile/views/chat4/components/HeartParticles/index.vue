<template>
  <div class="heart-particles" ref="containerRef">
    <!-- Canvas容器，用于canvas-confetti -->
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted } from 'vue'
// @ts-ignore
import confetti from 'canvas-confetti'

interface Props {
  clickTrigger?: number // 点击触发计数器
}

const props = defineProps<Props>()

// 容器引用
const containerRef = ref<HTMLElement>()

// 自动飘心的定时器
let autoHeartTimer: number | null = null

// 性能优化：复用canvas和confetti实例
let sharedCanvas: HTMLCanvasElement | null = null
let sharedConfetti: any = null

// 性能优化：防抖控制
let isCreatingEffect = false
let pendingEffectCount = 0
const MAX_CONCURRENT_EFFECTS = 3

// 心形emoji数组
const heartEmojis = ['❤️', '🧡', '💛', '💚', '💙', '💜', '🤍', '🖤', '💖', '💕']

// 创建心形形状（使用官方的shapeFromText方法）
const heartShapes = heartEmojis.map((emoji) => confetti.shapeFromText({ text: emoji, scalar: 2 }))

// 缓存位置计算结果
let cachedPosition: { x: number; y: number } | null = null
let lastViewportSize = { width: 0, height: 0 }

// 计算heart-button的相对位置（动态查找，带缓存优化）
const getHeartButtonPosition = () => {
  const viewportWidth = window.innerWidth
  const viewportHeight = window.innerHeight

  // 如果视口大小没有变化，返回缓存的位置
  if (
    cachedPosition &&
    lastViewportSize.width === viewportWidth &&
    lastViewportSize.height === viewportHeight
  ) {
    return cachedPosition
  }

  // 动态查找heart-button元素
  const heartButton = document.querySelector('.heart-button')

  if (heartButton) {
    // 获取按钮的实际位置
    const buttonRect = heartButton.getBoundingClientRect()
    const heartButtonCenterX = buttonRect.left + buttonRect.width / 2
    const heartButtonCenterY = buttonRect.top + buttonRect.height / 2

    // particles容器覆盖整个视口，所以直接使用视口坐标
    const relativeX = heartButtonCenterX / viewportWidth
    const relativeY = heartButtonCenterY / viewportHeight

    const position = {
      x: Math.max(0, Math.min(1, relativeX)), // 确保在0-1范围内
      y: Math.max(0, Math.min(1, relativeY)) // 确保在0-1范围内
    }

    // 缓存结果
    cachedPosition = position
    lastViewportSize = { width: viewportWidth, height: viewportHeight }

    return position
  } else {
    // 如果找不到按钮，使用默认位置（右下角）
    console.warn('Heart button not found, using default position')
    const defaultPosition = { x: 0.9, y: 0.9 }
    cachedPosition = defaultPosition
    lastViewportSize = { width: viewportWidth, height: viewportHeight }
    return defaultPosition
  }
}

// 初始化共享canvas
const initSharedCanvas = () => {
  if (!containerRef.value || sharedCanvas) return

  const container = containerRef.value

  // 创建共享canvas
  sharedCanvas = document.createElement('canvas')
  sharedCanvas.style.position = 'absolute'
  sharedCanvas.style.top = '0'
  sharedCanvas.style.left = '0'
  sharedCanvas.style.width = '100%'
  sharedCanvas.style.height = '100%'
  sharedCanvas.style.pointerEvents = 'none'
  sharedCanvas.style.zIndex = '1000'

  container.appendChild(sharedCanvas)

  sharedConfetti = confetti.create(sharedCanvas, {
    resize: true,
    useWorker: true
  })
}

// 清理共享canvas
const cleanupSharedCanvas = () => {
  if (sharedCanvas && sharedCanvas.parentNode) {
    sharedCanvas.parentNode.removeChild(sharedCanvas)
    sharedCanvas = null
    sharedConfetti = null
  }
}

// 创建心形粒子效果（优化版本）
const createHeartEffect = (isUserClick = false) => {
  if (!containerRef.value) return

  // 性能优化：限制并发效果数量
  if (pendingEffectCount >= MAX_CONCURRENT_EFFECTS) {
    return
  }

  // 性能优化：防抖处理
  if (isCreatingEffect && !isUserClick) {
    return
  }

  isCreatingEffect = true
  pendingEffectCount++

  // 确保共享canvas已初始化
  if (!sharedCanvas || !sharedConfetti) {
    initSharedCanvas()
  }

  if (!sharedConfetti) return

  // 获取heart-button的位置（使用缓存）
  const heartButtonPos = getHeartButtonPosition()

  // 配置粒子效果 - 从heart-button位置向上浮起
  const particleCount = isUserClick
    ? Math.floor(Math.random() * 3) + 3 // 减少用户点击的粒子数量
    : Math.floor(Math.random() * 2) + 1

  // 随机选择一个心形emoji
  const randomHeartShape = heartShapes[Math.floor(Math.random() * heartShapes.length)]

  // 批量创建粒子，减少setTimeout调用
  const particles = []
  for (let i = 0; i < particleCount; i++) {
    particles.push({
      delay: i * (isUserClick ? 40 : 80), // 减少延迟间隔
      config: {
        particleCount: 1,
        angle: 90 + (Math.random() - 0.5) * 20,
        spread: isUserClick ? 25 : 15, // 减少扩散范围
        origin: {
          x: heartButtonPos.x + (Math.random() - 0.5) * 0.06,
          y: heartButtonPos.y + (Math.random() - 0.5) * 0.06
        },
        shapes: [randomHeartShape],
        scalar: (isUserClick ? 1.1 : 0.8) + Math.random() * 0.3, // 减少大小变化
        gravity: -0.3 - Math.random() * 0.1,
        drift: (Math.random() - 0.5) * 0.3,
        ticks: 120 + Math.random() * 30, // 减少生命周期
        startVelocity: (isUserClick ? 14 : 10) + Math.random() * 4,
        decay: 0.94 + Math.random() * 0.03,
        flat: true,
        disableForReducedMotion: false
      }
    })
  }

  // 使用 requestIdleCallback 优化性能
  let processedCount = 0
  const processParticles = () => {
    if (processedCount < particles.length && sharedConfetti) {
      const particle = particles[processedCount]

      // 对于用户点击，立即处理；对于自动效果，使用空闲时间处理
      if (isUserClick) {
        setTimeout(() => {
          if (sharedConfetti) {
            sharedConfetti(particle.config)
          }
        }, particle.delay)
      } else {
        // 使用 requestIdleCallback 在浏览器空闲时处理
        const scheduleParticle = () => {
          if (window.requestIdleCallback) {
            window.requestIdleCallback(() => {
              setTimeout(() => {
                if (sharedConfetti) {
                  sharedConfetti(particle.config)
                }
              }, particle.delay)
            })
          } else {
            // 降级到 setTimeout
            setTimeout(() => {
              if (sharedConfetti) {
                sharedConfetti(particle.config)
              }
            }, particle.delay)
          }
        }
        scheduleParticle()
      }

      processedCount++

      if (processedCount < particles.length) {
        requestAnimationFrame(processParticles)
      }
    }
  }

  processParticles()

  // 重置状态
  setTimeout(
    () => {
      isCreatingEffect = false
      pendingEffectCount = Math.max(0, pendingEffectCount - 1)
    },
    isUserClick ? 200 : 100
  )
}

// 开始自动飘心
const startAutoHearts = () => {
  if (autoHeartTimer) return

  autoHeartTimer = window.setInterval(() => {
    if (Math.random() < 0.8) {
      // 80% 概率，基本每次都发射
      createHeartEffect(false)
    }
  }, 2000) // 每2秒检查一次
}

// 停止自动飘心
const stopAutoHearts = () => {
  if (autoHeartTimer) {
    clearInterval(autoHeartTimer)
    autoHeartTimer = null
  }
}

// 监听点击触发
watch(
  () => props.clickTrigger,
  (newVal, oldVal) => {
    if (newVal && newVal !== oldVal) {
      createHeartEffect(true)
    }
  }
)

// 窗口大小变化处理
const handleResize = () => {
  // 清除位置缓存，强制重新计算
  cachedPosition = null
}

// 清除位置缓存（当DOM结构可能发生变化时调用）
const clearPositionCache = () => {
  cachedPosition = null
}

// 生命周期
onMounted(() => {
  // 清除位置缓存，确保重新计算按钮位置
  clearPositionCache()
  startAutoHearts()
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  stopAutoHearts()
  cleanupSharedCanvas()
  // 移除事件监听
  window.removeEventListener('resize', handleResize)
})

// 暴露方法
defineExpose({
  triggerHearts: () => createHeartEffect(true)
})
</script>

<style lang="less" scoped>
.heart-particles {
  position: relative;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: visible;
}

// 心形泡泡上浮动画 - 直线上浮
@keyframes heartBubbleFloat {
  0% {
    opacity: 1;
    transform: translateY(0) scale(0.8);
  }
  10% {
    opacity: 1;
    transform: translateY(-50px) scale(1.1);
  }
  50% {
    opacity: 1;
    transform: translateY(-250px) scale(1);
  }
  80% {
    opacity: 0.6;
    transform: translateY(-350px) scale(0.9);
  }
  100% {
    opacity: 0;
    transform: translateY(-450px) scale(0.7);
  }
}

// 心形泡泡上浮动画 - 向左飘
@keyframes heartBubbleFloatLeft {
  0% {
    opacity: 1;
    transform: translateY(0) translateX(0) scale(0.8);
  }
  10% {
    opacity: 1;
    transform: translateY(-50px) translateX(-10px) scale(1.1);
  }
  50% {
    opacity: 1;
    transform: translateY(-250px) translateX(-30px) scale(1);
  }
  80% {
    opacity: 0.6;
    transform: translateY(-350px) translateX(-45px) scale(0.9);
  }
  100% {
    opacity: 0;
    transform: translateY(-450px) translateX(-60px) scale(0.7);
  }
}

// 心形泡泡上浮动画 - 向右飘
@keyframes heartBubbleFloatRight {
  0% {
    opacity: 1;
    transform: translateY(0) translateX(0) scale(0.8);
  }
  10% {
    opacity: 1;
    transform: translateY(-50px) translateX(10px) scale(1.1);
  }
  50% {
    opacity: 1;
    transform: translateY(-250px) translateX(30px) scale(1);
  }
  80% {
    opacity: 0.6;
    transform: translateY(-350px) translateX(45px) scale(0.9);
  }
  100% {
    opacity: 0;
    transform: translateY(-450px) translateX(60px) scale(0.7);
  }
}
</style>
