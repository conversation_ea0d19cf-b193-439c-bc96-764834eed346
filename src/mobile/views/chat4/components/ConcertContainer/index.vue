<template>
  <div class="concert-container">
    <!-- 增强背景组件 -->
    <EnhancedBackground
      :background-video="backgroundVideo"
      :background-image="backgroundImage"
      :default-image="backgroundImage"
      :transition-mode="'fade'"
      class="background-container"
    />

    <!-- 返回按钮插槽 -->
    <div class="back-button-slot">
      <slot name="back-button" />
    </div>

    <!-- 金币显示插槽 -->
    <div class="coin-display-slot">
      <slot name="credit-display" />
    </div>

    <!-- 好感度系统显示插槽 -->
    <div class="stage-slot">
      <slot name="stage" />
    </div>

    <!-- 心形粒子效果插槽 -->
    <div class="heart-particles-slot">
      <slot name="heart-particles" />
    </div>

    <!-- 弹幕区域 -->
    <div class="danmaku-area">
      <DanmakuEngine
        ref="danmakuEngineRef"
        :danmaku="serverDanmakuList"
        :is-visible="true"
        :max-visible="16"
        :auto-remove="true"
        :remove-delay="4000"
        :speed="100"
        @danmaku-complete="handleDanmakuComplete"
      />
    </div>

    <!-- 底部操作按钮插槽 -->
    <div class="bottom-actions-slot">
      <slot name="bottom-actions" />
    </div>

    <!-- 底部弹幕选项 -->
    <div class="danmaku-options-area">
      <DanmakuOptions
        ref="danmakuOptionsRef"
        :options="danmakuOptions"
        @option-selected="handleOptionSelected"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useChat4Store, type Comment } from '@/store/chat4'
import EnhancedBackground from '@/mobile/views/chat2/components/ChatBackground/EnhancedBackground.vue'
import DanmakuEngine from '../DanmakuFlow/DanmakuEngine.vue'
import DanmakuOptions from '../DanmakuFlow/DanmakuOptions.vue'

interface Props {
  characterName?: string
  characterAvatar?: string
  backgroundVideo?: string
  backgroundImage?: string
  currentScene?: string
}

const props = withDefaults(defineProps<Props>(), {
  characterName: 'Character',
  characterAvatar: '',
  backgroundVideo: '',
  backgroundImage: '',
  currentScene: ''
})

const emit = defineEmits<{
  'back-click': []
  'scene-event': [eventName: string, data: any]
  'message-sent': [message: string]
  'danmaku-sent': [content: string, type: 'normal' | 'special']
}>()

// Store
const chat4Store = useChat4Store()

// 引用
const danmakuEngineRef = ref<InstanceType<typeof DanmakuEngine>>()
const danmakuOptionsRef = ref<InstanceType<typeof DanmakuOptions>>()

// 服务器弹幕数据（从Chat4 Store获取）
const serverDanmakuList = computed(() => {
  // 依赖版本号确保响应式更新
  chat4Store.danmakuState.version

  // 将Chat4 Store的弹幕数据转换为DanmakuEngine需要的格式
  return chat4Store.danmakuState.liveComments.map((comment) => ({
    id: comment.id,
    content: comment.content,
    type: (comment.type === 'streamer' || comment.type === 'self'
      ? 'special'
      : comment.type === 'system'
      ? 'system'
      : 'normal') as 'normal' | 'special' | 'system',
    avatar:
      comment.type === 'streamer' || comment.type === 'self' ? props.characterAvatar : undefined,
    timestamp: Date.now()
  }))
})

// 弹幕选项数据 - 根据Figma设计（写死的数据）
const danmakuOptions = ref([
  { id: '1', text: 'You are wonderful！', type: 'normal' as const },
  { id: '2', text: 'You are the best in my heart！', type: 'normal' as const },
  { id: '3', text: 'I will always support you！', type: 'normal' as const },
  { id: '4', text: 'No one is more dazzling than you！', type: 'normal' as const },
  { id: '5', text: 'Awesome! Lighting up for you!', type: 'normal' as const },
  { id: '6', text: 'Perfect! Shouting for you!', type: 'normal' as const },
  { id: '7', text: "You're my light! Love you to the core!", type: 'normal' as const },
  { id: '8', text: 'Help! Freaking out over you!', type: 'special' as const },
  { id: '9', text: 'On fire! Gotta give you all the hype!', type: 'normal' as const }
])

// 注意：现在使用服务器弹幕数据，不再需要本地ID生成

// 注意：现在使用服务器弹幕数据，不再需要本地addDanmaku函数

// 处理弹幕选项选择
const handleOptionSelected = (option: any) => {
  const type = option.type === 'special' ? 'special' : 'normal'

  // 添加到Chat4 Store的弹幕流中，用户自己的弹幕都标记为'self'类型
  const userComment: Comment = {
    id: `user_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
    username: 'You',
    content: option.text,
    type: 'self' // 用户自己的弹幕都是'self'类型，会显示为特殊弹幕样式
  }

  chat4Store.addLiveComment(userComment)

  // 发送弹幕事件
  emit('danmaku-sent', option.text, type)

  // 发送场景事件
  emit('scene-event', 'danmaku-sent', {
    content: option.text,
    type,
    scene: 'concert'
  })
}

// 处理弹幕完成
const handleDanmakuComplete = (danmakuId: string) => {
  console.log('Danmaku completed:', danmakuId)
}

// 生命周期
onMounted(() => {
  // 现在使用服务器弹幕数据，不需要添加模拟弹幕
  console.log('ConcertContainer mounted, using server danmaku data')
})

// 暴露方法给父组件
defineExpose({
  clearDanmaku: () => {
    // 使用Chat4 Store的方法清空弹幕数据
    chat4Store.clearLiveComments()
    danmakuEngineRef.value?.clearAll()
  },
  getDanmakuCount: () => chat4Store.danmakuState.liveComments.length
})
</script>

<style scoped>
.concert-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.background-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

/* 插槽定位 */
.back-button-slot {
  position: absolute;
  top: 25px;
  left: 16px;
  z-index: 20;
}

.coin-display-slot {
  position: absolute;
  top: 25px;
  right: 10px;
  z-index: 10;
}

.stage-slot {
  position: absolute;
  top: 60px;
  right: 10px;
  z-index: 10;
}

.heart-particles-slot {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 15; /* 在弹幕之上，但在UI元素之下 */
}

/* 弹幕区域 */
.danmaku-area {
  position: absolute;
  left: 0;
  right: 0;
  top: 200px;
  bottom: 80px;
  z-index: 5;
  pointer-events: none;
}

/* 底部操作按钮区域 */
.bottom-actions-slot {
  position: absolute;
  bottom: 72px;
  right: 16px;
  z-index: 15;
}

/* 底部弹幕选项区域 */
.danmaku-options-area {
  position: absolute;
  bottom: 16px;
  left: 0;
  right: 0;
  z-index: 10;
}
</style>
