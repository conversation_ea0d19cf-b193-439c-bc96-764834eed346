<template>
  <div class="timer-container">
    <!-- 沙漏图标容器 -->
    <div class="hourglass-container" v-if="isCountdown">
      <div class="hourglass-background">
        <HourglassIcon class="hourglass-icon" />
      </div>
    </div>

    <!-- 直播指示器 -->
    <div class="live-indicator" v-else-if="isLive">
      <div class="live-dot"></div>
    </div>

    <!-- 时间显示 -->
    <div class="time-display" :class="{ 'is-warning': isWarning }">
      <span class="time-text">{{ displayTime }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted } from 'vue'
import HourglassIcon from '@/assets/icon/hourglass-icon.svg'

interface Props {
  time?: string
  isLive?: boolean
  isCountdown?: boolean
  maxDuration?: number // 最大停留时间（秒），默认7分30秒
}

const props = withDefaults(defineProps<Props>(), {
  time: '',
  isLive: false,
  isCountdown: true,
  maxDuration: 450 // 7分30秒 = 450秒
})

const emit = defineEmits<{
  'time-up': []
}>()

// 倒计时状态
const remainingTime = ref(props.maxDuration)
let timer: ReturnType<typeof setInterval> | null = null

// 计算显示时间
const displayTime = computed(() => {
  if (props.isCountdown) {
    const minutes = Math.floor(remainingTime.value / 60)
    const seconds = remainingTime.value % 60
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }
  return props.time
})

// 是否进入警告状态（最后1分钟）
const isWarning = computed(() => {
  return props.isCountdown && remainingTime.value <= 60
})

// 开始倒计时
const startCountdown = () => {
  if (timer) return

  timer = setInterval(() => {
    remainingTime.value--

    if (remainingTime.value <= 0) {
      stopCountdown()
      emit('time-up')
    }
  }, 1000)
}

// 停止倒计时
const stopCountdown = () => {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
}

// 重置倒计时
const resetCountdown = () => {
  stopCountdown()
  remainingTime.value = props.maxDuration
}

onMounted(() => {
  if (props.isCountdown) {
    startCountdown()
  }
})

onUnmounted(() => {
  stopCountdown()
})

// 暴露方法给父组件
defineExpose({
  startCountdown,
  stopCountdown,
  resetCountdown
})
</script>

<style lang="less" scoped>
.timer-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  width: 50px;
}

.hourglass-container {
  position: relative;
  width: 42px;
  height: 42px;
}

.hourglass-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 42px;
  height: 42px;
  border-radius: 50%;
  background: #1f0038;
  border: 1px solid #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;

  /* 添加渐变背景以模拟设计稿效果 */
  background: linear-gradient(135deg, #1f0038 0%, #2a0a4a 50%, #1f0038 100%);
  box-shadow:
    inset 0 2px 4px rgba(255, 255, 255, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.3);
}

.hourglass-icon {
  width: 16px;
  height: 18.67px;
  fill: #daff96;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3)) drop-shadow(0 0 4px rgba(218, 255, 150, 0.5));

  /* 添加内发光效果 */
  opacity: 0.95;
}

.live-indicator {
  display: flex;
  align-items: center;
  justify-content: center;

  .live-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #ff3c3c;
    animation: pulse 2s infinite;
  }
}

.time-display {
  background: rgba(0, 0, 0, 0.5);
  border-radius: 100px;
  padding: 4px 14px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;

  &.is-warning {
    animation: warning-pulse 1s infinite;
  }
}

.time-text {
  font-family: 'Work Sans', sans-serif;
  font-size: 11px;
  font-weight: 500;
  line-height: 1.173;
  color: #ff004d;
  text-align: center;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

@keyframes warning-pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}
</style>
