<template>
  <div class="stream-header">
    <div class="avatar-container">
      <img :src="actorAvatar || '/default-avatar.png'" :alt="actorName" class="avatar" />
    </div>
    <div class="info-container">
      <div class="top-row">
        <div class="actor-name">{{ actorName }}</div>
        <div class="live-tag">
          <div class="fire-icon">🔥</div>
          <span class="viewer-count">{{ formatViewerCount(viewerCount) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  actorName: string
  actorAvatar?: string
  viewerCount: number
  isLive: boolean
}

const props = defineProps<Props>()

// 格式化观看人数
const formatViewerCount = (count: number): string => {
  if (count >= 1000) {
    return `${Math.floor(count / 1000)},${String(count % 1000).padStart(3, '0')}`
  }
  return count.toString()
}
</script>

<style lang="less" scoped>
.stream-header {
  display: flex;
  align-items: center;
  gap: 9px;
}

.avatar-container {
  .avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    object-fit: cover;
    border: 1.67px solid #fffcde;
  }
}

.info-container {
  display: flex;
  flex-direction: column;
}

.top-row {
  display: flex;
  align-items: center;
  gap: 8px;
}

.actor-name {
  font-family: 'Work Sans', sans-serif;
  font-size: 13px;
  font-weight: 600;
  line-height: 1.17;
  color: white;
  text-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
}

.live-tag {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 0;

  .fire-icon {
    width: 10px;
    height: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .viewer-count {
    font-family: 'Work Sans', sans-serif;
    font-size: 11px;
    font-weight: 500;
    line-height: 1.17;
    color: white;
    text-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
  }
}
</style>
