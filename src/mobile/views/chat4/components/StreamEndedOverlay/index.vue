<template>
  <div class="stream-ended-overlay" v-if="visible">
    <!-- 背景图片 -->
    <div class="background-image" :style="{ backgroundImage: `url(${backgroundImage})` }" />

    <!-- 模糊遮罩层 -->
    <div class="blur-overlay" />

    <!-- 返回按钮 -->
    <button class="back-button" @click="$emit('back-click')">
      <span class="back-icon"><icon-left /></span>
    </button>

    <!-- 主要内容 -->
    <div class="content-container">
      <!-- 主播头像 -->
      <div class="avatar-container">
        <img :src="actorAvatar" :alt="actorName" class="avatar" />
      </div>

      <!-- 主播名字 -->
      <h2 class="actor-name">{{ actorName }}</h2>

      <!-- 结束提示文本 -->
      <p class="ended-text">The stream has ended.</p>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <!-- <button class="explore-button" @click="$emit('explore-others')">Explore Others</button> -->
        <button class="chat-button" @click="$emit('start-chatting')">Start Chatting</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  visible: boolean
  actorName: string
  actorAvatar: string
  backgroundImage: string
}

defineProps<Props>()

defineEmits<{
  'back-click': []
  'explore-others': []
  'start-chatting': []
}>()
</script>

<style lang="less" scoped>
.stream-ended-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  z-index: 1;
}

.blur-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(31, 0, 56, 0.2);
  backdrop-filter: blur(30px);
  z-index: 2;
}

.back-button {
  position: absolute;
  top: 20px;
  left: 20px;
  width: 36px;
  height: 36px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
  }

  &:active {
    transform: scale(0.95);
  }

  .back-icon {
    font-family: 'SF Pro', sans-serif;
    font-size: 24px;
    color: #ffffff;
    line-height: 1;
  }
}

.content-container {
  position: absolute;
  top: 45%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 292px;
  height: 254px;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 10;
}

.avatar-container {
  width: 90px;
  height: 90px;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 16px;

  .avatar {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.actor-name {
  font-family: 'Work Sans', sans-serif;
  font-weight: 600;
  font-size: 20px;
  line-height: 1.17;
  color: #ffffff;
  text-align: center;
  margin: 0 0 30px 0;
  text-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
}

.ended-text {
  font-family: 'Work Sans', sans-serif;
  font-weight: 600;
  font-size: 20px;
  line-height: 1.17;
  color: #ffffff;
  text-align: center;
  margin: 0 0 30px 0;
  text-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
}

.action-buttons {
  display: flex;
  gap: 20px;
  width: 100%;
  justify-content: center;
}

.explore-button,
.chat-button {
  width: 136px;
  height: 42px;
  border: 2px solid #1f0038;
  border-bottom: 6px solid #1f0038;
  border-radius: 26px;
  font-family: 'Work Sans', sans-serif;
  font-weight: 600;
  font-size: 15px;
  line-height: 1.17;
  color: #241d49;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(1px);
    border-bottom: 2px solid #1f0038;
  }
}

.explore-button {
  background: linear-gradient(180deg, #d6cafe 0%, #ca93f2 100%);
  box-shadow: 0px 2px 12px rgba(176, 152, 255, 1);
}

.chat-button {
  background: linear-gradient(180deg, #f5ffe2 0%, #daff96 100%);
  box-shadow: 0px 1.86px 11.13px rgba(218, 255, 150, 1);
}
</style>
