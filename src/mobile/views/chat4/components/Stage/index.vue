<template>
  <div class="stage-container" @click="handleClick">
    <!-- Stage 图标 -->
    <div class="stage-icon">
      <StageIcon />
    </div>

    <!-- Stage 状态 -->
    <div class="stage-status">
      <div class="stage-badge">
        <div class="current-stage-info">
          <span class="stage-text">{{ currentStage }}</span>
          <span class="level-text-badge">{{ currentLevelText }}</span>
        </div>
        <ArrowIcon class="arrow-icon" />
        <div class="next-stage-info">
          <span class="next-stage">{{ nextStage }}</span>
          <span class="next-level-text-badge">{{ nextLevelText }}</span>
        </div>
      </div>

      <!-- 进度条 -->
      <div class="stage-progress">
        <div class="progress-bg"></div>
        <div class="progress-fill" :style="{ width: `${progressPercentage}%` }"></div>
        <div class="progress-indicator">{{ progressText }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import StageIcon from '@/assets/icon/stage-icon.svg'
import ArrowIcon from '@/assets/icon/arrow-icon.svg'
import type { FavorabilityState } from '@/types/favorability'

interface Props {
  favorabilityState: FavorabilityState
}

interface Emits {
  (e: 'click'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 计算当前阶段
const currentStage = computed(() => {
  // 根据当前等级显示阶段名称 (从level0开始)
  const level = props.favorabilityState.currentLevel
  switch (level) {
    case 'level0':
      return 'New'
    case 'level1':
      return 'Friend'
    case 'level2':
      return 'Close'
    default:
      return 'New'
  }
})

// 计算下一阶段
const nextStage = computed(() => {
  const nextLevel = props.favorabilityState.nextLevel
  if (!nextLevel) return 'Max'

  switch (nextLevel) {
    case 'level1':
      return 'Friend'
    case 'level2':
      return 'Close'
    default:
      return 'Max'
  }
})

// 计算进度百分比
const progressPercentage = computed(() => {
  const { currentHeartValue, nextLevelHeartValue } = props.favorabilityState
  if (!nextLevelHeartValue) return 100

  return Math.min((currentHeartValue / nextLevelHeartValue) * 100, 100)
})

// 计算当前等级文本
const currentLevelText = computed(() => {
  const levelNumber = props.favorabilityState.currentLevel.replace('level', '')
  return `Lv${levelNumber}`
})

// 计算下一等级文本
const nextLevelText = computed(() => {
  const nextLevel = props.favorabilityState.nextLevel
  if (!nextLevel) return 'Max'

  const levelNumber = nextLevel.replace('level', '')
  return `Lv${levelNumber}`
})

// 计算进度文本
const progressText = computed(() => {
  const { currentHeartValue, nextLevelHeartValue } = props.favorabilityState
  if (!nextLevelHeartValue) return 'Max'

  return `${currentHeartValue}/${nextLevelHeartValue}`
})

// 点击事件处理
const handleClick = () => {
  emit('click')
}
</script>

<style lang="less" scoped>
.stage-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  width: 82px;
  cursor: pointer;
  transition: transform 0.2s ease;

  &:hover {
    transform: scale(1.05);
  }

  &:active {
    transform: scale(0.95);
  }
}

.stage-icon {
  width: 82px;
  height: 82px;
  display: flex;
  align-items: center;
  justify-content: center;

  svg {
    width: 100%;
    height: 100%;
  }
}

.stage-status {
  display: flex;
  flex-direction: column;
  gap: 2px;
  width: 100%;
}

.stage-badge {
  display: flex;
  align-items: center;
  gap: 2px;
  padding: 3px 4px;
  background: linear-gradient(180deg, #b565f0 0%, #e7c7ff 100%);
  border: 1px solid;
  border-image: linear-gradient(180deg, #d7a2fe 0%, #c473ff 100%) 1;
  border-radius: 4px;
  box-shadow: inset 0px 2px 0px 0px rgba(255, 255, 255, 0.15);
  width: 74px;
  height: auto;
  justify-content: center;

  .current-stage-info,
  .next-stage-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1px;
  }

  .stage-text,
  .next-stage {
    font-family: 'Work Sans', sans-serif;
    font-weight: 600;
    font-size: 9px;
    line-height: 1.17;
    text-align: center;
  }

  .stage-text {
    color: #ffffff;
  }

  .next-stage {
    color: #f8d5ff;
  }

  .level-text-badge,
  .next-level-text-badge {
    font-family: 'Work Sans', sans-serif;
    font-weight: 500;
    font-size: 7px;
    line-height: 1.17;
    text-align: center;
  }

  .level-text-badge {
    color: #ffffff;
  }

  .next-level-text-badge {
    color: #f8d5ff;
  }

  .arrow-icon {
    flex-shrink: 0;
    margin: 0 1px;
  }
}

.stage-progress {
  position: relative;
  width: 73px;
  height: 8px;
  margin-top: 2px;

  .progress-bg {
    position: absolute;
    top: 2px;
    left: 0;
    width: 100%;
    height: 8px;
    background: linear-gradient(180deg, #b565f0 0%, #e7c7ff 100%);
    border: 1px solid;
    border-image: linear-gradient(180deg, #d7a2fe 0%, #bc66fa 100%) 1;
    border-radius: 4px;
  }

  .progress-fill {
    position: absolute;
    top: 2px;
    left: 0;
    height: 8px;
    background: linear-gradient(180deg, #ffffff 0%, #ffe6eb 69.46%);
    border-radius: 4px;
    transition: width 0.3s ease;
  }

  .progress-indicator {
    position: absolute;
    top: 0;
    right: 0;
    font-family: 'Work Sans', sans-serif;
    font-weight: 600;
    font-size: 8px;
    line-height: 1.17;
    color: #ffffff;
    -webkit-text-stroke: 0.5px #8250a6;
    text-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
  }
}
</style>
