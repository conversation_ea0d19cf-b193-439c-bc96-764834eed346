<template>
  <div class="video-call-input-overlay" @click="handleOverlayClick">
    <div class="input-container" @click.stop>
      <div class="input-wrapper">
        <input
          ref="inputRef"
          v-model="message"
          type="text"
          :placeholder="placeholder"
          class="message-input"
          @keyup.enter="handleSend"
          @blur="handleInputBlur"
          @input="handleTyping"
        />
        <button
          class="send-button"
          :class="{ disabled: !message.trim() }"
          @click="handleSend"
          :disabled="!message.trim()"
        >
          <SendIcon />
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, onMounted } from 'vue'
import SendIcon from '@/assets/icon/send-icon.svg'

interface Props {
  placeholder?: string
}

interface Emits {
  (e: 'send-message', message: string): void
  (e: 'close'): void
  (e: 'typing'): void
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: 'Chat here'
})

const emit = defineEmits<Emits>()

// 状态
const message = ref('')
const inputRef = ref<HTMLInputElement>()

// 处理输入事件
const handleTyping = () => {
  emit('typing')
}

// 处理发送消息
const handleSend = () => {
  const trimmedMessage = message.value.trim()
  if (trimmedMessage) {
    emit('send-message', trimmedMessage)
    message.value = ''
  }
}

// 处理覆盖层点击（关闭输入框）
const handleOverlayClick = () => {
  emit('close')
}

// 处理输入框失焦
const handleInputBlur = () => {
  // 延迟关闭，避免点击发送按钮时输入框先关闭
  setTimeout(() => {
    if (!message.value.trim()) {
      emit('close')
    }
  }, 100)
}

// 组件挂载后自动聚焦
onMounted(async () => {
  await nextTick()
  inputRef.value?.focus()
})
</script>

<style lang="less" scoped>
.video-call-input-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  padding-bottom: env(safe-area-inset-bottom, 0);
}

.input-container {
  width: 100%;
  background: #f6f6f6;
  padding: 8px 15px;
  animation: slideUp 0.3s ease-out;
}

.input-wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
}

.message-input {
  flex: 1;
  background: #4c3c59;
  border: none;
  border-radius: 20px;
  padding: 10px 15px;
  color: white;
  font-family: 'Work Sans', sans-serif;
  font-weight: 400;
  font-size: 12px;
  outline: none;
  box-shadow: 0px 0px 10px 0px rgba(218, 255, 150, 0.15);

  &::placeholder {
    color: rgba(255, 255, 255, 0.7);
  }

  &:focus {
    box-shadow: 0px 0px 15px 0px rgba(218, 255, 150, 0.25);
  }
}

.send-button {
  width: 36px;
  height: 36px;
  background: #ca93f2;
  border: none;
  border-radius: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;

  &:not(.disabled):hover {
    background: #b87ee8;
    transform: scale(1.05);
  }

  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  svg {
    width: 18px;
    height: 18px;
    color: white;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

/* 适配安全区域 */
@supports (padding-bottom: env(safe-area-inset-bottom)) {
  .input-container {
    padding-bottom: calc(8px + env(safe-area-inset-bottom));
  }
}
</style>
