<template>
  <div class="payment-confirm-modal" v-if="visible" @click="handleBackdropClick">
    <div class="modal-content" @click.stop>
      <!-- 标题 -->
      <div class="modal-header">
        <h2 class="modal-title">Confirm Payment</h2>
      </div>

      <!-- 内容 -->
      <div class="modal-body">
        <div class="payment-info">
          <div class="diamond-display">
            <div class="diamond-icon">
              <img src="https://cdn.magiclight.ai/assets/mobile/diamond.png" alt="Diamond" />
            </div>
            <span class="diamond-amount">{{ coins }}</span>
          </div>
          <p class="payment-description">
            Confirm payment of <strong>{{ coins }} diamonds</strong> to unlock scene?
          </p>
        </div>
      </div>

      <!-- 按钮 -->
      <div class="modal-footer">
        <button class="cancel-button" @click="handleCancel" :disabled="loading">Cancel</button>
        <button class="confirm-button" @click="handleConfirm" :disabled="loading">
          <span v-if="!loading">Confirm</span>
          <div v-else class="loading-spinner"></div>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface Props {
  visible: boolean
  coins: number
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'confirm'): void
  (e: 'cancel'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const loading = ref(false)

const handleConfirm = () => {
  if (loading.value) return
  loading.value = true
  emit('confirm')
}

const handleCancel = () => {
  if (loading.value) return
  emit('cancel')
  emit('update:visible', false)
}

const handleBackdropClick = () => {
  if (loading.value) return
  handleCancel()
}

// 重置加载状态的方法，供父组件调用
const resetLoading = () => {
  loading.value = false
}

defineExpose({
  resetLoading
})
</script>

<style lang="less" scoped>
.payment-confirm-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
  background: rgba(0, 0, 0, 0.6);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.modal-content {
  width: 100%;
  max-width: 320px;
  background: #ffffff;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.modal-header {
  padding: 24px 20px 16px 20px;
  text-align: center;

  .modal-title {
    font-family: 'Work Sans', sans-serif;
    font-weight: 600;
    font-size: 20px;
    line-height: 1.2;
    color: #1f0038;
    margin: 0;
  }
}

.modal-body {
  padding: 0 20px 24px 20px;

  .payment-info {
    text-align: center;

    .diamond-display {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      margin-bottom: 16px;

      .diamond-icon {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;

        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }

      .diamond-amount {
        font-family: 'Work Sans', sans-serif;
        font-weight: 600;
        font-size: 24px;
        line-height: 1.2;
        color: #8e2ed4;
      }
    }

    .payment-description {
      font-family: 'Work Sans', sans-serif;
      font-weight: 400;
      font-size: 16px;
      line-height: 1.4;
      color: #333333;
      margin: 0;

      strong {
        font-weight: 600;
        color: #8e2ed4;
      }
    }
  }
}

.modal-footer {
  display: flex;
  gap: 12px;
  padding: 0 20px 24px 20px;

  .cancel-button,
  .confirm-button {
    flex: 1;
    height: 44px;
    border: none;
    border-radius: 22px;
    font-family: 'Work Sans', sans-serif;
    font-weight: 600;
    font-size: 16px;
    line-height: 1.2;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }

    &:hover:not(:disabled) {
      transform: translateY(-1px);
    }

    &:active:not(:disabled) {
      transform: translateY(0);
    }
  }

  .cancel-button {
    background: #f5f5f5;
    color: #666666;

    &:hover:not(:disabled) {
      background: #eeeeee;
    }
  }

  .confirm-button {
    background: #ca93f2;
    color: #1f0038;

    &:hover:not(:disabled) {
      background: #b088e8;
    }

    .loading-spinner {
      width: 20px;
      height: 20px;
      border: 2px solid rgba(31, 0, 56, 0.3);
      border-top: 2px solid #1f0038;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
