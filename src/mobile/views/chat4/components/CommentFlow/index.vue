<template>
  <div class="comment-flow" v-if="isVisible">
    <TransitionGroup name="comment" tag="div" class="comments-container">
      <CommentBubble
        v-for="comment in visibleComments"
        :key="comment.id"
        :comment="comment"
        @animation-complete="handleCommentComplete"
      />
    </TransitionGroup>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import CommentBubble from './CommentBubble.vue'

interface Comment {
  id: string
  username: string
  content: string
  type: 'normal' | 'highlight' | 'system' | 'streamer' | 'self' | 'join'
}

interface Props {
  comments: Comment[]
  isVisible: boolean
  maxVisible?: number
}

const props = withDefaults(defineProps<Props>(), {
  maxVisible: 5
})

// 显示的评论列表
const visibleComments = computed(() => {
  return props.comments.slice(-props.maxVisible)
})

// 处理评论动画完成
const handleCommentComplete = (commentId: string) => {
  // 可以在这里处理评论消失的逻辑
  console.log('Comment animation complete:', commentId)
}

// 监听新评论，触发动画
// watch(
//   () => props.comments.length,
//   (newLength, oldLength) => {
//     if (newLength > oldLength) {
//       // 有新评论时的处理逻辑
//       console.log('New comment added')
//     }
//   }
// )
</script>

<style lang="less" scoped>
.comment-flow {
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
}

.comments-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  height: 100%;
  justify-content: flex-end;
}

// 评论进入/离开动画
.comment-enter-active {
  transition: all 0.3s ease-out;
}

.comment-leave-active {
  transition: all 0.3s ease-in;
}

.comment-enter-from {
  opacity: 0;
  transform: translateX(-20px);
}

.comment-leave-to {
  opacity: 0;
  transform: translateX(20px);
}

.comment-move {
  transition: transform 0.3s ease;
}
</style>
