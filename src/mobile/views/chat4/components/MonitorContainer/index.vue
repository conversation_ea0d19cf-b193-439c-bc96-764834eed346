<template>
  <div class="monitor-container">
    <!-- 白色背景 -->
    <div class="white-background">
      <!-- 顶部区域 -->
      <div class="header-area">
        <!-- 返回按钮 -->
        <!-- <button class="back-button" @click="$emit('go-to-living-room')">
          <span class="back-icon">􀆉</span>
        </button> -->

        <!-- Monitor 标题 -->
        <div class="monitor-title">Monitor</div>
      </div>

      <!-- 监控屏幕 -->
      <div class="monitor-screen" @click="handleScreenClick">
        <!-- 背景图片/视频 -->
        <EnhancedChatContainer
          :is-playing-video="false"
          :background-video="backgroundVideo"
          :background-image="backgroundImage"
          :default-image="backgroundImage"
          class="screen-background"
        />

        <!-- 暗色遮罩 -->
        <div class="screen-overlay"></div>

        <!-- 右下角监控时长 -->
        <div class="monitor-duration">
          <span class="duration-text">{{ formattedDuration }}</span>
        </div>

        <!-- 点击提示 -->
        <div class="click-hint">
          <span class="hint-text">Tap to zoom</span>
        </div>
      </div>

      <!-- Live Monitoring 标签 -->
      <div class="live-monitoring">
        <div class="video-icon">
          <component :is="MonitorVideoIcon" />
        </div>
        <span class="live-text">Live Monitoring</span>
      </div>

      <!-- 底部信息区域 -->
      <div class="bottom-section">
        <!-- 控制按钮 -->
        <div class="control-buttons">
          <!-- Chat Room 按钮 -->
          <button class="control-button chat-room-btn" @click="handleGoToLivingRoom">
            <div class="button-content">
              <div class="button-icon">
                <MonitorHomeIcon />
              </div>
              <span class="button-text">Chat Room</span>
            </div>
          </button>

          <!-- 日期时间按钮 -->
          <button class="control-button datetime-btn">
            <div class="button-content">
              <div class="button-icon">
                <MonitorCalendarIcon />
              </div>
              <span class="button-text">{{ currentDate }}</span>
            </div>
          </button>
        </div>

        <!-- 角色消息 -->
        <div
          v-if="displayMessage"
          class="character-message"
          v-html="displayMessage.replace(/\n/g, '<br>')"
        ></div>
      </div>
    </div>

    <!-- 监控弹窗 -->
    <div v-if="showZoomModal" class="monitor-modal" @click="handleModalClick">
      <div class="monitor-modal-content" @click.stop>
        <!-- 监控头部 -->
        <div class="monitor-header">
          <div class="monitor-info">
            <div class="camera-id">CAM-01</div>
            <div class="location">{{ props.characterName }}'s Room</div>
          </div>
          <div class="monitor-status">
            <div class="recording-indicator">
              <div class="rec-dot"></div>
              <span>REC</span>
            </div>
            <div class="timestamp">{{ currentDate }}</div>
          </div>
          <button class="monitor-close" @click="closeZoomModal">
            <span>×</span>
          </button>
        </div>

        <!-- 监控画面区域 -->
        <div class="monitor-viewport">
          <!-- 视频容器 -->
          <div class="video-container">
            <EnhancedChatContainer
              :is-playing-video="false"
              :background-video="backgroundVideo"
              :background-image="backgroundImage"
              :default-image="backgroundImage"
              class="monitor-video"
              :style="{
                '--zoom-level': zoomLevel,
                '--pan-x': panX + 'px',
                '--pan-y': panY + 'px'
              }"
            />
          </div>

          <!-- 监控叠加信息 -->
          <div class="monitor-overlay">
            <!-- 左上角信息 -->
            <div class="overlay-top-left">
              <div class="live-badge">
                <div class="live-dot"></div>
                <span>LIVE</span>
              </div>
              <div class="zoom-info">ZOOM: {{ Math.round(zoomLevel * 100) }}%</div>
            </div>

            <!-- 右上角信息 -->
            <div class="overlay-top-right">
              <div class="duration">{{ formattedDuration }}</div>
            </div>

            <!-- 底部信息 -->
            <!-- <div class="overlay-bottom">
              <div class="message" v-html="displayMessage.replace(/\n/g, '<br>')"></div>
            </div> -->
          </div>
        </div>

        <!-- 控制面板 -->
        <div class="monitor-controls">
          <!-- 左侧：缩放控制 -->
          <div class="control-section">
            <div class="section-title">ZOOM</div>
            <div class="zoom-buttons">
              <button
                class="monitor-btn"
                :disabled="buttonStates.zoomOutDisabled"
                @mousedown="startPress(zoomOut)"
                @mouseup="stopPress"
                @mouseleave="stopPress"
                @touchstart="startPress(zoomOut)"
                @touchend="stopPress"
                @touchcancel="stopPress"
              >
                −
              </button>
              <button
                class="monitor-btn"
                :disabled="buttonStates.zoomInDisabled"
                @mousedown="startPress(zoomIn)"
                @mouseup="stopPress"
                @mouseleave="stopPress"
                @touchstart="startPress(zoomIn)"
                @touchend="stopPress"
                @touchcancel="stopPress"
              >
                +
              </button>
            </div>
          </div>

          <!-- 中间：方向控制 -->
          <div class="control-section">
            <div class="section-title">PAN/TILT</div>
            <div class="direction-pad">
              <button
                class="dir-btn up"
                :disabled="buttonStates.panUpDisabled"
                @mousedown="startPress(panUp)"
                @mouseup="stopPress"
                @mouseleave="stopPress"
                @touchstart="startPress(panUp)"
                @touchend="stopPress"
                @touchcancel="stopPress"
              >
                ▲
              </button>
              <div class="middle-row">
                <button
                  class="dir-btn left"
                  :disabled="buttonStates.panLeftDisabled"
                  @mousedown="startPress(panLeft)"
                  @mouseup="stopPress"
                  @mouseleave="stopPress"
                  @touchstart="startPress(panLeft)"
                  @touchend="stopPress"
                  @touchcancel="stopPress"
                >
                  ◀
                </button>
                <button class="dir-btn center" @click="resetPan">●</button>
                <button
                  class="dir-btn right"
                  :disabled="buttonStates.panRightDisabled"
                  @mousedown="startPress(panRight)"
                  @mouseup="stopPress"
                  @mouseleave="stopPress"
                  @touchstart="startPress(panRight)"
                  @touchend="stopPress"
                  @touchcancel="stopPress"
                >
                  ▶
                </button>
              </div>
              <button
                class="dir-btn down"
                :disabled="buttonStates.panDownDisabled"
                @mousedown="startPress(panDown)"
                @mouseup="stopPress"
                @mouseleave="stopPress"
                @touchstart="startPress(panDown)"
                @touchend="stopPress"
                @touchcancel="stopPress"
              >
                ▼
              </button>
            </div>
          </div>

          <!-- 右侧：功能按钮 -->
          <div class="control-section">
            <div class="section-title">CONTROL</div>
            <div class="function-buttons">
              <button
                class="monitor-btn reset"
                :disabled="buttonStates.resetDisabled"
                @click="resetView"
              >
                RESET
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue'
import { useChatMessagesStore } from '@/store/chat-messages'
import { useChatResourcesStore } from '@/store/chat-resources'
import EnhancedChatContainer from '@/mobile/components/EnhancedChatContainer.vue'
import MonitorVideoIcon from '@/assets/icon/monitor-video-icon.svg'
import MonitorHomeIcon from '@/assets/icon/monitor-home-icon.svg'
import MonitorCalendarIcon from '@/assets/icon/monitor-calendar-icon.svg'

interface Props {
  characterName?: string
  characterAvatar?: string
  backgroundImage?: string
  backgroundVideo?: string
}

interface Emits {
  (e: 'go-to-living-room'): void
}

const props = withDefaults(defineProps<Props>(), {
  characterName: 'Tsunade',
  characterAvatar: '',
  backgroundImage: '',
  backgroundVideo: ''
})

const emit = defineEmits<Emits>()

// Store
const chatMessagesStore = useChatMessagesStore()
const chatResourcesStore = useChatResourcesStore()

// 状态
const currentTime = ref('')
const callStartTime = ref(Date.now())
const currentDateTime = ref(new Date())
const currentTimestamp = ref(Date.now())
// 计算显示消息：优先使用 videoDescription，否则使用聊天消息
const displayMessage = computed(() => {
  // 优先使用 videoDescription
  if (chatResourcesStore.videoDescription) {
    return chatResourcesStore.videoDescription
  }

  // 如果正在场景切换，保持当前消息
  if (isTransitioning.value) {
    return currentDisplayMessage.value
  }

  // 获取最新的角色消息
  const messages = chatMessagesStore.messages
  if (!messages || messages.length === 0) {
    return ''
  }

  // 从后往前找最新的 system 消息
  for (let i = messages.length - 1; i >= 0; i--) {
    const message = messages[i]
    if (message.sender_type === 'system' && message.content?.text) {
      return message.content.text
    }
  }

  return ''
})

// 用于存储当前显示的消息（在场景切换时保持不变）
const currentDisplayMessage = ref('')
const isTransitioning = ref(false)
const showZoomModal = ref(false)

// 缩放和平移控制
const zoomLevel = ref(1)
const panX = ref(0)
const panY = ref(0)
const panStep = 20 // 每次平移的像素数

// 长按控制
const pressTimer = ref<number | null>(null)
const isPressing = ref(false)
const pressInterval = 100 // 长按时的执行间隔（毫秒）

// 计算平移边界，防止出现黑边
const getPanLimits = () => {
  if (zoomLevel.value <= 1) {
    return { maxX: 0, maxY: 0 }
  }

  // 动态获取监控视口的实际尺寸
  const monitorViewport = document.querySelector('.monitor-viewport')
  let viewportWidth = 400 // 默认值
  let viewportHeight = 225 // 默认值（16:9）

  if (monitorViewport) {
    const rect = monitorViewport.getBoundingClientRect()
    viewportWidth = rect.width
    viewportHeight = rect.height
  }

  const scale = zoomLevel.value

  // 现在 transform 顺序是 translate() scale()，所以计算更直接
  // 缩放后，内容会超出视口，可平移的最大距离就是超出的部分
  // 超出距离 = (缩放后尺寸 - 视口尺寸) / 2
  const scaledWidth = viewportWidth * scale
  const scaledHeight = viewportHeight * scale

  const maxX = (scaledWidth - viewportWidth) / 2
  const maxY = (scaledHeight - viewportHeight) / 2

  return {
    maxX: Math.max(0, maxX),
    maxY: Math.max(0, maxY)
  }
}

// 计算按钮禁用状态
const buttonStates = computed(() => {
  const { maxX, maxY } = getPanLimits()

  return {
    // 缩放按钮状态
    zoomInDisabled: zoomLevel.value >= 3,
    zoomOutDisabled: zoomLevel.value <= 1,

    // 方向按钮状态
    panUpDisabled: zoomLevel.value <= 1 || panY.value >= maxY,
    panDownDisabled: zoomLevel.value <= 1 || panY.value <= -maxY,
    panLeftDisabled: zoomLevel.value <= 1 || panX.value >= maxX,
    panRightDisabled: zoomLevel.value <= 1 || panX.value <= -maxX,

    // 重置按钮状态
    resetDisabled: zoomLevel.value <= 1 && panX.value === 0 && panY.value === 0
  }
})

// 计算属性
const currentDate = computed(() => {
  const now = currentDateTime.value
  const month = now.toLocaleDateString('en-US', { month: 'long' })
  const day = now.getDate()
  const hours = now.getHours().toString().padStart(2, '0')
  const minutes = now.getMinutes().toString().padStart(2, '0')
  const seconds = now.getSeconds().toString().padStart(2, '0')
  return `${month} ${day} / ${hours}:${minutes}:${seconds}`
})

const formattedDuration = computed(() => {
  const duration = Math.floor((currentTimestamp.value - callStartTime.value) / 1000)
  const minutes = Math.floor(duration / 60)
  const seconds = duration % 60
  return `${minutes}:${seconds.toString().padStart(2, '0')}`
})

// 监听 displayMessage 变化，更新 currentDisplayMessage
watch(
  displayMessage,
  (newMessage) => {
    if (!isTransitioning.value) {
      currentDisplayMessage.value = newMessage
    }
  },
  { immediate: true }
)

// 处理场景切换
const handleSceneTransition = () => {
  isTransitioning.value = true
  // 保持当前消息显示，避免闪现
}

// 处理返回客厅按钮点击
const handleGoToLivingRoom = () => {
  handleSceneTransition()
  emit('go-to-living-room')
}

// 处理监控屏幕点击
const handleScreenClick = () => {
  showZoomModal.value = true
}

// 处理弹窗背景点击
const handleModalClick = () => {
  closeZoomModal()
}

// 关闭放大弹窗
const closeZoomModal = () => {
  showZoomModal.value = false
  // 关闭弹窗时重置视图
  resetView()
}

// 缩放控制方法
const zoomIn = () => {
  if (zoomLevel.value < 3) {
    zoomLevel.value = Math.min(3, zoomLevel.value + 0.25)
    // 缩放后调整平移值以保持在边界内
    constrainPanValues()
  }
}

const zoomOut = () => {
  if (zoomLevel.value > 1) {
    zoomLevel.value = Math.max(1, zoomLevel.value - 0.25)
    // 如果缩放到1，重置平移
    if (zoomLevel.value === 1) {
      panX.value = 0
      panY.value = 0
    } else {
      // 缩放后调整平移值以保持在边界内
      constrainPanValues()
    }
  }
}

// 约束平移值在有效范围内
const constrainPanValues = () => {
  const { maxX, maxY } = getPanLimits()
  panX.value = Math.max(-maxX, Math.min(maxX, panX.value))
  panY.value = Math.max(-maxY, Math.min(maxY, panY.value))
}

// 平移控制方法
const panUp = () => {
  if (zoomLevel.value > 1) {
    const { maxY } = getPanLimits()
    panY.value = Math.min(maxY, panY.value + panStep)
  }
}

const panDown = () => {
  if (zoomLevel.value > 1) {
    const { maxY } = getPanLimits()
    panY.value = Math.max(-maxY, panY.value - panStep)
  }
}

const panLeft = () => {
  if (zoomLevel.value > 1) {
    const { maxX } = getPanLimits()
    panX.value = Math.min(maxX, panX.value + panStep)
  }
}

const panRight = () => {
  if (zoomLevel.value > 1) {
    const { maxX } = getPanLimits()
    panX.value = Math.max(-maxX, panX.value - panStep)
  }
}

const resetPan = () => {
  panX.value = 0
  panY.value = 0
}

const resetView = () => {
  zoomLevel.value = 1
  panX.value = 0
  panY.value = 0
}

// 长按功能实现
const startPress = (action: () => void) => {
  if (isPressing.value) return

  isPressing.value = true
  action() // 立即执行一次

  // 延迟后开始连续执行
  setTimeout(() => {
    if (isPressing.value) {
      pressTimer.value = setInterval(() => {
        if (isPressing.value) {
          action()
        }
      }, pressInterval) as unknown as number
    }
  }, 300) // 300ms 后开始连续执行
}

const stopPress = () => {
  isPressing.value = false
  if (pressTimer.value) {
    clearInterval(pressTimer.value)
    pressTimer.value = null
  }
}

// 更新当前时间
const updateCurrentTime = () => {
  const now = new Date()
  const hours = now.getHours().toString().padStart(2, '0')
  const minutes = now.getMinutes().toString().padStart(2, '0')
  currentTime.value = `${hours}:${minutes}`

  // 同时更新日期时间和时间戳
  currentDateTime.value = now
  currentTimestamp.value = now.getTime()
}

// 定时器
let timeTimer: ReturnType<typeof setInterval> | null = null

onMounted(() => {
  // 立即更新时间
  updateCurrentTime()

  // 每秒更新一次时间
  timeTimer = setInterval(updateCurrentTime, 1000)
})

onBeforeUnmount(() => {
  if (timeTimer) {
    clearInterval(timeTimer)
  }
  // 清理长按定时器
  stopPress()
})
</script>

<style lang="less" scoped>
.monitor-container {
  position: relative;
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
  overflow: hidden;
}

.white-background {
  width: 100%;
  height: 100%;
  background: #ffffff;
  position: relative;
}

/* 顶部区域 */
.header-area {
  position: relative;
  width: 100%;
  height: 50px;
}

.back-button {
  position: absolute;
  z-index: 1;
  top: 5px;
  left: 7px;
  width: 36px;
  height: 36px;
  background: rgba(255, 255, 255, 0.8);
  border: none;
  border-radius: 22.5px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  backdrop-filter: blur(20px);

  .back-icon {
    color: #4c3c59;
    font-family: 'SF Pro', sans-serif;
    font-size: 24px;
    font-weight: 400;
  }
}

.monitor-title {
  position: absolute;
  top: 15px;
  left: 50%;
  transform: translateX(-50%);
  color: #1f0038;
  font-family: 'Work Sans', sans-serif;
  font-weight: 600;
  font-size: 17px;
}

/* 监控屏幕 */
.monitor-screen {
  position: relative;
  margin: 4px auto 0;
  width: calc(100% - 30px);
  max-width: 345px;
  aspect-ratio: 345/194;
  border: 2px solid #1f0038;
  border-radius: 10px;
  box-shadow: 0px 4px 0px 0px #1f0038;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    transform: scale(1.02);
    box-shadow: 0px 6px 0px 0px #1f0038;
  }

  &:active {
    transform: scale(0.98);
    box-shadow: 0px 2px 0px 0px #1f0038;
  }

  .screen-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  .screen-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 125px;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
  }

  .monitor-duration {
    position: absolute;
    z-index: 1;
    bottom: 6px;
    right: 6px;
    display: flex;
    align-items: center;
    background: rgba(0, 0, 0, 0.5);
    padding: 4px 6px;
    border-radius: 10px;

    .duration-text {
      color: #ffffff;
      font-family: 'Work Sans', sans-serif;
      font-weight: 500;
      font-size: 10px;
    }
  }

  .click-hint {
    position: absolute;
    z-index: 1;
    bottom: 6px;
    left: 6px;
    display: flex;
    align-items: center;
    background: rgba(31, 0, 56, 0.8);
    padding: 4px 8px;
    border-radius: 10px;
    animation: fadeInOut 3s ease-in-out infinite;

    .hint-text {
      color: #ffffff;
      font-family: 'Work Sans', sans-serif;
      font-weight: 500;
      font-size: 9px;
    }
  }
}

/* Live Monitoring 标签 */
.live-monitoring {
  position: absolute;
  z-index: 1;
  top: 62px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 4px;
  background: rgba(0, 0, 0, 0.7);
  padding: 0 10px;
  height: 20px;
  border-radius: 50px;
  backdrop-filter: blur(10px);
  animation: breathe 3s ease-in-out infinite;

  .video-icon {
    width: 14px;
    height: 14px;
    display: flex;
    align-items: center;
    justify-content: center;

    svg {
      width: 13.42px;
      height: 10.27px;
      color: #ff5353;
      animation: pulse 2s infinite;
    }
  }

  .live-text {
    color: #ffffff;
    font-family: 'Work Sans', sans-serif;
    font-weight: 500;
    font-size: 11px;
  }
}

/* 底部信息区域 */
.bottom-section {
  position: absolute;
  top: 290px;
  left: 0;
  right: 0;
  bottom: 0;
  background: #f5f5f5;
  border-radius: 20px 20px 0 0;
  padding: 30px 15px;
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.control-buttons {
  display: flex;
  justify-content: space-between;
  gap: 9px;
}

.control-button {
  position: relative;
  width: 168px;
  height: 64px;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  box-shadow: 0px 4px 0px 0px rgba(202, 147, 242, 0.5);

  .button-content {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #d8a2ff;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 20px;
    gap: 8px;

    .button-text {
      color: #ffffff;
      font-family: 'Work Sans', sans-serif;
      font-weight: 500;
      font-size: 14px;
    }
  }

  .button-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }
}

.character-message {
  color: #1f0038;
  font-family: 'Work Sans', sans-serif;
  font-weight: 500;
  font-size: 15px;
  line-height: 1.17;
  text-align: left;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

@keyframes breathe {
  0%,
  100% {
    opacity: 0.8;
    transform: translateX(-50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translateX(-50%) scale(1.02);
  }
}

@keyframes fadeInOut {
  0%,
  100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}

/* 放大弹窗样式 */
.zoom-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(10px);
  animation: modalFadeIn 0.3s ease-out;
}

.zoom-modal-content {
  width: 90%;
  max-width: 400px;
  background: #ffffff;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0px 10px 30px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease-out;
}

.zoom-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 20px 15px;
  background: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;

  .zoom-title {
    color: #1f0038;
    font-family: 'Work Sans', sans-serif;
    font-weight: 600;
    font-size: 18px;
  }

  .close-button {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 16px;
    background: rgba(31, 0, 56, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(31, 0, 56, 0.2);
      transform: scale(1.1);
    }

    .close-icon {
      color: #1f0038;
      font-size: 16px;
      font-weight: bold;
    }
  }
}

.zoom-screen {
  position: relative;
  width: 100%;
  aspect-ratio: 345/194;
  background: #000;
  overflow: hidden;

  .zoom-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  .zoom-duration {
    position: absolute;
    z-index: 1;
    bottom: 10px;
    right: 10px;
    display: flex;
    align-items: center;
    background: rgba(0, 0, 0, 0.7);
    padding: 6px 10px;
    border-radius: 12px;

    .duration-text {
      color: #ffffff;
      font-family: 'Work Sans', sans-serif;
      font-weight: 500;
      font-size: 12px;
    }
  }

  .zoom-live-indicator {
    position: absolute;
    z-index: 1;
    top: 10px;
    left: 10px;
    display: flex;
    align-items: center;
    gap: 6px;
    background: rgba(0, 0, 0, 0.7);
    padding: 6px 12px;
    border-radius: 20px;
    animation: breathe 3s ease-in-out infinite;

    .video-icon {
      width: 16px;
      height: 16px;
      display: flex;
      align-items: center;
      justify-content: center;

      svg {
        width: 15px;
        height: 12px;
        color: #ff5353;
        animation: pulse 2s infinite;
      }
    }

    .live-text {
      color: #ffffff;
      font-family: 'Work Sans', sans-serif;
      font-weight: 500;
      font-size: 12px;
    }
  }
}

.zoom-info {
  padding: 20px;
  background: #f5f5f5;

  .zoom-message {
    color: #1f0038;
    font-family: 'Work Sans', sans-serif;
    font-weight: 500;
    font-size: 16px;
    line-height: 1.4;
    text-align: center;
  }
}

@keyframes modalFadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes modalSlideIn {
  0% {
    transform: scale(0.8) translateY(20px);
    opacity: 0;
  }
  100% {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

/* 控制面板样式 */
.control-panel {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  border-radius: 16px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 10;
}

.zoom-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  justify-content: center;
}

.control-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
  }

  &:disabled {
    opacity: 0.4;
    cursor: not-allowed;
  }
}

.control-icon {
  font-size: 20px;
  font-weight: bold;
}

.zoom-level {
  color: white;
  font-family: 'Work Sans', sans-serif;
  font-weight: 500;
  font-size: 14px;
  min-width: 50px;
  text-align: center;
}

.pan-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.horizontal-controls {
  display: flex;
  gap: 8px;
  align-items: center;
}

.direction-btn {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  border: none;
  background: rgba(255, 255, 255, 0.15);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.25);
    transform: scale(1.05);
  }

  &:disabled {
    opacity: 0.4;
    cursor: not-allowed;
  }

  &.center {
    background: rgba(74, 144, 226, 0.3);

    &:hover:not(:disabled) {
      background: rgba(74, 144, 226, 0.5);
    }
  }
}

.direction-icon {
  font-size: 16px;
  font-weight: bold;
}

.reset-btn {
  padding: 8px 16px;
  border-radius: 8px;
  border: none;
  background: rgba(74, 144, 226, 0.3);
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(74, 144, 226, 0.5);
    transform: translateY(-1px);
  }
}

.reset-text {
  font-family: 'Work Sans', sans-serif;
  font-weight: 500;
  font-size: 12px;
}

/* 监控弹窗样式 */
.monitor-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.monitor-modal-content {
  width: 95%;
  max-width: 900px;
  max-height: 95%;
  background: #0a0a0a;
  border: 2px solid #333;
  border-radius: 8px;
  overflow: hidden;
  animation: modalSlideIn 0.3s ease-out;
  display: flex;
  flex-direction: column;
  font-family: 'Courier New', monospace;
}

/* 监控头部 */
.monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: linear-gradient(135deg, #1a1a1a, #2a2a2a);
  border-bottom: 1px solid #444;
  min-height: 50px;
}

.monitor-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.camera-id {
  color: #00ff00;
  font-weight: bold;
  font-size: 14px;
  letter-spacing: 1px;
}

.location {
  color: #888;
  font-size: 11px;
  text-transform: uppercase;
}

.monitor-status {
  display: flex;
  align-items: center;
  gap: 16px;
}

.recording-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #ff4444;
  font-size: 12px;
  font-weight: bold;
}

.rec-dot {
  width: 8px;
  height: 8px;
  background: #ff4444;
  border-radius: 50%;
  animation: pulse 1.5s infinite;
}

.timestamp {
  color: #00ff00;
  font-size: 11px;
  font-family: 'Courier New', monospace;
}

.monitor-close {
  width: 30px;
  height: 30px;
  border: 1px solid #555;
  background: #222;
  color: #fff;
  cursor: pointer;
  font-size: 18px;
  transition: all 0.2s ease;

  &:hover {
    background: #444;
    border-color: #777;
  }
}

/* 监控画面区域 */
.monitor-viewport {
  position: relative;
  width: 100%;
  aspect-ratio: 16/9;
  background: #000;
  overflow: hidden;
  border: 2px solid #333;
  margin: 0;
}

.video-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.monitor-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;

  :deep(.video-canvas) {
    transform: translate(var(--pan-x, 0), var(--pan-y, 0)) scale(var(--zoom-level, 1));
    transform-origin: center center;
    transition: transform 0.3s ease;
  }
}

/* 监控叠加信息 */
.monitor-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}

.overlay-top-left {
  position: absolute;
  top: 12px;
  left: 12px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.live-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  background: rgba(0, 0, 0, 0.8);
  padding: 4px 8px;
  border-radius: 4px;
  color: #ff4444;
  font-size: 11px;
  font-weight: bold;
  border: 1px solid #ff4444;
}

.live-dot {
  width: 6px;
  height: 6px;
  background: #ff4444;
  border-radius: 50%;
  animation: pulse 1s infinite;
}

.zoom-info {
  background: rgba(0, 0, 0, 0.8);
  padding: 4px 8px;
  border-radius: 4px;
  color: #00ff00;
  font-size: 11px;
  font-weight: bold;
  border: 1px solid #00ff00;
}

.overlay-top-right {
  position: absolute;
  top: 12px;
  right: 12px;
}

.duration {
  background: rgba(0, 0, 0, 0.8);
  padding: 4px 8px;
  border-radius: 4px;
  color: #00ff00;
  font-size: 11px;
  font-weight: bold;
  border: 1px solid #00ff00;
}

.overlay-bottom {
  position: absolute;
  bottom: 12px;
  left: 50%;
  transform: translateX(-50%);
}

.message {
  background: rgba(0, 0, 0, 0.8);
  padding: 6px 12px;
  border-radius: 4px;
  color: #fff;
  font-size: 12px;
  text-align: center;
  border: 1px solid #555;
  max-width: 300px;
}

/* 监控控制面板 */
.monitor-controls {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px;
  background: linear-gradient(135deg, #1a1a1a, #2a2a2a);
  border-top: 1px solid #444;
  gap: 20px;
}

.control-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.section-title {
  color: #888;
  font-size: 10px;
  font-weight: bold;
  letter-spacing: 1px;
  text-transform: uppercase;
  margin-bottom: 4px;
}

.zoom-buttons {
  display: flex;
  gap: 8px;
}

.monitor-btn {
  width: 40px;
  height: 40px;
  border: 1px solid #555;
  background: #222;
  color: #fff;
  cursor: pointer;
  font-size: 18px;
  font-weight: bold;
  transition: all 0.2s ease;
  font-family: 'Courier New', monospace;

  &:hover:not(:disabled) {
    background: #444;
    border-color: #777;
    transform: scale(1.05);
  }

  &:disabled {
    opacity: 0.4;
    cursor: not-allowed;
  }

  &.reset {
    width: 60px;
    font-size: 10px;
    letter-spacing: 1px;
  }
}

.direction-pad {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.middle-row {
  display: flex;
  gap: 4px;
  align-items: center;
}

.dir-btn {
  width: 32px;
  height: 32px;
  border: 1px solid #555;
  background: #222;
  color: #fff;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    background: #444;
    border-color: #777;
  }

  &:disabled {
    opacity: 0.4;
    cursor: not-allowed;
  }

  &.center {
    background: #333;
    border-color: #666;
  }
}

.function-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 控制面板 */
.control-panel {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px;
  background: rgba(31, 0, 56, 0.05);
  border-top: 1px solid rgba(31, 0, 56, 0.1);
  gap: 24px;
}

.control-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.control-label {
  color: #1f0038;
  font-family: 'Work Sans', sans-serif;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.zoom-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.control-btn {
  width: 44px;
  height: 44px;
  border: none;
  background: rgba(31, 0, 56, 0.1);
  border-radius: 22px;
  color: #1f0038;
  cursor: pointer;
  font-size: 20px;
  font-weight: bold;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);

  &:hover:not(:disabled) {
    background: rgba(31, 0, 56, 0.2);
    transform: scale(1.05);
  }

  &:disabled {
    opacity: 0.4;
    cursor: not-allowed;
  }
}

.zoom-display {
  color: #1f0038;
  font-family: 'Work Sans', sans-serif;
  font-size: 14px;
  font-weight: 600;
  min-width: 50px;
  text-align: center;
}

.direction-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.horizontal-controls {
  display: flex;
  gap: 8px;
  align-items: center;
}

.direction-btn {
  width: 36px;
  height: 36px;
  border: none;
  background: rgba(31, 0, 56, 0.1);
  border-radius: 18px;
  color: #1f0038;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover:not(:disabled) {
    background: rgba(31, 0, 56, 0.2);
    transform: scale(1.05);
  }

  &:disabled {
    opacity: 0.4;
    cursor: not-allowed;
  }

  &.center {
    background: rgba(74, 144, 226, 0.2);
    color: #4a90e2;

    &:hover:not(:disabled) {
      background: rgba(74, 144, 226, 0.3);
    }
  }
}

.reset-button {
  padding: 12px 24px;
  border: none;
  background: rgba(74, 144, 226, 0.2);
  border-radius: 20px;
  color: #4a90e2;
  cursor: pointer;
  font-family: 'Work Sans', sans-serif;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(74, 144, 226, 0.3);
    transform: translateY(-1px);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
}
</style>
