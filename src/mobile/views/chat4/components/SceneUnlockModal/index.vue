<template>
  <div v-if="visible" class="scene-unlock-modal-overlay" @click="handleOverlayClick">
    <div class="scene-unlock-modal" @click.stop>
      <!-- 关闭按钮 -->
      <button class="close-button" @click="$emit('close')">
        <span>×</span>
      </button>

      <!-- 锁定图标 -->
      <div class="lock-icon-container">
        <div class="lock-icon">
          <LockIcon />
        </div>
        <div class="lock-glow"></div>
      </div>

      <!-- 标题 -->
      <h2 class="modal-title">Scene Locked</h2>

      <!-- 场景名称 -->
      <div class="scene-name">{{ sceneDisplayName }}</div>

      <!-- 解锁条件 -->
      <div class="unlock-conditions">
        <div v-if="requiredLevel" class="condition-item">
          <div class="condition-icon">
            <HeartIcon />
          </div>
          <div class="condition-text">
            <span class="condition-label">Favorability Level</span>
            <span class="condition-value">{{ requiredLevel }}</span>
          </div>
        </div>

        <div v-if="requiredCoins && requiredCoins > 0" class="condition-item">
          <div class="condition-icon">
            <DiamondIcon />
          </div>
          <div class="condition-text">
            <span class="condition-label">Diamonds Required</span>
            <span class="condition-value">{{ requiredCoins }}</span>
          </div>
        </div>

        <div v-if="requiredHeartValue && requiredHeartValue > 0" class="condition-item">
          <div class="condition-icon">
            <HeartIcon />
          </div>
          <div class="condition-text">
            <span class="condition-label">Heart Value Needed</span>
            <span class="condition-value">{{ requiredHeartValue }}</span>
          </div>
        </div>
      </div>

      <!-- 当前进度 -->
      <div class="current-progress">
        <div class="progress-item">
          <span class="progress-label">Current Level:</span>
          <span class="progress-value">{{ currentLevel }}</span>
        </div>
        <div v-if="currentHeartValue !== undefined" class="progress-item">
          <span class="progress-label">Current Heart Value:</span>
          <span class="progress-value">{{ currentHeartValue }}</span>
        </div>
      </div>

      <!-- 提示文本 -->
      <div class="hint-text">Build a stronger relationship to unlock this scene</div>

      <!-- 按钮组 -->
      <div class="button-group">
        <button class="action-button secondary" @click="$emit('close')">Got it</button>
        <button class="action-button primary" @click="$emit('boost-favorability')">
          Boost Favorability
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import LockIcon from '@/assets/icon/lock-icon.svg'
import HeartIcon from '@/assets/icon/heart-icon.svg'
import DiamondIcon from '@/assets/icon/diamond-icon.svg'

interface Props {
  visible: boolean
  sceneName: string
  requiredLevel?: string
  requiredCoins?: number
  requiredHeartValue?: number
  currentLevel?: string
  currentHeartValue?: number
}

interface Emits {
  (e: 'close'): void
  (e: 'boost-favorability'): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  sceneName: '',
  currentLevel: 'Lv1',
  currentHeartValue: 0
})

const emit = defineEmits<Emits>()

// 场景显示名称映射
const sceneDisplayName = computed(() => {
  const nameMap: Record<string, string> = {
    video: 'Video Call',
    monitor: 'Monitor',
    meetup: 'Meet Up',
    tip: 'Tip & Gifts'
  }
  return nameMap[props.sceneName] || props.sceneName
})

// 处理遮罩层点击
const handleOverlayClick = () => {
  emit('close')
}
</script>

<style scoped lang="less">
.scene-unlock-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.3s ease-out;
}

.scene-unlock-modal {
  background: linear-gradient(135deg, #2a1f3d 0%, #1a1329 100%);
  border-radius: 24px;
  padding: 32px 24px;
  width: 90%;
  max-width: 400px;
  position: relative;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  animation: slideUp 0.3s ease-out;
  text-align: center;
}

.close-button {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 32px;
  height: 32px;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;

  span {
    color: rgba(255, 255, 255, 0.7);
    font-size: 20px;
    font-weight: 300;
  }

  &:hover {
    background: rgba(255, 255, 255, 0.2);

    span {
      color: white;
    }
  }
}

.lock-icon-container {
  position: relative;
  margin: 0 auto 24px;
  width: 80px;
  height: 80px;
}

.lock-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;

  svg {
    width: 40px;
    height: 40px;
    color: white;
  }
}

.lock-glow {
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  background: radial-gradient(circle, rgba(255, 107, 107, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

.modal-title {
  font-family: 'Work Sans', sans-serif;
  font-size: 24px;
  font-weight: 600;
  color: white;
  margin: 0 0 8px;
}

.scene-name {
  font-family: 'Work Sans', sans-serif;
  font-size: 16px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 32px;
}

.unlock-conditions {
  margin-bottom: 24px;
}

.condition-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.condition-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #7c4dff, #9c7eff);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;

  svg {
    width: 18px;
    height: 18px;
    color: white;
  }
}

.condition-text {
  flex: 1;
  text-align: left;
}

.condition-label {
  display: block;
  font-family: 'Work Sans', sans-serif;
  font-size: 12px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 2px;
}

.condition-value {
  display: block;
  font-family: 'Work Sans', sans-serif;
  font-size: 16px;
  font-weight: 600;
  color: white;
}

.current-progress {
  margin-bottom: 24px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.progress-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;

  &:last-child {
    margin-bottom: 0;
  }
}

.progress-label {
  font-family: 'Work Sans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.6);
}

.progress-value {
  font-family: 'Work Sans', sans-serif;
  font-size: 14px;
  font-weight: 600;
  color: #7c4dff;
}

.hint-text {
  font-family: 'Work Sans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.5);
  margin-bottom: 32px;
  line-height: 1.4;
}

.button-group {
  display: flex;
  gap: 12px;
}

.action-button {
  flex: 1;
  height: 48px;
  border: none;
  border-radius: 12px;
  font-family: 'Work Sans', sans-serif;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;

  &.secondary {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);

    &:hover {
      background: rgba(255, 255, 255, 0.15);
      color: white;
    }
  }

  &.primary {
    background: linear-gradient(135deg, #7c4dff, #9c7eff);
    color: white;

    &:hover {
      background: linear-gradient(135deg, #6c3ce6, #8c6eff);
      transform: translateY(-1px);
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.1);
  }
}
</style>
