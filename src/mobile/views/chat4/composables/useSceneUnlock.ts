/**
 * 场景解锁状态管理 Composable
 */
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useChatEventsStore } from '@/store/chat-events'
import { useUserStore } from '@/store/user'
import type { SceneUnlockStatus } from '@/types/favorability'
import { SceneUnlockUtils } from '@/types/favorability'

export function useSceneUnlock() {
  const chatEventsStore = useChatEventsStore()
  const userStore = useUserStore()

  // 场景解锁状态映射
  const sceneUnlockStatuses = ref<Map<string, SceneUnlockStatus>>(new Map())

  /**
   * 计算指定场景的解锁状态
   */
  const getSceneUnlockStatus = (sceneId: string): SceneUnlockStatus => {
    const { sceneConditions, levelInfos, currentHeartValue } = chatEventsStore.favorabilityState
    const currentCoins = userStore.userInfo?.coins || 0

    return SceneUnlockUtils.calculateSceneUnlockStatus(
      sceneId,
      sceneConditions,
      levelInfos,
      currentHeartValue,
      currentCoins
    )
  }

  /**
   * 更新所有场景的解锁状态
   */
  const updateAllSceneUnlockStatuses = () => {
    const { sceneConditions } = chatEventsStore.favorabilityState

    // 获取所有唯一的场景ID
    const sceneIds = [...new Set(sceneConditions.map((condition) => condition.scene_id))]

    // 计算每个场景的解锁状态
    sceneIds.forEach((sceneId) => {
      const status = getSceneUnlockStatus(sceneId)
      sceneUnlockStatuses.value.set(sceneId, status)
    })

    console.log('Updated scene unlock statuses:', Object.fromEntries(sceneUnlockStatuses.value))
  }

  /**
   * 检查场景是否已解锁
   */
  const isSceneUnlocked = (sceneId: string): boolean => {
    const status = sceneUnlockStatuses.value.get(sceneId)
    return status?.isUnlocked ?? true // 默认已解锁
  }

  /**
   * 获取场景需要的等级文本
   */
  const getSceneRequiredLevelText = (sceneId: string): string | undefined => {
    const status = sceneUnlockStatuses.value.get(sceneId)
    if (!status || status.isUnlocked || !status.requiredLevel) {
      return undefined
    }
    return SceneUnlockUtils.formatLevelText(status.requiredLevel)
  }

  /**
   * 获取场景需要的金币数量
   */
  const getSceneRequiredCoins = (sceneId: string): number | undefined => {
    const status = sceneUnlockStatuses.value.get(sceneId)
    if (!status || status.isUnlocked) {
      return undefined
    }
    return status.requiredCoins
  }

  /**
   * 获取场景需要的好感度值
   */
  const getSceneRequiredHeartValue = (sceneId: string): number | undefined => {
    const status = sceneUnlockStatuses.value.get(sceneId)
    if (!status || status.isUnlocked) {
      return undefined
    }
    return status.requiredHeartValue
  }

  /**
   * 处理场景解锁状态更新事件
   */
  const handleSceneUnlockStatusUpdate = (event: CustomEvent) => {
    console.log('Received scene unlock status update event:', event.detail)
    updateAllSceneUnlockStatuses()
  }

  /**
   * 处理场景解锁条件更新事件
   */
  const handleScenesUnlockConditionsUpdate = (event: CustomEvent) => {
    console.log('Received scenes unlock conditions update event:', event.detail)
    updateAllSceneUnlockStatuses()
  }

  /**
   * 处理等级信息更新事件
   */
  const handleLevelInfoUpdate = (event: CustomEvent) => {
    console.log('Received level info update event:', event.detail)
    updateAllSceneUnlockStatuses()
  }

  // 计算属性：所有场景的解锁状态
  const allSceneUnlockStatuses = computed(() => {
    return Object.fromEntries(sceneUnlockStatuses.value)
  })

  // 生命周期钩子
  onMounted(() => {
    // 监听场景解锁状态更新事件
    window.addEventListener(
      'sceneUnlockStatusUpdated',
      handleSceneUnlockStatusUpdate as EventListener
    )
    window.addEventListener(
      'scenesUnlockConditionsUpdated',
      handleScenesUnlockConditionsUpdate as EventListener
    )
    window.addEventListener('levelInfoUpdated', handleLevelInfoUpdate as EventListener)

    // 初始化时计算一次
    updateAllSceneUnlockStatuses()
  })

  onUnmounted(() => {
    // 清理事件监听器
    window.removeEventListener(
      'sceneUnlockStatusUpdated',
      handleSceneUnlockStatusUpdate as EventListener
    )
    window.removeEventListener(
      'scenesUnlockConditionsUpdated',
      handleScenesUnlockConditionsUpdate as EventListener
    )
    window.removeEventListener('levelInfoUpdated', handleLevelInfoUpdate as EventListener)
  })

  return {
    // 状态
    sceneUnlockStatuses,
    allSceneUnlockStatuses,

    // 方法
    getSceneUnlockStatus,
    updateAllSceneUnlockStatuses,
    isSceneUnlocked,
    getSceneRequiredLevelText,
    getSceneRequiredCoins,
    getSceneRequiredHeartValue
  }
}

/**
 * 场景ID映射 - 将导航按钮的场景名称映射到实际的场景ID
 */
// 场景键常量，避免硬编码字符串
export const SCENE_KEYS = {
  LIVE: 'live',
  MONITOR: 'monitor',
  VIDEO: 'video',
  MEETUP: 'meetup',
  DANCING: 'dancing',
  CONCERT: 'concert',
  TIP: 'tip',
  GIFT: 'gift',
  DANCE: 'dance'
} as const

export const SCENE_ID_MAP = {
  [SCENE_KEYS.LIVE]: 'Living', // 直播场景
  [SCENE_KEYS.MONITOR]: 'Monitor',
  [SCENE_KEYS.VIDEO]: 'Video',
  [SCENE_KEYS.MEETUP]: 'Meetup',
  [SCENE_KEYS.DANCING]: 'Dancing',
  [SCENE_KEYS.CONCERT]: 'Concert',
  [SCENE_KEYS.TIP]: 'Tip',
  [SCENE_KEYS.GIFT]: 'Tip', // gift功能映射到Tip场景
  [SCENE_KEYS.DANCE]: 'Dancing' // dance功能映射到Dancing场景
} as const

/**
 * 根据导航按钮的场景名称获取实际的场景ID
 */
export function getSceneIdFromNavName(navName: string): string {
  return SCENE_ID_MAP[navName as keyof typeof SCENE_ID_MAP] || navName
}

/**
 * 场景ID到状态机方法的映射
 */
export const SCENE_NAVIGATION_MAP = {
  Living: 'goToLive',
  Video: 'goToVideo',
  Monitor: 'goToMonitor',
  Meetup: 'goToMeetup',
  Dancing: 'goToDancing',
  Concert: 'goToConcert',
  Tip: 'goToTip',
  Phone: 'goToChat'
} as const

/**
 * 统一的场景导航处理函数
 * @param sceneId 场景ID
 * @param stateMachine 状态机实例
 * @param specialHandlers 特殊处理函数（如打开礼物弹窗）
 */
export async function handleSceneNavigation(
  sceneId: string,
  stateMachine: any,
  specialHandlers?: {
    onTip?: () => void
    onUnknown?: (sceneId: string) => void
  }
): Promise<void> {
  console.log('Navigating to scene:', sceneId)

  // 特殊处理：Tip场景
  if (sceneId === 'Tip' && specialHandlers?.onTip) {
    specialHandlers.onTip()
    return
  }

  // 获取对应的状态机方法
  const methodName = SCENE_NAVIGATION_MAP[sceneId as keyof typeof SCENE_NAVIGATION_MAP]

  if (methodName && typeof stateMachine[methodName] === 'function') {
    try {
      await stateMachine[methodName]()
      console.log(`Successfully navigated to ${sceneId} using ${methodName}`)
    } catch (error) {
      console.error(`Failed to navigate to ${sceneId}:`, error)
      if (specialHandlers?.onUnknown) {
        specialHandlers.onUnknown(sceneId)
      }
    }
  } else {
    console.warn(`No navigation method found for scene: ${sceneId}`)
    if (specialHandlers?.onUnknown) {
      specialHandlers.onUnknown(sceneId)
    }
  }
}
