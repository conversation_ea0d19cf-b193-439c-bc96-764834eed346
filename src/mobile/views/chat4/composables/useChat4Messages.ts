import { ref, computed, watch, onUnmounted } from 'vue'
import { useChatMessagesStore } from '@/store/chat-messages'
import { getChat4HistoryAPI, type Chat4HistoryRequest } from '@/api/chat-multivariate'
import { useStoryStore } from '@/store/story'
import { useChat4Store } from '@/store/chat4'
import { Chat4SceneUtils } from '@/types/chat4-scene'

// 全局防抖计时器，避免组件重新挂载时丢失
let globalDanmakuDebounceTimer: ReturnType<typeof setTimeout> | null = null

// 聊天消息类型定义（用于ChatInterface组件）
export interface ChatMessage {
  id: string
  type: 'actor' | 'user'
  content: string
  timestamp: number
}

// Comment类型定义
export interface Comment {
  id: string
  username: string
  content: string
  type: 'normal' | 'highlight' | 'system' | 'streamer' | 'self' | 'join'
}

export function useChat4Messages() {
  const chatMessagesStore = useChatMessagesStore()
  const storyStore = useStoryStore()
  const chat4Store = useChat4Store()

  // 监听场景变化，停止弹幕循环
  watch(
    () => chat4Store.currentScene,
    (newScene, oldScene) => {
      if (oldScene && newScene !== oldScene) {
        console.log('Scene changed, stopping danmaku loop:', { from: oldScene, to: newScene })
        stopDanmakuLoop()
      }
    }
  )

  // 组件卸载时清理
  onUnmounted(() => {
    stopDanmakuLoop()
  })

  // 直播评论数据现在由 Chat4 Store 管理，转换为可变数组
  const liveComments = computed(() => [...chat4Store.danmakuState.liveComments])

  // 消息加载状态
  const isLoadingMessages = ref(false)
  const hasInitializedMessages = ref(false)

  // 添加进入房间提醒
  const addJoinRoomMessage = () => {
    const joinMessage: Comment = {
      id: `join_room_${Date.now()}`,
      username: 'System',
      content: 'You Join the room.',
      type: 'join'
    }

    // 使用 Chat4 Store 的方法添加评论（内部会检查场景）
    chat4Store.addLiveComment(joinMessage)
  }

  // 弹幕计时器数组，用于场景切换时清理
  const danmakuTimers: ReturnType<typeof setTimeout>[] = []

  // 循环弹幕的控制变量
  let danmakuLoopActive = false
  let currentLoopData: any = null

  // 处理弹幕事件 - 直接从 chat-events 接收
  const handleChat4DanmakuEvent = (event: CustomEvent) => {
    const eventData = event.detail.eventData

    // 防抖：如果短时间内多次触发，取消之前的定时器
    if (globalDanmakuDebounceTimer) {
      clearTimeout(globalDanmakuDebounceTimer)
    }

    // 延迟500ms处理，避免重复触发
    globalDanmakuDebounceTimer = setTimeout(() => {
      processDanmakuData(eventData)
      globalDanmakuDebounceTimer = null
    }, 500)
  }

  // 实际处理弹幕数据的函数
  const processDanmakuData = (eventData: any) => {
    try {
      const { names = [], sentences = [] } = eventData.data || {}

      if (!Array.isArray(names) || !Array.isArray(sentences)) {
        console.warn('Invalid danmaku data format:', eventData)
        return
      }

      if (names.length === 0 || sentences.length === 0) {
        console.warn('Empty names or sentences for danmaku:', { names, sentences })
        return
      }

      // 高效算法：随机分配句子给用户，确保每个句子只被一个人说
      const danmakuList: Comment[] = []

      // 创建句子的副本用于随机分配
      const availableSentences = [...sentences]

      // 为每个用户随机分配一个唯一的句子
      names.forEach((name: string) => {
        if (availableSentences.length > 0) {
          // 随机选择一个句子
          const randomIndex = Math.floor(Math.random() * availableSentences.length)
          const selectedSentence = availableSentences[randomIndex]

          // 从可用句子中移除已选择的句子
          availableSentences.splice(randomIndex, 1)

          const danmakuData: Comment = {
            id: `danmaku_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
            username: name,
            content: selectedSentence,
            type: 'normal' as const
          }
          danmakuList.push(danmakuData)
        }
      })

      // 如果用户数量超过句子数量，剩余用户重新开始分配
      if (names.length > sentences.length) {
        const remainingNames = names.slice(sentences.length)
        const sentencesCopy = [...sentences]

        remainingNames.forEach((name: string) => {
          if (sentencesCopy.length > 0) {
            const randomIndex = Math.floor(Math.random() * sentencesCopy.length)
            const selectedSentence = sentencesCopy[randomIndex]
            sentencesCopy.splice(randomIndex, 1)

            const danmakuData: Comment = {
              id: `danmaku_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
              username: name,
              content: selectedSentence,
              type: 'normal' as const
            }
            danmakuList.push(danmakuData)
          }
        })
      }

      // 生成更多弹幕以实现持续效果
      // 根据场景调整弹幕数量
      let minDanmakuCount: number
      if (Chat4SceneUtils.isConcertScene(chat4Store.currentScene)) {
        // 演唱会场景：生成大量弹幕确保极密集效果
        minDanmakuCount = Math.max(60, names.length * 6) // 至少60条弹幕，或者用户数的6倍
      } else {
        // 直播场景：原来的数量
        minDanmakuCount = Math.max(20, names.length * 2) // 至少20条弹幕，或者用户数的2倍
      }
      const originalList = [...danmakuList]

      // 重复生成弹幕直到达到最小数量
      let currentRepeat = 1
      while (danmakuList.length < minDanmakuCount && currentRepeat < 10) {
        // 最多重复10轮，避免无限循环
        originalList.forEach((item, index) => {
          // 为每个重复的弹幕生成新的ID和稍微不同的时间戳
          danmakuList.push({
            ...item,
            id: `danmaku_${Date.now()}_${currentRepeat}_${index}_${Math.random()
              .toString(36)
              .substring(2, 6)}`,
            // 可以稍微变化用户名，让它看起来更真实
            username:
              Math.random() > 0.7
                ? `${item.username}${Math.floor(Math.random() * 99)}`
                : item.username
          })
        })
        currentRepeat++
      }
      // 启动循环弹幕系统
      startDanmakuLoop(danmakuList, eventData)
    } catch (error) {
      console.error('Error handling danmaku event in useChat4Messages:', error)
    }
  }

  // 启动循环弹幕系统
  const startDanmakuLoop = (danmakuList: Comment[], originalEventData: any) => {
    // 停止之前的循环
    danmakuLoopActive = false
    currentLoopData = originalEventData

    // 启动新的循环
    danmakuLoopActive = true

    const playDanmakuRound = () => {
      if (!danmakuLoopActive) return

      // 累积延迟插入弹幕，让效果更真实
      let cumulativeDelay = 0

      danmakuList.forEach((danmaku) => {
        // 根据场景调整弹幕间隔
        let interval: number
        if (Chat4SceneUtils.isConcertScene(chat4Store.currentScene)) {
          // 演唱会场景：极密集的弹幕，几乎连续
          interval = Math.random() * 600 + 200 // 200-800ms (0.2-0.8秒)
        } else {
          // 直播场景：原来的频率
          interval = Math.random() * 5000 + 1000 // 1000-6000ms (1-6秒)
        }
        cumulativeDelay += interval

        const timer = setTimeout(() => {
          if (!danmakuLoopActive) return

          // 使用 Chat4 Store 的方法添加弹幕（内部会检查场景和限制数量）
          try {
            chat4Store.addLiveComment(danmaku)
          } catch (error) {
            console.error('Error adding live comment:', error)
          }
        }, cumulativeDelay)

        // 将计时器保存到数组中，用于后续清理
        danmakuTimers.push(timer)
      })

      // 根据场景调整轮次间隔
      let extraWait: number
      if (Chat4SceneUtils.isConcertScene(chat4Store.currentScene)) {
        // 演唱会场景：无间隔，弹幕无缝连接
        extraWait = 100 // 额外等待0.1秒
      } else {
        // 直播场景：原来的间隔
        extraWait = 5000 // 额外等待5秒
      }
      const totalDuration = cumulativeDelay + extraWait
      const loopTimer = setTimeout(() => {
        console.log('Danmaku loop timer triggered, active:', danmakuLoopActive)
        if (danmakuLoopActive) {
          // 重新生成弹幕列表，增加随机性
          const { names = [], sentences = [] } = originalEventData.data || {}
          console.log(
            'Restarting danmaku loop with names:',
            names.length,
            'sentences:',
            sentences.length
          )
          if (names.length > 0 && sentences.length > 0) {
            // 重新生成一轮弹幕（更新danmakuList以增加随机性）
            const newList = generateDanmakuList(names, sentences)
            danmakuList.splice(0, danmakuList.length, ...newList) // 替换原数组内容
            console.log('Generated new danmaku list with', newList.length, 'items')
            playDanmakuRound() // 递归调用，开始下一轮
          } else {
            console.warn('No names or sentences available for danmaku loop')
          }
        } else {
          console.log('Danmaku loop stopped, not restarting')
        }
      }, totalDuration)

      danmakuTimers.push(loopTimer)
    }

    // 开始第一轮
    console.log(
      'Starting danmaku loop with',
      danmakuList.length,
      'items, active:',
      danmakuLoopActive
    )
    playDanmakuRound()

    console.log('Danmaku loop started')
  }

  // 生成弹幕列表的辅助函数
  const generateDanmakuList = (names: string[], sentences: string[]): Comment[] => {
    const danmakuList: Comment[] = []

    // 创建句子的副本用于随机分配
    const availableSentences = [...sentences]

    // 为每个用户随机分配一个唯一的句子
    names.forEach((name: string) => {
      if (availableSentences.length > 0) {
        // 随机选择一个句子
        const randomIndex = Math.floor(Math.random() * availableSentences.length)
        const selectedSentence = availableSentences[randomIndex]

        // 从可用句子中移除已选择的句子
        availableSentences.splice(randomIndex, 1)

        const danmakuData: Comment = {
          id: `danmaku_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
          username: Math.random() > 0.8 ? `${name}${Math.floor(Math.random() * 99)}` : name, // 20%概率添加数字后缀
          content: selectedSentence,
          type: 'normal' as const
        }
        danmakuList.push(danmakuData)
      }
    })

    return danmakuList
  }

  // 停止弹幕循环
  const stopDanmakuLoop = () => {
    danmakuLoopActive = false
    currentLoopData = null
    console.log('Danmaku loop stopped')
  }

  // 清理所有弹幕计时器
  const clearDanmakuTimers = () => {
    // 停止弹幕循环
    stopDanmakuLoop()

    // 清理所有计时器
    danmakuTimers.forEach((timer) => clearTimeout(timer))
    danmakuTimers.length = 0 // 清空数组
  }

  // 清理全局防抖计时器（仅在页面卸载时使用）
  const clearGlobalDebounceTimer = () => {
    if (globalDanmakuDebounceTimer) {
      clearTimeout(globalDanmakuDebounceTimer)
      globalDanmakuDebounceTimer = null
    }
  }

  // 注意：事件监听器现在在主页面中注册，不在这里注册
  // 这样可以避免场景切换时监听器丢失的问题

  // 将chat-messages store的消息格式转换为ChatInterface组件期望的格式
  const formattedChatMessages = computed<ChatMessage[]>(() => {
    return chatMessagesStore.messages.map((message) => ({
      id: message.id,
      type: message.sender_type === 'user' ? 'user' : 'actor',
      content: message.content.text || message.content.html || '',
      timestamp: new Date(message.create_time).getTime()
    }))
  })

  // 根据是否有真实消息数据决定显示哪个消息源
  const displayMessages = computed<ChatMessage[]>(() => {
    // 直接返回真实消息，不显示示例消息
    // 场景切换时消息会被清空，等待服务器返回新场景的消息
    return formattedChatMessages.value
  })

  // 获取 Chat4 聊天历史记录
  const loadChat4History = async (location?: string) => {
    if (!storyStore.currentStory?.id || !storyStore.currentActor?.id) {
      console.warn('Missing story or actor information for loading chat history')
      return
    }

    try {
      const requestData: Chat4HistoryRequest = {
        story_id: storyStore.currentStory.id,
        actor_id: storyStore.currentActor.id
      }

      // 如果有场景位置参数，添加到请求中
      if (location) {
        requestData.location = location
      }

      console.log('Loading Chat4 history with params:', requestData)

      const response = await getChat4HistoryAPI(requestData)
      if (response.data.code === '0') {
        const messages = response.data.data.history || []
        console.log('Loaded Chat4 history messages:', messages)

        if (!messages.length) {
          console.log('No Chat4 history messages found')
          return
        }

        // 将历史消息转换为聊天消息格式并添加到消息存储
        const formattedMessages = messages.map((msg) => ({
          id: msg.id,
          msg_type: 'text' as const,
          sender_type: msg.sender.type === 'user' ? ('user' as const) : ('actor' as const),
          content: { text: msg.content },
          create_time: new Date(msg.timestamp).toISOString(),
          sender: {
            avatar_url:
              msg.sender.type === 'actor' ? storyStore.currentActor?.avatar_url || '' : '',
            name: msg.sender.name
          }
        }))

        // 直接设置消息数组
        chatMessagesStore.messages.splice(
          0,
          chatMessagesStore.messages.length,
          ...formattedMessages
        )
      } else {
        console.error('Failed to load Chat4 history:', response.data.message)
      }
    } catch (error) {
      console.error('Error loading Chat4 history:', error)
    }
  }

  // 注意：actor 消息转弹幕的逻辑现在由 Chat4 Store 统一管理
  // 不再需要在这里监听消息变化

  // 场景切换时的消息处理（仅在需要历史记录时使用）
  const handleSceneChange = async (newScene: string, oldScene: string) => {
    if (newScene !== oldScene && newScene) {
      console.log(`Scene changed from ${oldScene} to ${newScene}`)

      // 注意：不再清空消息，因为状态机已经处理了
      // 这个函数现在主要用于加载历史记录（如果需要的话）

      // 根据新场景加载对应的历史记录
      let location: string | undefined

      // 根据场景类型设置 location 参数
      if (Chat4SceneUtils.isLivingScene(newScene)) {
        location = 'living' // 直播场景
      } else if (Chat4SceneUtils.isPhoneScene(newScene)) {
        location = 'phone' // 聊天室场景
      }

      // 只有在没有消息且需要历史记录时才加载
      // 通常服务器会在场景跳转时返回新的消息事件，所以这里可能不需要加载历史记录
      if (location && chatMessagesStore.messages.length === 0) {
        console.log(`No messages found, loading history for ${location}`)
        await loadChat4History(location)
      } else {
        console.log(`Messages exist or no location specified, skipping history load`)
      }
    }
  }

  return {
    liveComments,
    formattedChatMessages,
    displayMessages,
    loadChat4History,
    handleSceneChange,
    addJoinRoomMessage,
    clearDanmakuTimers,
    clearGlobalDebounceTimer, // 暴露全局防抖计时器清理函数
    handleChat4DanmakuEvent // 暴露事件处理函数给主页面使用
  }
}
