<template>
  <div class="region-restricted-page">
    <RegionRestriction :show-alternatives="true" />
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import RegionRestriction from '@/shared/components/RegionRestriction.vue'
import { reportEvent } from '@/utils/report'
import { ReportEvent } from '@/interface'

// 页面标题
document.title = `服务暂不可用 - ${import.meta.env.VITE_WEBSITE_TITLE || 'PlayShot'}`

// 页面挂载时上报事件
onMounted(() => {
  reportEvent(ReportEvent.RegionRestrictionShown, {
    page: 'region-restricted',
    userAgent: navigator.userAgent,
    timestamp: Date.now()
  })
})
</script>

<style scoped lang="less">
.region-restricted-page {
  width: 100%;
  height: 100vh;
  overflow: hidden;
}
</style>
