<template>
  <div class="recharge-success">
    <div class="success-content">
      <div class="success-animation">
        <div class="circle-outer"></div>
        <div class="circle-inner">
          <icon-check-circle-fill class="check-icon" />
        </div>
      </div>

      <h1 class="title">Payment Successful!</h1>
      <div class="description">Your diamonds have been added to your account</div>

      <div class="amount-info">
        <img
          class="diamond-icon"
          src="https://cdn.magiclight.ai/assets/mobile/diamond.png"
          alt="diamond"
        />
        <span class="amount">+{{ amount }}</span>
      </div>

      <button class="continue-button" @click="handleContinue">Continue</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { IconCheckCircleFill } from '@arco-design/web-vue/es/icon'
import { useUserStore } from '@/store/user'
import { reportEvent } from '@/utils'
import { ReportEvent } from '@/interface'
import { useRechargeStore } from '@/store/recharge'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const rechargeStore = useRechargeStore()
const amount = ref(route.query.amount || 0)

const handleContinue = () => {
  reportEvent(ReportEvent.ClickContinueAfterRecharge, {
    userId: userStore.userInfo?.uuid,
    amount: amount.value
  })

  // Refresh user info to get updated balance
  userStore.getUserInfo()

  // Navigate back to previous page or home
  router.replace((route.query.redirect as string) || '/')
}

onMounted(() => {
  // 关闭充值弹窗
  rechargeStore.hideRechargeModal()

  reportEvent(ReportEvent.ShowRechargeSuccess, {
    userId: userStore.userInfo?.uuid,
    amount: amount.value
  })

  // Facebook Pixel tracking with retry
  const tryFacebookPixel = (retries = 0, maxRetries = 20) => {
    if (window.fbq) {
      window.fbq('track', 'ShowRechargeSuccess', {
        userId: userStore.userInfo?.uuid,
        amount: amount.value
      })
    } else if (retries < maxRetries) {
      // Retry after 200ms, up to maxRetries times (4 seconds total)
      setTimeout(() => tryFacebookPixel(retries + 1, maxRetries), 200)
    }
  }
  tryFacebookPixel()
})
</script>

<style lang="less" scoped>
.recharge-success {
  height: calc(var(--vh, 1vh) * 100);
  background: linear-gradient(135deg, #2b1b3b 0%, #1a0f24 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px;
}

.success-content {
  width: 100%;
  max-width: 400px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  animation: fadeInUp 0.6s ease;
}

.success-animation {
  position: relative;
  width: 120px;
  height: 120px;
  margin-bottom: 32px;

  .circle-outer {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: linear-gradient(135deg, #ca93f2 0%, #9b6cc8 100%);
    opacity: 0.2;
    animation: pulse 2s infinite;
  }

  .circle-inner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #ca93f2 0%, #9b6cc8 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    animation: scaleIn 0.3s ease;
  }

  .check-icon {
    width: 40px;
    height: 40px;
    color: white;
    animation: bounceIn 0.6s ease 0.2s both;
  }
}

.title {
  font-size: 28px;
  font-weight: 600;
  color: white;
  margin: 0 0 12px;
}

.description {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 32px;
}

.amount-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px 32px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  animation: fadeInScale 0.3s ease 0.4s both;
  margin-bottom: 40px;
  width: fit-content;
  align-self: center;

  .diamond-icon {
    width: 32px;
    height: 32px;
  }

  .amount {
    font-size: 32px;
    font-weight: 700;
    color: #daff96;
  }
}

.continue-button {
  width: 100%;
  height: 50px;
  border-radius: 25px;
  border: none;
  background: linear-gradient(90deg, #ca93f2 0%, #9b6cc8 100%);
  color: white;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  animation: fadeIn 0.3s ease 0.6s both;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(202, 147, 242, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0;
  }
  to {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    opacity: 0.2;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.3;
  }
  100% {
    transform: scale(0.95);
    opacity: 0.2;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
</style>
