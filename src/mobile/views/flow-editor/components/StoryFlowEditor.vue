<template>
  <div class="story-flow-editor">
    <div class="graph-container" ref="graphContainer"></div>
    <div class="property-panel" :class="{ 'panel-visible': selectedNode }">
      <div class="panel-header">
        <h3>{{ selectedNode?.name || '节点配置' }}</h3>
        <button class="close-button" @click="selectedNode = null">
          <svg viewBox="0 0 24 24" class="icon-close">
            <path
              fill="currentColor"
              d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
            />
          </svg>
        </button>
      </div>

      <div class="panel-content">
        <PropertyPanel
          v-if="selectedNode"
          :scene="selectedScene"
          @update="handlePropertyUpdate"
          @update-scene="updateScene"
        />
      </div>

      <div class="panel-actions">
        <div class="action-buttons">
          <button class="add-node" @click="addChildNode">添加子节点</button>
          <button class="delete-node" @click="deleteNode">删除节点</button>
        </div>
      </div>
    </div>

    <!-- 右键菜单 -->
    <div class="context-menu" v-show="contextMenuVisible" :style="contextMenuStyle as any">
      <ul class="menu-list">
        <li @click="handleAddNode">添加节点</li>
        <li @click="handleDeleteNode">删除节点</li>
        <li @click="handleCopyNode">复制节点</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch, defineExpose, nextTick, shallowRef } from 'vue'
import { useEditorStore } from '@/store/editor'
import PropertyPanel from '@/mobile/components/Editor/PropertyPanel.vue'
import type { Scene, GameEvent, ActionHandler } from '@/types/editor'
import { Graph, Shape, FunctionExt, Node } from '@antv/x6'
import { EdgeView } from '@antv/x6/es/view'
import { register } from '@antv/x6-vue-shape'
import { nanoid } from 'nanoid'
import NodeComponent from './NodeComponent.vue'

// 为 Scene 中的 plot 定义额外的类型（如果在原类型定义中没有）
interface SceneEventPlot {
  next_scene_id?: string
  [key: string]: any
}

// 使用接口扩展事件类型
interface SceneEvent {
  plot?: SceneEventPlot
  [key: string]: any
}

const graphContainer = ref<HTMLElement | null>(null)
const editorStore = useEditorStore()
const graph = shallowRef<Graph | null>(null)
const selectedNode = ref<Scene | null>(null)
const contextMenuVisible = ref(false)
const contextMenuPosition = ref({ x: 0, y: 0 })
const contextMenuTargetId = ref<string | null>(null)

// 右键菜单样式
const contextMenuStyle = computed(() => ({
  position: 'absolute' as const,
  left: `${contextMenuPosition.value.x}px`,
  top: `${contextMenuPosition.value.y}px`
}))

// 获取选中节点对应的场景
const selectedScene = computed(() => {
  if (!selectedNode.value) return null
  return editorStore.gameConfig.scenes.find((s) => s.id === selectedNode.value.id) || null
})

// 将场景数据渲染为图表
const renderScenesToGraph = () => {
  console.log('开始渲染图表')

  try {
    if (!graph.value) {
      console.error('图表对象不存在，尝试重新初始化图表')
      initGraph()
      if (!graph.value) {
        console.error('图表初始化失败，无法继续渲染')
        return
      }
    }

    // 保存当前视图状态
    const isFirstRender = graph.value.getCells().length === 0
    const currentScale = graph.value.zoom()
    const currentTranslate = graph.value.translate()

    // 清空画布
    console.log('清空已有单元格')
    graph.value.clearCells()

    // 尝试添加一个测试节点，确认基本渲染功能正常
    try {
      console.log('添加测试节点')
      const testNode = graph.value.addNode({
        id: 'test-node',
        shape: 'rect', // 使用内置的矩形节点
        x: 100,
        y: 100,
        width: 120,
        height: 60,
        attrs: {
          body: {
            fill: '#ca93f2',
            stroke: '#fff',
            strokeWidth: 2,
            rx: 8,
            ry: 8
          },
          label: {
            text: '测试节点',
            fill: '#fff',
            fontSize: 14
          }
        }
      })
      console.log('测试节点添加成功:', testNode)
    } catch (testNodeError) {
      console.error('添加测试节点失败:', testNodeError)
    }

    const scenes = editorStore.gameConfig.scenes
    console.log('场景数据:', JSON.stringify(scenes, null, 2))

    if (!scenes || scenes.length === 0) {
      console.warn('没有场景数据可供渲染')
      return
    }

    console.log('开始渲染场景:', scenes.length, scenes)

    // 创建节点和边
    const nodes: Node[] = []
    const edges: any[] = []

    // 建立场景 ID 到场景对象的映射，便于查找
    const sceneMap = new Map<string, Scene>()
    scenes.forEach((scene) => {
      sceneMap.set(scene.id, scene)
    })

    // 检查场景结构
    const rootScene = scenes.find((s) => s.id === '~')
    if (!rootScene) {
      console.error('未找到根场景 (~)，无法渲染树状图')
      return
    }

    console.log('找到根场景:', rootScene)

    // 布局算法 - 分层布局
    let currentX = 200
    let currentY = 100
    const nodeWidth = 180
    const nodeHeight = 60
    const horizontalSpacing = 220
    const verticalSpacing = 120

    // 记录每个节点的位置
    const nodePositions: Record<string, { x: number; y: number }> = {}

    // 宽度映射，用于计算子节点的水平分布
    const levelWidthMap: Record<number, number> = {}
    const nodesByLevel: Record<number, string[]> = {}

    // 计算每个节点的层级
    const nodeLevels: Record<string, number> = {}

    // 第一步：确定节点层级
    const calculateNodeLevels = (nodeId: string, level: number) => {
      nodeLevels[nodeId] = level

      if (!nodesByLevel[level]) {
        nodesByLevel[level] = []
      }
      nodesByLevel[level].push(nodeId)

      // 找出所有子节点
      const children = scenes.filter((s) => s.parent_id === nodeId)
      children.forEach((child) => {
        calculateNodeLevels(child.id, level + 1)
      })
    }

    // 从根节点开始计算层级
    calculateNodeLevels('~', 0)

    console.log('节点层级计算完成:', nodeLevels, nodesByLevel)

    // 计算每层的宽度
    const maxLevel = Math.max(...Object.keys(nodesByLevel).map(Number))
    for (let level = 0; level <= maxLevel; level++) {
      const nodesInLevel = nodesByLevel[level] || []
      levelWidthMap[level] = nodesInLevel.length * horizontalSpacing
    }

    console.log('层级宽度计算完成:', levelWidthMap)

    // 第二步：计算节点位置
    const containerWidth = graphContainer.value?.clientWidth || 1000

    for (let level = 0; level <= maxLevel; level++) {
      const nodesInLevel = nodesByLevel[level] || []
      const levelWidth = levelWidthMap[level]
      const startX = (containerWidth - levelWidth) / 2 + horizontalSpacing / 2

      nodesInLevel.forEach((nodeId, index) => {
        nodePositions[nodeId] = {
          x: startX + index * horizontalSpacing,
          y: level * verticalSpacing + 100
        }
      })
    }

    console.log('节点位置计算完成:', nodePositions)

    // 确保自定义节点已经注册
    try {
      console.log('检查自定义节点是否已注册')

      // 检查是否有节点使用自定义节点类型
      const nodes = graph.value.getNodes()
      const hasCustomNode = nodes.some((node) => node.shape === 'custom-node')
      console.log('是否存在自定义节点:', hasCustomNode)

      // 无论如何尝试重新注册自定义节点
      console.warn('尝试重新注册自定义节点')
      // 尝试重新注册自定义节点
      register({
        shape: 'custom-node',
        width: 180,
        height: 60,
        component: NodeComponent,
        ports: {
          groups: {
            top: {
              position: 'top',
              attrs: {
                circle: {
                  r: 4,
                  magnet: true,
                  stroke: '#C2C8D5',
                  strokeWidth: 1,
                  fill: '#fff'
                }
              }
            },
            bottom: {
              position: 'bottom',
              attrs: {
                circle: {
                  r: 4,
                  magnet: true,
                  stroke: '#C2C8D5',
                  strokeWidth: 1,
                  fill: '#fff'
                }
              }
            }
          }
        }
      })
    } catch (err) {
      console.error('检查自定义节点注册状态时出错:', err)
    }

    // 第三步：创建节点
    scenes.forEach((scene) => {
      // 跳过根节点，不在图表中显示
      if (scene.id === '~') return

      const position = nodePositions[scene.id] || { x: 200, y: 200 }

      console.log('创建节点:', scene.id, scene.name, position)

      try {
        // 创建 X6 节点
        const node = graph.value!.addNode({
          id: scene.id,
          shape: 'custom-node',
          x: position.x,
          y: position.y,
          data: {
            name: scene.name,
            id: scene.id,
            type:
              scene.id === '_BEGIN_'
                ? 'begin'
                : scene.id === '_END_'
                ? 'end'
                : scene.id.startsWith('Begin-')
                ? scene.id.toLowerCase()
                : 'normal'
          }
        })

        nodes.push(node)
      } catch (nodeErr) {
        console.error('创建节点失败:', scene.id, nodeErr)
      }
    })

    console.log('节点创建完成:', nodes.length)

    // 第四步：创建边
    scenes.forEach((scene) => {
      if (scene.parent_id && scene.parent_id !== '~') {
        // 创建父子关系的边（忽略与根节点的连接）
        console.log('创建父子边:', scene.parent_id, '->', scene.id)

        try {
          const edge = graph.value!.addEdge({
            source: scene.parent_id,
            target: scene.id,
            attrs: {
              line: {
                stroke: '#A069C3',
                strokeWidth: 2
              }
            },
            router: {
              name: 'manhattan'
            },
            connector: {
              name: 'rounded'
            }
          })
          edges.push(edge)
        } catch (err) {
          console.error('创建父子边失败:', scene.parent_id, '->', scene.id, err)
        }
      } else if (scene.parent_id === '~') {
        // 对于连接到根节点的子节点，我们找到根节点的首个子节点（通常是 _BEGIN_），并连接到它
        const beginNode = scenes.find((s) => s.id === '_BEGIN_')
        if (beginNode && scene.id !== '_BEGIN_') {
          console.log('创建到起始节点的边: _BEGIN_ ->', scene.id)
          try {
            const edge = graph.value!.addEdge({
              source: '_BEGIN_',
              target: scene.id,
              attrs: {
                line: {
                  stroke: '#A069C3',
                  strokeWidth: 2,
                  strokeDasharray: '5 5'
                }
              }
            })
            edges.push(edge)
          } catch (err) {
            console.error('创建到起始节点的边失败:', '_BEGIN_', '->', scene.id, err)
          }
        }
      }

      // 为每个事件的 next_scene_id 创建跳转边
      scene.events?.forEach((event: any) => {
        const sceneEvent = event as SceneEvent
        if (sceneEvent.plot?.next_scene_id && sceneEvent.plot.next_scene_id !== scene.id) {
          console.log('创建跳转边:', scene.id, '->', sceneEvent.plot.next_scene_id)

          try {
            const jumpEdge = graph.value!.addEdge({
              source: scene.id,
              target: sceneEvent.plot.next_scene_id,
              attrs: {
                line: {
                  stroke: '#ca93f2',
                  strokeWidth: 2,
                  strokeDasharray: '5 5'
                }
              },
              labels: [
                {
                  attrs: {
                    text: {
                      text: 'Jump',
                      fill: '#ca93f2'
                    },
                    rect: {
                      fill: 'rgba(0, 0, 0, 0.5)',
                      rx: 3,
                      ry: 3,
                      padding: 5
                    }
                  },
                  position: 0.5
                }
              ]
            })
            edges.push(jumpEdge)
          } catch (err) {
            console.error('创建跳转边失败:', scene.id, '->', sceneEvent.plot.next_scene_id, err)
          }
        }
      })
    })

    console.log('边创建完成:', edges.length)

    // 自动布局并居中视图（仅在第一次渲染时）
    setTimeout(() => {
      try {
        if (isFirstRender) {
          // 首次渲染时自动适应视图
          graph.value?.zoomToFit({ padding: 50 })
        } else {
          // 恢复之前的视图状态
          graph.value?.zoom(currentScale, { absolute: true })
          graph.value?.translate(currentTranslate.tx, currentTranslate.ty)
        }
      } catch (zoomErr) {
        console.error('缩放图表失败:', zoomErr)
      }
    }, 100)
  } catch (renderErr) {
    console.error('渲染图表过程中出错:', renderErr)
  }
}

// 初始化图表
const initGraph = () => {
  console.log('初始化图表')
  if (!graphContainer.value) {
    console.error('图表容器未找到')
    return
  }

  // 检查图表容器的尺寸
  console.log('图表容器尺寸:', {
    width: graphContainer.value.clientWidth,
    height: graphContainer.value.clientHeight,
    offsetWidth: graphContainer.value.offsetWidth,
    offsetHeight: graphContainer.value.offsetHeight
  })

  // 检查图表容器内部的元素
  console.log('图表容器子元素数量:', graphContainer.value.children.length)

  // 检查DOM中是否已经有SVG元素
  const existingSvg = graphContainer.value.querySelector('svg')
  if (existingSvg) {
    console.log('已存在SVG元素:', existingSvg)
  }

  // 注册自定义节点
  console.log('准备注册自定义节点')
  try {
    register({
      shape: 'custom-node',
      width: 180,
      height: 60,
      component: NodeComponent,
      ports: {
        groups: {
          top: {
            position: 'top',
            attrs: {
              circle: {
                r: 4,
                magnet: true,
                stroke: '#C2C8D5',
                strokeWidth: 1,
                fill: '#fff'
              }
            }
          },
          bottom: {
            position: 'bottom',
            attrs: {
              circle: {
                r: 4,
                magnet: true,
                stroke: '#C2C8D5',
                strokeWidth: 1,
                fill: '#fff'
              }
            }
          }
        }
      }
    })
    console.log('自定义节点注册成功')
  } catch (error) {
    console.error('注册自定义节点失败:', error)
  }

  // 创建 X6 画布
  try {
    graph.value = new Graph({
      container: graphContainer.value,
      width: graphContainer.value.clientWidth || 800,
      height: graphContainer.value.clientHeight || 600,
      background: {
        color: '#1a0030'
      },
      grid: {
        visible: true,
        type: 'doubleMesh',
        args: [
          {
            color: '#6e6e6e',
            thickness: 1
          },
          {
            color: '#3e3e3e',
            thickness: 1,
            factor: 4
          }
        ]
      },
      connecting: {
        router: 'manhattan',
        connector: {
          name: 'rounded',
          args: {
            radius: 8
          }
        },
        anchor: 'center',
        connectionPoint: 'boundary',
        allowBlank: false,
        snap: {
          radius: 20
        },
        createEdge() {
          return new Shape.Edge({
            attrs: {
              line: {
                stroke: '#A069C3',
                strokeWidth: 2,
                targetMarker: {
                  name: 'block',
                  width: 12,
                  height: 8
                }
              }
            },
            zIndex: 0
          })
        }
      },
      highlighting: {
        magnetAdsorbed: {
          name: 'stroke',
          args: {
            padding: 4,
            attrs: {
              strokeWidth: 2,
              stroke: '#ca93f2'
            }
          }
        }
      },
      mousewheel: {
        enabled: true,
        zoomAtMousePosition: true,
        modifiers: 'ctrl',
        minScale: 0.5,
        maxScale: 3
      },
      interacting: {
        nodeMovable: true
      },
      panning: true
    })

    // 检查图表是否成功创建
    if (graph.value) {
      console.log('图表创建成功')

      // 检查图表创建的SVG元素
      setTimeout(() => {
        const svg = graphContainer.value?.querySelector('svg')
        if (svg) {
          console.log('SVG元素创建成功:', svg)
          console.log('SVG尺寸:', {
            width: svg.getAttribute('width'),
            height: svg.getAttribute('height')
          })
        } else {
          console.error('SVG元素未创建')
        }
      }, 100)
    } else {
      console.error('图表创建失败')
    }
  } catch (graphError) {
    console.error('创建图表时出错:', graphError)
  }

  // 节点点击事件
  graph.value.on('node:click', ({ node }) => {
    const nodeId = node.id as string
    console.log('节点被点击:', nodeId)

    const scene = editorStore.gameConfig.scenes.find((s) => s.id === nodeId)
    if (scene) {
      selectedNode.value = scene
      console.log('设置当前选中节点:', scene)
    } else {
      console.warn('未找到对应的场景:', nodeId)
    }

    // 高亮显示选中的节点
    graph.value!.getNodes().forEach((n) => {
      if (n.id === nodeId) {
        n.attr('body/strokeWidth', 2)
        n.attr('body/stroke', '#ca93f2')
      } else {
        n.attr('body/strokeWidth', 1)
        n.attr('body/stroke', 'rgba(255, 255, 255, 0.1)')
      }
    })
  })

  // 画布点击事件
  graph.value.on('blank:click', () => {
    selectedNode.value = null
    contextMenuVisible.value = false
  })

  // 节点右键菜单
  graph.value.on('node:contextmenu', ({ node, e }) => {
    e.preventDefault()
    contextMenuVisible.value = true
    contextMenuPosition.value = { x: e.clientX, y: e.clientY }
    contextMenuTargetId.value = node.id as string
  })

  console.log('图表事件绑定完成')
}

// 处理属性面板的更新事件
const handlePropertyUpdate = (event: GameEvent | ActionHandler) => {
  // 处理从PropertyPanel发出的事件/动作处理程序更新
  console.log('收到属性面板更新:', event)
  // 这里可以根据实际需求进行处理，比如更新场景中的事件或动作处理程序
}

// 更新场景
const updateScene = (scene: Scene) => {
  editorStore.updateScene(scene)
  renderScenesToGraph()
}

// 添加子节点
const addChildNode = () => {
  if (!selectedNode.value) return

  // 创建新场景
  const newScene: Scene = {
    id: nanoid(),
    name: '新场景',
    parent_id: selectedNode.value.id,
    events: []
  }

  // 添加到 store
  editorStore.gameConfig.scenes.push(newScene)

  // 更新图表
  renderScenesToGraph()

  // 选中新创建的节点
  selectedNode.value = newScene
}

// 删除节点
const deleteNode = () => {
  if (!selectedNode.value) return

  // 不能删除特殊节点
  if (['_BEGIN_', '_END_'].includes(selectedNode.value.id)) {
    return
  }

  // 从 store 中删除
  const index = editorStore.gameConfig.scenes.findIndex((s) => s.id === selectedNode.value?.id)
  if (index >= 0) {
    editorStore.gameConfig.scenes.splice(index, 1)
  }

  // 更新图表
  renderScenesToGraph()
  selectedNode.value = null
}

// 右键菜单处理函数
const handleAddNode = () => {
  if (!contextMenuTargetId.value) return

  const targetScene = editorStore.gameConfig.scenes.find((s) => s.id === contextMenuTargetId.value)
  if (targetScene) {
    selectedNode.value = targetScene
    addChildNode()
  }

  contextMenuVisible.value = false
}

const handleDeleteNode = () => {
  if (!contextMenuTargetId.value) return

  const targetScene = editorStore.gameConfig.scenes.find((s) => s.id === contextMenuTargetId.value)
  if (targetScene) {
    selectedNode.value = targetScene
    deleteNode()
  }

  contextMenuVisible.value = false
}

const handleCopyNode = () => {
  if (!contextMenuTargetId.value) return

  const targetScene = editorStore.gameConfig.scenes.find((s) => s.id === contextMenuTargetId.value)
  if (targetScene) {
    // 创建新场景
    const newScene: Scene = {
      ...JSON.parse(JSON.stringify(targetScene)),
      id: nanoid(),
      name: `${targetScene.name} (复制)`
    }

    // 添加到 store
    editorStore.gameConfig.scenes.push(newScene)

    // 更新图表
    renderScenesToGraph()

    // 选中新创建的节点
    selectedNode.value = newScene
  }

  contextMenuVisible.value = false
}

// 监听 store 中场景数据变化，更新图表
watch(
  () => editorStore.gameConfig.scenes,
  () => {
    renderScenesToGraph()
  },
  { deep: true }
)

// 添加一个简单的渲染测试方法
const renderTestNode = () => {
  console.log('执行简单节点渲染测试')

  if (!graphContainer.value) {
    console.error('图表容器未找到，无法渲染测试节点')
    return
  }

  // 清空容器内容
  while (graphContainer.value.firstChild) {
    graphContainer.value.removeChild(graphContainer.value.firstChild)
  }

  // 手动创建一个SVG元素
  const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg')
  svg.setAttribute('width', '100%')
  svg.setAttribute('height', '100%')
  svg.style.backgroundColor = '#1a0030'
  graphContainer.value.appendChild(svg)

  // 添加一个简单的矩形
  const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect')
  rect.setAttribute('x', '100')
  rect.setAttribute('y', '100')
  rect.setAttribute('width', '200')
  rect.setAttribute('height', '80')
  rect.setAttribute('rx', '10')
  rect.setAttribute('ry', '10')
  rect.setAttribute('fill', '#ca93f2')
  rect.setAttribute('stroke', 'white')
  rect.setAttribute('stroke-width', '2')
  svg.appendChild(rect)

  // 添加文本
  const text = document.createElementNS('http://www.w3.org/2000/svg', 'text')
  text.setAttribute('x', '200')
  text.setAttribute('y', '140')
  text.setAttribute('text-anchor', 'middle')
  text.setAttribute('dominant-baseline', 'middle')
  text.setAttribute('fill', 'white')
  text.setAttribute('font-size', '16')
  text.textContent = '测试节点'
  svg.appendChild(text)

  console.log('测试节点渲染完成')
}

// 初始化和渲染
onMounted(() => {
  console.log('StoryFlowEditor 组件已挂载')

  // 使用 nextTick 确保 DOM 完全渲染
  nextTick(() => {
    console.log('DOM 已更新，开始初始化图表')
    console.log('图表容器元素:', graphContainer.value)
    console.log('图表容器innerHTML:', graphContainer.value?.innerHTML)

    // 打印DOM层次结构
    if (graphContainer.value) {
      console.log('story-flow-editor节点:', graphContainer.value.parentElement)
      console.log('flow-editor-content节点:', graphContainer.value.parentElement?.parentElement)
    }

    if (!graphContainer.value) {
      console.error('图表容器未找到')
      return
    }

    // 设置容器样式以确保可见性
    if (graphContainer.value) {
      graphContainer.value.style.width = '100%'
      graphContainer.value.style.height = '100%'
      graphContainer.value.style.minHeight = '500px'
      graphContainer.value.style.backgroundColor = '#1a0030'
      graphContainer.value.style.border = '1px solid yellow' // 添加明显的边框用于调试
    }

    // 初始化图表
    initGraph()

    // 从 store 中加载场景数据并渲染为图表
    setTimeout(() => {
      renderScenesToGraph()

      // 再次检查DOM
      console.log('渲染后的图表容器innerHTML:', graphContainer.value?.innerHTML)

      const svg = graphContainer.value?.querySelector('svg')
      if (svg) {
        console.log('SVG元素存在, 子元素数量:', svg.children.length)
      } else {
        console.error('渲染后SVG元素不存在')
      }
    }, 300)
  })

  // 响应窗口大小变化
  window.addEventListener('resize', () => {
    if (graph.value && graphContainer.value) {
      const width = graphContainer.value.clientWidth || 800
      const height = graphContainer.value.clientHeight || 600
      console.log('重置图表尺寸:', { width, height })
      graph.value.resize(width, height)
    }
  })

  // 点击其他地方关闭右键菜单
  document.addEventListener('click', () => {
    contextMenuVisible.value = false
  })
})

// 暴露方法给父组件
defineExpose({
  renderScenesToGraph,
  initGraph,
  renderTestNode
})
</script>

<style lang="less" scoped>
.story-flow-editor {
  display: flex;
  position: relative;
  width: 100%;
  height: 100%;
  flex: 1;
  overflow: hidden;

  .graph-container {
    flex: 1;
    height: 100%;
    width: 100%;
    position: relative;
    overflow: hidden;
    min-height: 500px;
    min-width: 500px;
    background-color: #1a0030;
    border: 1px dashed rgba(255, 255, 255, 0.1);
  }

  .property-panel {
    position: absolute;
    top: 0;
    right: 0;
    width: 350px;
    height: 100%;
    background: #2a0049;
    border-left: 1px solid rgba(255, 255, 255, 0.1);
    transform: translateX(100%);
    transition:
      transform 0.3s ease,
      box-shadow 0.3s ease;
    display: flex;
    flex-direction: column;
    z-index: 10;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0);

    &.panel-visible {
      transform: translateX(0);
      box-shadow: -5px 0 15px rgba(0, 0, 0, 0.3);
    }

    .panel-header {
      padding: 16px;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      display: flex;
      align-items: center;
      justify-content: space-between;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
        color: #fff;
        display: flex;
        align-items: center;
        gap: 8px;

        &::before {
          content: '';
          display: block;
          width: 4px;
          height: 16px;
          background: #ca93f2;
          border-radius: 2px;
        }
      }

      .close-button {
        width: 30px;
        height: 30px;
        border: none;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
        color: rgba(255, 255, 255, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s;

        &:hover {
          background: rgba(255, 255, 255, 0.2);
        }

        .icon-close {
          width: 18px;
          height: 18px;
        }
      }
    }

    .panel-content {
      flex: 1;
      overflow-y: auto;
      padding: 16px;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb:hover {
        background-color: rgba(255, 255, 255, 0.2);
      }
    }

    .panel-actions {
      padding: 16px;
      border-top: 1px solid rgba(255, 255, 255, 0.1);

      .action-buttons {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 10px;

        button {
          padding: 10px;
          border: none;
          border-radius: 6px;
          font-size: 14px;
          cursor: pointer;
          transition: all 0.2s;

          &.add-node {
            background: linear-gradient(90deg, #ca93f2 0%, #9b6cc8 100%);
            color: #fff;

            &:hover {
              box-shadow: 0 2px 8px rgba(202, 147, 242, 0.3);
              transform: translateY(-2px);
            }
          }

          &.delete-node {
            background: rgba(255, 77, 79, 0.2);
            color: #ff4d4f;

            &:hover {
              background: rgba(255, 77, 79, 0.3);
              transform: translateY(-2px);
            }
          }
        }
      }
    }
  }

  .context-menu {
    position: fixed;
    z-index: 100;
    min-width: 120px;
    background: #3a0066;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    border-radius: 6px;

    .menu-list {
      list-style: none;
      margin: 0;
      padding: 0;

      li {
        padding: 8px 16px;
        color: #fff;
        cursor: pointer;
        transition: background-color 0.2s;

        &:hover {
          background: rgba(255, 255, 255, 0.1);
        }

        &:first-child {
          border-top-left-radius: 6px;
          border-top-right-radius: 6px;
        }

        &:last-child {
          border-bottom-left-radius: 6px;
          border-bottom-right-radius: 6px;
        }
      }
    }
  }
}
</style>
