<template>
  <div class="flow-editor-container">
    <div class="editor-header">
      <button class="back-button" @click="navigateBack">
        <svg viewBox="0 0 24 24" class="icon-arrow-left">
          <path
            fill="currentColor"
            d="M20,11V13H8L13.5,18.5L12.08,19.92L4.16,12L12.08,4.08L13.5,5.5L8,11H20Z"
          />
        </svg>
        返回
      </button>
      <div class="title">{{ storyTitle }}</div>
      <div class="action-buttons">
        <button @click="saveStory" class="save-button">
          <svg viewBox="0 0 24 24" class="icon-save">
            <path
              fill="currentColor"
              d="M15,9H5V5H15M12,19A3,3 0 0,1 9,16A3,3 0 0,1 12,13A3,3 0 0,1 15,16A3,3 0 0,1 12,19M17,3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V7L17,3Z"
            />
          </svg>
          保存
        </button>
        <button @click="loadExampleData" class="example-button">
          <svg viewBox="0 0 24 24" class="icon-example">
            <path
              fill="currentColor"
              d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20M10,13H8V11H10V13M10,17H8V15H10V17M14,13H12V11H14V13M14,17H12V15H14V17Z"
            />
          </svg>
          加载示例
        </button>
        <button @click="testRender" class="test-button">
          <svg viewBox="0 0 24 24" class="icon-test">
            <path
              fill="currentColor"
              d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M10,17L5,12L6.41,10.59L10,14.17L17.59,6.58L19,8L10,17Z"
            />
          </svg>
          测试渲染
        </button>
      </div>
      <div class="action-tip">
        <svg viewBox="0 0 24 24" class="icon-tip">
          <path
            fill="currentColor"
            d="M13,9H11V7H13M13,17H11V11H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"
          />
        </svg>
        提示: 鼠标滚轮缩放，按住空格拖动画布，右键点击节点打开菜单
      </div>
    </div>

    <div class="loading-overlay" v-show="loading">
      <div class="loading-spinner"></div>
      <div class="loading-text">{{ loadingText }}</div>
    </div>

    <div class="flow-editor-content">
      <StoryFlowEditor ref="flowEditorRef" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter, useRoute } from 'vue-router'
import { onMounted, ref, nextTick } from 'vue'
import { useEditorStore } from '@/store/editor'
import StoryFlowEditor from './components/StoryFlowEditor.vue'
import { nanoid } from 'nanoid'
import { Message } from '@arco-design/web-vue'
import type { GameEventPlot } from '@/types/editor'

// Extiende la interfaz GameEventPlot para incluir next_scene_id
interface ExtendedGameEventPlot extends GameEventPlot {
  next_scene_id?: string
}

const router = useRouter()
const route = useRoute()
const editorStore = useEditorStore()
const loading = ref(false)
const loadingText = ref('正在加载...')
const flowEditorRef = ref<InstanceType<typeof StoryFlowEditor> | null>(null)

const storyTitle = ref('故事流程编辑器')

onMounted(async () => {
  const storyId = route.params.id as string
  if (storyId) {
    try {
      loading.value = true
      loadingText.value = '正在加载故事...'
      // 这里应该从API加载故事数据
      await new Promise((resolve) => setTimeout(resolve, 1000))
      storyTitle.value = '故事流程: ' + storyId
      loading.value = false
    } catch (error) {
      console.error('加载故事失败:', error)
      Message.error('加载故事失败')
      loading.value = false
    }
  }
})

// 导航回上一页
const navigateBack = () => {
  router.go(-1)
}

// 保存故事
const saveStory = async () => {
  try {
    loading.value = true
    loadingText.value = '正在保存...'
    // 这里应该调用API保存故事数据
    await new Promise((resolve) => setTimeout(resolve, 1000))
    Message.success('保存成功')
    loading.value = false
  } catch (error) {
    console.error('保存失败:', error)
    Message.error('保存失败')
    loading.value = false
  }
}

// 加载示例数据
const loadExampleData = () => {
  try {
    loading.value = true
    loadingText.value = '加载示例数据中...'

    // 生成示例数据
    const rootId = '~'
    const beginId = '_BEGIN_'
    const endId = '_END_'
    const levelOne1 = nanoid()
    const levelOne2 = nanoid()
    const levelOne3 = nanoid()
    const levelTwo1 = nanoid()
    const levelTwo2 = nanoid()
    const levelTwo3 = nanoid()
    const levelTwo4 = nanoid()

    console.log('清空当前场景', editorStore.gameConfig.scenes.length)
    // 清空当前场景
    editorStore.gameConfig.scenes = []

    // 添加根场景
    editorStore.gameConfig.scenes.push({
      id: rootId,
      name: '故事流程',
      events: []
    })

    // 添加开始场景
    editorStore.gameConfig.scenes.push({
      id: beginId,
      name: '开始章节',
      parent_id: rootId,
      events: []
    })

    // 添加三个一级场景
    editorStore.gameConfig.scenes.push({
      id: levelOne1,
      name: '第一章',
      parent_id: beginId,
      events: [
        {
          id: nanoid(),
          type: 'message',
          plot: {
            content: {
              text: '这是第一章节的内容'
            },
            sender_type: 'actor',
            msg_type: 'text',
            next_scene_id: levelTwo1
          } as ExtendedGameEventPlot
        }
      ]
    })

    editorStore.gameConfig.scenes.push({
      id: levelOne2,
      name: '第二章',
      parent_id: beginId,
      events: [
        {
          id: nanoid(),
          type: 'message',
          plot: {
            content: {
              text: '这是第二章节的内容'
            },
            sender_type: 'actor',
            msg_type: 'text'
          }
        }
      ]
    })

    editorStore.gameConfig.scenes.push({
      id: levelOne3,
      name: '第三章',
      parent_id: beginId,
      events: [
        {
          id: nanoid(),
          type: 'message',
          plot: {
            content: {
              text: '这是第三章节的内容'
            },
            sender_type: 'actor',
            msg_type: 'text',
            next_scene_id: levelTwo4
          } as ExtendedGameEventPlot
        }
      ]
    })

    // 添加四个二级场景
    editorStore.gameConfig.scenes.push({
      id: levelTwo1,
      name: '第一章-分支1',
      parent_id: levelOne1,
      events: [
        {
          id: nanoid(),
          type: 'message',
          plot: {
            content: {
              text: '这是第一章分支1的内容'
            },
            sender_type: 'actor',
            msg_type: 'text'
          }
        }
      ]
    })

    editorStore.gameConfig.scenes.push({
      id: levelTwo2,
      name: '第一章-分支2',
      parent_id: levelOne1,
      events: [
        {
          id: nanoid(),
          type: 'message',
          plot: {
            content: {
              text: '这是第一章分支2的内容'
            },
            sender_type: 'actor',
            msg_type: 'text'
          }
        }
      ]
    })

    editorStore.gameConfig.scenes.push({
      id: levelTwo3,
      name: '第二章-分支1',
      parent_id: levelOne2,
      events: [
        {
          id: nanoid(),
          type: 'message',
          plot: {
            content: {
              text: '这是第二章分支1的内容'
            },
            sender_type: 'actor',
            msg_type: 'text'
          }
        }
      ]
    })

    editorStore.gameConfig.scenes.push({
      id: levelTwo4,
      name: '第三章-分支1',
      parent_id: levelOne3,
      events: [
        {
          id: nanoid(),
          type: 'message',
          plot: {
            content: {
              text: '这是第三章分支1的内容'
            },
            sender_type: 'actor',
            msg_type: 'text',
            next_scene_id: endId
          } as ExtendedGameEventPlot
        }
      ]
    })

    // 添加结束场景
    editorStore.gameConfig.scenes.push({
      id: endId,
      name: '结束章节',
      parent_id: rootId,
      events: []
    })

    // 设置当前故事名称
    editorStore.storyConfig.project_name = '示例故事流程'
    storyTitle.value = '示例故事流程'

    console.log('示例数据加载完成，场景数量:', editorStore.gameConfig.scenes.length)
    console.log('场景数据:', JSON.stringify(editorStore.gameConfig.scenes, null, 2))

    // 确保DOM更新后再渲染图表
    setTimeout(() => {
      const editorRef = flowEditorRef.value
      if (editorRef) {
        console.log('正在触发流程图渲染...')

        // 1. 首先尝试简单渲染测试
        if (typeof editorRef.renderTestNode === 'function') {
          try {
            console.log('尝试执行简单渲染测试')
            editorRef.renderTestNode()
            console.log('简单渲染测试完成')
            loading.value = false
            Message.success('测试节点渲染成功')
            return
          } catch (err) {
            console.error('简单渲染测试失败:', err)
          }
        }

        // 2. 如果简单渲染失败，尝试标准渲染流程
        // 首先尝试重新初始化图表
        if (typeof editorRef.initGraph === 'function') {
          try {
            console.log('尝试重新初始化图表...')
            editorRef.initGraph()
          } catch (err) {
            console.error('初始化图表失败:', err)
          }
        }

        // 然后进行渲染
        if (typeof editorRef.renderScenesToGraph === 'function') {
          try {
            console.log('开始渲染场景图表...')
            editorRef.renderScenesToGraph()
            console.log('渲染方法调用成功')
          } catch (err) {
            console.error('渲染场景图表失败:', err)
          }
        } else {
          console.warn('流程编辑器组件未暴露 renderScenesToGraph 方法')
        }
      } else {
        console.error('未找到流程编辑器组件引用')
      }

      loading.value = false
      Message.success('示例数据加载成功')
    }, 500) // 增加延迟以确保DOM完全更新
  } catch (error) {
    console.error('加载示例数据失败:', error)
    Message.error('加载示例数据失败')
    loading.value = false
  }
}

// 测试渲染
const testRender = () => {
  const editorRef = flowEditorRef.value
  if (editorRef) {
    if (typeof editorRef.renderTestNode === 'function') {
      try {
        console.log('执行测试渲染...')
        editorRef.renderTestNode()
        Message.success('测试节点渲染成功')
      } catch (err) {
        console.error('测试渲染失败:', err)
        Message.error('测试渲染失败')
      }
    } else {
      Message.warning('测试渲染方法不可用')
    }
  } else {
    Message.error('未找到流程编辑器组件引用')
  }
}
</script>

<style lang="less" scoped>
.flow-editor-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #1f0038;
  overflow: hidden;

  .editor-header {
    display: flex;
    align-items: center;
    padding: 16px 24px;
    background: linear-gradient(180deg, #2a0049 0%, #1f0038 100%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    z-index: 20;
    height: 60px;

    .back-button {
      display: flex;
      align-items: center;
      gap: 8px;
      color: rgba(255, 255, 255, 0.8);
      cursor: pointer;
      background: rgba(255, 255, 255, 0.05);
      padding: 8px 16px;
      border-radius: 20px;
      transition: all 0.2s;

      &:hover {
        color: #fff;
        background: rgba(255, 255, 255, 0.1);
      }
    }

    .title {
      flex: 1;
      text-align: center;
      font-size: 18px;
      font-weight: 600;
      color: #fff;
      margin: 0;
    }

    .action-buttons {
      display: flex;
      align-items: center;
      gap: 16px;

      .save-button,
      .example-button,
      .test-button {
        padding: 8px 16px;
        border: none;
        border-radius: 20px;
        color: #fff;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
      }

      .save-button {
        background: linear-gradient(90deg, #ca93f2 0%, #9b6cc8 100%);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(202, 147, 242, 0.3);
        }
      }

      .example-button {
        background: rgba(255, 255, 255, 0.1);

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          transform: translateY(-2px);
        }
      }

      .test-button {
        background: rgba(255, 255, 255, 0.1);

        &:hover {
          background: rgba(255, 255, 255, 0.2);
          transform: translateY(-2px);
        }
      }
    }

    .action-tip {
      display: flex;
      align-items: center;
      gap: 6px;
      color: rgba(255, 255, 255, 0.6);
      font-size: 14px;
      background: rgba(255, 255, 255, 0.05);
      padding: 8px 16px;
      border-radius: 20px;

      :deep(svg) {
        width: 16px;
        height: 16px;
        color: #ca93f2;
      }
    }
  }

  .flow-editor-content {
    flex: 1;
    overflow: hidden;
    position: relative;
    display: flex;
    height: calc(100vh - 80px); /* 减去头部高度 */
    min-height: 600px;
    margin: 0;
    padding: 0;
    background-color: #1a0030;
    border: 2px solid rgba(255, 255, 255, 0.05);
  }

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 100;

    .loading-spinner {
      width: 50px;
      height: 50px;
      border: 3px solid transparent;
      border-top-color: #ca93f2;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 20px;
    }

    .loading-text {
      color: white;
      font-size: 16px;
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
