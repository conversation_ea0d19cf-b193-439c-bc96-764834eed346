import { RouteRecordRaw } from 'vue-router'

export interface LandingPageConfig {
  storyId: string
  storyQuote?: string
  characterKey: string // For reporting events (e.g. 'character2', 'character3')
  otherStoryIds: string[]
  showStoryIntro?: boolean // 是否显示故事介绍页面
}

interface StoryConfig {
  id: string
  quote: string
}

interface CharacterConfig {
  key: string
  name: string
  storyId: {
    test: string | StoryConfig
    prod: string | StoryConfig
  }
  showStoryIntro?: boolean
}

// 环境判断
const isTest = import.meta.env.MODE === 'development'

// 统一的角色配置
export const characterConfigs: CharacterConfig[] = [
  {
    key: 'hancock',
    name: '<PERSON>',
    storyId: {
      test: '466c25c9-05ba-412c-a9fe-5e47e2c2ad87',
      prod: '8d12a9ea-0b83-4506-b912-fa85435d3476'
    }
  },
  {
    key: 'tsunade',
    name: 'Tsun<PERSON>',
    storyId: {
      test: '5a1af651-4feb-49a4-8262-dee508ebefc8',
      prod: '06770b09-b95b-4e60-8293-d8914d7c0b0b'
    }
  },
  {
    key: 'joh',
    name: 'Joh',
    storyId: {
      test: 'ebb24fcf-5349-47a8-a377-a0fdd3ecff27',
      prod: '277ed832-0a27-4187-b2f3-4f5b4135bf01'
    }
  },
  {
    key: 'amy',
    name: 'Amy',
    storyId: {
      test: 'ef9986ff-759c-4f09-8b81-bca26d293fde',
      prod: 'cdd9d2b7-0c2b-423e-92e2-aa75a888b8f9'
    }
  },
  {
    key: 'tsunade2',
    name: 'Tsunade',
    storyId: {
      test: '2d523e7c-70a8-4748-878c-733996891133',
      prod: 'ac88d3ef-efc4-48ad-a3b4-b4c405f0d7e1'
    }
  },
  {
    key: 'nami',
    name: 'Nami',
    storyId: {
      test: 'ac49f68d-e995-4a66-8d99-7eb1270adbdb',
      prod: '018c2738-2e6a-498f-b0f9-1d027d652c31'
    }
  },
  {
    key: 'robin',
    name: 'Robin',
    storyId: {
      test: '10353a52-4770-4bee-8ae1-181448f1dbb0',
      prod: '78d69bb6-1f20-44dd-9d8c-cc7a92bb70b3'
    }
  },
  {
    key: 'tsunade3',
    name: 'Tsunade',
    storyId: {
      test: '42a3103d-ad46-4072-b99f-f30d492f176d',
      prod: '8c5bcb88-2e9c-4715-b670-4751d40dd297'
    }
  },
  {
    key: 'tifa',
    name: 'Tifa',
    storyId: {
      test: '6bd1e0ff-e8a3-4238-b832-a09a93057fff',
      prod: '9b538d87-2b2c-43a1-be82-22026bdb6c61'
    }
  },
  {
    key: 'gym',
    name: 'Gym',
    storyId: {
      test: '6ef4aab4-d705-407b-b109-e3f3ece46d73',
      prod: '339fd4a5-1cbf-49aa-aae2-9043906cff83'
    },
    showStoryIntro: true
  },
  {
    key: 'agent',
    name: 'Agent',
    storyId: {
      test: 'b785d406-0fc9-4b7a-a75b-d872f5f62269',
      prod: '2c0363ee-88c5-41e1-904f-5bea71712f56'
    }
  },
  {
    key: 'cora',
    name: 'Cora',
    storyId: {
      test: 'c3c10356-5f7a-406d-a88a-a70c6933c172',
      prod: 'bfb573d0-ec3d-4b4c-9d8d-63aa1b200160'
    },
    showStoryIntro: true
  },
  {
    key: 'molly',
    name: 'Molly',
    storyId: {
      test: 'b3fc2d7c-d0bf-48c6-b08a-0201af9d58cb',
      prod: 'b2fd6a46-0438-4aeb-a9d0-5cd330eec660'
    }
  },
  {
    key: 'serena',
    name: 'Serena',
    storyId: {
      test: 'a8c9a82e-75c7-4d4a-9183-1fc39334009e',
      prod: 'e9bed3ea-56d7-4f9a-888d-fd8c44d27af8'
    }
  },
  {
    key: 'clara',
    name: 'Clara',
    storyId: {
      test: 'a8c9a82e-75c7-4d4a-9183-1fc39334009e',
      prod: 'eb5e6f2a-e1a8-405e-991e-d409cc7d3ba5'
    }
  },
  {
    key: 'chloe',
    name: 'Chloe',
    storyId: {
      test: 'a8c9a82e-75c7-4d4a-9183-1fc39334009e',
      prod: '69ff092d-9e89-4a50-ae8c-21dfcf1f6f5e'
    }
  },
  {
    key: 'lily',
    name: 'Lily',
    storyId: {
      test: '909d174b-ba54-4979-be76-5db04e549a28',
      prod: '842e49ba-b836-4e52-9af9-c09dd3d737fa'
    },
    showStoryIntro: true
  },
  // 要在四个内随机一个故事
  // {
  //   key: 'judy',
  //   name: 'Judy',
  //   storyId: {
  //     test: [
  //       {
  //         id: '1c2f9b13-2868-43ce-b695-409fab00bfba',
  //         quote: "You're so lucky to date a superstar like me~!"
  //       },
  //       {
  //         id: '65c28773-e6e5-485b-acd8-ff94c767d8cb',
  //         quote: 'Wanna bake desserts with me, honey?'
  //       }
  //     ][Math.floor(Math.random() * 2)],
  //     prod: [
  //       {
  //         id: '1c2f9b13-2868-43ce-b695-409fab00bfba',
  //         quote: "You're so lucky to date a superstar like me~!"
  //       },
  //       {
  //         id: '65c28773-e6e5-485b-acd8-ff94c767d8cb',
  //         quote: 'Wanna bake desserts with me, honey?'
  //       }
  //     ][Math.floor(Math.random() * 2)]
  //   },
  //   showStoryIntro: true
  // },
  {
    key: 'judy',
    name: 'Judy',
    storyId: {
      test: [
        {
          id: '0deed33c-cc9b-4b94-beed-dca26b9edaac',
          quote: "You're so lucky to date a superstar like me~!"
        },
        {
          id: '28513d0d-5e43-4068-8c3d-368037f44fc5',
          quote: 'Wanna bake desserts with me, honey?'
        }
      ][Math.floor(Math.random() * 2)],
      prod: [
        {
          id: '0deed33c-cc9b-4b94-beed-dca26b9edaac',
          quote: "You're so lucky to date a superstar like me~!"
        },
        {
          id: '28513d0d-5e43-4068-8c3d-368037f44fc5',
          quote: 'Wanna bake desserts with me, honey?'
        }
      ][Math.floor(Math.random() * 2)]
    },
    showStoryIntro: true
  },
  {
    key: 'asuka',
    name: 'asuka',
    storyId: {
      test: '90c19d5c-a4b6-4d15-a2a0-f578b0bb9e95',
      prod: '22d39fc9-d61a-4e7d-8301-fd9055bcbec1'
    },
    showStoryIntro: true
  },
  {
    key: 'asukb',
    name: 'asukb',
    storyId: {
      test: '3f56266a-247b-4bcf-8ba4-7e2cbf49872b',
      prod: 'bfd10494-7686-4b3d-902d-190f3a150614'
    },
    showStoryIntro: true
  }
]

export const characterConfigs2: CharacterConfig[] = [
  {
    key: 'nobara',
    name: 'Nobara',
    storyId: {
      test: '6ef4aab4-d705-407b-b109-e3f3ece46d73',
      prod: 'c1171d28-12d5-479d-8245-5f7b8625dadf'
    }
  },
  {
    key: 'Beel',
    name: 'Beel',
    storyId: {
      test: 'b785d406-0fc9-4b7a-a75b-d872f5f62269',
      prod: 'b9b44261-5606-4c0b-8416-bb6465b7dcc3'
    }
  },
  {
    key: 'yor',
    name: 'Yor',
    storyId: {
      test: 'c3c10356-5f7a-406d-a88a-a70c6933c172',
      prod: '22d39fc9-d61a-4e7d-8301-fd9055bcbec1'
    }
  },

  {
    key: 'keqing',
    name: 'keqing',
    storyId: {
      test: '909d174b-ba54-4979-be76-5db04e549a28',
      prod: 'd34bc617-1b40-4d19-9e72-810b393eb485'
    }
  }
]

// 获取当前环境的 storyId
const getStoryId = (config: CharacterConfig) => {
  const storyId = config.storyId[isTest ? 'test' : 'prod']
  return typeof storyId === 'string' ? storyId : storyId.id
}

const getStoryId2 = (config: CharacterConfig) => {
  const storyId = config.storyId[isTest ? 'test' : 'prod']
  return typeof storyId === 'string' ? storyId : storyId.id
}

// 获取所有 storyIds
const getAllStoryIds = () => characterConfigs.map((config) => getStoryId(config))

const getAllStoryIds2 = () => characterConfigs2.map((config) => getStoryId2(config))

// 生成最终配置
const configs: Record<string, LandingPageConfig> = characterConfigs.reduce(
  (acc, config, index) => {
    const currentStoryId = getStoryId(config)
    const storyConfig = config.storyId[isTest ? 'test' : 'prod']
    const allStoryIds = getAllStoryIds()

    acc[`character${index + 1}`] = {
      storyId: currentStoryId,
      storyQuote: typeof storyConfig === 'string' ? undefined : storyConfig.quote,
      characterKey: config.key,
      otherStoryIds: allStoryIds.filter((id) => id !== currentStoryId),
      ...(config.showStoryIntro ? { showStoryIntro: true } : {})
    }

    return acc
  },
  {} as Record<string, LandingPageConfig>
)

const configs2: Record<string, LandingPageConfig> = characterConfigs2.reduce(
  (acc, config, index) => {
    const currentStoryId = getStoryId2(config)
    const storyConfig = config.storyId[isTest ? 'test' : 'prod']
    const allStoryIds = getAllStoryIds2()

    acc[`character${index + 1}`] = {
      storyId: currentStoryId,
      storyQuote: typeof storyConfig === 'string' ? undefined : storyConfig.quote,
      characterKey: config.key,
      otherStoryIds: allStoryIds,
      ...(config.showStoryIntro ? { showStoryIntro: true } : {})
    }

    return acc
  },
  {} as Record<string, LandingPageConfig>
)
// 根据角色配置生成路由
const landingRoutes: RouteRecordRaw[] = characterConfigs.map(({ key }) => ({
  path: `/${key}:catchAll(.*)*`,
  name: key,
  component: () => import('../views/landingpage/direct-route.vue'),
  meta: {
    requiresAuth: false,
    showMenu: false
  }
}))

const landingRoutes2: RouteRecordRaw[] = characterConfigs2.map(({ key }) => ({
  path: `/other:catchAll(.*)*`,
  name: key,
  component: () => import('../views/landingpage/index2.vue'),
  meta: {
    requiresAuth: false,
    showMenu: false
  }
}))

export {
  landingRoutes,
  landingRoutes2,
  configs as landingPageConfigs,
  configs2 as landingPageConfigs2
}
