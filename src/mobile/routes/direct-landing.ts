import { RouteRecordRaw } from 'vue-router'
import { characterConfigs, characterConfigs2, landingPageConfigs, landingPageConfigs2 } from './landing'

// 根据角色配置生成直接导航的路由
const directLandingRoutes: RouteRecordRaw[] = characterConfigs.map(({ key }) => ({
  path: `/direct/${key}:catchAll(.*)*`,
  name: `direct-${key}`,
  component: () => import('../views/landingpage/direct-route.vue'),
  meta: {
    requiresAuth: false,
    showMenu: false
  }
}))

const directLandingRoutes2: RouteRecordRaw[] = characterConfigs2.map(({ key }) => ({
  path: `/direct/other:catchAll(.*)*`,
  name: `direct-${key}`,
  component: () => import('../views/landingpage/direct-route.vue'),
  meta: {
    requiresAuth: false,
    showMenu: false
  }
}))

export {
  directLandingRoutes,
  directLandingRoutes2
}
