import { RouteRecordRaw } from 'vue-router'
import { useUserStore } from '@/store/user'
const creationRoutes: RouteRecordRaw[] = [
  {
    path: '/creation',
    component: () => import('@/mobile/views/editor/index.vue'),
    beforeEnter: (to, from, next) => {
      const userStore = useUserStore()
      if (!userStore.isAdmin) {
        next('/')
      } else {
        next()
      }
    },
    children: [
      {
        path: '',
        name: 'creation',
        component: () => import('@/mobile/views/editor/list.vue'),
        meta: {
          title: '故事配置列表',
          layout: 'mobile',
          requiresAuth: true
        }
      },
      {
        path: 'editor/:id',
        name: 'editor',
        component: () => import('@/mobile/views/editor/editor.vue'),
        meta: {
          title: '故事编辑器',
          layout: 'mobile',
          requiresAuth: true,
          showMenu: false
        }
      },
      {
        path: 'flow-editor/:id',
        name: 'flowEditor',
        component: () => import('@/mobile/views/flow-editor/index.vue'),
        meta: {
          requiresAuth: true,
          title: '故事流程编辑器'
        }
      }
    ]
  }
]

export default creationRoutes
