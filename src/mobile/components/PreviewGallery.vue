<template>
  <div class="preview-gallery">
    <div v-for="actor in actors" :key="actor.id" class="character-card">
      <div class="character-image" @click="handleClick(actor)">
        <img v-img-compress :src="actor.preview_url" :alt="actor.name" />
        <div class="character-card-overlay">
          <div class="character-info">
            <img class="character-avatar" :src="actor.avatar_url" :alt="actor.name" />
            <div class="character-name">{{ actor.name }}</div>
          </div>
          <div class="character-subtitle">{{ actor.subtitle }}</div>
          <div class="character-count">💘 {{ actor.count }}</div>
          <div class="chat-button">
            <img src="https://cdn.magiclight.ai/assets/playshot/play.png" alt="Play" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Actor {
  id: string | number
  name: string
  subtitle: string
  preview_url: string
  avatar_url: string
  count: number
}

const props = defineProps<{
  actors: Actor[]
}>()

const emit = defineEmits<{
  (e: 'click', actor: Actor): void
}>()

const handleClick = (actor: Actor) => {
  emit('click', actor)
}
</script>

<style lang="less" scoped>
.preview-gallery {
  min-height: 400px;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  justify-content: space-between;
  margin-bottom: 26px;
}

.character-card {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  width: 100%;
  height: 100%;

  .character-image {
    position: relative;
    width: 100%;
    height: 100%;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .character-card-overlay {
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      gap: 8px;
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 16px;
      background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%);
      color: white;

      .character-avatar {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        object-fit: cover;
      }

      .character-info {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .character-name {
        font-size: 12px;
        font-weight: 600;
      }

      .character-subtitle {
        font-size: 15px;
        font-weight: 600;
        max-width: 110px;
      }

      .chat-button {
        display: inline-flex;
        align-items: center;
        position: absolute;
        right: 4px;
        bottom: 10px;
        img {
          width: 40px;
          height: 40px;
        }
        &:not(:disabled) {
          animation: breathe 2s ease-in-out infinite;
        }
      }
    }
  }
}

@keyframes breathe {
  0% {
    opacity: 0.6;
    transform: scale(0.95);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  100% {
    opacity: 0.6;
    transform: scale(0.95);
  }
}
</style>
