<template>
  <div v-if="visible" class="guide-modal">
    <div class="guide-content">
      <!-- Swiper container -->
      <swiper
        :modules="[Pagination]"
        :pagination="{ clickable: true } as any"
        @slideChange="handleSlideChange"
        @reachEnd="handleReachEnd"
        @swiper="setControlledSwiper"
        ref="swiper"
      >
        <swiper-slide v-for="(image, index) in guideImages" :key="index">
          <div class="slide-content">
            <img :src="image" :alt="'Guide ' + (index + 1)" class="guide-image" />
          </div>
        </swiper-slide>
      </swiper>
      <div class="next-button" @click="handleNext">
        <div class="next-button-text" v-if="swiperIndex < guideImages.length - 1">Next</div>
        <div class="next-button-text" v-else>I Got It</div>
        <div class="play-button">
          <img src="https://cdn.magiclight.ai/assets/playshot/play.png" alt="play" />
        </div>
      </div>
      <!-- Close button moved below -->
      <div class="close-button" @click="handleClose">
        <img src="https://cdn.magiclight.ai/assets/playshot/close.png" alt="close" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Pagination } from 'swiper/modules'
import type { Swiper as SwiperType } from 'swiper'
import 'swiper/css'
import 'swiper/css/pagination'

const props = defineProps<{
  visible: boolean
}>()

const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

const swiperRef = ref<SwiperType | null>(null)
const swiperIndex = ref(0)

const setControlledSwiper = (swiper: SwiperType) => {
  swiperRef.value = swiper
}

// Guide images array
const guideImages = [
  'https://cdn.magiclight.ai/assets/playshot/guide-1.png',
  'https://cdn.magiclight.ai/assets/playshot/guide-2.png',
  'https://cdn.magiclight.ai/assets/playshot/guide-3.png'
]

const handleClose = () => {
  emit('update:visible', false)
}

const handleNext = () => {
  if (swiperRef.value) {
    swiperIndex.value++
    swiperRef.value.slideTo(swiperIndex.value)
  }
  if (swiperIndex.value === guideImages.length) {
    handleClose()
  }
}

const handleSlideChange = (swiper: SwiperType) => {
  swiperIndex.value = swiper.activeIndex
}

const handleReachEnd = () => {
  // setTimeout(() => {
  //   handleClose()
  // }, 500)
}
</script>

<style lang="less" scoped>
@import '@/assets/style/mixin.less';
.guide-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(8px);
}

.guide-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

:deep(.swiper) {
  width: 100%;
  height: 65vh;
  display: flex;
  align-items: center;
  margin-bottom: 5vh;
}

:deep(.swiper-wrapper) {
  height: 100%;
  display: flex;
  align-items: center;
}

:deep(.swiper-slide) {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 0 5vw;
}

.slide-content {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;

  .guide-image {
    width: auto;
    height: auto;
    max-width: 90vw;
    max-height: 60vh;
    object-fit: contain;
  }
}

.next-button {
  position: absolute;
  bottom: 12vh;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  width: 75vw;
  max-width: 283px;
  height: 40px;
  padding: 0px 12px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  flex-shrink: 0;
  border-radius: 40px;
  background: #ca93f2;
  color: #fff;
  cursor: pointer;
  z-index: 10;
  font-size: 16px;
  font-weight: 500;
  color: #241d49;
  font-size: 15px;
  font-weight: 600;
}
.play-button {
  display: inline-flex;
  align-items: center;
  position: absolute;
  left: 65%;
  // bottom: 10px;
  img {
    width: 30px;
    height: 30px;
  }

  .breathing-animation();
}

.close-button {
  position: absolute;
  bottom: 6vh;
  left: 50%;
  transform: translateX(-50%);
  width: 32px;
  height: 32px;
  z-index: 10;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

// :deep(.swiper-pagination) {
//   bottom: 23vh !important;
// }

:deep(.swiper-pagination-bullet) {
  width: 8px;
  height: 8px;
  background: rgba(255, 255, 255, 0.4);
  opacity: 1;
  margin: 0 6px !important;
}

:deep(.swiper-pagination-bullet-active) {
  background: #fff;
  border-radius: 4px;
}
</style>
