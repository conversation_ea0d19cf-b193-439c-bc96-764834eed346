<template>
  <div class="invite-code-modal" v-if="visible">
    <div class="modal-overlay" @click="handleClose"></div>
    <div class="modal-content">
      <div class="modal-header">
        <h3 class="modal-title">Your Invite Link</h3>
        <button class="close-button" @click="handleClose">×</button>
      </div>
      <div class="modal-body">
        <p class="invite-description">
          Share this link with your friends and earn rewards when they sign up!
        </p>

        <div class="invite-code-container">
          <div class="invite-link" @click="copyCode">
            {{ getInviteLink() }}
            <div class="copy-hint">Tap to copy link</div>
          </div>
        </div>

        <div class="share-options">
          <button class="share-button" @click="handleShare">
            <!-- <span class="share-icon">📤</span> -->
            Share
          </button>
          <button class="copy-button" @click="copyCode">
            <!-- <span class="copy-icon">📋</span> -->
            Copy
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'
import { Message } from '@/mobile/components/Message'

const props = defineProps<{
  visible: boolean
  inviteCode: string
}>()

const emit = defineEmits<{
  (e: 'close'): void
  (e: 'copy'): void
  (e: 'share'): void
}>()

const handleClose = () => {
  emit('close')
}

const getInviteLink = () => {
  if (!props.inviteCode) return ''
  // 创建带有邀请码参数的完整链接
  const baseUrl = window.location.origin
  return `${baseUrl}?invite=${props.inviteCode}`
}

const copyCode = async () => {
  if (!props.inviteCode) return

  try {
    // 复制完整的邀请链接而不仅仅是邀请码
    const inviteLink = getInviteLink()
    await navigator.clipboard.writeText(inviteLink)
    Message.success('Invite link copied to clipboard!')
    emit('copy')
  } catch (err) {
    console.error('Failed to copy invite link:', err)
    Message.error('Failed to copy invite link')
  }
}

const handleShare = async () => {
  if (!props.inviteCode) return

  // 获取完整的邀请链接
  const inviteLink = getInviteLink()

  // 如果支持网页分享API
  if (navigator.share) {
    try {
      await navigator.share({
        title: 'Join me on Magic Partner!',
        text: `Use my invite link to sign up and get rewards!`,
        url: inviteLink
      })
      emit('share')
    } catch (err) {
      console.error('Failed to share:', err)
      // 如果用户取消分享，不显示错误
      if (err.name !== 'AbortError') {
        Message.error('Failed to share')
      }
    }
  } else {
    // 如果不支持分享API，则复制到剪贴板
    copyCode()
  }
}
</script>

<style lang="less" scoped>
.invite-code-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;

  .modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
  }

  .modal-content {
    position: relative;
    width: 90%;
    max-width: 360px;
    background: #261742;
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    z-index: 1001;

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .modal-title {
        font-size: 18px;
        font-weight: 600;
        color: #fff;
        margin: 0;
      }

      .close-button {
        background: none;
        border: none;
        color: rgba(255, 255, 255, 0.7);
        font-size: 24px;
        cursor: pointer;
        padding: 0;
        line-height: 1;
      }
    }

    .modal-body {
      .invite-description {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 20px;
        text-align: center;
      }

      .invite-code-container {
        margin-bottom: 24px;

        .invite-link {
          background: rgba(202, 147, 242, 0.15);
          border: 1px dashed #ca93f2;
          border-radius: 8px;
          padding: 16px;
          text-align: center;
          font-size: 16px;
          color: #ca93f2;
          position: relative;
          cursor: pointer;
          word-break: break-all;
          overflow: hidden;

          .copy-hint {
            position: absolute;
            bottom: 4px;
            right: 8px;
            font-size: 10px;
            color: rgba(255, 255, 255, 0.6);
            font-weight: normal;
          }

          &:active {
            background: rgba(202, 147, 242, 0.25);
          }
        }
      }

      .share-options {
        display: flex;
        gap: 12px;

        button {
          flex: 1;
          height: 44px;
          border-radius: 22px;
          border: none;
          font-size: 14px;
          font-weight: 600;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;

          &.share-button {
            background: #ca93f2;
            color: #1f0038;
          }

          &.copy-button {
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
          }
        }
      }
    }
  }
}
</style>
