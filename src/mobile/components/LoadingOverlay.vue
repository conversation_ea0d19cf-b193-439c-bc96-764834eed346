<template>
  <div class="loading-overlay" :style="overlayStyle">
    <div class="loading-container">
      <div ref="loadingIconRef" class="loading-icon">
        <IconLoading />
      </div>
      <div ref="loadingTextRef" class="loading-text">LOADING</div>
      <div v-if="showProgress" class="progress-text">{{ progress }}%</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, computed } from 'vue'
import IconLoading from '@/assets/icon/loading-icon.svg'
import { animate } from 'motion'

const props = defineProps<{
  progress?: number
  showProgress?: boolean
  backgroundImage?: string
}>()

const loadingIconRef = ref<HTMLElement | null>(null)
const loadingTextRef = ref<HTMLElement | null>(null)
let loadingAnimation: { stop: () => void } | null = null

const overlayStyle = computed(() => ({
  backgroundImage: props.backgroundImage ? `url(${props.backgroundImage})` : 'none',
  backgroundSize: 'cover',
  backgroundPosition: 'center',
  backgroundRepeat: 'no-repeat'
}))

const initLoadingAnimation = () => {
  if (!loadingIconRef.value || !loadingTextRef.value) return null

  let lastTriggerTime = 0
  let textAnimating = false

  // Icon animation - 使用精确的关键帧确保上下速度一致
  const iconAnimation = animate(
    loadingIconRef.value,
    {
      transform: ['translateY(-24px)', 'translateY(0)', 'translateY(0)', 'translateY(-24px)']
    },
    {
      duration: 1.2,
      repeat: Infinity,
      easing: [0.5, 0, 0.5, 1], // 使用线性贝塞尔曲线
      times: [0, 0.4, 0.6, 1], // 精确控制每个关键帧的时间点
      onUpdate: (latest) => {
        // 从 transform 字符串中提取 Y 值
        const match = latest.match(/-?\d+/)
        const y = match ? parseInt(match[0]) : 0

        // 当图标接近底部且文字没有在动画中时触发挤压效果
        if (y > -2 && y < 2 && !textAnimating && Date.now() - lastTriggerTime > 1000) {
          textAnimating = true
          lastTriggerTime = Date.now()

          // 挤压效果
          animate(
            loadingTextRef.value!,
            {
              transform: ['scale(1, 1)', 'scale(1.3, 0.7)']
            },
            {
              duration: 0.1,
              easing: [0.87, 0, 0.13, 1]
            }
          ).then(() => {
            // 弹性恢复
            animate(
              loadingTextRef.value!,
              {
                transform: ['scale(1.3, 0.7)', 'scale(0.85, 1.15)', 'scale(1, 1)']
              },
              {
                duration: 0.35,
                easing: [0.34, 1.56, 0.64, 1]
              }
            ).then(() => {
              textAnimating = false
            })
          })
        }
      }
    }
  )

  return {
    stop: () => {
      iconAnimation.stop()
    }
  }
}

onMounted(() => {
  loadingAnimation = initLoadingAnimation()
})

onBeforeUnmount(() => {
  if (loadingAnimation) {
    loadingAnimation.stop()
  }
})
</script>

<style lang="less" scoped>
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7); // Fallback background color
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.15); // Overlay on top of the background image
    backdrop-filter: blur(8px);
    z-index: -1;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 32px;
  border-radius: 20px;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8px);
  text-align: center;
  width: 170px;
  height: 170px;
}

.loading-icon {
  width: 44px;
  height: 44px;
  will-change: transform;
  margin-bottom: 4px;
  display: flex;
  justify-content: center;
  align-items: center;

  :deep(svg) {
    width: 100%;
    height: 100%;
  }
}

.loading-text {
  color: #fff;
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 2px;
  will-change: transform;
  transform-origin: center;
  margin-bottom: 8px;
}

.progress-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 16px;
  font-weight: 600;
  font-family: 'Work Sans';
  letter-spacing: 0.5px;
}
</style>
