import { App, createApp } from 'vue'
import MessageComponent from './index.vue'
import router from '@/router'

let messageInstance: any = null

const createInstance = () => {
  if (!messageInstance) {
    const messageApp = createApp(MessageComponent)
    const mountNode = document.createElement('div')
    document.body.appendChild(mountNode)
    messageInstance = messageApp.mount(mountNode) as any

    // 添加路由监听，在路由变化时清除所有消息
    router.beforeEach((_to, _from, next) => {
      if (messageInstance) {
        messageInstance.clear()
      }
      next()
    })
  }
  return messageInstance
}

export const Message: any = {
  success(content: string, duration?: number) {
    const instance = createInstance()
    instance.success(content, duration)
  },
  error(content: string, duration?: number) {
    const instance = createInstance()
    instance.error(content, duration)
  },
  info(content: string, duration?: number) {
    const instance = createInstance()
    instance.info(content, duration)
  },
  warning(content: string, duration?: number) {
    const instance = createInstance()
    instance.warning(content, duration)
  },
  loading(content: string, duration?: number) {
    const instance = createInstance()
    instance.loading(content, duration)
  },
  normal(content: string, duration?: number) {
    const instance = createInstance()
    instance.normal(content, duration)
  },
  clear() {
    const instance = createInstance()
    instance.clear()
  }
}

export default {
  install(app: App) {
    app.config.globalProperties.$message = Message
  }
}
