<template>
  <div class="menu-item" :class="{ danger }" @click="handleClick">
    <slot></slot>
  </div>
</template>

<script setup lang="ts">
import { tooltipState } from './tooltipState'

defineProps<{
  danger?: boolean
}>()

const emit = defineEmits<{
  (e: 'click'): void
}>()

const handleClick = () => {
  // 关闭当前打开的 tooltip
  if (tooltipState.close) {
    tooltipState.close()
  }
  // 触发点击事件
  emit('click')
}
</script>

<style lang="less" scoped>
.menu-item {
  padding: 8px 16px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;

  &:hover {
    background: rgba(204, 213, 255, 0.05);
    color: #fff;
  }

  &.danger {
    color: #ff4d4f;

    &:hover {
      background: rgba(255, 77, 79, 0.1);
    }
  }
}
</style>
