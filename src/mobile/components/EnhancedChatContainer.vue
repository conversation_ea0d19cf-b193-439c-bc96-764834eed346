<template>
  <div class="enhanced-chat-container">
    <!-- 背景层 - 始终显示，但在播放视频时降低z-index -->
    <EnhancedBackground
      ref="backgroundRef"
      :background-video="backgroundVideo"
      :background-image="backgroundImage"
      :default-image="defaultImage"
      :animated-images="animatedImages"
      :transition-mode="transitionMode"
      :class="{
        'background-component': true,
        'background-behind-video': isPlayingVideo
      }"
      @resource-loading="handleResourceLoading"
      @transition-complete="handleTransitionComplete"
      @animated-image-complete="handleAnimatedImageComplete"
    />

    <!-- 视频播放器 -->
    <div
      v-show="isPlayingVideo"
      ref="videoContainerRef"
      class="video-container"
      :class="{ 'fade-in': isVideoFadingIn, 'fade-out': isVideoFadingOut }"
    >
      <slot name="video-player"></slot>
    </div>

    <!-- 聊天界面 -->
    <div
      v-show="!isPlayingVideo"
      ref="chatInterfaceRef"
      class="chat-interface"
      :class="{ 'fade-in': isChatFadingIn, 'fade-out': isChatFadingOut }"
    >
      <slot name="chat-interface"></slot>
    </div>

    <!-- 解锁按钮层 - 独立层级，与背景和聊天界面同级 -->
    <div v-if="hasUnlockButton" class="unlock-button-layer">
      <button class="unlock-button" @click="handleUnlockClick">
        <div class="unlock-content">
          <div class="unlock-icon">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path
                d="M6 10V8C6 5.79086 7.79086 4 10 4H14C16.2091 4 18 5.79086 18 8V10"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
              />
              <rect
                x="4"
                y="10"
                width="16"
                height="10"
                rx="2"
                stroke="currentColor"
                stroke-width="2"
              />
              <circle cx="12" cy="15" r="1" fill="currentColor" />
            </svg>
          </div>
          <span class="unlock-text">Unlock</span>
          <div class="unlock-cost">
            <span class="cost-amount">{{ unlockCost }}</span>
            <img
              src="https://cdn.magiclight.ai/assets/mobile/diamond.png"
              alt="Diamond"
              class="diamond-icon"
            />
          </div>
        </div>
      </button>
    </div>

    <!-- 加载指示器 -->
    <div v-if="isLoading" class="loading-indicator">
      <div class="loading-spinner"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, ref, watch, onBeforeUnmount, PropType, computed } from 'vue'
import { animate } from 'animejs'
type AnimeInstance = any // 简化类型定义
import EnhancedBackground from '@/mobile/views/chat2/components/ChatBackground/EnhancedBackground.vue'
import { TransitionMode } from '@/mobile/composables/useBackgroundTransitionManager'
import { useChat4Store } from '@/store/chat4'
import { useChatResourcesStore } from '@/store/chat-resources'

// Store引用
const chat4Store = useChat4Store()
const chatResourcesStore = useChatResourcesStore()

// 引用DOM元素
const backgroundRef = ref<InstanceType<typeof EnhancedBackground> | null>(null)
const videoContainerRef = ref<HTMLElement | null>(null)
const chatInterfaceRef = ref<HTMLElement | null>(null)

// 动画状态
const isVideoFadingIn = ref(false)
const isVideoFadingOut = ref(false)
const isChatFadingIn = ref(false)
const isChatFadingOut = ref(false)

// 动画实例
let videoAnimation: AnimeInstance | null = null
let chatAnimation: AnimeInstance | null = null

// 加载状态
const isLoading = ref(false)
const isBackgroundLoading = ref(false)
const isTransitionComplete = ref(true)

// 计算属性
const hasUnlockButton = computed(() => {
  const blurState = chatResourcesStore.backgroundBlurState
  return (
    blurState.isBlurred &&
    blurState.tag &&
    blurState.isBlurRequired &&
    blurState.requiredHeartValue > blurState.currentHeartValue
  )
})

const unlockCost = computed(() => {
  const blurState = chatResourcesStore.backgroundBlurState
  return Math.max(0, blurState.requiredHeartValue - blurState.currentHeartValue)
})

// 组件属性
const props = defineProps({
  isPlayingVideo: {
    type: Boolean,
    default: false
  },
  backgroundVideo: {
    type: String,
    default: ''
  },
  backgroundImage: {
    type: String,
    default: ''
  },
  defaultImage: {
    type: String,
    default: ''
  },
  animatedImages: {
    type: Array as PropType<string[]>,
    default: () => []
  },
  transitionMode: {
    type: String as PropType<TransitionMode>,
    default: 'fade'
  }
})

// 组件事件
const emit = defineEmits<{
  (e: 'transition-complete'): void
  (e: 'animated-image-complete'): void
}>()

// 计算属性
const isResourceLoading = computed(() => {
  return isBackgroundLoading.value || !isTransitionComplete.value
})

// 处理背景资源加载状态变化
const handleResourceLoading = (loading: boolean) => {
  isBackgroundLoading.value = loading
  updateLoadingState()
}

// 处理背景过渡完成
const handleTransitionComplete = () => {
  isTransitionComplete.value = true
  updateLoadingState()
  emit('transition-complete')
}

// 处理动画图片序列完成
const handleAnimatedImageComplete = () => {
  emit('animated-image-complete')
}

// 解锁按钮点击处理
const handleUnlockClick = () => {
  const blurState = chatResourcesStore.backgroundBlurState
  if (blurState.tag && unlockCost.value > 0) {
    console.log('Unlock button clicked:', {
      tag: blurState.tag,
      required: blurState.requiredHeartValue,
      current: blurState.currentHeartValue,
      cost: unlockCost.value
    })
    chat4Store.requestUnlockBlur(blurState.tag, unlockCost.value)
  }
}

// 更新加载状态
const updateLoadingState = () => {
  isLoading.value = isResourceLoading.value
}

// 监听视频播放状态变化
watch(
  () => props.isPlayingVideo,
  (isPlaying, prevIsPlaying) => {
    if (isPlaying === prevIsPlaying) return

    if (isPlaying) {
      // 从聊天界面切换到视频播放
      playToVideoAnimation()
    } else {
      // 从视频播放切换到聊天界面
      playToChatAnimation()
    }
  }
)

// 切换到视频播放的动画 - 优化版本
const playToVideoAnimation = () => {
  // 重置动画状态
  isVideoFadingIn.value = true
  isVideoFadingOut.value = false
  isChatFadingIn.value = false
  isChatFadingOut.value = true

  // 停止之前的动画
  if (videoAnimation) {
    videoAnimation.pause()
    videoAnimation = null
  }
  if (chatAnimation) {
    chatAnimation.pause()
    chatAnimation = null
  }

  // 确保视频容器可见并设置初始状态
  if (videoContainerRef.value) {
    videoContainerRef.value.style.display = 'block'
    videoContainerRef.value.style.opacity = '0'
    videoContainerRef.value.style.transform = 'scale(0.95)'
    videoContainerRef.value.style.willChange = 'transform, opacity'
  }

  // 使用 requestAnimationFrame 确保 DOM 更新
  requestAnimationFrame(() => {
    // 聊天界面淡出动画 - 更快的退出动画
    if (chatInterfaceRef.value) {
      chatInterfaceRef.value.style.willChange = 'transform, opacity'
      chatAnimation = animate(chatInterfaceRef.value, {
        opacity: [1, 0],
        translateY: [0, 15],
        duration: 350, // 减少动画时间
        easing: 'easeOutQuart',
        complete: () => {
          isChatFadingOut.value = false
          if (chatInterfaceRef.value) {
            chatInterfaceRef.value.style.display = 'none'
            chatInterfaceRef.value.style.willChange = 'auto'
          }
        }
      })
    }

    // 视频容器淡入动画 - 更流畅的进入动画
    if (videoContainerRef.value) {
      videoAnimation = animate(videoContainerRef.value, {
        opacity: [0, 1],
        scale: [0.95, 1],
        duration: 600, // 减少动画时间
        easing: 'easeOutCubic',
        delay: 200, // 减少延迟
        complete: () => {
          isVideoFadingIn.value = false
          if (videoContainerRef.value) {
            videoContainerRef.value.style.willChange = 'auto'
          }
        }
      })
    }
  })
}

// 切换到聊天界面的动画 - 与视频淡出效果协调，实现无缝衔接
const playToChatAnimation = () => {
  // 重置动画状态
  isVideoFadingIn.value = false
  isVideoFadingOut.value = true
  isChatFadingIn.value = true
  isChatFadingOut.value = false

  // 停止之前的动画
  if (videoAnimation) {
    videoAnimation.pause()
    videoAnimation = null
  }
  if (chatAnimation) {
    chatAnimation.pause()
    chatAnimation = null
  }

  // 确保聊天界面可见并设置初始状态
  if (chatInterfaceRef.value) {
    chatInterfaceRef.value.style.display = 'block'
    chatInterfaceRef.value.style.opacity = '0'
    chatInterfaceRef.value.style.transform = 'translateY(15px)'
    chatInterfaceRef.value.style.willChange = 'transform, opacity'
  }

  // 使用 requestAnimationFrame 确保 DOM 更新
  requestAnimationFrame(() => {
    // 优化：视频容器延迟淡出，让视频内部的淡出动画先完成
    if (videoContainerRef.value) {
      videoContainerRef.value.style.willChange = 'opacity'

      // 延迟容器淡出，让视频内部动画先执行
      setTimeout(() => {
        if (videoContainerRef.value) {
          videoAnimation = animate(videoContainerRef.value, {
            opacity: [1, 0],
            duration: 400, // 快速淡出容器
            easing: 'easeOutQuart',
            complete: () => {
              isVideoFadingOut.value = false
              if (videoContainerRef.value) {
                videoContainerRef.value.style.display = 'none'
                videoContainerRef.value.style.willChange = 'auto'
                // 重置可能的变形
                videoContainerRef.value.style.transform = 'none'
              }
            }
          })
        }
      }, 400) // 延迟400ms，让视频内部动画先执行
    }

    // 聊天界面淡入动画 - 与视频淡出同步开始
    if (chatInterfaceRef.value) {
      chatAnimation = animate(chatInterfaceRef.value, {
        opacity: [0, 1],
        translateY: [15, 0],
        duration: 600, // 与视频淡出时间匹配
        easing: 'easeOutCubic',
        delay: 200, // 稍微延迟，让背景切换先开始
        complete: () => {
          isChatFadingIn.value = false
          if (chatInterfaceRef.value) {
            chatInterfaceRef.value.style.willChange = 'auto'
          }
        }
      })
    }
  })
}

// 清理动画实例
onBeforeUnmount(() => {
  if (videoAnimation) {
    videoAnimation.pause()
    videoAnimation = null
  }
  if (chatAnimation) {
    chatAnimation.pause()
    chatAnimation = null
  }
})
</script>

<style lang="less" scoped>
.enhanced-chat-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;

  .background-component {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    will-change: transform, opacity;
    transition: z-index 0.1s;

    &.background-behind-video {
      z-index: 0; /* 在视频播放时降低z-index，使其位于视频层之下 */
    }
  }

  .video-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    will-change: opacity; /* 优化：只变化透明度，避免变形冲突 */
    transform-origin: center center;

    &.fade-in {
      animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    }

    &.fade-out {
      animation: fadeOut 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    }
  }

  .chat-interface {
    position: relative;
    width: 100%;
    height: 100%;
    z-index: 3;
    will-change: transform, opacity;
    pointer-events: auto;

    &.fade-in {
      animation: fadeIn 0.7s cubic-bezier(0.4, 0, 0.2, 1) 0.2s forwards;
    }

    &.fade-out {
      animation: fadeOut 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    }
  }

  .unlock-button-layer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    pointer-events: none;
    display: flex;
    align-items: center;
    justify-content: center;

    .unlock-button {
      position: relative;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      border-radius: 20px;
      color: white;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      min-width: 160px;
      padding: 0;
      overflow: hidden;
      pointer-events: auto;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
      }

      &:hover {
        transform: translateY(-3px) scale(1.02);
        box-shadow: 0 12px 40px rgba(102, 126, 234, 0.6);

        &::before {
          left: 100%;
        }
      }

      &:active {
        transform: translateY(-1px) scale(0.98);
        box-shadow: 0 6px 24px rgba(102, 126, 234, 0.4);
      }
    }

    .unlock-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 18px 24px;
      position: relative;
      z-index: 1;
    }

    .unlock-icon {
      margin-bottom: 8px;
      opacity: 0.95;

      svg {
        width: 20px;
        height: 20px;
        stroke: currentColor;
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
      }
    }

    .unlock-text {
      font-size: 15px;
      font-weight: 700;
      margin-bottom: 8px;
      text-align: center;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
      letter-spacing: 0.5px;
    }

    .unlock-cost {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
      background: rgba(255, 255, 255, 0.25);
      padding: 6px 12px;
      border-radius: 12px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.3);

      .cost-amount {
        font-size: 14px;
        font-weight: 700;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
      }

      .diamond-icon {
        width: 16px;
        height: 16px;
        filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
      }
    }
  }

  .loading-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
  }

  .loading-spinner {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top-color: #fff;
    animation: spin 1s linear infinite;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
