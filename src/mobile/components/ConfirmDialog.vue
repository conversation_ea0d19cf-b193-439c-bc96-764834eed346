<template>
  <BaseDrawer v-model:visible="showDialog" height="auto">
    <div class="confirm-dialog">
      <div class="dialog-header">
        <div class="dialog-title">
          <slot name="icon"></slot>
          <span>{{ title }}</span>
        </div>
      </div>
      <div class="dialog-content">
        <slot name="content">
          <p class="content-text">{{ content }}</p>
        </slot>
        <div class="dialog-footer">
          <button class="dialog-button cancel" @click="handleCancel">{{ cancelText }}</button>
          <button class="dialog-button confirm" @click="handleConfirm">{{ confirmText }}</button>
        </div>
      </div>
    </div>
  </BaseDrawer>
</template>

<script setup lang="ts">
import BaseDrawer from '@/mobile/components/BaseDrawer.vue'
import { ref } from 'vue'
const props = defineProps<{
  title: string
  content?: string
  confirmText?: string
  cancelText?: string
  showCancel?: boolean
  onBeforeConfirm?: () => boolean
}>()

const showDialog = ref(false)

const emit = defineEmits<{
  (e: 'confirm'): void
  (e: 'cancel'): void
  (e: 'update:visible', value: boolean): void
}>()

const handleConfirm = () => {
  if (props.onBeforeConfirm && !props.onBeforeConfirm()) {
    return
  }
  emit('confirm')
  showDialog.value = false
  emit('update:visible', false)
}

const handleCancel = () => {
  showDialog.value = false
  emit('cancel')
  emit('update:visible', false)
}
</script>

<style lang="less" scoped>
.confirm-dialog {
  .dialog-header {
    margin-bottom: 24px;

    .dialog-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 17px;
      font-weight: 600;
      color: #fff;

      .arco-icon {
        font-size: 20px;
        color: #ca93f2;
      }
    }
  }

  .dialog-content {
    margin-bottom: 32px;

    .content-text {
      margin: 0;
      font-size: 15px;
      color: rgba(255, 255, 255, 0.8);
      line-height: 1.6;
    }
  }

  .dialog-footer {
    display: flex;
    gap: 12px;

    .dialog-button {
      flex: 1;
      padding: 12px 24px;
      border-radius: 12px;
      font-size: 15px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      border: none;

      &.cancel {
        background: rgba(255, 255, 255, 0.05);
        color: rgba(255, 255, 255, 0.8);
        border: 1px solid rgba(255, 255, 255, 0.1);

        &:active {
          background: rgba(255, 255, 255, 0.08);
        }
      }

      &.confirm {
        background: #ca93f2;
        color: #1f0038;

        &:active {
          background: #b77de0;
        }
      }
    }
  }
}
</style>
