<template>
  <div v-if="genderStore.showModal" class="gender-modal">
    <div class="content">
      <div class="logo">
        <img :src="logoUrl" alt="Reelplay" />
      </div>

      <div class="card">
        <h1>Please select the type you're interested in.</h1>
        <p class="description">
          We'll recommend content that suits your taste based on your choice.
        </p>

        <div class="options">
          <button
            class="option-button"
            :class="{ active: selectedGender === 'male' }"
            @click="handleSelect('male')"
          >
            <div class="icon">
              <icon-male />
            </div>
            For Male Players
          </button>

          <button
            class="option-button"
            :class="{ active: selectedGender === 'female' }"
            @click="handleSelect('female')"
          >
            <div class="icon">
              <icon-female />
            </div>
            For Female Players
          </button>
        </div>

        <button class="skip-button" @click="handleCancel">Skip for Now</button>

        <p class="hint">You can change your preference later in the settings.</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useGenderStore } from '@/store/gender'
import IconMale from '@/assets/icon/male.svg'
import IconFemale from '@/assets/icon/female.svg'

const logoUrl = computed(
  () => import.meta.env.VITE_LOGO_URL || 'https://cdn.magiclight.ai/assets/playshot/logo-v2.png'
)

const genderStore = useGenderStore()
const selectedGender = ref('')

const props = defineProps<{
  visible: boolean
}>()

const emits = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'select', value: string): void
}>()

const selected = ref('')

const handleSelect = (gender: string) => {
  selected.value = gender
  localStorage.setItem('user_gender', gender)
  emits('select', gender)
  emits('update:visible', false)
}

const handleCancel = () => {
  handleSelect('unknown')
}
</script>

<style lang="less" scoped>
.gender-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
  z-index: 1000;
  background: #1a0831;
  background-image: url('https://cdn.magiclight.ai/assets/mobile/gender-bg.png');
  background-repeat: no-repeat;
  background-size: 100% auto;
  background-position: top center;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    // background: linear-gradient(180deg, rgba(26, 8, 49, 0.2) 0%, #1a0831 100%);
  }
}

.content {
  position: relative;
  z-index: 1;
  padding: 48px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  justify-content: center;
}

.logo {
  margin-bottom: 32px;
  img {
    height: 40px;
  }
}

.card {
  background: #1f0038;
  border-radius: 24px;
  padding: 32px 20px;
  width: 100%;
  max-width: 400px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

h1 {
  color: #fff;

  text-align: center;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
}

.description {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.6);
  margin: 0 0 10px;
  line-height: 1.5;
  text-align: center;
}

.options {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
  margin-bottom: 24px;
}

.option-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  background: #411d5d;
  border: 1px solid transparent;
  border-radius: 12px;
  padding: 20px;
  width: 100%;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 15px;
  font-weight: 600;
  color: #ca93f2;

  .icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.8;
  }

  &:active {
    transform: scale(0.98);
  }

  &.active {
    background: rgba(202, 147, 242, 0.1);
    border-color: #ca93f2;

    .icon {
      opacity: 1;
    }
  }
}

.skip-button {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 12px;
  font-size: 16px;
  padding: 20px;
  cursor: pointer;
  width: 100%;
  margin-top: auto;
  margin-bottom: 24px;
  transition: all 0.2s ease;
  color: #fff;
  font-weight: 500;
  &:active {
    transform: scale(0.98);
    background: rgba(255, 255, 255, 0.15);
  }
}

.hint {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.4);
  margin: 0;
  text-align: center;
}
</style>
