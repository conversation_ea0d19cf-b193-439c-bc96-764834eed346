<template>
  <EnhancedChatContainer
    :is-playing-video="isPlayingVideo"
    :background-video="backgroundVideo"
    :background-image="backgroundImage"
    :default-image="defaultImage"
    :animated-images="animatedImages"
    :transition-mode="transitionMode"
    @transition-complete="handleTransitionComplete"
    @animated-image-complete="handleAnimatedImageComplete"
  >
    <template #video-player>
      <slot name="video-player"></slot>
    </template>

    <template #chat-interface>
      <slot name="chat-interface"></slot>
    </template>
  </EnhancedChatContainer>
</template>

<script setup lang="ts">
import { defineEmits, PropType } from 'vue'
import EnhancedChatContainer from '@/mobile/components/EnhancedChatContainer.vue'
import { TransitionMode } from '@/mobile/composables/useBackgroundTransitionManager'

// 组件属性
defineProps({
  isPlayingVideo: {
    type: Boolean,
    default: false
  },
  backgroundVideo: {
    type: String,
    default: ''
  },
  backgroundImage: {
    type: String,
    default: ''
  },
  defaultImage: {
    type: String,
    default: ''
  },
  animatedImages: {
    type: Array as PropType<string[]>,
    default: () => []
  },
  transitionMode: {
    type: String as PropType<TransitionMode>,
    default: 'fade'
  }
})

// 组件事件
const emit = defineEmits<{
  (e: 'transition-complete'): void
  (e: 'animated-image-complete'): void
}>()

// 处理过渡完成事件
const handleTransitionComplete = () => {
  emit('transition-complete')
}

// 处理动画图片序列完成事件
const handleAnimatedImageComplete = () => {
  emit('animated-image-complete')
}
</script>
