<template>
  <Teleport to="body">
    <div v-if="checkinStore.visible" class="checkin-modal">
      <div class="modal-mask"></div>
      <div class="modal-content">
        <div class="close-btn" @click.stop="handleClose">
          <icon-close />
        </div>

        <h2 class="title">Your Daily Diamond Awaits!</h2>

        <div class="rewards-grid">
          <div
            v-for="[key, reward] in Object.entries(sysConfigStore.checkInCoinsPerDay)"
            :key="key"
            class="reward-item"
            :class="{
              'is-today': checkinStore.getDayNumber(key) === checkinStore.currentDay,
              'is-claimed': checkinStore.getDayNumber(key) < checkinStore.currentDay
            }"
          >
            <div class="day-label">Day {{ checkinStore.getDayNumber(key) }}</div>
            <div class="diamond-icon">
              <img
                v-if="checkinStore.getDayNumber(key) !== 7"
                class="credit-icon"
                src="https://cdn.magiclight.ai/assets/mobile/diamond.png"
              />
              <img
                v-else
                class="credit-icon"
                src="https://static.playshot.ai/static/images/checkin/diamonds.png"
              />
            </div>
            <div class="amount">
              <template
                v-if="
                  checkinStore.getDayNumber(key) < checkinStore.currentDay ||
                  (checkinStore.getDayNumber(key) === checkinStore.currentDay &&
                    checkinStore.todayClaimed)
                "
              >
                <span class="taken-text">Taken ✓</span>
              </template>
              <template v-else>
                {{ reward }}
              </template>
            </div>
          </div>
        </div>

        <button
          class="claim-button"
          :class="{ 'is-claimed': checkinStore.todayClaimed }"
          :disabled="checkinStore.loading"
          @click.stop="handleClaim"
        >
          <template v-if="checkinStore.loading">
            <a-spin :size="18" />
          </template>
          <template v-else-if="checkinStore.todayClaimed"> Back tomorrow for more! </template>
          <template v-else> Get Today's Reward 🎁 </template>
        </button>

        <div class="view-tasks-link" @click="handleViewTasks">View all daily tasks</div>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { watch } from 'vue'
import { useRouter } from 'vue-router'
import { IconClose } from '@arco-design/web-vue/es/icon'
import { useSysConfigStore } from '@/store/sysconfig'
import { useCheckinStore } from '@/store/checkin'
import { reportEvent } from '@/utils'
import { ReportEvent } from '@/interface'

const router = useRouter()
const sysConfigStore = useSysConfigStore()
const checkinStore = useCheckinStore()

const emit = defineEmits<{
  (e: 'claim'): void
}>()

const handleClose = () => {
  reportEvent(ReportEvent.CheckinModalClose)
  checkinStore.hideModal()
}

const handleViewTasks = () => {
  checkinStore.hideModal()
  router.push('/daily-tasks')
}

const handleClaim = async () => {
  if (checkinStore.todayClaimed) {
    checkinStore.hideModal()
    return
  }
  const entry = Object.entries(sysConfigStore.checkInCoinsPerDay).find(
    ([key, reward]) => checkinStore.getDayNumber(key) === checkinStore.currentDay
  )
  const reward = entry ? entry[1] : 0
  reportEvent(ReportEvent.CheckinModalClaimClick, {
    day: checkinStore.currentDay,
    reward: reward
  })
  const success = await checkinStore.claimDailyReward()
  if (success) {
    emit('claim')
  }
}

watch(
  () => checkinStore.visible,
  (newVal) => {
    if (newVal) {
      reportEvent(ReportEvent.CheckinModalOpen)
    }
  }
)
</script>

<style lang="less" scoped>
.checkin-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;

  .modal-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
  }

  .modal-content {
    position: relative;
    width: 90%;
    max-width: 460px;
    background: #200238;
    border-radius: 16px;
    padding: 20px 16px 16px;
    z-index: 1;

    .close-btn {
      position: absolute;
      top: 12px;
      right: 12px;
      cursor: pointer;
      color: rgba(255, 255, 255, 0.6);

      &:hover {
        color: rgba(255, 255, 255, 0.8);
      }
    }

    .title {
      text-align: center;
      font-size: 15px;
      font-weight: 600;
      color: #fff;
      margin: 0 0 12px;
    }

    .rewards-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 8px;
      margin-bottom: 16px;

      .reward-item {
        position: relative;
        text-align: center;
        transition: all 0.3s;
        aspect-ratio: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 12px 0;
        border-radius: 20px;
        border: 2px solid #000;
        opacity: 0.5;
        background: #411d5d;
        &.is-today {
          background: #411d5d;
          box-shadow: 0px 0px 6px 0px #ec91ff;
          border: 2px solid #f0a8ff;
          opacity: 1;
        }

        .day-label {
          color: #efd9ff;
          font-size: 12px;
          font-weight: 600;
          margin-bottom: 6px;
        }

        .diamond-icon {
          margin: 2px 0;
          .credit-icon {
            width: 24px;
            height: 24px;
          }
        }

        .amount {
          font-size: 15px;
          font-weight: 600;
          color: #daff96;
          margin-top: 2px;

          .taken-text {
            color: #fff;
            height: 20px;
            padding: 3px 9px;
            text-align: center;
            font-size: 11px;
            font-weight: 600;
            border-radius: 10px;
            background: rgba(202, 147, 242, 0.3);
          }
        }

        &:last-child {
          grid-column: 1 / -1;
          background: #411d5d;
          aspect-ratio: 3.2;
          padding: 10px 0;
          .credit-icon {
            width: 95px;
            height: auto;
          }
          .amount {
            color: #e6c3ff;
          }
        }
      }
    }

    .claim-button {
      width: 100%;
      border-radius: 26px;
      height: 42px;
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 10px;
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;

      &:not(.is-claimed) {
        border-top: 2px solid #1f0038;
        border-right: 2px solid #1f0038;
        border-bottom: 6px solid #1f0038;
        border-left: 2px solid #1f0038;
        background: linear-gradient(180deg, #d6cafe 0%, #ca93f2 100%);
        box-shadow: 0px 1.855px 11.13px 0px #b098ff;
        color: #1f0038;

        &:hover {
          opacity: 0.9;
        }

        &:disabled {
          background: rgba(255, 255, 255, 0.1);
          cursor: not-allowed;
          color: rgba(255, 255, 255, 0.6);
        }
      }

      &.is-claimed {
        background: #ca93f2;
        border: none;
        color: #241d49;
        font-size: 15px;
        font-weight: 600;

        &:hover {
          opacity: 0.9;
        }
      }

      :deep(.arco-spin) {
        color: white;
      }
    }

    .view-tasks-link {
      margin-top: 12px;
      text-align: center;
      color: rgba(255, 255, 255, 0.7);
      font-size: 14px;
      cursor: pointer;
      text-decoration: underline;

      &:hover {
        color: #fff;
      }
    }
  }
}
</style>
