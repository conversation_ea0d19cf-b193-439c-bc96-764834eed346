<template>
  <div class="mobile-layout">
    <div class="page-container" :class="{ 'has-menu': showMenu }">
      <RouterTransition />
    </div>
    <div class="menu-container" v-if="showMenu">
      <Menu />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import Menu from './Menu.vue'
import RouterTransition from './RouterTransition.vue'

const route = useRoute()

const showMenu = computed(() => {
  return route.meta.showMenu !== false
})
</script>

<style lang="less" scoped>
@import '@/assets/style/theme.less';

.mobile-layout {
  height: calc(var(--vh, 1vh) * 100);
  background: linear-gradient(
    180deg,
    var(--mobile-bg-gradient-start) 0%,
    var(--mobile-bg-gradient-end) 100%
  );
  position: relative;
  width: 100%;
  margin: 0 auto;
  transition: background 0.3s ease;
}

.page-container {
  position: relative;
  height: calc(var(--vh, 1vh) * 100);
  width: 100%;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;

  &.has-menu {
    height: calc(var(--vh, 1vh) * 100 - var(--menu-height, 60px));
  }
}

.menu-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  height: var(--menu-height, 60px);
  background: inherit;
}
</style>
