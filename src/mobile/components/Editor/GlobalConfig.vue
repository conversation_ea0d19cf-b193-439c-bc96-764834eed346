<template>
  <div class="global-config">
    <a-form :model="storyConfig" layout="vertical">
      <!-- 基础信息配置 -->
      <div class="config-section">
        <h3 class="section-title">基础信息</h3>
        <a-form-item label="故事名称" name="project_name">
          <a-input v-model="storyConfig.project_name" placeholder="请输入故事名称" />
        </a-form-item>
        <a-form-item label="故事描述" name="description">
          <a-textarea
            v-model="storyConfig.description"
            :max-length="255"
            placeholder="请输入故事描述"
            :auto-size="{ minRows: 2, maxRows: 4 }"
          />
        </a-form-item>
      </div>

      <!-- 角色配置 -->
      <div class="config-section">
        <h3 class="section-title">角色配置</h3>
        <a-form-item label="主角选择">
          <div class="character-selector" @click="showCharacterSelect = true">
            <div v-if="editorStore.selectedActor" class="selected-character">
              <img
                :src="editorStore.selectedActor.avatar_url"
                :alt="editorStore.selectedActor.name"
              />
              <span class="character-name">{{ editorStore.selectedActor.name }}</span>
            </div>
            <div v-else class="select-button">
              <i class="ri-add-line"></i>
              <span>选择角色</span>
            </div>
          </div>
        </a-form-item>
      </div>

      <!-- 媒体资源配置 -->
      <div class="config-section">
        <h3 class="section-title">媒体资源</h3>
        <a-form-item label="预览图片">
          <MediaSelector
            v-model="storyConfig.preview_url"
            v-model:preview="storyConfig.preview_url"
            type="image"
          />
        </a-form-item>
        <a-form-item label="预览视频">
          <MediaSelector
            v-model="storyConfig.preview_video"
            v-model:preview="storyConfig.preview_video"
            type="video"
          />
        </a-form-item>
        <a-form-item label="背景音乐">
          <MediaSelector
            v-model="storyConfig.bgm_url"
            v-model:preview="storyConfig.bgm_url"
            type="audio"
          />
        </a-form-item>
      </div>

      <!-- 技能配置 -->
      <div class="config-section">
        <h3 class="section-title">技能配置</h3>
        <a-form-item label="启用技能">
          <a-switch v-model="isActiveSkill" @change="handleSkillToggle" />
        </a-form-item>
        <a-form-item v-if="isActiveSkill" label="技能选择">
          <div class="skill-selector" @click="showSkillSelect = true">
            <div v-if="selectedSkills.length > 0" class="selected-skills">
              <div v-for="skill in selectedSkills" :key="skill.id" class="skill-tag">
                <img :src="skill.image_url" :alt="skill.name" class="skill-avatar" />
                <span class="skill-name">{{ skill.name }}</span>
              </div>
            </div>
            <div v-else class="select-button">
              <i class="ri-add-line"></i>
              <span>选择技能</span>
            </div>
          </div>
        </a-form-item>
      </div>

      <!-- 交互配置 -->
      <!-- <div class="config-section">
        <h3 class="section-title">交互配置</h3>
        <a-form-item label="默认过渡效果" name="transition">
          <a-select v-model="formState.transition">
            <a-select-option value="fade">淡入淡出</a-select-option>
            <a-select-option value="slide">滑动</a-select-option>
            <a-select-option value="zoom">缩放</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="自动播放" name="autoplay">
          <a-switch v-model="formState.autoplay" />
        </a-form-item>
        <a-form-item v-if="formState.autoplay" label="播放间隔(秒)" name="autoplayInterval">
          <a-input-number
            v-model="formState.autoplayInterval"
            :min="1"
            :max="10"
            style="width: 100%"
          />
        </a-form-item>
      </div> -->
    </a-form>

    <template v-if="isDesktop">
      <DesktopCharacterSelect
        v-model:visible="showCharacterSelect"
        :actors="editorStore.actors"
        :story-is-purchased="true"
        :story-coins="0"
        @select="handleSelectActor"
      />
      <DesktopSkillSelect v-model:visible="showSkillSelect" v-model="selectedSkills" />
    </template>
    <template v-else>
      <CharacterSelect
        v-model:visible="showCharacterSelect"
        :actors="editorStore.actors"
        :story-is-purchased="true"
        :story-coins="0"
        @select="handleSelectActor"
      />
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { useEditorStore } from '@/store/editor'
import { storeToRefs } from 'pinia'
import MediaSelector from '@/mobile/components/Common/MediaSelector.vue'
import CharacterSelect from '@/mobile/views/chat/components/CharacterSelect.vue'
import DesktopCharacterSelect from '@/mobile/components/Editor/DesktopCharacterSelect.vue'
import DesktopSkillSelect from '@/mobile/components/Editor/DesktopSkillSelect.vue'
import type { Actor } from '@/api/stories'
import type { SkillInfo } from '@/interface/skill'
import { useWindowSize } from '@vueuse/core'
import { useSkillStore } from '@/store/skill'

const editorStore = useEditorStore()
const skillStore = useSkillStore()
const { storyConfig } = storeToRefs(editorStore)
const { width } = useWindowSize()

const showCharacterSelect = ref(false)
const showSkillSelect = ref(false)
const isDesktop = computed(() => width.value >= 768)
const selectedSkills = ref<SkillInfo[]>([])
const isActiveSkill = ref(false)

// 交互配置表单状态
const formState = reactive({
  transition: 'fade',
  autoplay: false,
  autoplayInterval: 3
})

// 处理角色选择
const handleSelectActor = (actor: Actor) => {
  editorStore.setSelectedActor(actor)
  showCharacterSelect.value = false
}

// 处理技能开关
const handleSkillToggle = (value: boolean) => {
  if (!value) {
    selectedSkills.value = []
  }
  editorStore.updateStoryConfig({
    is_active_skill: value,
    skill_ids: value ? selectedSkills.value.map((skill) => skill.id) : undefined
  })
}

// 监听技能选择变化
watch(
  selectedSkills,
  (newSkills) => {
    if (isActiveSkill.value) {
      editorStore.updateStoryConfig({
        skill_ids: newSkills.map((skill) => skill.id)
      })
    }
  },
  { deep: true }
)

// 初始化技能列表
skillStore.fetchAllSkillInfo()

// 初始化状态
if (storyConfig.value.is_active_skill) {
  isActiveSkill.value = true
  if (storyConfig.value.skill_ids?.length) {
    selectedSkills.value = skillStore.skills.filter(
      (skill) => storyConfig.value.skill_ids?.includes(skill.id)
    )
  }
}
</script>

<style lang="less" scoped>
.global-config {
  .config-section {
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .section-title {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.85);
    margin: 0 0 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  :deep(.arco-form-item) {
    .arco-form-item-label {
      color: rgba(255, 255, 255, 0.65);
    }
  }

  .character-selector {
    cursor: pointer;
    transition: all 0.3s ease;

    .select-button,
    .selected-character {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px;
      border-radius: 8px;
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
      color: rgba(255, 255, 255, 0.8);
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.08);
        border-color: #ca93f2;
      }

      i {
        font-size: 20px;
      }
    }

    .selected-character {
      img {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        object-fit: cover;
      }

      .character-name {
        font-size: 14px;
        color: #fff;
      }
    }
  }

  .skill-selector {
    cursor: pointer;
    transition: all 0.3s ease;

    .select-button,
    .selected-skills {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px;
      border-radius: 8px;
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
      color: rgba(255, 255, 255, 0.8);
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.08);
        border-color: #ca93f2;
      }

      i {
        font-size: 20px;
      }
    }

    .selected-skills {
      flex-wrap: wrap;

      .skill-tag {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 4px 12px;
        background: rgba(202, 147, 242, 0.1);
        border-radius: 20px;

        .skill-avatar {
          width: 24px;
          height: 24px;
          border-radius: 4px;
          object-fit: cover;
        }

        .skill-name {
          font-size: 14px;
          color: #ca93f2;
        }
      }
    }
  }
}
</style>
