<template>
  <div class="property-panel">
    <template v-if="event">
      <div class="panel-section">
        <!-- 等待事件 -->
        <template v-if="event.type === 'wait'">
          <div class="form-item">
            <div class="form-label">等待时间 (seconds)</div>
            <DurationPicker
              :model-value="localEvent.plot.seconds"
              @update:model-value="updatePlot('seconds', $event)"
            />
          </div>
        </template>

        <!-- 显示提示 -->
        <template v-else-if="event.type === 'show_tips'">
          <div class="form-item">
            <div class="form-label">提示内容 (content.text)</div>
            <textarea
              class="custom-textarea"
              :value="(localEvent.plot.content as MessageContent)?.text"
              @input="updateContent('text', ($event.target as HTMLTextAreaElement).value)"
              :rows="3"
              placeholder="请输入提示内容"
            ></textarea>
          </div>
        </template>

        <!-- 显示覆盖层 -->
        <template v-else-if="event.type === 'show_overlay'">
          <div class="form-item">
            <div class="form-label">按钮文本 (button.text)</div>
            <input
              type="text"
              class="custom-input"
              :value="localEvent.plot.button?.text"
              @input="updateOverlay('button.text', ($event.target as HTMLInputElement).value)"
              placeholder="请输入按钮文本"
            />
            <div class="form-item">
              <div class="form-label">浮层文本 (overlay.text)</div>
              <textarea
                class="custom-textarea"
                :value="localEvent.plot.overlay?.text"
                @input="updateOverlay('overlay.text', ($event.target as HTMLTextAreaElement).value)"
                placeholder="请输入浮层文本"
              />
            </div>
          </div>
          <!-- <div class="form-item">
            <div class="form-label">按钮图标 (button.icon)</div>
            <IconPicker
              :model-value="localEvent.plot.button?.icon"
              @update:model-value="updateOverlay('button.icon', $event)"
            />
          </div> -->
          <div class="form-item">
            <div class="form-label">按钮动作 (button.action)</div>
            <input
              type="text"
              class="custom-input"
              :value="localEvent.plot.button?.action"
              @input="updateOverlay('button.action', ($event.target as HTMLInputElement).value)"
              placeholder="请输入按钮动作"
            />
          </div>
        </template>

        <!-- 消息 -->
        <template v-else-if="event.type === 'message'">
          <div class="form-item">
            <div class="form-label">发送者类型 (sender_type)</div>
            <Select
              v-model="localEvent.plot.sender_type"
              @update:model-value="updatePlot('sender_type', $event)"
              :options="[
                { label: 'Actor', value: 'actor' },
                { label: 'User', value: 'user' }
              ]"
            />
          </div>
          <div class="form-item">
            <div class="form-label">消息类型 (msg_type)</div>
            <Select
              class="custom-select"
              v-model="localEvent.plot.msg_type"
              @update:model-value="updatePlot('msg_type', $event)"
              :options="[
                { label: '文本', value: 'text' },
                { label: 'HTML', value: 'html' },
                { label: '图片', value: 'image' },
                { label: '音频', value: 'audio' },
                { label: '视频', value: 'video' }
              ]"
            />
          </div>
          <div class="form-item">
            <div class="form-label">消息内容 (content.text)</div>
            <textarea
              class="custom-textarea"
              :value="(localEvent.plot.content as MessageContent)?.text"
              @input="updateContent('text', ($event.target as HTMLTextAreaElement).value)"
              :rows="3"
              placeholder="请输入消息内容"
            ></textarea>
          </div>
        </template>

        <!-- 显示图片 -->
        <template v-else-if="event.type === 'show_image'">
          <div class="form-item">
            <div class="form-label">图片 (url)</div>
            <MediaSelector type="image" v-model:preview="localEvent.plot.url" />
          </div>
          <div class="form-item">
            <label class="custom-checkbox">
              <input
                type="checkbox"
                :checked="localEvent.plot.is_fullscreen"
                @change="updatePlot('is_fullscreen', ($event.target as HTMLInputElement).checked)"
              />
              <span>全屏显示 (is_fullscreen)</span>
            </label>
          </div>
        </template>

        <!-- 播放视频 -->
        <template v-else-if="event.type === 'play_video'">
          <div class="form-item">
            <div class="form-label">视频 (url)</div>
            <MediaSelector type="video" v-model:preview="localEvent.plot.url" />
          </div>
          <div class="form-item">
            <label class="custom-checkbox">
              <input
                type="checkbox"
                :checked="localEvent.plot.is_background"
                @change="updatePlot('is_background', ($event.target as HTMLInputElement).checked)"
              />
              <span>作为背景视频</span>
            </label>
          </div>
          <div class="form-item" v-if="!localEvent.plot.is_background">
            <div class="form-label">最小观看时长 单位:秒 (min_watch_duration)</div>
            <input
              type="number"
              class="custom-input"
              :value="localEvent.plot.min_watch_duration || 2"
              @input="
                updatePlot('min_watch_duration', Number(($event.target as HTMLInputElement).value))
              "
              placeholder="请输入最小观看时长（秒）"
              min="0"
              step="1"
            />
          </div>
        </template>

        <!-- 播放音频 -->
        <template v-else-if="event.type === 'play_audio'">
          <div class="form-item">
            <div class="form-label">音频 (url)</div>
            <MediaSelector type="audio" v-model:preview="localEvent.plot.url" />
          </div>
          <div class="form-item">
            <label class="custom-checkbox">
              <input
                type="checkbox"
                :checked="localEvent.plot.is_bgm"
                @change="updatePlot('is_bgm', ($event.target as HTMLInputElement).checked)"
              />
              <span>作为背景音乐 (is_bgm)</span>
            </label>
          </div>
        </template>

        <!-- 动画图片 -->
        <template v-else-if="event.type === 'animated_images'">
          <div v-for="(url, index) in localEvent.plot.urls" :key="index" class="url-item">
            <div class="form-label">图片 {{ index + 1 }} (urls[{{ index }}])</div>
            <MediaSelector
              type="image"
              :model-value="url"
              @update:model-value="updateImage(index, $event)"
            />
            <button class="delete-button" @click="removeImage(index)">
              <svg viewBox="0 0 24 24" class="icon-delete">
                <path
                  fill="currentColor"
                  d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
                />
              </svg>
            </button>
          </div>
          <button class="add-button" @click="addImage">
            <svg viewBox="0 0 24 24" class="icon-add">
              <path fill="currentColor" d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
            </svg>
            <span>添加图片</span>
          </button>
        </template>

        <!-- 对话选项 -->
        <template v-else-if="event.type === 'show_chat_options'">
          <div class="form-item">
            <label class="custom-checkbox">
              <input
                type="checkbox"
                :checked="localEvent.plot.allow_input"
                @change="updatePlot('allow_input', ($event.target as HTMLInputElement).checked)"
              />
              <span>允许自由输入 (allow_input)</span>
            </label>
          </div>

          <div class="form-item" v-if="localEvent.plot.allow_input">
            <div class="form-item">
              <div class="form-label">输入提示文本 (input_placeholder)</div>
              <input
                type="text"
                class="custom-input"
                :value="localEvent.plot.input_placeholder"
                @input="updatePlot('input_placeholder', ($event.target as HTMLInputElement).value)"
                placeholder="请输入提示文本"
              />
            </div>
          </div>
          <div class="form-item">
            <div class="form-label">选项类型（卡片/按钮）</div>
            <Select
              class="custom-select"
              :model-value="localEvent.plot.style"
              @update:model-value="updatePlot('style', $event)"
              :options="[
                { label: '卡片(card)', value: 'card' },
                { label: '按钮(button)', value: 'button' }
              ]"
            />
          </div>
          <div
            v-for="(option, index) in localEvent.plot.options"
            :key="option.option_id"
            class="option-item"
          >
            <div class="form-item">
              <div class="form-label">选项文本 (options[{{ index }}].text)</div>
              <input
                type="text"
                class="custom-input"
                :value="option.text"
                @input="updateOptions(index, 'text', ($event.target as HTMLInputElement).value)"
                placeholder="请输入选项文本"
              />
            </div>

            <div class="form-item">
              <div class="form-label">跳转场景 (options[{{ index }}].scene_id)</div>
              <Select
                class="custom-select"
                :model-value="option.scene_id"
                @update:model-value="updateOptions(index, 'scene_id', $event)"
                :options="sceneOptions"
                placeholder="请选择跳转场景"
              />
            </div>
            <div class="form-item">
              <label class="custom-checkbox">
                <input
                  type="checkbox"
                  :checked="option.paid_required"
                  @change="
                    updateOptions(
                      index,
                      'paid_required',
                      ($event.target as HTMLInputElement).checked
                    )
                  "
                />
                <span>付费选项 (options[{{ index }}].paid_required)</span>
              </label>
            </div>
            <div class="form-item" v-if="option.paid_required">
              <div class="form-label">付费金额 (options[{{ index }}].coins)</div>
              <input
                type="number"
                class="custom-input"
                :value="option.coins"
                @input="
                  updateOptions(index, 'coins', Number(($event.target as HTMLInputElement).value))
                "
                placeholder="请输入付费金额"
                min="0"
                step="1"
              />
            </div>
            <div class="form-item">
              <label class="custom-checkbox">
                <input
                  type="checkbox"
                  :checked="option.is_highlight"
                  @change="
                    updateOptions(
                      index,
                      'is_highlight',
                      ($event.target as HTMLInputElement).checked
                    )
                  "
                />
                <span>隐藏选项 (options[{{ index }}].is_highlight)</span>
              </label>
            </div>
            <button class="delete-button" @click="removeOption(index)">
              <svg viewBox="0 0 24 24" class="icon-delete">
                <path
                  fill="currentColor"
                  d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"
                />
              </svg>
            </button>
          </div>
          <button class="add-button" @click="addOption">
            <svg viewBox="0 0 24 24" class="icon-add">
              <path fill="currentColor" d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
            </svg>
            <span>添加选项</span>
          </button>
        </template>

        <!-- 显示结局 -->
        <template v-else-if="event.type === 'show_ending'">
          <div class="form-item">
            <div class="form-label">结局内容 (content.html)</div>
            <textarea
              class="custom-textarea"
              :value="(localEvent.plot.content as MessageContent)?.html"
              @input="updateContent('html', ($event.target as HTMLTextAreaElement).value)"
              :rows="5"
              placeholder="请输入结局内容（支持HTML）"
            ></textarea>
          </div>
        </template>

        <!-- 更新任务进度 -->
        <template v-else-if="event.type === 'update_task_progress'">
          <div class="form-item">
            <div class="form-label">任务ID</div>
            <input
              type="text"
              class="custom-input"
              :value="localEvent.plot?.task_id"
              @input="updateTaskProgress('task_id', ($event.target as HTMLInputElement).value)"
              placeholder="请输入任务ID"
            />
          </div>
          <div class="form-item">
            <div class="form-label">任务描述</div>
            <textarea
              class="custom-textarea"
              :value="localEvent.plot?.task_description"
              @input="
                updateTaskProgress('task_description', ($event.target as HTMLTextAreaElement).value)
              "
              :rows="3"
              placeholder="请输入任务描述"
            ></textarea>
          </div>
        </template>

        <!-- 场景转换 -->
        <template v-else-if="event.type === 'scene_transition'">
          <div class="form-item">
            <div class="form-label">转换类型 (scene_transition.type)</div>
            <Select
              class="custom-select"
              :model-value="localEvent.plot.scene_transition?.type || 'fade'"
              @update:model-value="updatePlot('scene_transition', $event)"
              :options="[
                { label: '淡入淡出', value: 'fade' },
                { label: '滑动', value: 'slide' }
              ]"
            />
            <div>延迟时间 (seconds)</div>
            <input
              type="number"
              class="custom-input"
              :value="localEvent.plot.scene_transition?.seconds || 0"
              @input="
                updatePlot('scene_transition', {
                  ...localEvent.plot.scene_transition,
                  seconds: Number(($event.target as HTMLInputElement).value)
                })
              "
              placeholder="请输入延迟时间（秒）"
              min="0"
              step="1"
            />
          </div>
        </template>

        <!-- 好感度 -->
        <template v-else-if="event.type === 'heart_value'">
          <div class="form-item">
            <div class="form-label">好感度值 (heart_options.heart_value)</div>
            <input
              type="number"
              class="custom-input"
              :value="localEvent.plot.heart_options?.heart_value"
              @input="
                updateHeartOptions('heart_value', Number(($event.target as HTMLInputElement).value))
              "
              placeholder="请输入好感度值"
            />
          </div>
          <div class="form-item">
            <label class="custom-checkbox">
              <input
                type="checkbox"
                :checked="localEvent.plot.heart_options?.is_allow_message"
                @change="
                  updateHeartOptions(
                    'is_allow_message',
                    ($event.target as HTMLInputElement).checked
                  )
                "
              />
              <span>允许发送消息 (heart_options.is_allow_message)</span>
            </label>
          </div>
        </template>

        <!-- 互动事件 -->
        <template v-else-if="event?.type === 'interactive'">
          <template v-if="actionHandlers?.[0]">
            <div class="form-group">
              <label>
                <span>心值关键词 (heart_key)</span>
                <input
                  type="text"
                  :value="actionHandlers[0].params.heart_key"
                  @input="
                    (e: Event) =>
                      updateActionHandler('heart_key', (e.target as HTMLInputElement).value)
                  "
                  placeholder="请输入心值关键词"
                />
              </label>
            </div>
            <div class="form-group">
              <label>
                <span>心值奖励 (heart_value)</span>
                <div class="heart-value-input">
                  <a-input-number
                    :model-value="Number(actionHandlers[0].params.heart_value) || 0"
                    :min="0"
                    placeholder="请输入心值奖励"
                    style="width: 100%"
                    @input="
                      (value: number | null) => updateActionHandler('heart_value', value || 0)
                    "
                  >
                    <!-- <template #append>/ 100</template> -->
                  </a-input-number>
                  <!-- <div class="heart-value-hint">剩余可分配心值：{{ remainingHeartValue }}</div> -->
                </div>
              </label>
            </div>
            <div class="form-group">
              <label>
                <span>对话次数限制 (limit_chat_count)</span>
                <input
                  type="number"
                  :value="actionHandlers[0].params.limit_chat_count"
                  @input="
                    (e: Event) =>
                      updateActionHandler(
                        'limit_chat_count',
                        Number((e.target as HTMLInputElement).value)
                      )
                  "
                  min="1"
                  placeholder="请输入对话次数限制"
                />
              </label>
            </div>
            <div class="form-group">
              <label>
                <span>Actor同意词： (agree_sentences)</span>
                <textarea
                  :value="actionHandlers[0].params.agree_sentences?.join('\n')"
                  @input="
                    (e: Event) =>
                      updateActionHandler(
                        'agree_sentences',
                        (e.target as HTMLTextAreaElement).value.split('\n')
                      )
                  "
                  rows="3"
                ></textarea>
              </label>
            </div>
            <div class="form-group">
              <label>
                <span>提示词模板 (streamer_tpl)</span>
                <PromptTemplateEditor
                  :model-value="actionHandlers[0].params.streamer_tpl"
                  @update:model-value="(value) => updateActionHandler('streamer_tpl', value)"
                  scope="scene"
                  @template-selected="handleTemplateSelected"
                />
              </label>
            </div>
            <div class="form-group">
              <label class="checkbox-label">
                <input
                  type="checkbox"
                  :checked="actionHandlers[0].params.clean_history"
                  @change="
                    (e: Event) =>
                      updateActionHandler('clean_history', (e.target as HTMLInputElement).checked)
                  "
                />
                <span>清除历史记录 (clean_history)</span>
              </label>
            </div>
          </template>
        </template>

        <!-- 其他事件类型 -->
        <template v-else>
          <div class="empty-tip">
            <svg viewBox="0 0 24 24" class="icon-edit">
              <path
                fill="currentColor"
                d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"
              />
            </svg>
            <p>请选择要编辑的事件</p>
          </div>
        </template>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed, onMounted } from 'vue'
import { cloneDeep } from 'lodash-es'
import type { GameEvent, Scene, ActionHandler, MessageContent } from '@/types/editor'
import { useEditorStore } from '@/store/editor'
import DurationPicker from '@/mobile/components/Common/DurationPicker.vue'
import MediaSelector from '@/mobile/components/Common/MediaSelector.vue'
import IconPicker from '@/mobile/components/Common/IconPicker.vue'
import Select from '../Select.vue'
import { useChapterOptions } from '@/composables/useChapterOptions'
import PromptTemplateEditor from '@/components/PromptTemplateEditor.vue'
import type { PromptTemplate } from '@/api/prompt'

const props = defineProps<{
  event?: GameEvent
  scene?: Scene
  actionHandlers?: ActionHandler[]
}>()

const emit = defineEmits<{
  (e: 'update', event: GameEvent | ActionHandler): void
  (e: 'update-scene', scene: Scene): void
}>()

const editorStore = useEditorStore()

const localEvent = ref<GameEvent>(cloneDeep(props.event))

// Add sceneOptions computed property
const { chapterOptions: sceneOptions } = useChapterOptions(false)

// Add watch effect for url changes
watch(
  () => localEvent.value?.plot?.url,
  () => {
    if (localEvent.value) {
      emit('update', cloneDeep(localEvent.value))
    }
  }
)

watch(
  () => props.event,
  (newEvent) => {
    localEvent.value = cloneDeep(newEvent)
  },
  { deep: true }
)

const updatePlot = (key: string, value: any) => {
  if (!localEvent.value.plot) {
    localEvent.value.plot = {}
  }

  // 特殊处理 min_watch_duration 和 scene_transition
  if (key === 'min_watch_duration') {
    localEvent.value.plot[key] = value || 2 // 确保有默认值
  } else if (key === 'scene_transition') {
    if (!localEvent.value.plot.scene_transition) {
      localEvent.value.plot.scene_transition = {
        type: 'fade',
        seconds: value?.seconds || 0
      }
    }
    localEvent.value.plot.scene_transition = value
  } else if (key === 'is_background') {
    localEvent.value.plot[key] = value
    // 当 is_background 为 true 时，删除 min_watch_duration
    if (value && 'min_watch_duration' in localEvent.value.plot) {
      delete localEvent.value.plot.min_watch_duration
    }
  } else {
    localEvent.value.plot[key] = value
  }

  emit('update', cloneDeep(localEvent.value))
}

const updateContent = (key: string, value: any) => {
  if (!localEvent.value.plot.content) {
    localEvent.value.plot.content = { text: '', html: '', media_url: '' }
  }
  const content = localEvent.value.plot.content as MessageContent
  content[key] = value
  emit('update', cloneDeep(localEvent.value))
}

const updateOverlay = (key: string, value: any) => {
  if (!localEvent.value.plot) {
    localEvent.value.plot = {}
  }

  // 根据 key 的前缀来决定更新 button 还是 overlay
  if (key.startsWith('button.')) {
    if (!localEvent.value.plot.button) {
      localEvent.value.plot.button = {
        icon: '',
        text: '',
        action: ''
      }
    }
    const buttonKey = key.replace('button.', '')
    localEvent.value.plot.button[buttonKey] = value
  } else if (key.startsWith('overlay.')) {
    if (!localEvent.value.plot.overlay) {
      localEvent.value.plot.overlay = {
        text: '',
        position: 'center',
        display_time: 'before'
      }
    }
    const overlayKey = key.replace('overlay.', '')
    localEvent.value.plot.overlay[overlayKey] = value
  }

  emit('update', cloneDeep(localEvent.value))
}

const updateButton = (key: string, value: any) => {
  if (!localEvent.value.plot.button) {
    localEvent.value.plot.button = {
      icon: 'IconRight',
      text: '',
      action: ''
    }
  }
  localEvent.value.plot.button[key] = value
  emit('update', cloneDeep(localEvent.value))
}

const updateOptions = (index: number, key: string, value: any) => {
  if (!localEvent.value.plot.options) {
    localEvent.value.plot.options = []
  }
  if (!localEvent.value.plot.options[index]) {
    localEvent.value.plot.options[index] = {
      option_id: Date.now().toString(),
      text: '',
      action: 'continue'
    }
  }
  localEvent.value.plot.options[index][key] = value
  emit('update', cloneDeep(localEvent.value))
}

const addOption = () => {
  if (!localEvent.value.plot.options) {
    localEvent.value.plot.options = []
  }
  localEvent.value.plot.options.push({
    option_id: Date.now().toString(),
    text: '',
    action: 'continue',
    paid_required: false,
    coins: 0,
    scene_id: '',
    is_highlight: false
  })
  emit('update', cloneDeep(localEvent.value))
}

const removeOption = (index: number) => {
  if (localEvent.value.plot.options) {
    localEvent.value.plot.options.splice(index, 1)
    emit('update', cloneDeep(localEvent.value))
  }
}

const addImage = () => {
  if (!localEvent.value.plot.urls) {
    localEvent.value.plot.urls = []
  }
  localEvent.value.plot.urls.push('')
  emit('update', cloneDeep(localEvent.value))
}

const updateImage = (index: number, url: string) => {
  if (!localEvent.value.plot.urls) {
    localEvent.value.plot.urls = []
  }
  localEvent.value.plot.urls[index] = url
  emit('update', cloneDeep(localEvent.value))
}

const removeImage = (index: number) => {
  if (localEvent.value.plot.urls) {
    localEvent.value.plot.urls.splice(index, 1)
    emit('update', cloneDeep(localEvent.value))
  }
}

const updateTaskProgress = (key: string, value: any) => {
  if (!localEvent.value.plot) {
    localEvent.value.plot = {}
  }
  localEvent.value.plot[key] = value
  emit('update', cloneDeep(localEvent.value))
}

const updateHeartOptions = (key: string, value: any) => {
  if (!localEvent.value.plot.heart_options) {
    localEvent.value.plot.heart_options = {
      is_allow_message: false,
      heart_value: 0
    }
  }
  localEvent.value.plot.heart_options[key] = value
  emit('update', cloneDeep(localEvent.value))
}

// Action Handler related computed and methods
const hasActionHandler = computed(() => {
  return props.scene?.action_handlers?.length > 0
})

const actionHandler = computed(() => {
  return props.scene?.action_handlers?.[0]
})

// 计算剩余可分配心值
const remainingHeartValue = computed(() => {
  // 获取当前场景的心值
  const currentValue = props.actionHandlers?.[0]?.params.heart_value || 0
  // 返回剩余可分配的心值 + 当前场景的心值(因为可以重新分配)
  return editorStore.allocatableHeartValue + currentValue
})

// 检查心值是否已达到上限
const isHeartValueDisabled = computed(() => {
  return editorStore.allocatableHeartValue === 0 && !props.actionHandlers?.[0]?.params.heart_value
})

// 处理模板选择
const handleTemplateSelected = (template: PromptTemplate | null) => {
  if (template && props.actionHandlers?.[0]) {
    console.log('选择了模板:', template.name)
  }
}

const updateActionHandler = (key: string, value: any) => {
  if (!props.actionHandlers?.[0] || !props.scene) return

  const updatedHandler = cloneDeep(props.actionHandlers[0])

  if (key === 'heart_value') {
    const numValue = parseInt(value) || 0
    value = numValue
  } else if (key === 'streamer_tpl') {
    value = value.trim()
  }

  updatedHandler.params[key] = value
  editorStore.updateActionHandler(props.scene.id, updatedHandler)
}
</script>

<style lang="less" scoped>
.property-panel {
  height: 100%;
  // background: #1f0038;
  color: #fff;

  .panel-section {
    padding: 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    &:last-child {
      border-bottom: none;
    }

    .section-title {
      font-size: 15px;
      font-weight: 600;
      margin-bottom: 16px;
      color: #fff;
    }

    .section-content {
      .form-item {
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
        }

        .form-label {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.8);
          margin-bottom: 8px;
        }
      }
    }
  }

  .url-item,
  .option-item {
    position: relative;
    padding: 12px;
    margin-bottom: 12px;
    border-radius: 8px;
    background: rgba(204, 213, 255, 0.05);
    border: 1px solid rgba(184, 196, 255, 0.1);

    .arco-form-item:last-child {
      margin-bottom: 0;
    }

    .arco-button[status='danger'] {
      position: absolute;
      top: 8px;
      right: 8px;
      padding: 4px;
      border-radius: 4px;
      background: rgba(255, 77, 79, 0.1);

      &:active {
        background: rgba(255, 77, 79, 0.2);
      }
    }
  }

  .empty-tip {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: rgba(255, 255, 255, 0.5);

    .icon-edit {
      font-size: 32px;
      margin-bottom: 8px;
    }

    p {
      font-size: 14px;
      margin: 0;
    }
  }

  :deep(.arco-divider) {
    margin: 16px 0;
    border-color: rgba(255, 255, 255, 0.1);
  }

  :deep(.arco-button-dashed) {
    width: 100%;
    border-radius: 8px;
    border-color: rgba(184, 196, 255, 0.1);
    color: rgba(255, 255, 255, 0.5);

    &:active {
      color: #ca93f2;
      border-color: #ca93f2;
      background: rgba(202, 147, 242, 0.1);
    }
  }

  .custom-input {
    width: 100%;
    height: 42px;
    padding: 0 16px;
    border: 1px solid rgba(184, 196, 255, 0.1);
    background: rgba(204, 213, 255, 0.05);
    color: #fff;
    border-radius: 8px;
    font-size: 14px;
    outline: none;
    transition: all 0.3s ease;

    &:hover,
    &:focus {
      border-color: #ca93f2;
      background: rgba(204, 213, 255, 0.08);
    }

    &::placeholder {
      color: rgba(255, 255, 255, 0.3);
    }
  }

  .custom-textarea {
    width: 100%;
    min-height: 120px;
    padding: 12px 16px;
    border: 1px solid rgba(184, 196, 255, 0.1);
    background: rgba(204, 213, 255, 0.05);
    color: #fff;
    border-radius: 8px;
    font-size: 14px;
    outline: none;
    transition: all 0.3s ease;
    resize: vertical;

    &:hover,
    &:focus {
      border-color: #ca93f2;
      background: rgba(204, 213, 255, 0.08);
    }

    &::placeholder {
      color: rgba(255, 255, 255, 0.3);
    }
  }

  .custom-checkbox {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    user-select: none;

    input[type='checkbox'] {
      width: 18px;
      height: 18px;
      border: 1px solid rgba(184, 196, 255, 0.1);
      background: rgba(204, 213, 255, 0.05);
      border-radius: 4px;
      appearance: none;
      outline: none;
      cursor: pointer;
      position: relative;

      &:checked {
        background: #ca93f2;
        border-color: #ca93f2;

        &::after {
          content: '';
          position: absolute;
          left: 5px;
          top: 2px;
          width: 6px;
          height: 10px;
          border: solid white;
          border-width: 0 2px 2px 0;
          transform: rotate(45deg);
        }
      }

      &:hover {
        border-color: #ca93f2;
      }
    }

    span {
      color: rgba(255, 255, 255, 0.8);
      font-size: 14px;
    }
  }

  .add-button {
    width: 100%;
    height: 42px;
    border: 1px dashed rgba(184, 196, 255, 0.1);
    background: none;
    color: rgba(255, 255, 255, 0.5);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      border-color: #ca93f2;
      color: #ca93f2;
    }

    .icon-add {
      width: 20px;
      height: 20px;
    }
  }

  .delete-button {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 28px;
    height: 28px;
    border: none;
    background: rgba(255, 77, 79, 0.1);
    color: #ff4d4f;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 77, 79, 0.2);
    }

    .icon-delete {
      width: 16px;
      height: 16px;
    }
  }

  .form-tip {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.5);
    margin-top: 4px;
  }

  .heart-value-input {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .heart-value-hint {
      font-size: 12px;
      color: rgba(255, 255, 255, 0.6);
    }
  }

  .form-group {
    margin-bottom: 16px;

    .template-controls {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .template-select {
        width: 100%;
      }
    }

    label {
      display: flex;
      flex-direction: column;
      gap: 8px;

      span {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.8);
      }

      input[type='text'],
      input[type='number'],
      textarea {
        width: 100%;
        padding: 12px 16px;
        border: 1px solid rgba(184, 196, 255, 0.1);
        background: rgba(204, 213, 255, 0.05);
        color: #fff;
        border-radius: 8px;
        font-size: 14px;
        outline: none;
        transition: all 0.3s ease;

        &:hover,
        &:focus {
          border-color: #ca93f2;
          background: rgba(204, 213, 255, 0.08);
        }

        &::placeholder {
          color: rgba(255, 255, 255, 0.3);
        }
      }

      textarea {
        min-height: 80px;
        resize: vertical;
      }
    }

    .checkbox-label {
      flex-direction: row;
      align-items: center;
      cursor: pointer;

      input[type='checkbox'] {
        width: 18px;
        height: 18px;
        border: 1px solid rgba(184, 196, 255, 0.1);
        background: rgba(204, 213, 255, 0.05);
        border-radius: 4px;
        appearance: none;
        outline: none;
        cursor: pointer;
        position: relative;
        margin-right: 8px;

        &:checked {
          background: #ca93f2;
          border-color: #ca93f2;

          &::after {
            content: '';
            position: absolute;
            left: 5px;
            top: 2px;
            width: 6px;
            height: 10px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
          }
        }

        &:hover {
          border-color: #ca93f2;
        }
      }
    }

    :deep(.arco-input-number) {
      background: rgba(204, 213, 255, 0.05);
      border: 1px solid rgba(184, 196, 255, 0.1);
      border-radius: 8px;

      &:hover {
        border-color: #ca93f2;
        background: rgba(204, 213, 255, 0.08);
      }

      &.arco-input-number-focused {
        border-color: #ca93f2;
        background: rgba(204, 213, 255, 0.08);
      }

      .arco-input-number-input {
        color: #fff;

        &::placeholder {
          color: rgba(255, 255, 255, 0.3);
        }
      }

      .arco-input-number-step {
        border-left: 1px solid rgba(184, 196, 255, 0.1);

        &:hover {
          background: rgba(202, 147, 242, 0.1);
        }

        svg {
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }
  }
}
</style>
