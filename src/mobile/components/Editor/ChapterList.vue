<template>
  <div class="chapter-list">
    <div class="chapter-header">
      <div class="title">故事场景</div>
    </div>

    <div class="chapter-content">
      <div class="chapter-item" v-for="chapter in chapters" :key="chapter.id">
        <div class="chapter-header" @click="toggleChapter(chapter.id)">
          <div class="chapter-info">
            <icon-folder />
            <span class="chapter-name">{{ getChapterName(chapter) }}（故事大纲）</span>
          </div>
          <div class="chapter-actions">
            <Tooltip>
              <button class="action-button">
                <svg viewBox="0 0 24 24" class="icon-more">
                  <path
                    fill="currentColor"
                    d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"
                  />
                </svg>
              </button>
              <template #content>
                <MenuItem @click="handleAddSubChapter(chapter)" v-if="!chapter.parent_id">
                  <div class="menu-item-content">
                    <svg viewBox="0 0 24 24" class="icon">
                      <path fill="currentColor" d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
                    </svg>
                    <span>新建子章节</span>
                  </div>
                </MenuItem>
                <MenuItem @click="handleAddEvent(chapter)" v-else>
                  <div class="menu-item-content">
                    <svg viewBox="0 0 24 24" class="icon">
                      <path fill="currentColor" d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
                    </svg>
                    <span>添加事件</span>
                  </div>
                </MenuItem>
                <MenuItem
                  @click="handleDeleteChapter(chapter)"
                  v-if="chapter.id !== '_BEGIN_' && chapter.id !== '_END_'"
                >
                  <div class="menu-item-content">
                    <svg viewBox="0 0 24 24" class="icon">
                      <path
                        fill="currentColor"
                        d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"
                      />
                    </svg>
                    <span>删除章节</span>
                  </div>
                </MenuItem>
              </template>
            </Tooltip>
          </div>
        </div>

        <div v-show="expandedChapters[chapter.id]" class="sub-chapters">
          <div
            v-for="subChapter in subChapters"
            :key="subChapter.id"
            class="sub-chapter-item"
            :class="{ active: subChapter.id === currentSceneId }"
          >
            <div class="sub-chapter-header" @click.prevent.stop="toggleSubChapter(subChapter.id)">
              <div class="sub-chapter-info">
                <icon-file />
                <div class="sub-chapter-edit" @click.stop>
                  <input
                    type="text"
                    class="sub-chapter-name-input"
                    :value="getChapterName(subChapter)"
                    @change="
                      updateChapterName(subChapter, ($event.target as HTMLInputElement).value)
                    "
                    @blur="validateChapterName(subChapter)"
                    placeholder="请输入章节名称"
                  />
                  <input
                    type="text"
                    class="sub-chapter-id-input"
                    :value="subChapter.id"
                    @change="updateChapterId(subChapter, ($event.target as HTMLInputElement).value)"
                    placeholder="请输入章节ID"
                    :disabled="subChapter.id === '_BEGIN_' || subChapter.id === '_END_'"
                  />
                  <div class="form-item">
                    <div class="form-label">目标跳转章节（next_scene_id）</div>
                    <Select
                      :model-value="subChapter.next_scene_id"
                      @update:model-value="
                        (value) => updateSubChapter(subChapter.id, 'next_scene_id', value)
                      "
                      :options="chapterOptions"
                      placeholder="请选择跳转章节"
                    />
                  </div>
                  <div class="form-item">
                    <div class="form-label">条件跳转（conditions）</div>
                    <div class="condition-toggle">
                      <label class="custom-checkbox">
                        <input
                          type="checkbox"
                          :checked="!!subChapter.conditions"
                          @change="toggleConditions(subChapter)"
                        />
                        <span>启用条件跳转</span>
                      </label>
                    </div>
                    <ConditionBuilder
                      v-if="subChapter.conditions"
                      v-model="subChapter.conditions"
                      :chapter-options="chapterOptions"
                      @update:modelValue="
                        (value) =>
                          updateConditions(subChapter.id, value as unknown as ConditionValue)
                      "
                    />
                  </div>
                </div>
              </div>
              <div class="sub-chapter-actions">
                <Tooltip>
                  <button class="action-button">
                    <svg viewBox="0 0 24 24" class="icon-more">
                      <path
                        fill="currentColor"
                        d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"
                      />
                    </svg>
                  </button>
                  <template #content>
                    <MenuItem @click="handleAddSubChapter(subChapter)" v-if="!subChapter.parent_id">
                      <div class="menu-item-content">
                        <svg viewBox="0 0 24 24" class="icon">
                          <path fill="currentColor" d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
                        </svg>
                        <span>新建子章节</span>
                      </div>
                    </MenuItem>
                    <MenuItem @click="handleAddEvent(subChapter)" v-else>
                      <div class="menu-item-content">
                        <svg viewBox="0 0 24 24" class="icon">
                          <path fill="currentColor" d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
                        </svg>
                        <span>添加事件</span>
                      </div>
                    </MenuItem>
                    <MenuItem
                      v-if="subChapter.id !== '_BEGIN_'"
                      @click="handleCopyChapter(subChapter)"
                    >
                      <div class="menu-item-content">
                        <svg viewBox="0 0 24 24" class="icon">
                          <path
                            fill="currentColor"
                            d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"
                          />
                        </svg>
                        <span>复制章节</span>
                      </div>
                    </MenuItem>
                    <MenuItem
                      v-if="subChapter.id !== '_BEGIN_'"
                      danger
                      @click="handleDeleteChapter(subChapter)"
                    >
                      <div class="menu-item-content">
                        <svg viewBox="0 0 24 24" class="icon">
                          <path
                            fill="currentColor"
                            d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"
                          />
                        </svg>
                        <span>删除章节</span>
                      </div>
                    </MenuItem>
                  </template>
                </Tooltip>
              </div>
            </div>

            <div v-show="expandedSubChapters[subChapter.id]" class="sub-chapter-content">
              <div class="event-list">
                <div
                  v-if="subChapter.action_handlers?.length > 0"
                  class="event-item"
                  :class="{ active: currentEventId === 'interactive' }"
                >
                  <div class="event-header" @click="toggleEvent('interactive')">
                    <div class="event-info">
                      <icon-message class="event-icon" />
                      <div class="event-title">
                        <span>互动事件</span>
                        <span class="event-type">(interactive)</span>
                      </div>
                    </div>
                    <div class="event-actions">
                      <Tooltip>
                        <button class="action-button">
                          <svg viewBox="0 0 24 24" class="icon-more">
                            <path
                              fill="currentColor"
                              d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"
                            />
                          </svg>
                        </button>
                        <template #content>
                          <MenuItem danger @click="handleRemoveInteractiveEvent(subChapter)">
                            <div class="menu-item-content">
                              <svg viewBox="0 0 24 24" class="icon">
                                <path
                                  fill="currentColor"
                                  d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"
                                />
                              </svg>
                              <span>删除</span>
                            </div>
                          </MenuItem>
                        </template>
                      </Tooltip>
                    </div>
                  </div>
                  <div v-show="expandedEvents['interactive']" class="event-content">
                    <PropertyPanel
                      :event="{ type: 'interactive', id: 'interactive', plot: {} }"
                      :action-handlers="subChapter.action_handlers"
                      :scene="subChapter"
                      @update="(updated) => updateEvent(subChapter.id, 'interactive', updated)"
                    />
                  </div>
                </div>
                <div
                  v-for="(event, index) in subChapter.events"
                  :key="event.id"
                  class="event-item"
                  :class="{ active: event.id === currentEventId }"
                >
                  <div class="event-header" @click="toggleEvent(event.id)">
                    <div class="event-info">
                      <component :is="getEventIcon(event.type)" class="event-icon" />
                      <div class="event-title">
                        <span>{{ event.type === 'message' ? '对话' : '事件' }}</span>
                        <span class="event-type">({{ event.type }})</span>
                      </div>
                    </div>
                    <div class="event-actions">
                      <Tooltip>
                        <button class="action-button">
                          <svg viewBox="0 0 24 24" class="icon-more">
                            <path
                              fill="currentColor"
                              d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"
                            />
                          </svg>
                        </button>
                        <template #content>
                          <MenuItem v-if="index > 0" @click="moveEvent(subChapter.id, index, -1)">
                            <div class="menu-item-content">
                              <svg viewBox="0 0 24 24" class="icon">
                                <path
                                  fill="currentColor"
                                  d="M7.41 15.41L12 10.83l4.59 4.58L18 14l-6-6-6 6z"
                                />
                              </svg>
                              <span>上移</span>
                            </div>
                          </MenuItem>
                          <MenuItem
                            v-if="index < subChapter.events.length - 1"
                            @click="moveEvent(subChapter.id, index, 1)"
                          >
                            <div class="menu-item-content">
                              <svg viewBox="0 0 24 24" class="icon">
                                <path
                                  fill="currentColor"
                                  d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"
                                />
                              </svg>
                              <span>下移</span>
                            </div>
                          </MenuItem>
                          <MenuItem danger @click="handleRemoveEvent(subChapter.id, event.id)">
                            <div class="menu-item-content">
                              <svg viewBox="0 0 24 24" class="icon">
                                <path
                                  fill="currentColor"
                                  d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"
                                />
                              </svg>
                              <span>删除</span>
                            </div>
                          </MenuItem>
                        </template>
                      </Tooltip>
                    </div>
                  </div>
                  <div v-show="expandedEvents[event.id]" class="event-content">
                    <PropertyPanel
                      :event="event"
                      :scene="subChapter"
                      @update="(updated) => updateEvent(subChapter.id, event.id, updated)"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- <MaterialConfig v-if="selectedScene" :scene="selectedScene" /> -->

    <BaseDrawer v-model:visible="showEventTypes" height="auto">
      <div class="event-types">
        <div
          v-for="type in getAvailableEventTypes(selectedChapter)"
          :key="type.value"
          class="event-type-item"
          @click="handleAddEventWithType(type.value)"
        >
          <component :is="type.icon" class="type-icon" />
          <div class="type-label">
            <span>{{ type.label }}</span>
            <span class="type-key">({{ type.value }})</span>
          </div>
        </div>
      </div>
    </BaseDrawer>

    <ConfirmDialog
      v-model:visible="showDeleteConfirm"
      title="删除确认"
      content="确定要删除这个章节吗？删除后无法恢复。"
      confirmText="确定"
      cancelText="取消"
      @confirm="confirmDelete"
      @cancel="showDeleteConfirm = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, unref } from 'vue'
import { useEditorStore } from '@/store/editor'
import { Message } from '@/mobile/components/Message'
import { nanoid } from 'nanoid'
import BaseDrawer from '@/mobile/components/BaseDrawer.vue'
import ConfirmDialog from '@/mobile/components/ConfirmDialog.vue'
import PropertyPanel from '@/mobile/components/Editor/PropertyPanel.vue'
import MaterialConfig from '@/mobile/components/Editor/MaterialConfig.vue'
import SceneDialog from '@/mobile/components/Editor/SceneDialog.vue'
import Select from '@/mobile/components/Select.vue'
import ConditionBuilder from '@/mobile/components/Editor/ConditionBuilder.vue'
import type {
  Scene,
  GameEvent,
  GameEventType,
  ActionHandler,
  Condition,
  ConditionValue,
  ConditionConfig
} from '@/types/editor'
import Tooltip from '@/mobile/components/Common/Tooltip.vue'
import MenuItem from '@/mobile/components/Common/MenuItem.vue'
import { cloneDeep } from 'lodash-es'
import { useChapterOptions } from '@/composables/useChapterOptions'

const editorStore = useEditorStore()

const showMenu = ref(false)
const showEventTypes = ref(false)
const showDeleteConfirm = ref(false)
const expandedChapters = ref<Record<string, boolean>>({
  _BEGIN_: true,
  '~': true // 默认展开主章节
})
const expandedSubChapters = ref<Record<string, boolean>>({})
const expandedEvents = ref<Record<string, boolean>>({}) // 新增：控制事件的展开状态
const selectedChapter = ref<Scene | null>(null)
const chapterToDelete = ref<any>(null)

const currentSceneId = computed(() => editorStore.currentSceneId)
const currentEventId = computed(() => editorStore.currentEventId)

const { chapterOptions } = useChapterOptions()

const subChapters = computed(() => {
  // 获取子场景（排除特殊场景）
  const regularScenes = editorStore.gameConfig.scenes
    .filter((scene) => scene.id !== '_BEGIN_' && scene.id !== '_END_' && scene.id !== '~')
    .map((scene) => ({ ...scene }))
  // 获取特殊场景
  const beginScene = editorStore.gameConfig.scenes.find(
    (scene) => scene.id === '_BEGIN_' && scene.parent_id === '~'
  )
  const endScene = editorStore.gameConfig.scenes.find((scene) => scene.id === '_END_')
  // 按顺序组合：开始场景 -> 常规场景 -> 结束场景
  return [
    ...(beginScene ? [{ ...beginScene }] : []),
    ...regularScenes,
    ...(endScene ? [{ ...endScene }] : [])
  ]
})

const chapters = computed(() => {
  return editorStore.gameConfig.scenes.filter(
    (scene) =>
      (!scene.parent_id || scene.parent_id === '') && scene.id !== '_BEGIN_' && scene.id !== '_END_'
  )
})

// 修改初始化展开状态的函数
const initializeExpandedStates = () => {
  chapters.value.forEach((chapter) => {
    // Only set if not already defined
    if (expandedChapters.value[chapter.id] === undefined) {
      expandedChapters.value[chapter.id] = true
    }

    subChapters.value.forEach((subChapter) => {
      if (expandedSubChapters.value[subChapter.id] === undefined) {
        expandedSubChapters.value[subChapter.id] = true
      }
    })
  })
}

// 监听 chapters 变化
watch(
  () => chapters.value,
  () => {
    initializeExpandedStates()
  },
  { immediate: true }
)

// 监听当前场景变化
watch(
  () => currentSceneId.value,
  (newSceneId) => {
    if (newSceneId) {
      expandedSubChapters.value[newSceneId] = true
    }
  }
)

// 监听当前事件变化
watch(
  () => currentEventId.value,
  (newEventId) => {
    if (newEventId) {
      expandedEvents.value[newEventId] = true
    }
  }
)

const getChapterName = (chapter: Scene) => {
  if (chapter.id === '_BEGIN_') return '开始章节'
  if (chapter.id === '_END_') return '结束章节'
  return chapter.name || '未命名章节'
}

type EventType = GameEventType | 'interactive'

interface EventTypeItem {
  value: EventType
  label: string
  icon: string
  description?: string
}

const eventTypes: EventTypeItem[] = [
  { value: 'wait', label: '等待', icon: 'IconTimer' },
  { value: 'show_tips', label: '显示提示', icon: 'IconTips' },
  { value: 'show_overlay', label: '显示覆盖层', icon: 'IconLayers' },
  { value: 'message', label: '消息', icon: 'IconMessage' },
  { value: 'show_image', label: '显示图片', icon: 'IconImage' },
  { value: 'animated_images', label: '动画图片', icon: 'IconGif' },
  { value: 'play_video', label: '播放视频', icon: 'IconVideo' },
  { value: 'play_audio', label: '播放音频', icon: 'IconSound' },
  { value: 'show_chat_options', label: '对话选项', icon: 'IconList' },
  { value: 'update_user_coins', label: '更新金币', icon: 'IconCoin' },
  { value: 'update_task_progress', label: '更新任务', icon: 'IconTask' },
  { value: 'scene_transition', label: '场景转换', icon: 'IconSwitch' },
  { value: 'heart_value', label: '好感度', icon: 'IconHeart' },
  {
    value: 'interactive',
    label: '互动事件',
    icon: 'IconMessage',
    description: '添加一个互动事件，用于处理用户交互'
  },
  {
    value: 'show_ending',
    label: '显示结局',
    icon: 'IconEnding'
  }
]

const toggleChapter = (chapterId: string) => {
  expandedChapters.value[chapterId] = !expandedChapters.value[chapterId]
}

const toggleSubChapter = (chapterId: string) => {
  expandedSubChapters.value[chapterId] = !expandedSubChapters.value[chapterId]
  if (expandedSubChapters.value[chapterId]) {
    editorStore.setCurrentScene(chapterId)
  }
}

const toggleEvent = (eventId: string) => {
  if (currentEventId.value === eventId) {
    editorStore.setCurrentEvent('')
  } else {
    editorStore.setCurrentEvent(eventId)
  }
  // 切换事件的展开状态
  expandedEvents.value[eventId] = !expandedEvents.value[eventId]
}

const showChapterMenu = (chapter: Scene) => {
  selectedChapter.value = chapter
  showMenu.value = true
}

const handleAddSubChapter = (chapter: Scene) => {
  // 不允许在特殊场景下添加子章节
  if (chapter.id === '_BEGIN_' || chapter.id === '_END_') {
    Message.warning('不能在特殊场景下添加子章节')
    return
  }

  // 获取当前章节下已有的场景数量
  const existingScenes = editorStore.gameConfig.scenes.filter(
    (s) => s.id !== '_BEGIN_' && s.id !== '_END_' && s.id !== '~'
  )
  console.log('existingScenes', existingScenes)
  const sceneNumber = existingScenes.length + 1

  const newScene: Scene = {
    id: nanoid(),
    name: `章节${sceneNumber}`,
    events: [],
    parent_id: chapter.id
  }

  editorStore.gameConfig.scenes.push(newScene)
  editorStore.isModified = true
  expandedChapters.value[chapter.id] = true
  showMenu.value = false
  Message.success('章节已添加')
}

const handleAddEvent = (chapter: Scene) => {
  selectedChapter.value = chapter
  showEventTypes.value = true
  showMenu.value = false
}

const handleAddEventWithType = (type: EventType) => {
  if (!selectedChapter.value) return
  console.log('type', type)
  if (type === 'interactive') {
    // 添加互动事件
    const handler: ActionHandler = {
      type: 'ScoreLimitWithLLMChatV2',
      params: {
        level: selectedChapter.value.id,
        background: '',
        heart_key: '',
        heart_value: 0,
        clean_history: true,
        limit_chat_count: 8,
        agree_sentences: [],
        streamer_tpl: ''
      }
    }
    editorStore.addActionHandler(selectedChapter.value.id, handler)
  } else {
    // 处理其他事件类型
    const event: Partial<GameEvent> = {
      type: type,
      plot: {}
    }
    editorStore.addEvent(selectedChapter.value.id, event)
  }
  showEventTypes.value = false
}

const handleCopyChapter = (chapter: Scene) => {
  // 不允许复制特殊场景
  if (chapter.id === '_BEGIN_' || chapter.id === '_END_') {
    Message.warning('不能复制特殊场景')
    return
  }

  // 创建复制的章节
  const newId = nanoid()
  const copy: Scene = {
    ...chapter,
    id: newId,
    name: `${chapter.name} (复制)`,
    events: JSON.parse(JSON.stringify(chapter.events))
  }

  // 获取所有场景
  const scenes = editorStore.gameConfig.scenes

  // 找到当前章节在数组中的位置
  const currentIndex = scenes.findIndex((scene) => scene.id === chapter.id)
  if (currentIndex === -1) {
    return
  }

  // 在当前章节后插入新章节
  scenes.splice(currentIndex + 1, 0, copy)

  // 更新后续章节的 parent_id 关系
  if (chapter.parent_id) {
    // 如果是子章节，只需更新同级的子章节
    const siblingChapters = scenes.filter(
      (scene) =>
        scene.parent_id === chapter.parent_id && scene.id !== chapter.id && scene.id !== newId
    )

    // 对同级章节按位置排序
    const sortedSiblings = siblingChapters.sort((a, b) => {
      return scenes.findIndex((s) => s.id === a.id) - scenes.findIndex((s) => s.id === b.id)
    })

    let prevId = ''
    for (let i = 0; i < sortedSiblings.length; i++) {
      const scene = sortedSiblings[i]
      const sceneIndex = scenes.findIndex((s) => s.id === scene.id)

      if (sceneIndex > currentIndex) {
        // 如果是第一个后续章节，其 parent_id 应该指向新复制的章节
        if (prevId === '') {
          prevId = newId
        }

        // 更新 parent_id 为前一个章节的 ID
        scene.parent_id = prevId
        prevId = scene.id
      } else {
        prevId = scene.id
      }
    }
  } else {
    // 如果是主章节，更新后续所有主章节的关系
    const mainChapters = scenes.filter(
      (scene) =>
        (!scene.parent_id || scene.parent_id === '') &&
        scene.id !== '_BEGIN_' &&
        scene.id !== '_END_' &&
        scene.id !== chapter.id &&
        scene.id !== newId
    )

    // 对主章节按位置排序
    const sortedMainChapters = mainChapters.sort((a, b) => {
      return scenes.findIndex((s) => s.id === a.id) - scenes.findIndex((s) => s.id === b.id)
    })

    let prevId = ''
    for (let i = 0; i < sortedMainChapters.length; i++) {
      const scene = sortedMainChapters[i]
      const sceneIndex = scenes.findIndex((s) => s.id === scene.id)

      if (sceneIndex > currentIndex) {
        // 如果是第一个后续章节，其 parent_id 应该指向新复制的章节
        if (prevId === '') {
          prevId = newId
        }

        // 更新 parent_id 为前一个章节的 ID
        scene.parent_id = prevId
        prevId = scene.id
      } else {
        prevId = scene.id
      }
    }
  }

  editorStore.isModified = true
  showMenu.value = false
  Message.success('章节已复制')
}

const handleDeleteChapter = (chapter: Scene) => {
  chapterToDelete.value = chapter
  showDeleteConfirm.value = true
}

const confirmDelete = () => {
  if (!chapterToDelete.value) return

  // 获取要删除的章节
  const deletedId = chapterToDelete.value.id
  const chapterToRemove = editorStore.gameConfig.scenes.find((scene) => scene.id === deletedId)

  if (!chapterToRemove) return

  // 找到该章节的父章节ID
  const parentId = chapterToRemove.parent_id || ''

  // 找到所有以该章节为父章节的子章节
  const childChapters = editorStore.gameConfig.scenes.filter(
    (scene) => scene.parent_id === deletedId
  )

  // 更新所有子章节的parent_id为被删除章节的parent_id
  // 这样可以保持章节链的连续性
  childChapters.forEach((child) => {
    child.parent_id = parentId
  })

  // 只删除当前章节
  editorStore.removeScene(deletedId)
  editorStore.isModified = true

  delete expandedChapters.value[deletedId]
  delete expandedSubChapters.value[deletedId]
  delete expandedEvents.value[deletedId]

  childChapters.forEach((child) => {
    delete expandedSubChapters.value[child.id]
    if (child.events) {
      child.events.forEach((event) => {
        delete expandedEvents.value[event.id]
      })
    }
  })

  showDeleteConfirm.value = false
  chapterToDelete.value = null
  Message.success('章节已删除')
}

const moveEvent = (sceneId: string, index: number, direction: number) => {
  const scene = editorStore.gameConfig.scenes.find((s) => s.id === sceneId)
  if (!scene) return

  const events = [...scene.events]
  const newIndex = index + direction

  if (newIndex < 0 || newIndex >= events.length) return

  const temp = events[index]
  events[index] = events[newIndex]
  events[newIndex] = temp

  scene.events = events
  editorStore.isModified = true
}

const handleRemoveEvent = (sceneId: string, eventId: string) => {
  editorStore.removeEvent(sceneId, eventId)
  Message.success('事件已删除')
}

const updateEvent = (sceneId: string, eventId: string, updated: GameEvent | ActionHandler) => {
  const scene = editorStore.gameConfig.scenes.find((s) => s.id === sceneId)
  if (!scene) return

  if (eventId === 'interactive') {
    // 处理互动事件的更新
    scene.action_handlers = [updated as ActionHandler]
  } else {
    // 处理普通事件的更新
    const eventIndex = scene.events.findIndex((e) => e.id === eventId)
    if (eventIndex !== -1) {
      scene.events[eventIndex] = updated as GameEvent
    }
  }
  editorStore.isModified = true
}

const getEventIcon = (type: GameEventType) => {
  const eventType = eventTypes.find((t) => t.value === type)
  return eventType?.icon || 'IconQuestion'
}

const getEventLabel = (type: GameEventType) => {
  const eventType = eventTypes.find((t) => t.value === type)
  return eventType?.label || '未知事件'
}

const handleEdit = (chapter: Scene) => {
  // TODO: 实现编辑功能
  console.log('Edit chapter:', chapter)
}

const handleMove = (chapter: Scene) => {
  // TODO: 实现移动功能
  console.log('Move chapter:', chapter)
}

const handleDelete = (chapter: Scene) => {
  // TODO: 实现删除功能
  console.log('Delete chapter:', chapter)
}

const getAvailableEventTypes = (chapter: Scene) => {
  // 如果已经有互动事件，则不显示互动事件选项
  const hasInteractiveEvent = chapter?.action_handlers?.length > 0
  return eventTypes.filter((type) => {
    if (type.value === 'interactive') {
      return !hasInteractiveEvent
    }
    return true
  })
}

const handleRemoveInteractiveEvent = (chapter: Scene) => {
  const scene = editorStore.gameConfig.scenes.find((s) => s.id === chapter.id)
  if (scene) {
    scene.action_handlers = []
    editorStore.isModified = true
    Message.success('互动事件已删除')
  }
}

const updateChapterId = (chapter: Scene, newId: string) => {
  // 如果是特殊章节，不允许修改ID
  if (chapter.id === '_BEGIN_' || chapter.id === '_END_') {
    return
  }

  // 如果ID没有变化，直接返回
  if (chapter.id === newId) {
    return
  }

  // 检查ID是否已存在
  const isDuplicate = editorStore.gameConfig.scenes.some(
    (scene) => scene.id === newId && scene.id !== chapter.id
  )
  if (isDuplicate) {
    Message.error('章节ID已存在，请使用其他ID')
    return
  }

  // 保存旧ID，用于更新其他章节的parent_id
  const oldId = chapter.id

  // 在 editorStore 中找到对应的场景
  const sceneToUpdate = editorStore.gameConfig.scenes.find((scene) => scene.id === oldId)
  if (!sceneToUpdate) {
    Message.error('未找到要更新的章节')
    return
  }

  // 更新当前章节的ID
  sceneToUpdate.id = newId

  // 更新当前章节的action_handlers中的level参数
  if (sceneToUpdate.action_handlers?.length > 0) {
    sceneToUpdate.action_handlers = sceneToUpdate.action_handlers.map((handler) => ({
      ...handler,
      params: {
        ...handler.params,
        level: newId
      }
    }))
  }

  // 更新其他章节的parent_id
  editorStore.gameConfig.scenes.forEach((scene) => {
    if (scene.parent_id === oldId) {
      scene.parent_id = newId
    }
  })

  // 更新本地引用
  chapter.id = newId

  editorStore.isModified = true
  Message.success('章节ID已更新')
}

const updateChapterNextSceneId = (chapter: Scene, value: string) => {
  const updatedChapter = cloneDeep(chapter)
  updatedChapter.next_scene_id = value
  editorStore.updateScene(updatedChapter)
}

const validateChapterId = (chapter: Scene) => {
  // 如果是特殊章节，跳过验证
  if (chapter.id === '_BEGIN_' || chapter.id === '_END_') {
    return
  }

  // ID格式验证
  if (!chapter.id || chapter.id.length < 2) {
    Message.error('章节ID长度不能小于2个字符')
    chapter.id = generateUniqueId()
    return
  }

  // ID唯一性验证（排除自身）
  const isDuplicate = editorStore.gameConfig.scenes.some(
    (scene) => scene.id === chapter.id && scene !== chapter
  )
  if (isDuplicate) {
    // 生成一个新的唯一ID
    chapter.id = generateUniqueId()
    Message.error('章节ID已存在，已自动生成新ID')
    return
  }

  // 如果验证通过，更新章节ID
  updateChapterId(chapter, chapter.id)
}

// 生成唯一ID的辅助函数
const generateUniqueId = () => {
  let newId = nanoid(8)
  while (editorStore.gameConfig.scenes.some((scene) => scene.id === newId)) {
    newId = nanoid(8)
  }
  return newId
}

const updateChapterName = (chapter: Scene, name: string) => {
  if (!name.trim()) {
    return
  }

  // 在 editorStore 中找到对应的场景
  const sceneToUpdate = editorStore.gameConfig.scenes.find((scene) => scene.id === chapter.id)
  if (!sceneToUpdate) {
    Message.error('未找到要更新的章节')
    return
  }

  // 更新 store 中的章节名称
  sceneToUpdate.name = name.trim()
  // 更新本地引用
  chapter.name = name.trim()

  editorStore.isModified = true
  Message.success('章节名称已更新')
}

const validateChapterName = (chapter: Scene) => {
  if (!chapter.name || chapter.name.trim().length < 2) {
    Message.error('章节名称长度不能小于2个字符')
    // 设置一个默认名称并更新
    updateChapterName(chapter, '未命名章节')
  }
}

const updateSubChapter = (id: string, key: string, value: string | undefined) => {
  const scene = editorStore.gameConfig.scenes.find((s) => s.id === id)
  if (scene) {
    scene[key] = value
    editorStore.updateScene(scene)
  }
}

const updateConditions = (id: string, value: ConditionValue) => {
  const scene = editorStore.gameConfig.scenes.find((s) => s.id === id)
  if (!scene) return
  scene.conditions = value as unknown as ConditionConfig[]
  editorStore.updateScene(scene)
}

const toggleConditions = (chapter: Scene) => {
  const scene = editorStore.gameConfig.scenes.find((s) => s.id === chapter.id)
  if (!scene) return

  if (scene.conditions) {
    // 如果已有条件，则删除
    delete scene.conditions
  } else {
    // 如果没有条件，则添加一个默认条件
    scene.conditions = [
      {
        type: 'single',
        attribute: 'intelligence',
        operator: '>',
        value: 0,
        echo: '',
        expression: ''
      }
    ]
  }
  editorStore.updateScene(scene)
}
</script>

<style lang="less" scoped>
.chapter-list {
  .chapter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    .title {
      font-size: 17px;
      font-weight: 600;
      color: #fff;
    }
  }

  .chapter-content {
    padding: 16px;
  }

  .chapter-item {
    margin-bottom: 8px;

    .chapter-header {
      display: flex;
      justify-content: space-between;
      // align-items: center;
      padding: 12px 16px;
      background: rgba(204, 213, 255, 0.05);
      border-radius: 12px;
      cursor: pointer;

      &:active {
        background: rgba(204, 213, 255, 0.08);
      }

      .chapter-info {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #fff;

        .arco-icon {
          font-size: 20px;
          color: #ca93f2;
        }

        .chapter-name {
          font-size: 15px;
        }
      }
    }

    .sub-chapters {
      margin: 8px 0 8px 24px;

      .sub-chapter-item {
        margin-bottom: 8px;
        background: rgba(204, 213, 255, 0.03);
        border-radius: 12px;
        overflow: hidden;

        &.active {
          background: rgba(202, 147, 242, 0.1);
        }

        .sub-chapter-header {
          display: flex;
          justify-content: space-between;
          // align-items: center;
          padding: 12px 16px;
          cursor: pointer;

          &:active {
            background: rgba(204, 213, 255, 0.05);
          }

          .sub-chapter-info {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #fff;

            .arco-icon {
              font-size: 18px;
              color: #ca93f2;
            }

            .sub-chapter-edit {
              display: flex;
              flex-direction: column;
              gap: 4px;
              flex: 1;

              .form-item {
                margin-bottom: 16px;

                .form-label {
                  display: block;
                  margin-bottom: 8px;
                  color: #fff;
                }

                .custom-textarea {
                  width: 100%;
                  min-height: 80px;
                  padding: 12px 16px;
                  border: 1px solid rgba(184, 196, 255, 0.1);
                  background: rgba(204, 213, 255, 0.05);
                  color: #fff;
                  border-radius: 8px;
                  font-size: 14px;
                  outline: none;
                  transition: all 0.3s ease;
                  resize: vertical;

                  &:hover,
                  &:focus {
                    border-color: #ca93f2;
                    background: rgba(204, 213, 255, 0.08);
                  }

                  &::placeholder {
                    color: rgba(255, 255, 255, 0.3);
                  }
                }
              }
            }
          }
        }

        .event-list {
          border-top: 1px solid rgba(255, 255, 255, 0.1);

          .event-item {
            .event-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 12px 16px;
              cursor: pointer;

              &:active {
                background: rgba(204, 213, 255, 0.05);
              }

              .event-info {
                display: flex;
                align-items: center;
                gap: 8px;
                color: #fff;

                .event-icon {
                  font-size: 16px;
                  color: #ca93f2;
                }

                .event-title {
                  display: flex;
                  align-items: center;
                  gap: 4px;
                }
              }
            }

            .event-content {
              padding: 16px;
              border-top: 1px solid rgba(255, 255, 255, 0.1);
            }

            &.active {
              background: rgba(202, 147, 242, 0.1);
            }
          }
        }
      }
    }
  }
}

.menu-list {
  .menu-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    color: #fff;
    font-size: 15px;
    cursor: pointer;

    &:active {
      background: rgba(204, 213, 255, 0.05);
    }

    &.danger {
      color: #ff4d4f;

      .arco-icon {
        color: #ff4d4f;
      }
    }

    .arco-icon {
      font-size: 20px;
      color: #ca93f2;
    }
  }
}

.event-types {
  .event-type-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    color: #fff;
    font-size: 15px;
    cursor: pointer;

    &:active {
      background: rgba(204, 213, 255, 0.05);
    }

    .type-icon {
      font-size: 20px;
      color: #ca93f2;
    }

    .type-label {
      display: flex;
      align-items: center;
      gap: 4px;

      .type-key {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.5);
      }
    }
  }
}

.scene-dialog {
  .scene-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    .title {
      font-size: 17px;
      font-weight: 600;
      color: #fff;
    }
  }

  .scene-content {
    padding: 16px;
  }

  .scene-item {
    margin-bottom: 16px;

    label {
      display: block;
      margin-bottom: 8px;
      color: #fff;
    }
  }
}

.action-button {
  width: 28px;
  height: 28px;
  padding: 0;
  border: none;
  background: none;
  color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(204, 213, 255, 0.05);
    color: #fff;
  }

  .icon-more {
    width: 20px;
    height: 20px;
  }
}

.menu-item-content {
  display: flex;
  align-items: center;
  gap: 8px;

  .icon {
    width: 16px;
    height: 16px;
  }
}

.event-title {
  display: flex;
  align-items: center;
  gap: 4px;

  .event-type {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.5);
  }
}

.scene-generation {
  margin-top: 16px;
  padding: 16px;
  background: rgba(204, 213, 255, 0.05);
  border-radius: 12px;

  .scene-generation-header {
    margin-bottom: 16px;
    font-size: 17px;
    font-weight: 600;
    color: #fff;
  }

  .scene-generation-content {
    .form-item {
      margin-bottom: 16px;

      .form-label {
        display: block;
        margin-bottom: 8px;
        color: #fff;
      }

      .custom-textarea {
        width: 100%;
        padding: 8px;
        background: transparent;
        border: 1px solid transparent;
        color: #fff;
        border-radius: 4px;
        font-size: 14px;
        transition: all 0.3s;

        &:hover,
        &:focus {
          border-color: rgba(202, 147, 242, 0.3);
          background: rgba(204, 213, 255, 0.05);
        }
      }
    }

    .character-list {
      .character-item {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;

        .character-inputs {
          display: flex;
          gap: 8px;

          input {
            background: transparent;
            border: 1px solid transparent;
            color: #fff;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 14px;
            width: 100%;
            transition: all 0.3s;

            &:hover,
            &:focus {
              border-color: rgba(202, 147, 242, 0.3);
              background: rgba(204, 213, 255, 0.05);
            }
          }
        }

        .delete-button {
          width: 28px;
          height: 28px;
          padding: 0;
          border: none;
          background: none;
          color: rgba(255, 255, 255, 0.5);
          cursor: pointer;
          border-radius: 6px;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.2s ease;

          &:hover {
            background: rgba(204, 213, 255, 0.05);
            color: #ff4d4f;
          }
        }
      }

      .add-button {
        width: 100%;
        padding: 8px;
        background: transparent;
        border: none;
        color: #fff;
        font-size: 15px;
        cursor: pointer;
        border-radius: 4px;
        display: flex;
        align-items: center;
        gap: 8px;
        transition: all 0.2s ease;

        &:hover {
          background: rgba(204, 213, 255, 0.05);
        }
      }
    }
  }
}

.sub-chapter-edit {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;

  input {
    background: transparent;
    border: 1px solid transparent;
    color: #fff;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 14px;
    width: 100%;
    transition: all 0.3s;

    &:hover,
    &:focus {
      border-color: rgba(202, 147, 242, 0.3);
      background: rgba(204, 213, 255, 0.05);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    &.error {
      border-color: #ff4d4f;
    }
  }

  .sub-chapter-name-input {
    font-weight: 500;
  }

  .sub-chapter-id-input {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
  }

  .form-item {
    margin-bottom: 8px;

    .form-label {
      font-size: 12px;
      color: rgba(255, 255, 255, 0.7);
      margin-bottom: 4px;
    }

    .custom-textarea {
      width: 100%;
      min-height: 80px;
      padding: 12px 16px;
      border: 1px solid rgba(184, 196, 255, 0.1);
      background: rgba(204, 213, 255, 0.05);
      color: #fff;
      border-radius: 8px;
      font-size: 14px;
      outline: none;
      transition: all 0.3s ease;
      resize: vertical;

      &:hover,
      &:focus {
        border-color: #ca93f2;
        background: rgba(204, 213, 255, 0.08);
      }

      &::placeholder {
        color: rgba(255, 255, 255, 0.3);
      }
    }
  }
}

.condition-builder {
  .condition-type {
    margin-bottom: 12px;
  }

  .condition-content {
    background: rgba(204, 213, 255, 0.05);
    border-radius: 8px;
    padding: 12px;
  }

  .attribute-condition {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;

    .value-input {
      width: 80px;
      height: 42px;
      padding: 0 12px;
      border: 1px solid rgba(184, 196, 255, 0.1);
      background: rgba(204, 213, 255, 0.05);
      color: #fff;
      border-radius: 8px;
      font-size: 14px;
      outline: none;

      &:hover,
      &:focus {
        border-color: #ca93f2;
        background: rgba(204, 213, 255, 0.08);
      }
    }

    .delete-condition {
      width: 28px;
      height: 28px;
      padding: 0;
      border: none;
      background: none;
      color: rgba(255, 255, 255, 0.5);
      cursor: pointer;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;

      &:hover {
        background: rgba(204, 213, 255, 0.05);
        color: #ff4d4f;
      }

      .icon-delete {
        width: 16px;
        height: 16px;
      }
    }
  }

  .add-condition {
    width: 100%;
    height: 42px;
    padding: 0 12px;
    border: 1px dashed rgba(184, 196, 255, 0.1);
    background: none;
    color: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.2s ease;

    &:hover {
      border-color: #ca93f2;
      color: #ca93f2;
    }

    .icon-add {
      width: 16px;
      height: 16px;
    }

    span {
      font-size: 14px;
    }
  }
}

.condition-toggle {
  margin-bottom: 12px;

  .custom-checkbox {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    user-select: none;

    input[type='checkbox'] {
      position: relative;
      width: 36px;
      height: 20px;
      appearance: none;
      background: rgba(204, 213, 255, 0.1);
      border-radius: 10px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:checked {
        background: #ca93f2;

        &::before {
          transform: translateX(16px);
        }
      }

      &::before {
        content: '';
        position: absolute;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        top: 2px;
        left: 2px;
        background: #fff;
        transition: all 0.3s ease;
      }
    }

    span {
      color: #fff;
      font-size: 14px;
    }
  }
}
</style>
