<template>
  <div class="action-bar">
    <div class="action-group">
      <template v-if="!hasScenes">
        <button class="action-button" @click="handleCreate">
          <div class="button-content">
            <div class="icon">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path d="M12 5v14M5 12h14" />
              </svg>
            </div>
            <span>新建故事场景</span>
          </div>
        </button>
        <button class="action-button" @click="handleImport">
          <div class="button-content">
            <div class="icon">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4M7 10l5 5 5-5M12 15V3" />
              </svg>
            </div>
            <span>导入故事场景配置</span>
          </div>
        </button>
      </template>
      <template v-else>
        <button class="action-button" @click="handleExport">
          <div class="button-content">
            <div class="icon">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4M7 10l5-5 5 5M12 4v12" />
              </svg>
            </div>
            <span>导出故事配置</span>
          </div>
        </button>

        <!-- <button class="action-button" @click="handlePreview">
          <div class="button-content">
            <div class="icon">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" />
                <circle cx="12" cy="12" r="3" />
              </svg>
            </div>
            <span>预览</span>
          </div>
        </button> -->
      </template>
      <button class="action-button" @click="handleSave">
        <div class="button-content">
          <div class="icon">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
            >
              <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z" />
              <polyline points="17 21 17 13 7 13 7 21" />
              <polyline points="7 3 7 8 15 8" />
            </svg>
          </div>
          <span>保存故事</span>
        </div>
      </button>

      <button class="action-button" @click="handleFlowEditor">
        <div class="button-content">
          <div class="icon">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
            >
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="3" y1="9" x2="21" y2="9"></line>
              <line x1="9" y1="21" x2="9" y2="9"></line>
            </svg>
          </div>
          <span>流程编辑器</span>
        </div>
      </button>

      <button
        v-if="editorStore.storyConfig.project_id"
        class="action-button"
        @click="handlePublish"
      >
        <div class="button-content">
          <div class="icon">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
            >
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
              <polyline points="17 8 12 3 7 8" />
              <line x1="12" y1="3" x2="12" y2="15" />
            </svg>
          </div>
          <span>发布故事</span>
        </div>
      </button>
    </div>
    <input
      ref="fileInputRef"
      type="file"
      accept=".yaml,.yml"
      class="file-input"
      @change="onFileChange"
    />

    <SceneDialog v-model:visible="showSceneDialog" @confirm="handleSceneConfirm" />
    <ConfirmDialog
      v-model:visible="showPublishConfirm"
      title="发布确认"
      confirmText="确定"
      cancelText="取消"
      content="确定要发布这个故事吗？发布后将无法修改。"
      @confirm="confirmPublish"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useEditorStore } from '@/store/editor'
import { Message } from '@/mobile/components/Message'
import SceneDialog from './SceneDialog.vue'
import ConfirmDialog from '@/mobile/components/ConfirmDialog.vue'
import { publishStory } from '@/api/editor'
import { nanoid } from 'nanoid'

const router = useRouter()
const route = useRoute()
const editorStore = useEditorStore()
const fileInputRef = ref<HTMLInputElement>()
const showSceneDialog = ref(false)
const showPublishConfirm = ref(false)

const hasScenes = computed(
  () => editorStore.gameConfig.scenes.filter((scene) => scene.id !== '~').length > 0
)

const handleCreate = () => {
  showSceneDialog.value = true
}

const handleSceneConfirm = (data: { name: string }) => {
  // 创建父场景
  const parentScene = {
    id: '~',
    name: data.name,
    events: []
  }
  editorStore.gameConfig.scenes.push(parentScene)

  // 创建开始场景
  const beginScene = {
    id: '_BEGIN_',
    name: '开始章节',
    events: [],
    parent_id: parentScene.id
  }
  editorStore.gameConfig.scenes.push(beginScene)

  // 创建场景1
  const scene1 = {
    id: nanoid(),
    name: '场景1',
    events: [],
    parent_id: parentScene.id
  }
  editorStore.gameConfig.scenes.push(scene1)

  // 创建结束场景
  const endScene = {
    id: '_END_',
    name: '结束章节',
    events: [],
    parent_id: parentScene.id
  }
  editorStore.gameConfig.scenes.push(endScene)

  Message.success('场景已创建')
  showSceneDialog.value = false
}

const handleImport = () => {
  fileInputRef.value?.click()
}

const onFileChange = async (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (!file) return

  try {
    const content = await file.text()
    const success = editorStore.importFromYAML(content)
    if (success) {
      Message.success('导入成功')
    } else {
      Message.error('导入失败')
    }
  } catch (error) {
    Message.error('导入失败')
  }

  // 重置 input 以允许重复导入相同文件
  if (fileInputRef.value) {
    fileInputRef.value.value = ''
  }
}

const handleExport = () => {
  const yaml = editorStore.exportToYAML()
  const blob = new Blob([yaml], { type: 'text/yaml' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = 'game-config.yaml'
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
  Message.success('导出成功')
}

const handleSave = async () => {
  try {
    await editorStore.saveStory()
    Message.success('保存成功')

    // 如果当前是新建故事，保存成功后更新路由
    if (route.params.id === 'new' && editorStore.currentStoryId) {
      router.replace(`/creation/editor/${editorStore.currentStoryId}`)
    }
  } catch (error) {
    if (error instanceof Error) {
      Message.error(error.message)
    } else {
      Message.error('保存失败')
    }
  }
}

const handlePreview = () => {
  // TODO: 实现预览功能
  Message.info('预览功能开发中')
}

const handlePublish = async () => {
  // 检查必要条件
  if (!editorStore.storyConfig.project_name) {
    Message.error('请先输入故事名称')
    return
  }

  if (!editorStore.storyConfig.actor_id) {
    Message.error('请先选择故事角色')
    return
  }

  // 如果有未保存的修改，先保存
  if (editorStore.isModified) {
    try {
      await editorStore.saveStory()
    } catch (error) {
      Message.error('保存故事失败，请重试')
      return
    }
  }

  showPublishConfirm.value = true
}

const confirmPublish = async () => {
  try {
    await publishStory(editorStore.storyConfig.project_id)
    Message.success('发布成功')
    showPublishConfirm.value = false
    // 发布成功后跳转到故事列表页
    router.replace('/creation')
  } catch (error) {
    Message.error('发布失败，请重试')
  }
}

const handleFlowEditor = async () => {
  try {
    // 如果有未保存的修改，先保存
    if (editorStore.isModified) {
      await editorStore.saveStory()
    }

    // 跳转到流程编辑器
    const storyId = editorStore.currentStoryId || route.params.id
    router.push(`/pc/flow-editor/${storyId}`)
  } catch (error) {
    Message.error('保存故事失败，请先保存再进入流程编辑器')
  }
}
</script>

<style lang="less" scoped>
.action-bar {
  padding: 16px;
  background: linear-gradient(180deg, rgba(31, 0, 56, 0) 0%, #1f0038 100%);
  backdrop-filter: blur(8px);
  position: sticky;
  bottom: 0;
  z-index: 10;

  .action-group {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 12px;
  }

  .action-button {
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    color: #fff;
    transition: transform 0.2s;
    -webkit-tap-highlight-color: transparent;

    &:active {
      transform: scale(0.95);

      .button-content {
        background: rgba(202, 147, 242, 0.2);
      }
    }

    .button-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;
      padding: 12px;
      border-radius: 16px;
      background: rgba(204, 213, 255, 0.05);
      transition: background-color 0.2s;
    }

    .icon {
      width: 24px;
      height: 24px;
      color: #ca93f2;

      svg {
        width: 100%;
        height: 100%;
      }
    }

    span {
      font-size: 12px;
      color: rgba(255, 255, 255, 0.8);
    }
  }

  .file-input {
    display: none;
  }
}
</style>
