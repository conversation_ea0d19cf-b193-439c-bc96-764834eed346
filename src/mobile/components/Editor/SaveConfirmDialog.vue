<template>
  <ConfirmDialog
    v-model:visible="modelValue"
    title="保存提示"
    content="当前有未保存的更改，是否保存？"
    confirm-text="保存"
    cancel-text="不保存"
    :show-cancel="true"
    @confirm="$emit('confirm')"
    @cancel="$emit('cancel')"
  >
    <template #icon>
      <icon-save />
    </template>
  </ConfirmDialog>
</template>

<script setup lang="ts">
import ConfirmDialog from '@/mobile/components/ConfirmDialog.vue'

const modelValue = defineModel<boolean>()

defineEmits<{
  (e: 'confirm'): void
  (e: 'cancel'): void
}>()
</script>
