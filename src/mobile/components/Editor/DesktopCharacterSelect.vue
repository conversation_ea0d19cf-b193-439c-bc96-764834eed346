<template>
  <div class="desktop-character-select" v-if="visible">
    <div class="modal-content">
      <div class="modal-header">
        <h2>Select Character</h2>
        <button class="close-button" @click="handleClose" :disabled="isLoading">
          <icon-close />
        </button>
      </div>

      <div class="loading-overlay" v-if="isLoading">
        <div class="loading-spinner"></div>
      </div>

      <div class="modal-body">
        <div class="character-grid">
          <div
            v-for="actor in actors"
            :key="actor.id"
            class="character-card"
            :class="{ 'is-selected': selectedActor?.id === actor.id }"
            @click="selectActor(actor)"
          >
            <div class="card-content">
              <img :src="actor.preview_url" :alt="actor.name" class="preview-url" />
              <div class="character-info">
                <div class="character-name">{{ actor.name }}</div>
                <div v-if="!actor.is_purchased" class="cost-badge">
                  <img
                    src="https://cdn.magiclight.ai/assets/mobile/diamond.png"
                    class="diamond-icon"
                  />
                  {{ getActorCost(actor) }}
                </div>
                <div v-else-if="actor.is_purchased && actor.coins > 0" class="unlocked-badge">
                  Unlocked
                </div>
                <div v-else class="unlocked-badge">Free</div>
              </div>
            </div>
          </div>
        </div>

        <div class="selected-character-details" v-if="selectedActor">
          <div class="details-header">
            <h3>{{ selectedActor.name }}</h3>
            <div class="header-actions">
              <button v-if="isEditing" class="cancel-button" @click="cancelEdit">Cancel</button>
              <button class="edit-button" @click="toggleEdit" :class="{ active: isEditing }">
                {{ isEditing ? 'Save' : 'Edit' }}
              </button>
            </div>
          </div>
          <div class="details-content">
            <!-- Basic Info -->
            <div class="detail-section">
              <h4>Basic Information</h4>
              <div class="detail-item">
                <div class="label">Name:</div>
                <div class="value" v-if="!isEditing">{{ selectedActor.name }}</div>
                <input v-else v-model="editedActor.name" placeholder="Name" class="edit-input" />
              </div>
              <div class="detail-item">
                <div class="label">Subtitle:</div>
                <div class="value" v-if="!isEditing">{{ selectedActor.subtitle }}</div>
                <input
                  v-else
                  v-model="editedActor.subtitle"
                  placeholder="Subtitle"
                  class="edit-input"
                />
              </div>
              <div class="detail-item">
                <div class="label">Avatar URL:</div>
                <div class="value image-preview" v-if="!isEditing">
                  <img :src="selectedActor.avatar_url" :alt="selectedActor.name + ' avatar'" />
                </div>
                <MediaSelector
                  v-else
                  type="image"
                  v-model="editedActor.avatar_url"
                  v-model:preview="editedActor.avatar_url"
                />
              </div>
              <div class="detail-item">
                <div class="label">Preview URL:</div>
                <div class="value image-preview" v-if="!isEditing">
                  <img :src="selectedActor.preview_url" :alt="selectedActor.name + ' preview'" />
                </div>
                <MediaSelector
                  v-else
                  type="image"
                  v-model="editedActor.preview_url"
                  v-model:preview="editedActor.preview_url"
                />
              </div>
              <div class="detail-item">
                <div class="label">VoiceId:</div>
                <div class="value" v-if="!isEditing">{{ selectedActor.chat_config?.voice_id }}</div>
              </div>
              <div class="detail-item">
                <div class="label">Coins:</div>
                <div class="value" v-if="!isEditing">{{ selectedActor.coins }}</div>
                <input
                  v-else
                  v-model.number="editedActor.coins"
                  type="number"
                  placeholder="Coins"
                  class="edit-input"
                />
              </div>
              <div class="detail-item">
                <div class="label">Version:</div>
                <div class="value" v-if="!isEditing">{{ selectedActor.version }}</div>
                <input
                  v-else
                  v-model="editedActor.version"
                  placeholder="Version"
                  class="edit-input"
                />
              </div>
              <div class="detail-item">
                <div class="label">Is Purchased:</div>
                <div class="value" v-if="!isEditing">
                  {{ selectedActor.is_purchased ? 'Yes' : 'No' }}
                </div>
                <select v-else v-model="editedActor.is_purchased" class="edit-input">
                  <option :value="true">Yes</option>
                  <option :value="false">No</option>
                </select>
              </div>
            </div>

            <!-- Extra Fields -->
            <div class="detail-section">
              <h4>Extra Information</h4>
              <div v-for="(value, key) in selectedActor.extra" :key="key" class="detail-item">
                <div class="label">{{ key }}:</div>
                <div class="value" v-if="!isEditing">{{ value || 'Unknown' }}</div>
                <input
                  v-else
                  v-model="editedExtra[key]"
                  :placeholder="key"
                  class="edit-input"
                  @keyup.enter="saveChanges"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <div class="coins-info">Current coins: {{ userStore.userInfo?.coins || 0 }}</div>
        <button
          class="confirm-button"
          :disabled="!selectedActor || isLoading"
          @click="handleConfirm"
        >
          <span v-if="isLoading">Loading...</span>
          <template v-else>
            Confirm
            <span v-if="selectedActor && !selectedActor.is_purchased" class="cost">
              <img src="https://cdn.magiclight.ai/assets/mobile/diamond.png" class="diamond-icon" />
              {{ getActorCost(selectedActor) }}
            </span>
          </template>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, reactive } from 'vue'
import IconClose from '@/assets/icon/close.svg'
// import IconEdit from '@/assets/icon/edit.svg'
import { Message } from '@/mobile/components/Message'
import { useUserStore, useStoryStore } from '@/store'
import type { Actor } from '@/api/stories'
import { getUserChatHistory } from '@/api/chat'
import { updateActor } from '@/api/editor'
import MediaSelector from '@/mobile/components/Common/MediaSelector.vue'
import { isEqual } from 'lodash-es'

const props = defineProps<{
  visible: boolean
  actors: Actor[]
  storyIsPurchased: boolean
  storyCoins: number
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'select', actor: Actor): void
}>()

const userStore = useUserStore()
const storyStore = useStoryStore()
const selectedActor = ref<Actor | null>(props.actors[0])
const isLoading = ref(false)
const isEditing = ref(false)
const editedExtra = reactive<Record<string, string>>({})

interface EditedActor {
  name: string
  subtitle: string
  avatar_url: string
  preview_url: string
  coins: number
  is_purchased: boolean
  version: string
  chat_config?: Record<string, string>
}

const editedActor = reactive<EditedActor>({
  name: '',
  subtitle: '',
  avatar_url: '',
  preview_url: '',
  coins: 0,
  is_purchased: false,
  version: '',
  chat_config: {}
})

watch(
  () => props.visible,
  (newValue) => {
    if (!newValue) {
      isLoading.value = false
      isEditing.value = false
      // Reset editedExtra when modal closes
      Object.keys(editedExtra).forEach((key) => delete editedExtra[key])
    }
  }
)

watch(
  () => selectedActor.value,
  (newActor) => {
    // Reset editing state when selecting a new actor
    isEditing.value = false
    // Clear previous values
    Object.keys(editedExtra).forEach((key) => delete editedExtra[key])
    // Initialize with new actor's extra data
    if (newActor?.extra) {
      Object.entries(newActor.extra).forEach(([key, value]) => {
        editedExtra[key] = String(value || '')
      })
    }
  }
)

const getActorCost = (actor: Actor) => {
  if (!actor) return 0
  const totalCost = actor.coins + (props.storyIsPurchased ? 0 : props.storyCoins)
  return totalCost === 0 ? 'Free' : totalCost
}

const selectActor = (actor: Actor) => {
  selectedActor.value = actor
}

const handleClose = () => {
  if (!isLoading.value) {
    selectedActor.value = null
    emit('update:visible', false)
  }
}

const handleConfirm = async () => {
  if (!selectedActor.value || isLoading.value) return
  isLoading.value = true

  try {
    // const { data } = await getUserChatHistory(storyStore.currentStory?.id, selectedActor.value.id)
    emit('select', selectedActor.value)
  } catch (error) {
    console.error('Failed to check chat history:', error)
    Message.error('Failed to check chat history')
    isLoading.value = false
  }
}

const hasChanges = () => {
  if (!selectedActor.value) return false

  const originalActor = {
    name: selectedActor.value.name,
    subtitle: selectedActor.value.subtitle,
    avatar_url: selectedActor.value.avatar_url,
    preview_url: selectedActor.value.preview_url,
    coins: selectedActor.value.coins,
    is_purchased: selectedActor.value.is_purchased,
    version: selectedActor.value.version,
    extra: selectedActor.value.extra
  }

  const currentActor = {
    name: editedActor.name,
    subtitle: editedActor.subtitle,
    avatar_url: editedActor.avatar_url,
    preview_url: editedActor.preview_url,
    coins: editedActor.coins,
    is_purchased: editedActor.is_purchased,
    version: editedActor.version,
    extra: editedExtra
  }

  return !isEqual(originalActor, currentActor)
}

const cancelEdit = () => {
  isEditing.value = false
  // Reset form data
  if (selectedActor.value) {
    editedActor.name = selectedActor.value.name
    editedActor.subtitle = selectedActor.value.subtitle
    editedActor.avatar_url = selectedActor.value.avatar_url
    editedActor.preview_url = selectedActor.value.preview_url
    editedActor.coins = selectedActor.value.coins
    editedActor.is_purchased = selectedActor.value.is_purchased
    editedActor.version = selectedActor.value.version

    // Reset extra values
    Object.keys(editedExtra).forEach((key) => delete editedExtra[key])
    if (selectedActor.value.extra) {
      Object.entries(selectedActor.value.extra).forEach(([key, value]) => {
        editedExtra[key] = value || ''
      })
    }
  }
}

const toggleEdit = async () => {
  if (isEditing.value) {
    if (hasChanges()) {
      await saveChanges()
    } else {
      isEditing.value = false
    }
  } else {
    // Start editing - initialize editedActor and editedExtra with current values
    if (selectedActor.value) {
      editedActor.name = selectedActor.value.name
      editedActor.subtitle = selectedActor.value.subtitle
      editedActor.avatar_url = selectedActor.value.avatar_url
      editedActor.preview_url = selectedActor.value.preview_url
      editedActor.coins = selectedActor.value.coins
      editedActor.is_purchased = selectedActor.value.is_purchased
      editedActor.version = selectedActor.value.version

      // Clear previous extra values
      Object.keys(editedExtra).forEach((key) => delete editedExtra[key])
      // Set new extra values
      if (selectedActor.value.extra) {
        Object.entries(selectedActor.value.extra).forEach(([key, value]) => {
          editedExtra[key] = value || ''
        })
      }
    }
    isEditing.value = true
  }
}

const saveChanges = async () => {
  if (!selectedActor.value) return

  try {
    isLoading.value = true
    const updatedActor = {
      ...selectedActor.value,
      ...editedActor,
      extra: { ...editedExtra }
    }

    const { data } = await updateActor(updatedActor)
    if (data.code === '0') {
      // Update the local actor data with the edited data
      if (selectedActor.value) {
        Object.assign(selectedActor.value, editedActor)
        selectedActor.value.extra = { ...editedExtra }
      }
      Message.success('Character updated successfully')
      isEditing.value = false
    } else {
      throw new Error(data.message || 'Failed to update character')
    }
  } catch (error) {
    console.error('Failed to update character:', error)
    Message.error(error instanceof Error ? error.message : 'Failed to update character')
  } finally {
    isLoading.value = false
  }
}
</script>

<style lang="less" scoped>
.desktop-character-select {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;
}

.modal-content {
  width: 90%;
  max-width: 1200px;
  max-height: 90vh;
  background: linear-gradient(180deg, #2b1b3b 0%, #1a0f24 100%);
  border-radius: 24px;
  display: flex;
  flex-direction: column;
  animation: scaleIn 0.3s ease;
  overflow: hidden;
}

.modal-header {
  padding: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  h2 {
    color: white;
    font-size: 24px;
    font-weight: 600;
    margin: 0;
  }
}

.close-button {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: transparent;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  padding: 0;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  :deep(svg) {
    width: 24px;
    height: 24px;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.modal-body {
  flex: 1;
  display: flex;
  gap: 24px;
  padding: 24px;
  overflow: hidden;
}

.character-grid {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  overflow-y: auto;
  padding-right: 16px;
  padding-bottom: 16px;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }
}

.character-card {
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 16px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid transparent;
  height: 360px;

  &:hover {
    transform: translateY(-4px);
    border-color: rgba(202, 147, 242, 0.5);
  }

  &.is-selected {
    border-color: #ca93f2;
    box-shadow: 0 0 20px rgba(202, 147, 242, 0.3);
    transform: translateY(-4px);

    .character-name {
      color: #ca93f2;
    }
  }

  .card-content {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .preview-url {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .character-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 16px;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  }

  .character-name {
    font-size: 18px;
    font-weight: 600;
    color: white;
    margin-bottom: 8px;
    transition: color 0.3s ease;
  }

  .cost-badge,
  .unlocked-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    backdrop-filter: blur(4px);
  }

  .cost-badge {
    background: rgba(0, 0, 0, 0.6);
    color: #daff96;

    .diamond-icon {
      width: 16px;
      height: 16px;
    }
  }

  .unlocked-badge {
    background: rgba(218, 255, 150, 0.2);
    color: #daff96;
  }
}

.selected-character-details {
  width: 300px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: calc(90vh - 150px);
  overflow: hidden;

  .details-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    flex-shrink: 0;
    position: sticky;
    top: 0;
    margin: -20px -20px 0;
    padding: 20px;
    z-index: 1;
    backdrop-filter: blur(8px);

    h3 {
      color: white;
      font-size: 20px;
      font-weight: 600;
      margin: 0;
    }

    .header-actions {
      display: flex;
      gap: 8px;
      align-items: center;
    }

    .cancel-button {
      height: 32px;
      padding: 0 12px;
      border-radius: 16px;
      background: rgba(255, 77, 79, 0.1);
      border: none;
      color: #ff4d4f;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: rgba(255, 77, 79, 0.2);
      }
    }

    .edit-button {
      display: flex;
      align-items: center;
      gap: 6px;
      height: 32px;
      padding: 0 12px;
      border-radius: 16px;
      background: rgba(255, 255, 255, 0.1);
      border: none;
      color: white;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: rgba(202, 147, 242, 0.2);
      }

      &.active {
        background: rgba(202, 147, 242, 0.3);
        color: #ca93f2;
      }

      :deep(svg) {
        width: 16px;
        height: 16px;
      }
    }
  }

  .details-content {
    display: flex;
    flex-direction: column;
    gap: 12px;
    overflow-y: auto;
    padding: 16px 8px 0 0;
    margin-top: 4px;
    height: 100%;

    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.05);
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.2);
      border-radius: 4px;

      &:hover {
        background: rgba(255, 255, 255, 0.3);
      }
    }
  }

  .detail-item {
    margin-bottom: 12px;

    .label {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.6);
      margin-bottom: 4px;
    }

    .value {
      font-size: 16px;
      color: white;
      font-weight: 500;
      word-break: break-all;

      &.image-preview {
        word-break: normal;

        img {
          width: 100%;
          height: auto;
          max-height: 200px;
          object-fit: contain;
          border-radius: 8px;
          background: rgba(255, 255, 255, 0.05);

          &[alt$='avatar'] {
            width: 64px;
            height: 64px;
            border-radius: 50%;
            object-fit: cover;
            display: block;
          }
        }
      }
    }

    .edit-input {
      width: 100%;
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      color: white;
      font-size: 14px;
      padding: 8px 12px;
      transition: all 0.2s ease;

      &:hover {
        border-color: rgba(202, 147, 242, 0.5);
        background: rgba(255, 255, 255, 0.08);
      }

      &:focus {
        border-color: #ca93f2;
        background: rgba(255, 255, 255, 0.08);
        box-shadow: 0 0 0 2px rgba(202, 147, 242, 0.2);
        outline: none;
      }

      &[type='number'] {
        appearance: none;
        -moz-appearance: textfield;
        &::-webkit-outer-spin-button,
        &::-webkit-inner-spin-button {
          -webkit-appearance: none;
          margin: 0;
        }
      }
    }

    select.edit-input {
      appearance: none;
      background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3e%3cpath d='M7 10l5 5 5-5z'/%3e%3c/svg%3e");
      background-repeat: no-repeat;
      background-position: right 8px center;
      background-size: 16px;
      padding-right: 32px;
      cursor: pointer;

      &:hover {
        background-color: rgba(255, 255, 255, 0.08);
      }
    }
  }
}

.detail-section {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }

  h4 {
    color: rgba(255, 255, 255, 0.9);
    font-size: 16px;
    font-weight: 500;
    margin: 0 0 12px;
    position: relative;
    top: 0;
    background: inherit;
    padding: 4px 0;
  }
}

.modal-footer {
  padding: 24px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.coins-info {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

.confirm-button {
  min-width: 200px;
  height: 44px;
  border-radius: 22px;
  border: none;
  background: linear-gradient(90deg, #ca93f2 0%, #9b6cc8 100%);
  color: white;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &:not(:disabled):hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(202, 147, 242, 0.3);
  }

  &:not(:disabled):active {
    transform: translateY(0);
  }

  .cost {
    display: flex;
    align-items: center;
    gap: 4px;

    .diamond-icon {
      width: 20px;
      height: 20px;
    }
  }
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(26, 15, 36, 0.8);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(202, 147, 242, 0.3);
  border-radius: 50%;
  border-top-color: #ca93f2;
  animation: spin 1s linear infinite;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

select.edit-input {
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3e%3cpath d='M7 10l5 5 5-5z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-size: 16px;
  padding-right: 32px;
}
</style>
