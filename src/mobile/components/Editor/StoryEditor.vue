<template>
  <div class="story-editor">
    <!-- 移动端时显示返回按钮和全局配置 -->
    <template v-if="!isDesktop">
      <div class="editor-header">
        <div class="back-button" @click="router.back()">
          <icon-left />
          <span>点击返回</span>
        </div>
      </div>

      <div class="global-config">
        <div class="section-title">故事全局配置</div>
        <GlobalConfig />
      </div>
    </template>

    <div class="editor-content">
      <div class="material-section" :class="{ 'material-section-hidden': !showMaterial }">
        <div class="material-header">
          <div class="title">素材生成配置</div>
          <button class="toggle-button" @click="toggleMaterial">
            <svg v-if="showMaterial" viewBox="0 0 24 24" class="icon-up">
              <path fill="currentColor" d="M7.41 15.41L12 10.83l4.59 4.58L18 14l-6-6-6 6z" />
            </svg>
            <svg v-else viewBox="0 0 24 24" class="icon-down">
              <path fill="currentColor" d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6z" />
            </svg>
          </button>
        </div>
        <MaterialList v-show="showMaterial" />
      </div>
      <ChapterList />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useWindowSize } from '@vueuse/core'
import { useEditorStore } from '@/store/editor'
import { useEditorStore as useEditorStoreStore } from '@/store/editor'
import { storeToRefs } from 'pinia'
import ChapterList from './ChapterList.vue'
import MaterialList from './MaterialList.vue'
import GlobalConfig from './GlobalConfig.vue'
import IconLeft from '@arco-design/web-vue/es/icon'
import type { Actor } from '@/api/stories'
import { isEqual } from 'lodash-es'

const router = useRouter()
const route = useRoute()
const editorStore = useEditorStore()
const editorStoreStore = useEditorStoreStore()

// 使用 store 中的 storyConfig 替代本地的 config
const { storyConfig } = storeToRefs(editorStore)

// 判断是否为桌面端
const { width } = useWindowSize()
const isDesktop = computed(() => width.value >= 768)

const showMaterial = ref(true)
const toggleMaterial = () => {
  showMaterial.value = !showMaterial.value
}

onMounted(async () => {
  // 重置编辑器状态
  editorStore.resetState()

  // 获取路由参数中的 projectId
  const projectId = route.params.id as string
  if (projectId && projectId !== 'new') {
    // 如果有 projectId，说明是编辑模式，加载故事详情
    const success = await editorStore.loadStoryDetail(projectId)
    if (!success) {
      // 加载失败，返回上一页
      router.back()
      return
    }
  }

  // 确保角色列表已加载
  if (editorStore.actors.length === 0) {
    await editorStore.fetchActorList()
  }
})
</script>

<style lang="less" scoped>
.story-editor {
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
  height: 100%;

  .editor-header {
    position: sticky;
    top: 0;
    z-index: 10;
    display: flex;
    align-items: center;
    padding: 8px 16px;
    background-color: #1f0038;
    margin-bottom: 0;
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);

    .back-button {
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 8px;
      border-radius: 8px;
      cursor: pointer;
      color: rgba(255, 255, 255, 0.8);
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.05);
      }

      :deep(svg) {
        width: 20px;
        height: 20px;
      }

      span {
        font-size: 14px;
      }
    }
  }

  .global-config {
    padding: 0 16px;
  }

  .section-title {
    font-size: 17px;
    font-weight: 600;
    color: #fff;
    margin-bottom: 16px;
  }

  .editor-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 16px;
    height: 100%;
    overflow-y: auto;

    .material-section {
      background: #1f0038;
      border-radius: 8px;
      transition: all 0.3s ease;
      flex-shrink: 0;

      &.material-section-hidden {
        margin-bottom: -8px;
      }

      .material-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);

        .title {
          font-size: 15px;
          font-weight: 600;
          color: #fff;
        }

        .toggle-button {
          width: 32px;
          height: 32px;
          border: none;
          background: none;
          color: rgba(255, 255, 255, 0.8);
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;

          &:hover {
            color: #ca93f2;
          }

          svg {
            width: 24px;
            height: 24px;
          }
        }
      }
    }

    > * {
      min-width: 0;
      background: #1f0038;
      border-radius: 8px;

      &:last-child {
        flex-grow: 1;
      }
    }
  }
}
</style>
