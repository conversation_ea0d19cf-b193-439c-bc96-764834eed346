<template>
  <div class="story-preview">
    <div class="preview-container">
      <div v-if="!editorStore.currentStoryId" class="preview-placeholder">
        <div class="placeholder-content">
          <i class="ri-save-line"></i>
          <span>请先保存故事后再预览</span>
        </div>
      </div>
      <div v-else-if="!isValidActor" class="preview-placeholder">
        <div class="placeholder-content">
          <i class="ri-user-line"></i>
          <span>请先选择故事角色</span>
        </div>
      </div>
      <div v-else-if="!isPublished" class="preview-placeholder">
        <div class="placeholder-content">
          <i class="ri-upload-line"></i>
          <span>请先发布故事后再预览</span>
        </div>
      </div>
      <div v-else-if="!isStarted" class="preview-placeholder">
        <div class="placeholder-content">
          <i class="ri-eye-line"></i>
          <span>点击预览故事效果</span>
        </div>
        <a-button type="primary" @click="startPreview">开始预览</a-button>
      </div>

      <template v-else>
        <!-- <div class="preview-header">
          <a-button type="text" class="back-button" @click="stopPreview">
            <template #icon>
              <i class="ri-arrow-left-line"></i>
            </template>
            返回
          </a-button>
          <span class="preview-title">预览模式</span>
          <a-button type="text" class="refresh-button" @click="refreshPreview">
            <template #icon>
              <i class="ri-refresh-line"></i>
            </template>
          </a-button>
        </div> -->

        <div class="preview-content">
          <component
            v-if="showStoryIntro"
            :is="StoryIntroComponent"
            :preview-mode="true"
            @select-character="handleSelectCharacter"
          />
          <component
            v-else
            :is="Chat2Component"
            :character-id="currentActor?.id"
            :story-id="currentStory?.id"
            preview-mode
          />
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineAsyncComponent, computed, nextTick } from 'vue'
import { useStoryStore } from '@/store'
import { useEditorStore } from '@/store/editor'
import { Message } from '@arco-design/web-vue'

const storyStore = useStoryStore()
const editorStore = useEditorStore()

// 检查角色是否有效
const isValidActor = computed(() => {
  const actorId = editorStore.storyConfig.actor_id
  return actorId && actorId !== '00000000-0000-0000-0000-000000000000'
})

// 检查故事是否已发布
const isPublished = computed(() => {
  return editorStore.storyConfig.status === 'Published'
})

// 异步加载组件
const Chat2Component = defineAsyncComponent(() => import('@/mobile/views/chat2/index.vue'))
const StoryIntroComponent = defineAsyncComponent(
  () => import('@/mobile/views/chat/components/StoryIntro.vue')
)

const isStarted = ref(false)
const showStoryIntro = ref(false)

const currentStory = computed(() => storyStore.currentStory)
const currentActor = computed(() => storyStore.currentActor)

const startPreview = async () => {
  try {
    // 获取故事详情
    await storyStore.getStoreDetail(editorStore.currentStoryId)
    isStarted.value = true
    showStoryIntro.value = true
  } catch (error) {
    console.error('Failed to fetch story details:', error)
    Message.error('获取故事详情失败，请稍后重试')
  }
}

const stopPreview = () => {
  isStarted.value = false
  showStoryIntro.value = false
}

const refreshPreview = async () => {
  stopPreview()
  await nextTick()
  await startPreview()
}

const handleSelectCharacter = (actor: any) => {
  showStoryIntro.value = false
  storyStore.setCurrentActor(actor)
}
</script>

<style lang="less" scoped>
.story-preview {
  height: 100%;
  background: #1f0038;
  border-radius: 16px;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .preview-container {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .preview-placeholder {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 16px;
    color: rgba(255, 255, 255, 0.65);

    .placeholder-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;

      i {
        font-size: 24px;
      }
    }
  }

  .preview-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: rgba(0, 0, 0, 0.2);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    .preview-title {
      color: rgba(255, 255, 255, 0.85);
      font-size: 14px;
    }

    :deep(.arco-btn) {
      color: rgba(255, 255, 255, 0.85);

      &:hover {
        color: #fff;
        background: rgba(255, 255, 255, 0.1);
      }
    }
  }

  .preview-content {
    flex: 1;
    overflow: hidden;
    position: relative;
    background: #000;
    :deep(.story-intro),
    :deep(.story-intro-container),
    :deep(.chat-container) {
      height: 100% !important;
    }
  }
}
</style>
