<template>
  <div class="skill-editor-modal" v-if="visible" @click.self="handleClose">
    <div class="skill-editor">
      <div class="editor-header">
        <h2>{{ skill ? 'Edit Skill' : 'Create Skill' }}</h2>
        <button class="close-button" @click="handleClose">
          <icon-close />
        </button>
      </div>

      <div class="editor-body">
        <div class="form-group">
          <label>Name</label>
          <input v-model="formData.name" placeholder="Enter skill name" />
        </div>

        <div class="form-group">
          <label>Description</label>
          <textarea v-model="formData.description" placeholder="Enter skill description" rows="3" />
        </div>

        <div class="form-group">
          <label>Icon</label>
          <MediaSelector
            type="image"
            v-model="formData.icon_uri"
            v-model:preview="formData.icon_url"
          />
        </div>

        <div class="form-group">
          <label>Preview Image</label>
          <MediaSelector
            type="image"
            v-model="formData.image_uri"
            v-model:preview="formData.image_url"
          />
        </div>

        <div class="form-group">
          <label>Prompt</label>
          <textarea v-model="formData.prompt" placeholder="Enter skill prompt" rows="4" />
        </div>

        <div class="form-group">
          <label>Skill Effects</label>
          <div class="effects-list">
            <div
              v-for="(effect, index) in formData.skill_configs.effect"
              :key="index"
              class="effect-item"
            >
              <Select
                v-model="effect.attr"
                :options="attributeOptions"
                placeholder="Select attribute"
                class="effect-attr"
              />
              <input v-model="effect.value" placeholder="Value" class="effect-value" />
              <button class="remove-effect" @click="removeEffect(index)">
                <icon-delete />
              </button>
            </div>
            <button class="add-effect" @click="addEffect">Add Effect</button>
          </div>
        </div>

        <div class="form-group">
          <label>Conditional Effects</label>
          <div class="conditional-effects-list">
            <div
              v-for="(conditionalEffect, ceIndex) in formData.skill_configs.conditional_effect"
              :key="ceIndex"
              class="conditional-effect-item"
            >
              <div class="conditions-section">
                <h4>Conditions</h4>
                <div class="conditions-list">
                  <div
                    v-for="(condition, condIndex) in conditionalEffect.conditions"
                    :key="condIndex"
                    class="condition-item"
                  >
                    <Select
                      v-model="condition.attribute"
                      :options="attributeOptions"
                      placeholder="Select attribute"
                      class="condition-attr"
                    />
                    <select v-model="condition.operator" class="condition-operator">
                      <option value=">=">&gt;=</option>
                      <option value="<=">&lt;=</option>
                      <option value=">">&gt;</option>
                      <option value="<">&lt;</option>
                      <option value="=">=</option>
                    </select>
                    <input
                      v-model.number="condition.value"
                      type="number"
                      placeholder="Value"
                      class="condition-value"
                    />
                    <button class="remove-condition" @click="removeCondition(ceIndex, condIndex)">
                      <icon-delete />
                    </button>
                  </div>
                  <button class="add-condition" @click="addCondition(ceIndex)">
                    Add Condition
                  </button>
                </div>
              </div>

              <div class="modifiers-section">
                <h4>Modifiers</h4>
                <div class="modifiers-list">
                  <div
                    v-for="(modifier, modIndex) in conditionalEffect.modifiers"
                    :key="modIndex"
                    class="modifier-item"
                  >
                    <Select
                      v-model="modifier.attribute"
                      :options="attributeOptions"
                      placeholder="Select attribute"
                      class="modifier-attr"
                    />
                    <input
                      v-model.number="modifier.value"
                      type="number"
                      placeholder="Value"
                      class="modifier-value"
                    />
                    <button class="remove-modifier" @click="removeModifier(ceIndex, modIndex)">
                      <icon-delete />
                    </button>
                  </div>
                  <button class="add-modifier" @click="addModifier(ceIndex)">Add Modifier</button>
                </div>
              </div>

              <button class="remove-conditional-effect" @click="removeConditionalEffect(ceIndex)">
                Remove Conditional Effect
              </button>
            </div>
            <button class="add-conditional-effect" @click="addConditionalEffect">
              Add Conditional Effect
            </button>
          </div>
        </div>
      </div>

      <div class="editor-footer">
        <button type="button" @click="handleClose">Cancel</button>
        <button type="button" @click="handleSave" :disabled="saving">Save</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import type { SkillInfo } from '@/interface/skill'
import { useSkillStore } from '@/store/skill'
import MediaSelector from '@/mobile/components/Common/MediaSelector.vue'
import Select from '@/mobile/components/Select.vue'
import IconClose from '@/assets/icon/close.svg'
import IconDelete from '@/assets/icon/delete.svg'
import { Message } from '@/mobile/components/Message'

interface Props {
  visible: boolean
  skill?: SkillInfo
}

const props = defineProps<Props>()
const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'save', skill: SkillInfo): void
}>()

interface FormData {
  name: string
  description: string
  icon_url: string
  icon_uri: string
  image_url: string
  image_uri: string
  prompt: string
  skill_configs: {
    effect: Array<{
      attr: string
      value: string
    }>
    conditional_effect: Array<{
      conditions: Array<{
        attribute: string
        operator: string
        value: number
      }>
      modifiers: Array<{
        attribute: string
        value: number
      }>
    }>
  }
}

const skillStore = useSkillStore()
const saving = ref(false)
const formData = reactive<FormData>({
  name: '',
  description: '',
  icon_url: '',
  icon_uri: '',
  image_url: '',
  image_uri: '',
  prompt: '',
  skill_configs: {
    effect: [],
    conditional_effect: []
  }
})

const attributeOptions = [
  { label: '智慧（Intelligence）', value: 'intelligence' },
  { label: '力量（Strength）', value: 'strength' },
  { label: '幸福（Happiness）', value: 'happiness' },
  { label: '财富（Wealth）', value: 'wealth' }
]

const resetFormData = () => {
  if (props.skill) {
    formData.name = props.skill.name
    formData.description = props.skill.description
    formData.icon_url = props.skill.icon_url
    formData.icon_uri = props.skill.icon_url
    formData.image_url = props.skill.image_url
    formData.image_uri = props.skill.image_url
    formData.prompt = props.skill.prompt
    formData.skill_configs = {
      effect: props.skill.skill_configs?.effect?.map((e) => ({ ...e })) || [],
      conditional_effect:
        props.skill.skill_configs?.conditional_effect?.map((ce) => ({
          conditions: ce.conditions.map((c) => ({ ...c })),
          modifiers: ce.modifiers.map((m) => ({ ...m }))
        })) || []
    }
  } else {
    // Reset to empty state for new skill
    formData.name = ''
    formData.description = ''
    formData.icon_url = ''
    formData.icon_uri = ''
    formData.image_url = ''
    formData.image_uri = ''
    formData.prompt = ''
    formData.skill_configs = {
      effect: [],
      conditional_effect: []
    }
  }
}

watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      resetFormData()
    }
  }
)

const addEffect = () => {
  formData.skill_configs.effect.push({
    attr: '',
    value: ''
  })
}

const removeEffect = (index: number) => {
  formData.skill_configs.effect.splice(index, 1)
}

const addConditionalEffect = () => {
  formData.skill_configs.conditional_effect.push({
    conditions: [],
    modifiers: []
  })
}

const removeConditionalEffect = (index: number) => {
  formData.skill_configs.conditional_effect.splice(index, 1)
}

const addCondition = (ceIndex: number) => {
  formData.skill_configs.conditional_effect[ceIndex].conditions.push({
    attribute: '',
    operator: '>=',
    value: 0
  })
}

const removeCondition = (ceIndex: number, condIndex: number) => {
  formData.skill_configs.conditional_effect[ceIndex].conditions.splice(condIndex, 1)
}

const addModifier = (ceIndex: number) => {
  formData.skill_configs.conditional_effect[ceIndex].modifiers.push({
    attribute: '',
    value: 0
  })
}

const removeModifier = (ceIndex: number, modIndex: number) => {
  formData.skill_configs.conditional_effect[ceIndex].modifiers.splice(modIndex, 1)
}

const handleClose = () => {
  emit('update:visible', false)
  resetFormData()
}

const handleSave = async () => {
  saving.value = true
  try {
    const skillData: SkillInfo = {
      id: props.skill?.id,
      name: formData.name,
      description: formData.description,
      icon_url: formData.icon_uri,
      image_url: formData.image_uri,
      prompt: formData.prompt,
      skill_configs: {
        effect: formData.skill_configs.effect,
        conditional_effect: formData.skill_configs.conditional_effect
      }
    }

    const success = await skillStore.createOrUpdateSkill(skillData)
    if (success) {
      emit('save', skillData)
      Message.success('Skill saved successfully')
      handleClose()
    } else {
      Message.error('Failed to save skill')
      console.error('Failed to save skill')
    }
  } finally {
    saving.value = false
  }
}
</script>

<style lang="less" scoped>
.skill-editor-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
  z-index: 1001;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;
}

.skill-editor {
  background: linear-gradient(180deg, #2b1b3b 0%, #1a0f24 100%);
  border-radius: 24px;
  width: 800px;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  animation: scaleIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.95);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.editor-header {
  padding: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  h2 {
    color: white;
    font-size: 24px;
    font-weight: 600;
    margin: 0;
  }
}

.close-button {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: transparent;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  padding: 0;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  :deep(svg) {
    width: 24px;
    height: 24px;
  }
}

.editor-body {
  flex: 1;
  overflow-y: auto;
  padding: 24px;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }
}

.form-group {
  margin-bottom: 24px;

  label {
    display: block;
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    margin-bottom: 8px;
  }

  input,
  textarea {
    width: 100%;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    color: white;
    font-size: 14px;
    padding: 8px 12px;
    transition: all 0.2s ease;
    outline: none;
    resize: none;

    &::placeholder {
      color: rgba(255, 255, 255, 0.3);
    }

    &:hover {
      border-color: rgba(202, 147, 242, 0.5);
      background: rgba(255, 255, 255, 0.08);
    }

    &:focus {
      border-color: #ca93f2;
      background: rgba(255, 255, 255, 0.08);
      box-shadow: 0 0 0 2px rgba(202, 147, 242, 0.2);
    }
  }

  textarea {
    min-height: 80px;
    line-height: 1.5;
  }
}

.effects-list {
  display: flex;
  flex-direction: column;
  gap: 12px;

  .effect-item {
    display: flex;
    gap: 12px;
    align-items: center;

    .effect-attr {
      flex: 2;
    }

    .effect-value {
      flex: 1;
    }
  }

  .add-effect {
    height: 40px;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background: rgba(255, 255, 255, 0.08);
      border-color: rgba(202, 147, 242, 0.5);
      transform: translateY(-1px);
    }

    &:active {
      transform: translateY(0);
    }
  }

  .remove-effect {
    width: 32px;
    height: 32px;
    border-radius: 16px;
    background: rgba(255, 77, 79, 0.1);
    border: none;
    color: #ff4d4f;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
      background: rgba(255, 77, 79, 0.2);
    }

    :deep(svg) {
      width: 16px;
      height: 16px;
    }
  }
}

.conditional-effects-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.conditional-effect-item {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.conditions-section,
.modifiers-section {
  h4 {
    color: white;
    font-size: 14px;
    margin: 0 0 12px;
  }
}

.conditions-list,
.modifiers-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.condition-item,
.modifier-item {
  display: flex;
  gap: 8px;
  align-items: center;

  :deep(.custom-select) {
    flex: 2;
  }

  input,
  select {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    color: white;
    font-size: 14px;
    padding: 8px 12px;
    transition: all 0.2s ease;

    &:hover {
      border-color: rgba(202, 147, 242, 0.5);
    }

    &:focus {
      border-color: #ca93f2;
      box-shadow: 0 0 0 2px rgba(202, 147, 242, 0.2);
    }
  }
}

.condition-operator {
  width: 80px;
}

.condition-value,
.modifier-value {
  flex: 1;
}

.add-condition,
.add-modifier,
.add-conditional-effect {
  height: 36px;
  border-radius: 18px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(202, 147, 242, 0.5);
  }
}

.remove-condition,
.remove-modifier {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background: rgba(255, 77, 79, 0.1);
  border: none;
  color: #ff4d4f;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background: rgba(255, 77, 79, 0.2);
  }

  :deep(svg) {
    width: 16px;
    height: 16px;
  }
}

.remove-conditional-effect {
  height: 36px;
  border-radius: 18px;
  background: rgba(255, 77, 79, 0.1);
  border: none;
  color: #ff4d4f;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background: rgba(255, 77, 79, 0.2);
  }
}

.editor-footer {
  padding: 24px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  button {
    min-width: 100px;
    height: 40px;
    border-radius: 20px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;

    &:first-child {
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
      color: white;

      &:hover {
        background: rgba(255, 255, 255, 0.08);
        border-color: rgba(202, 147, 242, 0.5);
      }
    }

    &:last-child {
      background: linear-gradient(90deg, #ca93f2 0%, #9b6cc8 100%);
      border: none;
      color: white;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(202, 147, 242, 0.3);
      }

      &:active {
        transform: translateY(0);
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
      }
    }
  }
}
</style>
