<template>
  <div v-if="visible" class="modal-overlay" @click.self="handleClose">
    <div ref="modalRef" class="modal-content">
      <button class="close-button" @click="handleClose">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <path
            d="M18 6L6 18M6 6L18 18"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </button>
      <div class="title">Are you enjoying our game?</div>
      <div class="subtitle">Give the rate.</div>

      <div class="rating-section">
        <div class="rating-row" id="story-rating">
          <span class="rating-label">Story</span>
          <div class="radio">
            <template v-for="star in [5, 4, 3, 2, 1]" :key="`story-${star}`">
              <input
                type="radio"
                :id="`story-${star}`"
                :value="star"
                v-model="storyRating"
                name="story"
              />
              <label :for="`story-${star}`">
                <svg viewBox="0 0 36 36">
                  <path
                    class="star-shape"
                    d="M18 4.5
                       C18.7 7 20.5 12.5 21.5 15.5
                       C24 15.8 29.5 16.6 32.5 17
                       C30.5 18.4 25.5 22 23 23.8
                       C23.7 26 25.5 31.5 26.5 34.5
                       C24.5 33.1 19.5 29.5 17 27.7
                       C15 29.5 10 33.1 8 34.5
                       C9 31.5 10.8 26 11.5 23.8
                       C9 22 4 18.4 2 17
                       C5 16.6 10.5 15.8 13 15.5
                       C14 12.5 15.8 7 16.5 4.5
                       C17 4.5 17.5 4.5 18 4.5Z"
                  />
                </svg>
              </label>
            </template>
          </div>
        </div>

        <div class="rating-row" id="character-rating">
          <span class="rating-label">Character</span>
          <div class="radio">
            <template v-for="star in [5, 4, 3, 2, 1]" :key="`character-${star}`">
              <input
                type="radio"
                :id="`character-${star}`"
                :value="star"
                v-model="characterRating"
                name="character"
              />
              <label :for="`character-${star}`">
                <svg viewBox="0 0 36 36">
                  <path
                    class="star-shape"
                    d="M18 4.5
                       C18.7 7 20.5 12.5 21.5 15.5
                       C24 15.8 29.5 16.6 32.5 17
                       C30.5 18.4 25.5 22 23 23.8
                       C23.7 26 25.5 31.5 26.5 34.5
                       C24.5 33.1 19.5 29.5 17 27.7
                       C15 29.5 10 33.1 8 34.5
                       C9 31.5 10.8 26 11.5 23.8
                       C9 22 4 18.4 2 17
                       C5 16.6 10.5 15.8 13 15.5
                       C14 12.5 15.8 7 16.5 4.5
                       C17 4.5 17.5 4.5 18 4.5Z"
                  />
                </svg>
              </label>
            </template>
          </div>
        </div>

        <div class="rating-row" id="image-rating">
          <span class="rating-label">Image</span>
          <div class="radio">
            <template v-for="star in [5, 4, 3, 2, 1]" :key="`image-${star}`">
              <input
                type="radio"
                :id="`image-${star}`"
                :value="star"
                v-model="imageRating"
                name="image"
              />
              <label :for="`image-${star}`">
                <svg viewBox="0 0 36 36">
                  <path
                    class="star-shape"
                    d="M18 4.5
                       C18.7 7 20.5 12.5 21.5 15.5
                       C24 15.8 29.5 16.6 32.5 17
                       C30.5 18.4 25.5 22 23 23.8
                       C23.7 26 25.5 31.5 26.5 34.5
                       C24.5 33.1 19.5 29.5 17 27.7
                       C15 29.5 10 33.1 8 34.5
                       C9 31.5 10.8 26 11.5 23.8
                       C9 22 4 18.4 2 17
                       C5 16.6 10.5 15.8 13 15.5
                       C14 12.5 15.8 7 16.5 4.5
                       C17 4.5 17.5 4.5 18 4.5Z"
                  />
                </svg>
              </label>
            </template>
          </div>
        </div>
      </div>

      <textarea
        v-model="comment"
        class="comment-input"
        placeholder="Leave us a comment here"
        maxlength="500"
        @input="validateComment"
      />

      <button class="submit-button" :disabled="!isValid" @click="handleSubmit">
        <span class="submit-text">Submit and get</span>
        <span class="diamond">
          <span class="credit-amount">10</span>
          <img src="https://cdn.magiclight.ai/assets/mobile/diamond.png" alt="credits" />
        </span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { animate } from 'motion'
import { reportEvent } from '@/utils'
import { ReportEvent } from '@/interface'
import { useStoryStore } from '@/store/story'

interface Props {
  visible: boolean
  onClose: () => void
  onSubmit: (ratings: { story: number; character: number; image: number }, comment: string) => void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false
})

const storyStore = useStoryStore()

const storyRating = ref(0)
const characterRating = ref(0)
const imageRating = ref(0)
const comment = ref('')
const modalRef = ref()
const openTimestamp = ref(0)
const pauseStartTime = ref(0)
const totalPausedTime = ref(0)

const isValid = computed(() => {
  return storyRating.value > 0 && characterRating.value > 0 && imageRating.value > 0
})

// Handle page visibility change
const handleVisibilityChange = () => {
  if (document.hidden && openTimestamp.value > 0) {
    // Page is hidden, start counting pause time
    pauseStartTime.value = Date.now()
  } else if (pauseStartTime.value > 0) {
    // Page becomes visible again, add to total paused time
    totalPausedTime.value += Date.now() - pauseStartTime.value
    pauseStartTime.value = 0
  }
}

// Setup visibility change listener
onMounted(() => {
  document.addEventListener('visibilitychange', handleVisibilityChange)
})

onUnmounted(() => {
  document.removeEventListener('visibilitychange', handleVisibilityChange)
})

// 监听评分变化，添加动画
watch(
  [storyRating, characterRating, imageRating],
  ([newStory, newChar, newImage], [oldStory, oldChar, oldImage]) => {
    const animateStars = (selector: string) => {
      const stars = document.querySelectorAll(selector)
      stars.forEach((star, index) => {
        animate(
          star,
          {
            scale: [1, 1.15, 0.95, 1.05, 1],
            rotate: [0, 5, -5, 2, 0]
          },
          {
            duration: 0.3,
            delay: index * 0.03,
            easing: 'ease-out'
          }
        )
      })
    }

    if (newStory !== oldStory) {
      animateStars(`#story-rating .radio label:nth-child(-n+${newStory * 2})`)
    }
    if (newChar !== oldChar) {
      animateStars(`#character-rating .radio label:nth-child(-n+${newChar * 2})`)
    }
    if (newImage !== oldImage) {
      animateStars(`#image-rating .radio label:nth-child(-n+${newImage * 2})`)
    }
  }
)

watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      openTimestamp.value = Date.now()
      totalPausedTime.value = 0
      pauseStartTime.value = 0
      reportEvent(ReportEvent.RatingModalExposure, {
        storyId: storyStore.currentStory?.id,
        actorId: storyStore.currentActor?.id
      })
      nextTick(() => {
        animate(
          modalRef.value,
          { opacity: [0, 1], scale: [0.6, 1] },
          { duration: 0.3, easing: 'ease-out' }
        )
      })
    } else if (openTimestamp.value > 0) {
      // Calculate final paused time if page is currently hidden
      const finalPausedTime =
        pauseStartTime.value > 0
          ? totalPausedTime.value + (Date.now() - pauseStartTime.value)
          : totalPausedTime.value

      const durationMs = Date.now() - openTimestamp.value - finalPausedTime
      const durationSeconds = Math.round(durationMs / 1000)
      reportEvent(ReportEvent.RatingModalClose, { duration: durationSeconds })
      openTimestamp.value = 0
      totalPausedTime.value = 0
      pauseStartTime.value = 0
    }
  }
)

const handleClose = () => {
  props.onClose()
}

const handleSubmit = () => {
  if (!isValid.value) return
  const stars = document.querySelectorAll('.radio label svg')
  stars.forEach((star, index) => {
    animate(
      star,
      {
        scale: [1, 1.2, 0.9, 1],
        y: [0, -10, 0]
      },
      {
        duration: 0.4,
        delay: index * 0.02,
        easing: [0.22, 0.03, 0.26, 1]
      }
    )
  })

  setTimeout(() => {
    const storyStore = useStoryStore()
    props.onSubmit(
      {
        story: storyRating.value,
        character: characterRating.value,
        image: imageRating.value
      },
      comment.value
    )
    reportEvent(ReportEvent.RatingModalSubmit, {
      story: storyRating.value,
      character: characterRating.value,
      image: imageRating.value,
      comment: comment.value,
      storyId: storyStore.currentStory?.id,
      actorId: storyStore.currentActor?.id
    })
    // Reset form
    storyRating.value = 0
    characterRating.value = 0
    imageRating.value = 0
    comment.value = ''
  }, 600)
}

// Add input validation
const validateComment = (event: Event) => {
  const input = (event.target as HTMLTextAreaElement).value
  // Remove any potentially dangerous characters
  comment.value = input.replace(/[<>]/g, '')
}
</script>

<style lang="less" scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  position: relative;
  width: 90%;
  max-width: 320px;
  border-radius: 12px;
  background: #1f0038;
  padding: 32px 24px 28px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.title {
  color: #ca93f2;
  text-align: center;
  font-size: 17px;
  font-weight: 700;
}

.subtitle {
  color: #ca93f2;
  text-align: center;
  font-size: 17px;
  font-weight: 700;
}

.rating-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin: 4px 0 8px;
  padding: 4px;
}

.rating-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
  padding: 4px 0;
}

.rating-label {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  font-weight: 600;
  min-width: 70px;
  flex-shrink: 0;
}

.radio {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  flex-direction: row-reverse;
  min-width: 180px;
  padding-right: 4px;
}

.radio > input {
  position: absolute;
  appearance: none;
}

.radio > label {
  cursor: pointer;
  position: relative;
  display: inline-block;
  width: 28px;
  height: 28px;
  transform-origin: center;
  transition: transform 0.2s ease;

  svg {
    width: 100%;
    height: 100%;
    transform-origin: center;
    padding: 3px;
  }

  .star-shape {
    fill: #4c3471;
    stroke: #4c3471;
    stroke-width: 1.2;
    stroke-linejoin: round;
    stroke-linecap: round;
    transition: all 0.2s ease;
  }

  &:hover {
    transform: scale(1.1);
  }
}

// 基础状态：所有星星未激活
.radio label .star-shape {
  fill: #4c3471;
  stroke: #4c3471;
  filter: none;
}

// 选中态
.radio > input:checked ~ label .star-shape {
  fill: #daff96;
  stroke: #e3ff2f;
  filter: drop-shadow(0 0 6px rgba(218, 255, 150, 0.6));
}

// 悬停态：重置所有星星为未激活状态
.radio:hover label .star-shape {
  fill: #4c3471 !important;
  stroke: #4c3471 !important;
  filter: none !important;
}

// 悬停态：激活当前及之前的星星
.radio label:hover .star-shape,
.radio label:hover ~ label .star-shape {
  fill: #daff96 !important;
  stroke: #e3ff2f !important;
  filter: drop-shadow(0 0 6px rgba(218, 255, 150, 0.6)) !important;
}

.comment-input {
  width: 100%;
  height: 100px;
  background: #1f0038;
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 14px;
  font-size: 14px;
  line-height: 1.5;
  resize: none;
  margin-top: 8px;
  transition: all 0.2s ease;
  font-size: 15px;
  font-weight: 600;
  color: #fff;
  &::placeholder {
    color: rgba(255, 255, 255, 0.4);
  }

  &:focus {
    outline: none;
    border-color: rgba(182, 109, 255, 0.5);
    box-shadow: 0 0 0 3px rgba(182, 109, 255, 0.15);
  }
}

.submit-button {
  width: 100%;
  height: 42px;
  background: #ca93f2;
  border: none;
  border-radius: 40px;
  color: #241d49;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20px;
  box-shadow: 0 4px 12px rgba(182, 109, 255, 0.2);

  &:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(182, 109, 255, 0.3);
  }

  &:active:not(:disabled) {
    transform: translateY(1px);
    box-shadow: 0 2px 8px rgba(182, 109, 255, 0.2);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: linear-gradient(135deg, #8450bb 0%, #7339bb 100%);
  }

  .submit-text {
    font-weight: 600;
  }

  .diamond {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    border-radius: 8px;

    .credit-amount {
      color: #daff96;
      font-size: 15px;
      font-weight: 600;
    }

    img {
      width: 16px;
      height: 16px;
      object-fit: contain;
    }
  }
}

.close-button {
  position: absolute;
  top: 0px;
  right: 0px;
  width: 40px;
  height: 40px;
  border: none;
  background: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  z-index: 1;

  &:hover {
    color: rgba(255, 255, 255, 0.9);
    background: rgba(255, 255, 255, 0.1);
  }

  &:active {
    transform: scale(0.95);
  }
}
</style>
