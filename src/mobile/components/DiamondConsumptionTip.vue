<template>
  <div class="diamond-consumption-tip" :class="{ collapsed: isCollapsed }">
    <div class="tip-content" v-if="!isCollapsed">
      <div class="scrolling-container">
        <span class="tip-text">Enjoy your Spicy time, every 5min cost 5coins</span>
      </div>
    </div>
    <div class="collapsed-content" v-else>
      <img src="https://cdn.magiclight.ai/assets/mobile/diamond.png" class="icon" />
      <span class="amount">5</span>
      <span class="divider">/</span>
      <span class="time">5min</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 控制是否折叠显示
const isCollapsed = ref(false)

// 在组件挂载后一分钟后折叠显示
onMounted(() => {
  setTimeout(() => {
    isCollapsed.value = true
  }, 60000) // 60秒后折叠
})
</script>

<style lang="less" scoped>
.diamond-consumption-tip {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 2px 12px 2px 8px;
  border-radius: 34px;
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  position: relative;
  height: 24px;
  transition: all 0.5s ease;
  margin-left: 8px;
  overflow: hidden;
  white-space: nowrap;
  width: 200px; /* 固定宽度，适合滚动文本 */

  &.collapsed {
    padding: 2px 12px 2px 8px;
    width: auto; /* 折叠后宽度自适应内容 */
  }

  .tip-content {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #daff96;
    height: 100%;
    line-height: 1;
    position: relative;
    left: 2px;
    width: 180px; /* 控制容器宽度 */
    overflow: hidden;
  }

  .scrolling-container {
    width: 100%;
    overflow: hidden;
    position: relative;
  }

  .tip-text {
    font-size: 13px;
    font-weight: 600;
    display: inline-block;
    white-space: nowrap;
    height: 100%;
    animation: scrollText 10s linear infinite;
    // padding-right: 50px; /* 确保文本滚动时有足够的间距 */
    background: linear-gradient(to bottom, #ffffff 0%, #9c27b0 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0px 0px 2px rgba(179, 136, 255, 0.5);
    font-weight: 700;
  }

  @keyframes scrollText {
    0% {
      transform: translateX(60%);
    }
    100% {
      transform: translateX(-100%);
    }
  }

  .collapsed-content {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #daff96;
    height: 100%;
    line-height: 1;
    font-size: 13px;
    font-weight: 600;
    .icon {
      width: 10px;
      height: 10px;
      margin-right: 4px;
    }
    .amount,
    .time,
    .divider {
      background: linear-gradient(to bottom, #ffffff 0%, #9c27b0 100%);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      text-shadow: 0px 0px 2px rgba(179, 136, 255, 0.5);
      font-weight: 700;
    }

    .divider {
      margin: 0 2px;
    }
  }
}
</style>
