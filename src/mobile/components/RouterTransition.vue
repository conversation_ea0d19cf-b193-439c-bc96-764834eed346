<template>
  <div class="router-transition">
    <router-view v-slot="{ Component }">
      <transition
        :name="transitionName"
        @before-enter="beforeEnter"
        @enter="enter"
        @after-enter="afterEnter"
        @enter-cancelled="enterCancelled"
        @before-leave="beforeLeave"
        @leave="leave"
        @after-leave="afterLeave"
        @leave-cancelled="leaveCancelled"
      >
        <!-- 使用keep-alive来缓存Stories组件，避免URL参数变化时重新挂载 -->
        <keep-alive :include="['Stories']">
          <component :is="Component" v-bind="$attrs" class="page-view" />
        </keep-alive>
      </transition>
    </router-view>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const transitionName = ref('slide-left')
let isGestureBack = false

// 监听手势返回事件
onMounted(() => {
  // 监听 Android 返回手势
  window.addEventListener('popstate', () => {
    isGestureBack = true
  })

  // 监听 iOS 返回手势
  window.addEventListener(
    'touchstart',
    (e) => {
      if (e.touches[0].pageX <= 20) {
        // 从屏幕左边缘开始的触摸
        isGestureBack = true
      }
    },
    { passive: true }
  )

  window.addEventListener(
    'touchend',
    () => {
      setTimeout(() => {
        isGestureBack = false
      }, 100)
    },
    { passive: true }
  )
})

onBeforeUnmount(() => {
  window.removeEventListener('popstate', () => {})
  window.removeEventListener('touchstart', () => {})
  window.removeEventListener('touchend', () => {})
})

// 过渡钩子函数
const beforeEnter = (el: Element) => {
  if (isGestureBack) {
    transitionName.value = 'slide-right'
  } else {
    transitionName.value = 'slide-left'
  }
}

const enter = (el: Element, done: () => void) => {
  requestAnimationFrame(() => {
    requestAnimationFrame(() => {
      done()
    })
  })
}

const afterEnter = (el: Element) => {
  el.classList.remove('transition-active')
}

const enterCancelled = (el: Element) => {
  el.classList.remove('transition-active')
}

const beforeLeave = (el: Element) => {
  el.classList.add('transition-active')
}

const leave = (el: Element, done: () => void) => {
  requestAnimationFrame(() => {
    requestAnimationFrame(() => {
      done()
    })
  })
}

const afterLeave = (el: Element) => {
  el.classList.remove('transition-active')
}

const leaveCancelled = (el: Element) => {
  el.classList.remove('transition-active')
}

defineOptions({
  inheritAttrs: false
})
</script>

<style lang="less" scoped>
.router-transition {
  position: relative;
  width: 100%;
  height: 100%;
}

.page-view {
  scrollbar-width: none;
  &::-webkit-scrollbar {
    display: none;
  }
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  background-color: var(--page-bg-color, #180430);
  will-change: transform;
  transform: translate3d(0, 0, 0);
  -webkit-transform: translate3d(0, 0, 0);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  z-index: 1;
}

.transition-active {
  pointer-events: none;
}

// Slide left transition
.slide-left-enter-active,
.slide-left-leave-active,
.slide-right-enter-active,
.slide-right-leave-active {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  perspective: 1000;
  -webkit-perspective: 1000;
  transform-style: preserve-3d;
  will-change: transform;
  z-index: 2;
  background-color: var(--page-bg-color, #180430);
}

.slide-left-enter-from,
.slide-right-enter-from,
.slide-left-leave-to,
.slide-right-leave-to {
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

.slide-left-enter-from {
  transform: translate3d(100%, 0, 0);
  -webkit-transform: translate3d(100%, 0, 0);
}

.slide-left-leave-to {
  transform: translate3d(-100%, 0, 0);
  -webkit-transform: translate3d(-100%, 0, 0);
}

.slide-right-enter-from {
  transform: translate3d(-100%, 0, 0);
  -webkit-transform: translate3d(-100%, 0, 0);
}

.slide-right-leave-to {
  transform: translate3d(100%, 0, 0);
  -webkit-transform: translate3d(100%, 0, 0);
}

// Fade transition
.fade-enter-active,
.fade-leave-active {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
