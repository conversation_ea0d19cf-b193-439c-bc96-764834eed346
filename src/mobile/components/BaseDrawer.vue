<template>
  <Teleport :to="isDesktop ? '.chat-interface' : '#app'">
    <div v-show="visible" class="base-drawer" :class="{ visible }" @click.self="handleClose">
      <div
        ref="drawerRef"
        class="drawer-content"
        :style="{
          height: height,
          background: background,
          borderRadius: borderRadius
        }"
      >
        <div class="drawer-title" v-if="$slots.title">
          <slot name="title"></slot>
        </div>
        <div class="drawer-body">
          <slot></slot>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { animate } from 'motion'
import { ref, watch } from 'vue'
import { useDeviceDetection } from '@/composables/useDeviceDetection'

const props = defineProps<{
  visible: boolean
  height?: string
  background?: string
  borderRadius?: string
  maskColor?: string
  maskBlur?: string
  padding?: string
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
}>()

const drawerRef = ref<HTMLElement>()
const { isDesktop } = useDeviceDetection()

watch(
  () => props.visible,
  (newVal) => {
    if (!drawerRef.value) return

    animate(
      drawerRef.value,
      { y: newVal ? [window.innerHeight, 0] : [0, window.innerHeight] },
      {
        type: 'spring',
        stiffness: 300,
        damping: 30
      }
    )
  }
)

const handleClose = () => {
  emit('update:visible', false)
}
</script>

<style lang="less" scoped>
@import '@/assets/style/theme.less';

.base-drawer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: v-bind('maskColor || "rgba(0, 0, 0, 0.5)"');
  backdrop-filter: v-bind('maskBlur || "blur(4px)"');
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;

  &.visible {
    opacity: 1;
    visibility: visible;
  }
}

.drawer-content {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background: v-bind('background || "var(--mobile-bg-secondary)"');
  border-radius: 24px 24px 0 0;
  padding: v-bind('padding || "24px"');
  max-height: calc(var(--vh, 1vh) * 90);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  display: flex;
  flex-direction: column;
  transition: background 0.3s ease;

  &::-webkit-scrollbar {
    display: none;
  }
  scrollbar-width: none;
  -ms-overflow-style: none;

  .drawer-title {
    position: sticky;
    top: 0;
    width: 100%;
    background: v-bind('background || "var(--mobile-bg-secondary)"');
    text-align: center;
    font-size: 15px;
    font-weight: 600;
    color: var(--text-primary);
    z-index: 11;
    transition: all 0.3s ease;

    &::after {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      bottom: -16px;
      height: 16px;
      background: linear-gradient(
        to bottom,
        v-bind('background || "var(--mobile-bg-secondary)"'),
        transparent
      );
      pointer-events: none;
    }
  }

  .drawer-body {
    flex: 1;
    width: 100%;
    padding-top: 8px;
    margin-top: -8px;
  }
}
</style>
