<template>
  <div class="recharge-drawer" v-if="rechargeStore.visible" @click.self="handleClose">
    <div class="drawer-content">
      <div class="drawer-header">
        <h2>Purchase</h2>
        <button class="close-button" @click="handleClose">
          <icon-close />
        </button>
      </div>

      <div class="drawer-body">
        <div class="price-list">
          <!-- Show actual price items when data is loaded -->
          <template v-if="rechargeStore.priceList && rechargeStore.priceList.length > 0">
            <div
              v-for="item in rechargeStore.priceList"
              :key="item.id"
              class="price-item"
              :class="{ 'is-selected': selectedPrice?.id === item.id }"
              @click="selectPrice(item)"
            >
              <img :src="item.extra?.background_url" :alt="item.name" class="background-image" />
              <div class="price-info">
                <div class="info-content">
                  <div class="amount">${{ (item.amount / 100).toFixed(2) }}</div>
                  <div class="coins">
                    <img
                      class="diamond-icon"
                      src="https://cdn.magiclight.ai/assets/mobile/diamond.png"
                      alt="diamond"
                    />
                    {{ item.coins }}
                  </div>
                </div>
                <div v-if="item.extra?.discount_percent" class="discount">
                  {{ item.extra.discount_percent }}% OFF
                </div>
              </div>
            </div>
          </template>

          <!-- Show skeleton screens when loading or no data -->
          <template v-else>
            <div v-for="i in 3" :key="`skeleton-${i}`" class="price-item skeleton-item">
              <div class="skeleton-image"></div>
              <div class="price-info">
                <div class="info-content">
                  <div class="amount skeleton-text"></div>
                  <div class="coins skeleton-text"></div>
                </div>
                <div class="discount skeleton-discount" v-if="i === 1"></div>
              </div>
            </div>
          </template>
        </div>
      </div>

      <div class="drawer-footer">
        <button
          class="purchase-button"
          :disabled="!selectedPrice || loading"
          @click="handlePayment(selectedPrice!)"
        >
          Purchase Now
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { createStripeCheckout } from '@/utils/stripe-loader'
import { Message } from '@/mobile/components/Message'
import IconClose from '@/assets/icon/close.svg'
import { useUserStore } from '@/store/user'
import { useRechargeStore } from '@/store/recharge'
import { useChatUIStore } from '@/store/chat-ui'
import { useChatEventsStore } from '@/store/chat-events'
import { reportEvent } from '@/utils'
import { ReportEvent } from '@/interface'
import type { PriceItem } from '@/api/payment'
import { useRoute } from 'vue-router'
import { useRouter } from 'vue-router'

const userStore = useUserStore()
const rechargeStore = useRechargeStore()
const chatUIStore = useChatUIStore()
const chatEventsStore = useChatEventsStore()
const route = useRoute()
const router = useRouter()

const currentURL = `${window.location.origin}${route.fullPath}`
const cancelURL = currentURL

const loading = ref(false)
const selectedPrice = ref<PriceItem | null>(null)

const selectPrice = (price: PriceItem) => {
  selectedPrice.value = price
  reportEvent(ReportEvent.ClickRechargeButton, {
    userId: userStore.userInfo?.uuid,
    path: route.fullPath,
    priceId: price.id,
    amount: price.amount,
    coins: price.coins
  })
}

const handlePayment = async (price: PriceItem) => {
  if (loading.value || !price) return
  if (userStore.isGuest) {
    rechargeStore.visible = false
    router.push('/user/login')
    return
  }
  loading.value = true

  // Facebook Pixel tracking
  if (window.fbq) {
    window.fbq('track', 'AddToCart')
    window.fbq('track', 'InitiateCheckout')
  }

  reportEvent(ReportEvent.ClickPaymentButton, {
    userId: userStore.userInfo?.uuid,
    amount: price.amount,
    coins: price.coins
  })

  // 显示准备支付会话的消息
  Message.loading('Preparing payment session...', 10000)

  try {
    // 使用优化后的 Stripe 工具
    await createStripeCheckout({
      priceId: price.id,
      successUrl: `${window.location.origin}/payment/stripe-callback?amount=${price.coins}`,
      cancelUrl: cancelURL
    })

    // 注意：这里不需要关闭 loading，因为页面会跳转
  } catch (err: any) {
    // 处理所有错误
    console.error('Payment error:', err)
    Message.clear()
    Message.error(err.message || 'Payment failed, please try again later')
  } finally {
    // 确保在组件内部的 loading 状态被重置
    loading.value = false
  }
}

const handleClose = () => {
  selectedPrice.value = null
  loading.value = false
  rechargeStore.hideRechargeModal()
}

watch(
  () => rechargeStore.visible,
  (visible) => {
    if (visible) {
      loading.value = false
      selectedPrice.value = null
      reportEvent(ReportEvent.ShowRechargeModal, {
        userId: userStore.userInfo?.uuid,
        path: route.fullPath
      })
      rechargeStore.fetchPriceList()
    }
  }
)

watch(
  () => route.fullPath,
  () => {
    loading.value = false
    if (!rechargeStore.visible) {
      selectedPrice.value = null
    }
  }
)

onMounted(() => {
  loading.value = false
  selectedPrice.value = null
})
</script>

<style lang="less" scoped>
@import '@/assets/style/theme.less';

.recharge-drawer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  animation: fadeIn 0.3s ease;
}

.drawer-content {
  width: 100%;
  max-height: 90vh;
  background: linear-gradient(180deg, var(--mobile-bg-secondary) 0%, var(--mobile-bg-primary) 100%);
  border-radius: 24px 24px 0 0;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease;
  transition: background 0.3s ease;
}

.drawer-header {
  padding: 20px 20px 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  background: var(--mobile-bg-secondary);
  border-radius: 24px 24px 0 0;
  z-index: 2;
  transition: background 0.3s ease;

  h2 {
    color: var(--text-primary);
    font-size: 20px;
    font-weight: 600;
    margin: 0;
  }
}

.drawer-body {
  flex: 1;
  overflow-y: auto;
  padding: 0 20px;
  max-height: calc(90vh - 160px);
  -webkit-overflow-scrolling: touch;
}

.drawer-footer {
  padding: 16px 20px 24px;
  background: linear-gradient(180deg, transparent 0%, var(--mobile-bg-primary) 20%);
  z-index: 2;
  transition: background 0.3s ease;
}

.close-button {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--bg-tertiary);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--text-primary);
  padding: 0;
  z-index: 3;
  transition: all 0.3s ease;

  &:hover {
    background: var(--bg-hover);
  }

  :deep(svg) {
    width: 24px;
    height: 24px;
  }
}

.price-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px 0;
}

.price-item {
  position: relative;
  width: 100%;
  height: 120px;
  border-radius: 18px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid var(--border-color);
  display: flex;
  background: var(--bg-tertiary);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px var(--shadow-color);
  }

  &.is-selected {
    border-radius: 20px;
    border: 2px solid var(--accent-color);
    box-shadow: 0 0 0 1px var(--accent-color);
  }

  .background-image {
    width: 120px;
    height: 100%;
    object-fit: cover;
  }

  .price-info {
    flex: 1;
    padding: 16px 20px;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;

    .info-content {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .amount {
      font-size: 26px;
      font-weight: 600;
      color: var(--text-primary);
    }

    .coins {
      display: flex;
      align-items: center;
      gap: 4px;
      font-size: 18px;
      font-weight: 700;
      color: var(--accent-color);

      .diamond-icon {
        width: 24px;
        height: 24px;
      }
    }

    .discount {
      position: absolute;
      top: 0px;
      right: 0px;
      background: var(--accent-hover);
      padding: 3px 11px 3px 9px;
      border-radius: 0px 18px 0px 10px;
      font-size: 12px;
      font-weight: 600;
      color: var(--bg-primary);
    }
  }

  // Skeleton styles
  &.skeleton-item {
    cursor: default;

    &:hover {
      transform: none;
    }

    .skeleton-image {
      width: 120px;
      height: 100%;
      background: linear-gradient(
        90deg,
        var(--bg-tertiary) 25%,
        var(--bg-hover) 37%,
        var(--bg-tertiary) 63%
      );
      background-size: 200% 100%;
      animation: shimmer 1.5s infinite;
    }

    .skeleton-text {
      background: linear-gradient(
        90deg,
        var(--bg-tertiary) 25%,
        var(--bg-hover) 37%,
        var(--bg-tertiary) 63%
      );
      background-size: 200% 100%;
      animation: shimmer 1.5s infinite;
      border-radius: 4px;
      height: 20px;
    }

    .amount.skeleton-text {
      width: 70%;
      height: 26px;
    }

    .coins.skeleton-text {
      width: 50%;
      height: 18px;
    }

    .skeleton-discount {
      position: absolute;
      top: 0px;
      right: 0px;
      width: 70px;
      height: 22px;
      background: linear-gradient(
        90deg,
        var(--bg-tertiary) 25%,
        var(--bg-hover) 37%,
        var(--bg-tertiary) 63%
      );
      background-size: 200% 100%;
      animation: shimmer 1.5s infinite;
      border-radius: 0px 18px 0px 10px;
    }
  }
}

.purchase-button {
  width: 100%;
  height: 50px;
  border-radius: 25px;
  border: none;
  background: linear-gradient(90deg, var(--accent-color) 0%, var(--accent-hover) 100%);
  color: var(--bg-primary);
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &:not(:disabled):hover {
    opacity: 0.9;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px var(--accent-shadow);
  }

  &:not(:disabled):active {
    opacity: 0.8;
    transform: translateY(0);
  }
}

@media screen and (max-width: 360px) {
  .price-item {
    height: 110px;

    .background-image {
      width: 100px;
    }

    .price-info {
      padding: 12px 16px;

      .amount {
        font-size: 22px;
      }

      .coins {
        font-size: 16px;

        .diamond-icon {
          width: 20px;
          height: 20px;
        }
      }
    }
  }
}

@media screen and (max-height: 600px) {
  .drawer-content {
    max-height: 95vh;
  }

  .drawer-body {
    max-height: calc(95vh - 160px);
  }

  .price-item {
    height: 100px;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
</style>
