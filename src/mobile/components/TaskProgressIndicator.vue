<template>
  <div class="task-progress-container">
    <!-- 圆环进度指示器 -->
    <div class="progress-ring" @click="toggleTaskList">
      <svg class="progress-circle" width="40" height="40" viewBox="0 0 40 40">
        <circle
          class="progress-circle-bg"
          cx="20"
          cy="20"
          r="16"
          fill="none"
          stroke="rgba(255, 255, 255, 0.2)"
          stroke-width="4"
        />
        <circle
          class="progress-circle-value"
          cx="20"
          cy="20"
          r="16"
          fill="none"
          :stroke="currentTask?.percent === 100 ? '#e1fc91' : '#e1fc91'"
          stroke-width="4"
          :stroke-dasharray="circumference"
          :stroke-dashoffset="dashOffset"
          transform="rotate(-90 20 20)"
        />
      </svg>
      <span class="progress-text">
        <icon-task />
      </span>
    </div>

    <!-- 任务列表弹出层 -->
    <div v-if="showTaskList" class="task-list-modal" @click.self="toggleTaskList">
      <div class="task-list">
        <div class="task-list-header">
          <h3>Task Progress</h3>
          <button class="close-button" @click="toggleTaskList">
            <icon-close />
          </button>
        </div>
        <div class="task-list-content">
          <div v-for="task in tasks" :key="task.task_id" class="task-item">
            <div class="task-info">
              <div class="task-description">{{ task.description }}</div>
              <div class="task-progress">{{ task.percent }}%</div>
            </div>
            <div class="progress-bar">
              <div class="progress-bar-value" :style="{ width: `${task.percent}%` }" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useChatStore } from '@/store/chat'
import IconClose from '@/assets/icon/close.svg'
import IconTask from '@/assets/icon/task.svg'
import { ReportEvent } from '@/interface'
import { reportEvent } from '@/utils'

const chatStore = useChatStore()
const showTaskList = ref(false)

const circumference = computed(() => 2 * Math.PI * 16)
const currentTask = computed(() => {
  if (!chatStore.currentTaskId) return null
  return chatStore.tasks.find((task) => task.task_id === chatStore.currentTaskId)
})

const dashOffset = computed(() => {
  if (!currentTask.value) return circumference.value
  return circumference.value * (1 - currentTask.value.percent / 100)
})

const tasks = computed(() => chatStore.tasks)

const toggleTaskList = () => {
  showTaskList.value = !showTaskList.value
  if (showTaskList.value) {
    reportEvent(ReportEvent.TaskListOpen)
  }
}
</script>

<style lang="less" scoped>
.task-progress-container {
  position: fixed;
  top: 16px;
  right: 16px;
  z-index: 1000;
}

.progress-ring {
  position: relative;
  width: 40px;
  height: 40px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  backdrop-filter: blur(4px);
  transform: scale(1.3);

  .progress-circle {
    position: absolute;
    top: 0;
    left: 0;
  }

  .progress-circle-value {
    transition: stroke-dashoffset 0.3s ease;
  }

  .progress-text {
    position: relative;
    z-index: 2;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;

    :deep(svg) {
      width: 20px;
      height: 20px;
    }
  }
}

.task-list-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1001;
  backdrop-filter: blur(4px);
}

.task-list {
  width: 90%;
  max-width: 340px;
  background: #1f0038;
  border-radius: 16px;
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.task-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  h3 {
    color: #fff;
    font-size: 16px;
    font-weight: 600;
    margin: 0;
  }

  .close-button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    cursor: pointer;
    :deep(svg) {
      width: 30px;
      height: 30px;
    }
    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }
  }
}

.task-list-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.task-item {
  .task-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
  }

  .task-description {
    color: #fff;
    font-size: 14px;
    opacity: 0.8;
  }

  .task-progress {
    color: #ca93f2;
    font-size: 14px;
    font-weight: 600;
  }

  .progress-bar {
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    overflow: hidden;

    .progress-bar-value {
      height: 100%;
      background: #ca93f2;
      border-radius: 2px;
      transition: width 0.3s ease;
    }
  }
}
</style>
