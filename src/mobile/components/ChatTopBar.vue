<template>
  <div class="chat-top-bar">
    <div class="left-section">
      <button class="back-button" @click="$emit('back')">
        <icon-left />
      </button>
      <DiamondConsumptionTip v-if="showDiamondTip" class="diamond-tip" />
    </div>
    <div class="right-section">
      <CreditDisplay
        v-if="showCreditDisplay"
        :amount="amount"
        :show-add-button="showAddButton"
        @add="$emit('add')"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import CreditDisplay from '@/shared/components/CreditDisplay.vue'
import DiamondConsumptionTip from '@/mobile/components/DiamondConsumptionTip.vue'

defineProps({
  showDiamondTip: {
    type: Boolean,
    default: false
  },
  showCreditDisplay: {
    type: Boolean,
    default: true
  },
  amount: {
    type: Number,
    default: 0
  },
  showAddButton: {
    type: Boolean,
    default: true
  }
})

defineEmits(['back', 'add'])
</script>

<style lang="less" scoped>
@import '@/assets/style/theme.less';

.chat-top-bar {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 56px;
  padding: 0 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 10;

  .left-section {
    display: flex;
    align-items: center;
    gap: 12px;

    .back-button {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: var(--bg-tertiary);
      backdrop-filter: blur(10px);
      border: none;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: var(--bg-hover);
      }

      &:active {
        transform: scale(0.95);
      }

      :deep(svg) {
        width: 20px;
        height: 20px;
        color: var(--text-primary);
      }
    }

    .diamond-tip {
      margin-left: 8px;
    }
  }

  .right-section {
    display: flex;
    align-items: center;
  }
}
</style>
