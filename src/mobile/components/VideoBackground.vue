<template>
  <EnhancedVideoBackground
    :video-url="videoUrl"
    :muted="muted"
    :loop="loop"
    :autoplay="autoplay"
    :fade-out-on-end="fadeOutOnEnd"
    :show-volume-button="showVolumeButton"
    :show-skip-button="showSkipButton"
    :transition-duration="600"
    @video-ended="handleVideoEnded"
    @update:muted="handleMutedChange"
    @video-skipped="handleVideoSkipped"
    @video-loaded="handleVideoLoaded"
    @transition-complete="handleTransitionComplete"
  />
</template>

<script setup lang="ts">
import { defineEmits } from 'vue'
import EnhancedVideoBackground from '@/mobile/components/EnhancedVideoBackground.vue'

// 组件属性
defineProps<{
  videoUrl?: string
  muted?: boolean
  loop?: boolean
  autoplay?: boolean
  fadeOutOnEnd?: boolean
  showVolumeButton?: boolean
  showSkipButton?: boolean
}>()

// 组件事件
const emit = defineEmits<{
  (e: 'video-ended'): void
  (e: 'update:muted', value: boolean): void
  (e: 'video-skipped'): void
  (e: 'video-loaded'): void
}>()

// 处理视频结束
const handleVideoEnded = () => {
  emit('video-ended')
}

// 处理静音状态变化
const handleMutedChange = (value: boolean) => {
  emit('update:muted', value)
}

// 处理视频跳过
const handleVideoSkipped = () => {
  emit('video-skipped')
}

// 处理视频加载完成
const handleVideoLoaded = () => {
  emit('video-loaded')
}

// 处理过渡完成
const handleTransitionComplete = () => {
  // 可以在这里添加额外的逻辑
}

defineOptions({
  name: 'VideoBackground'
})
</script>
