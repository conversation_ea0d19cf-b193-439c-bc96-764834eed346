import { ref, onMounted, onUnmounted } from 'vue'

export function useDeviceRotation() {
  const isRotating = ref(false)
  const rotateX = ref(0)
  const rotateY = ref(0)
  const isEnabled = ref(false)
  let startX = 0
  let startY = 0
  let currentX = 0
  let currentY = 0

  const handleMouseDown = (e: MouseEvent) => {
    // 只在PC端且功能启用时生效
    if (window.innerWidth < 1366 || !isEnabled.value) return

    isRotating.value = true
    startX = e.clientX - currentX
    startY = e.clientY - currentY

    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
  }

  const handleMouseMove = (e: MouseEvent) => {
    if (!isRotating.value) return

    currentX = e.clientX - startX
    currentY = e.clientY - startY

    // 限制旋转角度在合理范围内
    rotateX.value = Math.max(Math.min(currentY * 0.5, 60), -60)
    rotateY.value = Math.max(Math.min(currentX * 0.5, 60), -60)

    const app = document.getElementById('app')
    if (app) {
      app.style.transform = `rotateX(${-rotateX.value}deg) rotateY(${rotateY.value}deg)`
    }
  }

  const handleMouseUp = () => {
    isRotating.value = false
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)

    // 添加弹性回弹动画
    const app = document.getElementById('app')
    if (app) {
      app.style.transition = 'transform 0.6s cubic-bezier(0.23, 1, 0.32, 1)'
      app.style.transform = 'rotateX(0deg) rotateY(0deg)'
      setTimeout(() => {
        rotateX.value = 0
        rotateY.value = 0
        currentX = 0
        currentY = 0
      }, 600)
    }
  }

  // 双击重置旋转
  const handleDoubleClick = () => {
    if (window.innerWidth < 1366 || !isEnabled.value) return

    const app = document.getElementById('app')
    if (app) {
      app.style.transition = 'transform 0.6s cubic-bezier(0.23, 1, 0.32, 1)'
      app.style.transform = 'rotateX(0deg) rotateY(0deg)'
      rotateX.value = 0
      rotateY.value = 0
      currentX = 0
      currentY = 0
    }
  }

  // 添加控制台彩蛋激活机制
  const activateEasterEgg = () => {
    if (window.innerWidth >= 1366) {
      isEnabled.value = true
      console.log(
        '%c🎮 3D旋转模式已激活～,请尝试拖拽手机旋转',
        'color: #ca93f2; font-size: 14px; font-weight: bold;'
      )
    }
  }

  // 在window上添加激活方法
  ;(window as any).magicRotate = () => {
    activateEasterEgg()
  }

  onMounted(() => {
    const app = document.getElementById('app')
    if (app) {
      app.addEventListener('mousedown', handleMouseDown)
      app.addEventListener('dblclick', handleDoubleClick)
    }
  })

  onUnmounted(() => {
    const app = document.getElementById('app')
    if (app) {
      app.removeEventListener('mousedown', handleMouseDown)
      app.removeEventListener('dblclick', handleDoubleClick)
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }
  })

  return {
    isRotating,
    rotateX,
    rotateY,
    isEnabled
  }
}
