import { ref } from 'vue'
import { useChatStore } from '@/store/chat'
import { useAudioManager } from '@/mobile/composables/useAudioManager'

const audioManager = useAudioManager()

export function useVideoManager() {
  const chatStore = useChatStore()
  const videoPlayerRef = ref<any>(null)

  const setVideoRef = (el: any) => {
    chatStore.videoElementRef = el?.$el?.querySelector('video') || null
  }

  const onVideoEnded = () => {
    console.log('Video ended, checking audio state before playing BGM')
    // 只有在非静音状态下才播放BGM
    if (!audioManager.isMuted) {
      console.log('Audio not muted, playing BGM')
      audioManager.playBgm()
    } else {
      console.log('Audio is muted, not playing BGM')
    }

    setTimeout(() => {
      if (chatStore.messageQueue && chatStore.messageQueue.length > 0) {
        chatStore.processEventQueue()
      }
    }, 100)
  }

  const onVideoLoaded = () => {
    if (chatStore.messageQueue && chatStore.messageQueue.length > 0) {
      chatStore.processEventQueue()
    }
  }

  const handleVideoSkipped = async () => {
    console.log('Video skipped, checking audio state before playing BGM')
    // 只有在非静音状态下才播放BGM
    if (!audioManager.isMuted) {
      console.log('Audio not muted, playing BGM')
      audioManager.playBgm()
    } else {
      console.log('Audio is muted, not playing BGM')
    }

    if (chatStore.messageQueue && chatStore.messageQueue.length > 0) {
      await chatStore.processEventQueue()
    }
  }

  return {
    videoPlayerRef,
    setVideoRef,
    onVideoEnded,
    onVideoLoaded,
    handleVideoSkipped
  }
}
