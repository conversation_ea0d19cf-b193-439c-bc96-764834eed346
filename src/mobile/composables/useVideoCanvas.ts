import { ref, onMounted, onUnmounted } from 'vue'

export function useVideoCanvas() {
  const canvasRef = ref<HTMLCanvasElement | null>(null)
  const videoRef = ref<HTMLVideoElement | null>(null)
  const needsPlayButton = ref(false)
  let animationFrameId: number | null = null
  let initialHeight = 0
  const isPC = window.innerWidth >= 1366
  let isDestroyed = false
  let isPaused = false
  let lastAnimationTimestamp = 0

  // 存储视频 URL 到帧 URL 的映射 (限制缓存大小)
  const frameCache = ref<Map<string, string>>(new Map())
  const MAX_CACHE_SIZE = 50

  // 缓存计算结果以避免重复计算
  let cachedCanvasWidth = 0
  let cachedCanvasHeight = 0
  let cachedVideoRatio = 0
  let cachedDrawParams: { x: number; y: number; width: number; height: number } | null = null

  // 计算PC设备上的高度值，与CSS保持一致
  const getPCHeight = () => {
    // 使用95vh但不超过900px
    return Math.min(window.innerHeight * 0.95, 900)
  }

  // 初始化canvas尺寸（只在必要时调用）
  const initializeCanvasSize = () => {
    if (!canvasRef.value) return

    let targetWidth: number
    let targetHeight: number

    // 尝试获取父容器的实际尺寸
    const parentElement = canvasRef.value.parentElement
    if (parentElement) {
      const rect = parentElement.getBoundingClientRect()
      if (rect.width > 0 && rect.height > 0) {
        targetWidth = rect.width
        targetHeight = rect.height
      } else {
        // 如果父容器尺寸为0，使用默认的全屏尺寸
        targetWidth = isPC ? 450 : window.innerWidth
        targetHeight = isPC ? getPCHeight() : initialHeight || window.innerHeight
      }
    } else {
      // 如果没有父容器，使用默认的全屏尺寸
      targetWidth = isPC ? 450 : window.innerWidth
      targetHeight = isPC ? getPCHeight() : initialHeight || window.innerHeight
    }

    // 只有在尺寸真正变化时才重设canvas
    if (canvasRef.value.width !== targetWidth || canvasRef.value.height !== targetHeight) {
      canvasRef.value.width = targetWidth
      canvasRef.value.height = targetHeight
      cachedCanvasWidth = targetWidth
      cachedCanvasHeight = targetHeight
      // 尺寸变化时重置绘制参数缓存
      cachedDrawParams = null
    }
  }

  // 计算绘制参数（带缓存）
  const getDrawParams = () => {
    if (!videoRef.value || !canvasRef.value) return null

    const videoRatio = videoRef.value.videoWidth / videoRef.value.videoHeight

    // 如果视频比例没有变化且canvas尺寸没有变化，使用缓存
    if (
      cachedDrawParams &&
      Math.abs(cachedVideoRatio - videoRatio) < 0.001 &&
      cachedCanvasWidth === canvasRef.value.width &&
      cachedCanvasHeight === canvasRef.value.height
    ) {
      return cachedDrawParams
    }

    // 重新计算绘制参数
    const canvasRatio = canvasRef.value.width / canvasRef.value.height
    let drawWidth = canvasRef.value.width
    let drawHeight = canvasRef.value.height
    let x = 0
    let y = 0

    // 确保视频始终填满画布，可能会裁剪部分内容
    if (videoRatio > canvasRatio) {
      drawHeight = canvasRef.value.height
      drawWidth = drawHeight * videoRatio
      x = (canvasRef.value.width - drawWidth) / 2
    } else {
      drawWidth = canvasRef.value.width
      drawHeight = drawWidth / videoRatio
      y = (canvasRef.value.height - drawHeight) / 2
    }

    // 缓存结果
    cachedDrawParams = { x, y, width: drawWidth, height: drawHeight }
    cachedVideoRatio = videoRatio
    cachedCanvasWidth = canvasRef.value.width
    cachedCanvasHeight = canvasRef.value.height

    return cachedDrawParams
  }

  const drawFrame = () => {
    if (!videoRef.value || !canvasRef.value || isDestroyed) return

    const ctx = canvasRef.value.getContext('2d')
    if (!ctx) return

    try {
      // 只在必要时初始化canvas尺寸
      initializeCanvasSize()

      // 使用缓存的绘制参数
      const drawParams = getDrawParams()
      if (!drawParams) return

      // 清除画布
      ctx.clearRect(0, 0, canvasRef.value.width, canvasRef.value.height)

      // 绘制视频帧，使用缓存的参数
      ctx.drawImage(videoRef.value, drawParams.x, drawParams.y, drawParams.width, drawParams.height)

      // 只有在组件未被销毁且未暂停时才继续请求下一帧
      if (!isDestroyed && !isPaused) {
        lastAnimationTimestamp = Date.now()
        animationFrameId = requestAnimationFrame(drawFrame)
      }
    } catch (error) {
      console.error('Error in drawFrame:', error)
      // 如果发生错误，尝试重新初始化
      if (!isDestroyed) {
        stopVideo()
        startVideo(videoRef.value?.src || '')
      }
    }
  }

  // 恢复动画播放
  const resumeAnimation = () => {
    if (isPaused && !animationFrameId && !isDestroyed && videoRef.value) {
      isPaused = false
      drawFrame()
    }
  }

  // 暂停动画播放但不停止视频
  const pauseAnimation = () => {
    isPaused = true
    if (animationFrameId) {
      cancelAnimationFrame(animationFrameId)
      animationFrameId = null
    }
  }

  // 检查动画是否需要恢复
  const checkAndResumeAnimation = () => {
    // 如果动画已经停止超过100ms，尝试恢复
    if (
      !animationFrameId &&
      !isPaused &&
      !isDestroyed &&
      videoRef.value &&
      Date.now() - lastAnimationTimestamp > 100
    ) {
      drawFrame()
    }
  }

  const startVideo = (url: string) => {
    if (!videoRef.value || isDestroyed) return

    // 确保在开始播放前已经有初始高度
    if (initialHeight === 0) {
      initialHeight = isPC ? getPCHeight() : window.innerHeight
    }
    isPaused = false

    // 重置缓存状态
    cachedDrawParams = null

    try {
      // 重置视频状态
      videoRef.value.pause()
      videoRef.value.currentTime = 0
      videoRef.value.src = url
      videoRef.value.load()

      // 设置播放速度
      videoRef.value.playbackRate = 1

      // 开始绘制
      if (!isDestroyed) {
        drawFrame()
      }
    } catch (error) {
      console.error('Error in startVideo:', error)
    }
  }

  const stopVideo = (resetTime = false) => {
    isPaused = true
    if (animationFrameId) {
      cancelAnimationFrame(animationFrameId)
      animationFrameId = null
    }
    if (videoRef.value) {
      needsPlayButton.value = false
      videoRef.value.pause()
      // 只有在需要重置时间时才重置 currentTime
      if (resetTime) {
        videoRef.value.currentTime = 0
      }
    }
  }

  const clearVideo = () => {
    if (videoRef.value) {
      videoRef.value.pause()
      videoRef.value.currentTime = 0
      videoRef.value.src = ''
      videoRef.value.load()
    }
    if (canvasRef.value) {
      const ctx = canvasRef.value.getContext('2d')
      if (ctx) {
        ctx.clearRect(0, 0, canvasRef.value.width, canvasRef.value.height)
      }
    }
  }

  const resetCanvas = () => {
    if (canvasRef.value) {
      const ctx = canvasRef.value.getContext('2d')
      if (ctx) {
        ctx.clearRect(0, 0, canvasRef.value.width, canvasRef.value.height)
      }
    }
  }

  // 监听键盘事件
  const handleKeyboardUp = () => {
    // 记录当前状态，但不立即暂停动画
    // 我们只在必要时暂停，并设置一个定时器来检查是否需要恢复
    if (!isPaused && animationFrameId) {
      lastAnimationTimestamp = Date.now()
    }
    // 调度检查而不是立即检查
    scheduleAnimationCheck()
  }

  const handleKeyboardDown = () => {
    // 键盘收起时，检查并恢复动画
    checkAndResumeAnimation()
  }

  // 使用更高效的检查机制，避免定时器轮询
  let animationCheckTimeout: number | null = null

  // 延迟检查动画状态（避免频繁轮询）
  const scheduleAnimationCheck = () => {
    if (animationCheckTimeout) {
      clearTimeout(animationCheckTimeout)
    }
    animationCheckTimeout = window.setTimeout(() => {
      checkAndResumeAnimation()
      animationCheckTimeout = null
    }, 200) // 200ms后检查一次，比1秒轮询更高效
  }

  onMounted(() => {
    // 监听键盘事件
    document.addEventListener('KeyboardUp', handleKeyboardUp)
    document.addEventListener('KeyboardDown', handleKeyboardDown)
  })

  onUnmounted(() => {
    isDestroyed = true
    isPaused = true
    stopVideo(true) // 重置时间
    clearVideo()
    resetCanvas()
    needsPlayButton.value = false

    // 移除事件监听
    document.removeEventListener('KeyboardUp', handleKeyboardUp)
    document.removeEventListener('KeyboardDown', handleKeyboardDown)

    // 清除定时器
    if (animationCheckTimeout !== null) {
      clearTimeout(animationCheckTimeout)
      animationCheckTimeout = null
    }
  })

  /**
   * 从当前 canvas 捕获帧并返回 URL
   * @returns 返回帧的 URL
   */
  const captureCurrentFrame = (): string => {
    if (!canvasRef.value) return ''

    try {
      // 将画布内容转换为 URL
      return canvasRef.value.toDataURL('image/jpeg', 0.9)
    } catch (error) {
      console.error('Error capturing current frame:', error)
      return ''
    }
  }

  /**
   * 从视频 URL 中捕获最后一帧
   * @param videoUrl 视频 URL
   * @returns 返回一个 Promise，解析为帧的 URL
   */
  const captureLastFrame = async (videoUrl: string): Promise<string> => {
    // 如果缓存中已经有这个视频的帧，直接返回
    if (frameCache.value.has(videoUrl)) {
      return frameCache.value.get(videoUrl)!
    }

    return new Promise((resolve, reject) => {
      // 创建一个临时视频元素
      const video = document.createElement('video')
      video.crossOrigin = 'anonymous' // 允许跨域
      video.muted = true // 静音
      video.preload = 'metadata' // 预加载元数据

      // 设置视频加载事件
      video.onloadedmetadata = () => {
        // 设置视频时间到接近结尾但不完全是结尾的位置
        // 这样可以避免一些视频在结尾处可能出现的黑屏或特效
        video.currentTime = Math.max(0, video.duration * 0.95)
      }

      // 当视频跳转到指定时间后触发
      video.onseeked = () => {
        try {
          // 创建一个临时画布
          const canvas = document.createElement('canvas')
          canvas.width = video.videoWidth
          canvas.height = video.videoHeight

          // 获取画布上下文
          const ctx = canvas.getContext('2d')
          if (!ctx) {
            reject(new Error('Failed to get canvas context'))
            return
          }

          // 将视频帧绘制到画布上
          ctx.drawImage(video, 0, 0, canvas.width, canvas.height)

          // 将画布内容转换为 URL
          const frameUrl = canvas.toDataURL('image/jpeg', 0.9)

          // 缓存帧 URL (限制缓存大小)
          if (frameCache.value.size >= MAX_CACHE_SIZE) {
            // 删除最旧的缓存项
            const firstKey = frameCache.value.keys().next().value
            if (firstKey) {
              frameCache.value.delete(firstKey)
            }
          }
          frameCache.value.set(videoUrl, frameUrl)

          // 清理资源
          video.pause()
          video.src = ''
          video.load()

          // 返回帧 URL
          resolve(frameUrl)
        } catch (error) {
          console.error('Error capturing video frame:', error)
          reject(error)
        }
      }

      // 错误处理
      video.onerror = (error) => {
        // console.error('Error loading video for frame capture:', error)
        reject(error)
      }

      // 设置视频源并开始加载
      video.src = videoUrl
      video.load()
    })
  }

  /**
   * 清除帧缓存
   */
  const clearFrameCache = () => {
    frameCache.value.clear()
  }

  return {
    canvasRef,
    videoRef,
    clearVideo,
    startVideo,
    stopVideo,
    needsPlayButton,
    resetCanvas,
    resumeAnimation,
    pauseAnimation,
    // 新增的帧捕获功能
    captureCurrentFrame,
    captureLastFrame,
    frameCache,
    clearFrameCache
  }
}
