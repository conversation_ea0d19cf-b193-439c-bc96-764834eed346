<template>
  <div v-if="showInstallPrompt && !isMobile" class="pwa-install-prompt">
    <div class="prompt-content">
      <button @click="dismissPrompt" class="close-btn" aria-label="Close">
        <svg
          width="20"
          height="20"
          viewBox="0 0 20 20"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M15 5L5 15M5 5l10 10"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </button>

      <div class="prompt-header">
        <div class="app-icon">
          <img
            v-if="iconUrl.startsWith('http')"
            :src="iconUrl"
            :alt="appName + ' icon'"
            width="40"
            height="40"
          />
          <svg
            v-else
            width="40"
            height="40"
            viewBox="0 0 512 512"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <rect width="512" height="512" rx="64" fill="url(#gradient)" />
            <defs>
              <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color: #ca93f2; stop-opacity: 1" />
                <stop offset="100%" style="stop-color: #764ba2; stop-opacity: 1" />
              </linearGradient>
            </defs>
            <circle cx="256" cy="256" r="120" fill="white" opacity="0.9" />
            <polygon points="220,200 220,312 340,256" fill="#ca93f2" />
          </svg>
        </div>
        <div class="prompt-text">
          <h3>Install {{ appName }}</h3>
          <p>Add to your home screen for a better experience</p>
        </div>
      </div>

      <div class="prompt-actions">
        <button @click="dismissPrompt" class="dismiss-btn">Not now</button>
        <button @click="installApp" class="install-btn">
          <span>Install</span>
          <svg
            width="16"
            height="16"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M8 1v10M4 7l4 4 4-4M2 14h12"
              stroke="currentColor"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import {
  shouldShowPWAPrompt,
  recordPWADismissal,
  incrementPWAPromptCount,
  isIOSDevice,
  getPWAInstallInstructions
} from '@/utils/pwa'
import { isMobileEnv } from '@/utils/util'

const appName = import.meta.env.VITE_APP_NAME || 'PlayShot'
const iconUrl = import.meta.env.VITE_ICON_URL || '/icon.svg'
const isMobile = isMobileEnv()

const showInstallPrompt = ref(false)
let deferredPrompt: any = null

// 计算安装指导文本
const installInstructions = computed(() => getPWAInstallInstructions())

const handleBeforeInstallPrompt = (e: Event) => {
  console.log('🎯 beforeinstallprompt event triggered')
  // 阻止默认的安装提示
  e.preventDefault()
  // 保存事件，以便稍后触发
  deferredPrompt = e
  console.log('💾 deferredPrompt saved:', deferredPrompt)

  // 检查是否应该显示提示
  if (shouldShowPWAPrompt()) {
    console.log('✅ Showing PWA install prompt')
    showInstallPrompt.value = true
    incrementPWAPromptCount()
  } else {
    console.log('❌ PWA install prompt blocked by shouldShowPWAPrompt()')
  }
}

const installApp = async () => {
  console.log('🔧 Install button clicked')
  console.log('- deferredPrompt:', deferredPrompt)
  console.log('- isIOSDevice():', isIOSDevice())

  if (deferredPrompt) {
    // Android/Chrome 自动安装
    console.log('📱 Using deferredPrompt for installation')
    deferredPrompt.prompt()

    const { outcome } = await deferredPrompt.userChoice
    console.log('👤 User choice:', outcome)

    if (outcome === 'accepted') {
      console.log('✅ User accepted the install prompt')
      // 记录安装成功事件
      if (window.gtag) {
        window.gtag('event', 'pwa_install_success', {
          event_category: 'PWA',
          event_label: 'auto_prompt'
        })
      }
    } else {
      console.log('❌ User dismissed the install prompt')
      recordPWADismissal()
    }

    deferredPrompt = null
  } else if (isIOSDevice()) {
    // iOS 显示手动安装指导
    console.log('🍎 Showing iOS manual installation instructions')
    alert(`To install ${appName}:\n\n${installInstructions.value}`)

    if (window.gtag) {
      window.gtag('event', 'pwa_install_instructions_shown', {
        event_category: 'PWA',
        event_label: 'ios_manual'
      })
    }
  } else {
    // 其他情况：显示通用安装指导
    console.log('💻 Showing generic installation instructions')
    const instructions = `To install ${appName}:

1. Look for an "Install" or "Add to Home Screen" option in your browser menu
2. Or check the address bar for an install icon
3. Follow your browser's installation prompts

Note: PWA installation may not be available in all browsers or if the app is already installed.`

    alert(instructions)

    if (window.gtag) {
      window.gtag('event', 'pwa_install_instructions_shown', {
        event_category: 'PWA',
        event_label: 'generic_manual'
      })
    }
  }

  showInstallPrompt.value = false
}

const dismissPrompt = () => {
  showInstallPrompt.value = false
  deferredPrompt = null
  recordPWADismissal()

  if (window.gtag) {
    window.gtag('event', 'pwa_install_dismissed', {
      event_category: 'PWA'
    })
  }
}

onMounted(() => {
  // 调试信息
  console.log('🔍 PWA Install Prompt Debug Info:')
  console.log('- shouldShowPWAPrompt():', shouldShowPWAPrompt())
  console.log(
    '- isPWAMode():',
    window.matchMedia && window.matchMedia('(display-mode: standalone)').matches
  )
  console.log('- isIOSDevice():', isIOSDevice())
  console.log(
    '- localStorage pwa-install-dismissed:',
    localStorage.getItem('pwa-install-dismissed')
  )
  console.log(
    '- localStorage pwa-install-prompt-count:',
    localStorage.getItem('pwa-install-prompt-count')
  )

  if (!shouldShowPWAPrompt()) {
    console.log('❌ PWA提示被阻止显示')
    return
  }

  console.log('✅ PWA提示条件满足，等待事件...')
  window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)

  // 对于iOS Safari，延迟显示手动安装提示
  if (isIOSDevice()) {
    console.log('📱 iOS设备，5秒后显示手动安装提示')
    setTimeout(() => {
      if (shouldShowPWAPrompt()) {
        console.log('📱 显示iOS PWA安装提示')
        showInstallPrompt.value = true
        incrementPWAPromptCount()
      }
    }, 5000) // 5秒后显示
  } else {
    // 为了测试，在非iOS设备上也显示提示（3秒后）
    console.log('💻 非iOS设备，3秒后显示测试提示')
    setTimeout(() => {
      if (shouldShowPWAPrompt()) {
        console.log('💻 显示测试PWA安装提示')
        showInstallPrompt.value = true
        incrementPWAPromptCount()
      }
    }, 3000)
  }
})

onUnmounted(() => {
  window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
})
</script>

<style lang="less" scoped>
@import '@/assets/style/theme.less';

.pwa-install-prompt {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  max-width: 400px;
}

.prompt-content {
  position: relative;
  background: rgba(31, 0, 56, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.3);
  padding: 24px;
  color: white;
  animation: slideInFromRight 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.close-btn {
  position: absolute;
  top: 12px;
  right: 12px;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  padding: 4px;
  border-radius: 6px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    color: white;
    background: rgba(255, 255, 255, 0.1);
  }
}

.prompt-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

.app-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 3px 12px rgba(202, 147, 242, 0.3);

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.prompt-text {
  flex: 1;

  h3 {
    margin: 0 0 4px 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--accent-color);
    line-height: 1.2;
  }

  p {
    margin: 0;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.4;
  }
}

.prompt-actions {
  display: flex;
  gap: 12px;
}

.dismiss-btn,
.install-btn {
  flex: 1;
  padding: 12px 20px;
  border: none;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.dismiss-btn {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);

  &:hover {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }
}

.install-btn {
  background: linear-gradient(135deg, var(--accent-color) 0%, #b87de0 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(202, 147, 242, 0.4);

  &:hover {
    background: linear-gradient(135deg, #b87de0 0%, var(--accent-color) 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(202, 147, 242, 0.5);
  }

  &:active {
    transform: translateY(-1px);
  }

  svg {
    transition: transform 0.2s ease;
  }

  &:hover svg {
    transform: translateY(1px);
  }
}
</style>
