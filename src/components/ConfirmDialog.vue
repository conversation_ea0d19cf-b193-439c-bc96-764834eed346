<template>
  <Teleport to="body">
    <Transition name="fade">
      <div v-if="visible" class="confirm-dialog-overlay" @click.stop="handleOverlayClick">
        <div class="confirm-dialog" @click.stop>
          <div v-if="showIcon" class="dialog-icon">
            <slot name="icon">
              <div class="default-icon">
                <IconConfirmDefault />
              </div>
            </slot>
          </div>
          <div class="dialog-title" :style="titleStyle">
            <slot>{{ title }}</slot>
          </div>
          <div v-if="content || $slots.content" class="dialog-content" :style="contentStyle">
            <slot name="content">{{ content }}</slot>
          </div>
          <div v-if="$slots.input" class="dialog-input">
            <slot name="input"></slot>
          </div>
          <div class="dialog-actions">
            <button
              v-if="showCancel"
              class="cancel-button"
              :style="cancelButtonStyle"
              @click="handleCancel"
            >
              {{ cancelText }}
            </button>
            <button class="confirm-button" :style="confirmButtonStyle" @click="handleConfirm">
              {{ confirmText }}
            </button>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import IconConfirmDefault from '@/assets/icon/confirm-default.svg'
import { defineProps, defineEmits, withDefaults } from 'vue'

interface Props {
  visible: boolean
  title?: string
  content?: string
  cancelText?: string
  confirmText?: string
  closeOnClickOverlay?: boolean
  showCancel?: boolean
  showIcon?: boolean
  titleStyle?: Record<string, string>
  contentStyle?: Record<string, string>
  confirmButtonStyle?: Record<string, string>
  cancelButtonStyle?: Record<string, string>
  onBeforeConfirm?: () => boolean | Promise<boolean>
}

const props = withDefaults(defineProps<Props>(), {
  showCancel: true,
  showIcon: true,
  cancelText: 'Cancel',
  confirmText: 'Confirm',
  closeOnClickOverlay: true,
  cancelButtonStyle: () => ({}),
  confirmButtonStyle: () => ({})
})

const emit = defineEmits<{
  'update:visible': [value: boolean]
  confirm: []
  cancel: []
}>()

const handleConfirm = async () => {
  try {
    // 如果有验证函数，先执行验证
    if (props.onBeforeConfirm) {
      const isValid = await props.onBeforeConfirm()
      if (!isValid) {
        return
      }
    }
    // 验证通过或没有验证函数，触发确认事件
    emit('confirm')
    emit('update:visible', false)
  } catch (error) {
    console.error('Validation error:', error)
  }
}

const handleCancel = () => {
  emit('cancel')
  emit('update:visible', false)
}

const handleOverlayClick = () => {
  if (props.closeOnClickOverlay) {
    handleCancel()
  }
}
</script>

<style lang="less" scoped>
.confirm-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.confirm-dialog {
  width: calc(100% - 32px);
  max-width: 320px;
  background: #1f0038;
  border-radius: 16px;
  padding: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  color: white;

  .dialog-content {
    width: 100%;
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
    font-size: 15px;
    line-height: 1.5;
  }

  .dialog-input {
    width: 100%;
    margin: 0 0 8px;

    :deep(input) {
      width: 100%;
      height: 42px;
      border-radius: 40px;
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      color: white;
      font-size: 15px;
      padding: 10px 16px;
      outline: none;
      transition: all 0.3s ease;

      &:focus {
        border-color: #ca93f2;
        background: rgba(255, 255, 255, 0.15);
      }

      &::placeholder {
        color: rgba(255, 255, 255, 0.5);
      }
    }

    :deep(.error-message) {
      color: #ff4d4f;
      font-size: 12px;
      margin-top: 4px;
      text-align: left;
    }
  }
}

.dialog-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;

  .default-icon {
    width: 48px;
    height: 48px;
    // border-radius: 24px;
    // background: rgba(255, 255, 255, 0.1);
    // display: flex;
    // align-items: center;
    // justify-content: center;
    // font-size: 24px;
    // color: #ffeb3b;
  }
}

.dialog-title {
  color: #ca93f2;
  text-align: center;
  font-size: 17px;
  font-weight: 700;
}

.dialog-actions {
  width: 100%;
  display: flex;
  gap: 12px;
  margin-top: 8px;

  button {
    flex: 1;
    height: 44px;
    border-radius: 8px;
    font-size: 15px;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: opacity 0.2s ease;

    &:active {
      opacity: 0.8;
    }
  }

  .cancel-button {
    border-radius: 40px;
    border: 1px solid rgba(255, 255, 255, 0.5);
    color: rgba(255, 255, 255, 0.8);
    font-size: 15px;
    font-weight: 600;
    background: #180430;
  }

  .confirm-button {
    border-radius: 40px;
    background: #ca93f2;
    border: 1px solid #ca93f2;
    color: #1f0038;
    font-size: 15px;
    font-weight: 600;
  }
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
