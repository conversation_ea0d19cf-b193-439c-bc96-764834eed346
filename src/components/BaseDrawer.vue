<template>
  <Teleport :to="isDesktop ? '.mobile-chat-container' : '#app'">
    <div v-show="visible" class="base-drawer" :class="{ visible }" @click.self="handleClose">
      <div
        ref="drawerRef"
        class="drawer-content"
        :style="{
          height: height,
          background: background,
          borderRadius: borderRadius
        }"
      >
        <slot></slot>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { animate } from 'motion'
import { ref, watch } from 'vue'
import { useDeviceDetection } from '@/composables/useDeviceDetection'

const props = defineProps<{
  visible: boolean
  height?: string
  background?: string
  borderRadius?: string
  maskColor?: string
  maskBlur?: string
  padding?: string
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
}>()

const drawerRef = ref<HTMLElement>()
const { isDesktop } = useDeviceDetection()

watch(
  () => props.visible,
  (newVal) => {
    if (!drawerRef.value) return

    animate(
      drawerRef.value,
      { y: newVal ? [window.innerHeight, 0] : [0, window.innerHeight] },
      {
        type: 'spring',
        stiffness: 300,
        damping: 30
      }
    )
  }
)

const handleClose = () => {
  emit('update:visible', false)
}
</script>

<style lang="less" scoped>
.base-drawer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: v-bind('maskColor || "rgba(0, 0, 0, 0.5)"');
  backdrop-filter: v-bind('maskBlur || "blur(4px)"');
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;

  &.visible {
    opacity: 1;
    visibility: visible;
  }
}

.drawer-content {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background: #1f0038;
  border-radius: 24px 24px 0 0;
  padding: 0 24px 24px 24px;
  max-height: 90vh;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}
</style>
