import type { ABTestConfig } from '@/utils/abtest'

/**
 * AB测试实验配置
 * 在这里定义所有的AB测试实验
 */
export const AB_TEST_EXPERIMENTS: ABTestConfig = {
  // 故事卡片标签显示测试
  story_tags_display: {
    variants: ['A', 'B'],
    weights: [0, 100], // A组50%，B组50%
    description: '故事卡片标签显示测试：A组超过两行省略，B组仅显示一行'
  },

  // 移动端默认主题测试
  mobile_default_theme: {
    variants: ['A', 'B'],
    weights: [50, 50], // A组50%，B组50%
    description: '移动端默认主题测试：A组默认亮色主题，B组默认暗色主题'
  },

  // 故事卡片设计版本测试
  story_card_design: {
    variants: ['A', 'B'],
    weights: [50, 50], // A组50%使用原版StoryCard，B组50%使用新版StoryCard V2
    description:
      '故事卡片设计版本测试：A组使用原版设计，B组使用新版设计（封面在上，描述区域白色背景在下）'
  }
}

/**
 * 获取实验配置
 */
export function getExperimentConfig(experimentName: string) {
  return AB_TEST_EXPERIMENTS[experimentName]
}

/**
 * 检查实验是否存在
 */
export function hasExperiment(experimentName: string): boolean {
  return experimentName in AB_TEST_EXPERIMENTS
}

/**
 * 获取所有实验名称
 */
export function getAllExperimentNames(): string[] {
  return Object.keys(AB_TEST_EXPERIMENTS)
}

/**
 * 获取所有实验配置
 */
export function getAllExperiments(): ABTestConfig {
  return AB_TEST_EXPERIMENTS
}
