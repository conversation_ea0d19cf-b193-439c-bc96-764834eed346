export default {
  // 参数
  'storeParams.getProjectType': '获取项目类型',
  'storeParams.update': '更新',
  'storeParams.getProjectCategory': '获取项目分类',
  'storeParams.add': '添加',
  'storeParams.network': '网络',
  'storeParams.getSolonTemplateData': '获取接龙模板数据',
  'storeParams.getSolonListData': '获取接龙列表数据',
  'storeParams.getUserConnectionDetailsData': '获取用户接龙详情数据',
  'storeParams.generate': '生成',
  'storeParams.getTemplateData': '获取模板数据',
  'storeParams.getLoraList': '获取Lora列表',
  'storeParams.report': '举报',
  'storeParams.pullProjectData': '拉取项目数据',
  'storeParams.man-machineVerification': '人机验证',
  'storeParams.createWork': '创建作品',
  'storeParams.retryTask': '重试任务',
  'storeParams.restoreParagraphState': '恢复段落状态',
  'storeParams.restorePhotoState': '恢复图片状态',
  'storeParams.getVideoInfo': '获取视频信息',
  'storeParams.getExclusiveImage': '获取专属形象',
  'storeParams.getImageList': '获取形象列表',
  'storeParams.updateProjectState': '更新项目状态',
  'storeParams.convertPhotoState': '转化图片状态',
  'storeParams.setStoryboard': '设置分镜',
  'storeParams.triggerGenVideo': '触发生成视频',
  'storeParams.pullParagraphData': '拉取段落数据',
  'storeParams.deleteParagraph': '删除段落',
  'storeParams.addParagraph': '新增段落',
  'storeParams.pullChapterData': '拉取章节数据',
  'storeParams.pullPhotoData': '拉取图片数据',
  'storeParams.pullStoryboardData': '拉取分镜数据',
  'storeParams.save': '保存',
  'storeParams.saveProjectData': '保存项目数据',
  'storeParams.saveParagraphData': '保存段落数据',
  'storeParams.paragraphSave': '段落保存',
  'storeParams.photoGenTaskSubmit': '图片生成任务提交',
  'storeParams.resetTask': '重置任务',
  'storeParams.oneClickSelectImage': '一键选形象',
  'storeParams.dataFormat': '数据格式',
  'storeParams.storyboardGenTaskSubmit': '分镜生成任务提交',
  'storeParams.createPhoto': '创建图片',
  'storeParams.storyboardGen': '分镜生成',
  'storeParams.updateCharacter': '更新人物',
  'storeParams.addCharacter': '新增人物',
  'storeParams.pullSourceData': '拉取音源数据',
  'storeParams.create': '创建',
  'storeParams.getInvitation': '获取邀请函',
  'storeParams.getChapterData': '获取章节数据',
  'storeParams.getProjectData': '获取项目数据',
  'storeParams.like': '点赞',
  'storeParams.getComment': '获取评论',
  'storeParams.getReply': '获取回复',
  'storeParams.vote': '投票',
  'storeParams.oneClickClearNotification': '一键清除通知',
  'storeParams.okClearNotification': '成功清除通知',
  'storeParams.clearNotification': '清除通知',
  'storeParams.pullWorkList': '拉取作品列表',

  // useStore
  'useStore.all': '全部',
  'useStore.xxxSuccess': '{info}成功',
  'useStore.xxxFail': '{info}失败',
  'useStore.xxxTimeout': '{info}超时',
  'useStore.xxxError': '{info}错误',
  'useStore.winnow': '精选',
  'useStore.newbieTutorial': '新手教程',

  // useAdminStore
  'useAdminStore.notClassified': '未分类',
  'useAdminStore.noActivity': '无活动',

  // useCustomPersonalLoraStore
  'useCustomPersonalLoraStore.tag1': '卡通',
  'useCustomPersonalLoraStore.tag2': '仙侠',
  'useCustomPersonalLoraStore.tag3': '宫廷',
  'useCustomPersonalLoraStore.tag4': '科幻',
  'useCustomPersonalLoraStore.tag5': '武侠',
  'useCustomPersonalLoraStore.tag6': '校园',
  'useCustomPersonalLoraStore.tag7': '异能',
  'useCustomPersonalLoraStore.tag8': '重生',
  'useCustomPersonalLoraStore.tag9': '悬疑',
  'useCustomPersonalLoraStore.tag10': '搞笑',
  'useCustomPersonalLoraStore.tag11': '清冷',
  'useCustomPersonalLoraStore.tag12': '高冷',
  'useCustomPersonalLoraStore.tag13': '张扬',
  'useCustomPersonalLoraStore.tag14': '善良',
  'useCustomPersonalLoraStore.tag15': '沉稳',
  'useCustomPersonalLoraStore.tag16': '温柔',
  'useCustomPersonalLoraStore.tag17': '傲慢',
  'useCustomPersonalLoraStore.tag18': '冷酷',
  'useCustomPersonalLoraStore.tag19': '偏执',
  'useCustomPersonalLoraStore.tag20': '自信',
  'useCustomPersonalLoraStore.RealStyleUnavailable': '上传人脸为动漫形象，写实风格不可用',
  'useCustomPersonalLoraStore.backStep1Guide': '请返回第一步填写形象描述',
  'useCustomPersonalLoraStore.noExclusiveImage': '用户不存在专属形象',
  'useCustomPersonalLoraStore.genError': '生成失败，请重试',

  // useCustomLoraStore
  'useCustomLoraStore.tag1': '卡通',
  'useCustomLoraStore.tag2': '仙侠',
  'useCustomLoraStore.tag3': '宫廷',
  'useCustomLoraStore.tag4': '科幻',
  'useCustomLoraStore.tag5': '武侠',
  'useCustomLoraStore.tag6': '校园',
  'useCustomLoraStore.tag7': '异能',
  'useCustomLoraStore.tag8': '重生',
  'useCustomLoraStore.tag9': '悬疑',
  'useCustomLoraStore.tag10': '搞笑',
  'useCustomLoraStore.tag11': '清冷',
  'useCustomLoraStore.tag12': '高冷',
  'useCustomLoraStore.tag13': '张扬',
  'useCustomLoraStore.tag14': '善良',
  'useCustomLoraStore.tag15': '沉稳',
  'useCustomLoraStore.tag16': '温柔',
  'useCustomLoraStore.tag17': '傲慢',
  'useCustomLoraStore.tag18': '冷酷',
  'useCustomLoraStore.tag19': '偏执',
  'useCustomLoraStore.tag20': '自信',
  'useCustomLoraStore.RealStyleUnavailable': '上传人脸为动漫形象，写实风格不可用',
  'useCustomLoraStore.backStep1Guide': '请返回第一步填写形象描述',
  'useCustomLoraStore.noExclusiveImage': '用户不存在专属形象',
  'useCustomLoraStore.genError': '生成失败，请重试',

  // useDenounceStore
  'useDenounceStore.tag1': '暴力血腥',
  'useDenounceStore.tag2': '危险行为',
  'useDenounceStore.tag3': '政治敏感',
  'useDenounceStore.tag4': '色情信息',
  'useDenounceStore.tag5': '引人不适',
  'useDenounceStore.tag6': '不良价值观引导',
  'useDenounceStore.tag7': '涉及权利侵犯',
  'useDenounceStore.selectReportProject': '请选择举报项目',
  'useDenounceStore.selectReportType': '请选择举报类型',
  'useDenounceStore.selectReportComment': '请选择举报评论',
  'useDenounceStore.selectReportReason': '请选择举报原因',

  // useEditorStore
  'useEditorStore.defaultSilence': '默认无声',
  'useEditorStore.voice-over': '旁白配音',
  'useEditorStore.photoInMotion': '图片转动态中',
  'useEditorStore.photoInGeneration': '生成图片中 ({count}/{total})',
  'useEditorStore.generatedGraphics': '已智能生成 {count} 个画面',
  'useEditorStore.selectProjectStyle': '请选择项目风格',
  'useEditorStore.titleMustWrite': '故事标题必填',
  'useEditorStore.mainMustWrite': '故事正文必填',
  'useEditorStore.styleNotSupportRatio': '当前风格不支持该画面比例',
  'useEditorStore.pleaseSelectRatio': '请选择画面比例',
  'useEditorStore.needVerification': '需要进行人机验证',
  'useEditorStore.reInputGuide': '验证失败，请重新输入',
  'useEditorStore.cantEmptyRoleName': '人物名称不能为空',
  'useEditorStore.selectImageForRole': '请为人物选择形象',
  'useEditorStore.selectDubForRole': '请为人物「{name}」选择配音',
  'useEditorStore.selectDubForParagraph': '请为第{index}个段落选择配音',
  'useEditorStore.addPhotoForParagraph': '请为第{index}个段落新增图片',
  'useEditorStore.handleParagraphGuide': '第{index}个图片生成失败，请处理该段落',
  'useEditorStore.waitForTaskHandle': '第{index}个段落还在处理任务，请稍等',
  'useEditorStore.photoGenUnfinish': '第{index}个段落图片还未生成完毕',
  'useEditorStore.feedbackFailRefresh': '图片反馈失败，请刷新重试',
  'useEditorStore.thankForYourFeedback': '感谢您的反馈！',
  'useEditorStore.toStaticState': '转静态',
  'useEditorStore.toRotationalState': '转动态',
  'useEditorStore.taskInitSuccess': '{msg}任务发起成功',
  'useEditorStore.cantCopyParagraph': '生成任务处理中，不能复制该段落',
  'useEditorStore.disassembleParagraphTip': '段落拆解完成后才能生成图片',
  'useEditorStore.lackParagraphData': '数据错误，缺少段落数据',
  'useEditorStore.selectRoleForCharacter': '请先为人物选择角色',
  'useEditorStore.selectStoryboardForParagraph': '请为第 {index} 个段落选择分镜',
  'useEditorStore.waitHandleFinish': '请等待第 {index} 个段落任务处理结束',
  'useEditorStore.doPhotoGenTaskFirst': '请先执行图片生成任务',
  'useEditorStore.photoNotGenTip': '该章节还有图片没生成，不能发布',
  'useEditorStore.videoCraftTip': '视频正在合成中，请耐心等待',
  'useEditorStore.videoGenError': '视频生成错误，无法发布，请重新生成',
  'useEditorStore.dataAbnormal': '数据异常',
  'useEditorStore.waitTaskHandle': '请等待任务处理完成',
  'useEditorStore.selectEditParagraph': '请选中编辑段落',
  'useEditorStore.dataErrorRefresh': '数据错误，请刷新页面',
  'useEditorStore.currentRoleSaved': '当前角色已保存',
  'useEditorStore.createWorkSuccess': '成功创建作品',
  'useEditorStore.deconstruction': '拆解段落',
  'useEditorStore.exceedParagraphLimit': '每个分镜上限{maxLimit}字，请删减后再合并',
  'useEditorStore.exceedDecomposeLimit': '拆解段落或项目字数达到上限',
  'useEditorStore.pleaseEnterContent': '请输入故事正文',

  // useInvitationStore
  'useInvitationStore.submitOkToGenPage': '提交成功，为你跳转到生成页面',

  // useProjectDetailStore
  'useInvitationStore.likeOkCreditAdd': '点赞成功，积分+{credit}',
  'useInvitationStore.limitVoteTip': '每天对同一作品限投1票，去看看别的作品吧～',
  'useInvitationStore.todayVoteFinish': '今天票数已投完，明天再来吧',
  'useInvitationStore.administratorVote': '投票成功，管理员不限制投票次数',
  'useInvitationStore.voteFailFinish': '投票失败，今日票数已投完，明日再来！',
  'useInvitationStore.voteSuccessFinish': '投票成功，今日票数已投完，明日再来！',
  'useInvitationStore.remainingVotes': '投票成功，今日还有{count}票数',
  'useInvitationStore.contributeOkKeepCreate': '投稿成功，继续创作赢奖金吧！',
  'useInvitationStore.waitFunctionInDev': '功能开发中，敬请期待',

  // useRewardStore
  'useInvitationStore.dailyLoginAddCredit': '每日登录积分 +{points}',

  // useSearchResultStore
  'useInvitationStore.allStyle': '全部风格'
}
