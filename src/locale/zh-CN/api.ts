export default {
  // history-lora
  'api.history-lora.sorry1': '抱歉，您上传的全身照图片数量不足，请重新上传',
  'api.history-lora.sorry2': '抱歉，您上传的半身照图片图片数量不足，请重新上传',
  'api.history-lora.sorry3': '抱歉，您上传的全身照图片质量太低，训练失败，请重新上传',
  'api.history-lora.sorry4': '抱歉，您上传的半身照图片质量太低，训练失败，请重新上传',
  'api.history-lora.sorry5': '抱歉，您上传的图片质量太低，训练失败，请重新上传',
  'api.history-lora.sorry6': '抱歉，您上传的图片数量不足，请重新上传',
  'api.history-lora.sorry7': '抱歉，您上传的图片涉嫌违规，请重新上传',
  'api.history-lora.sorry8': '抱歉，您上传的图片未检测到人脸，请重新上传',
  'api.history-lora.sorry9': '上传的图片涉嫌色情违规，请重新上传',
  'api.history-lora.sorry10': '上传图片涉嫌政治违规，请重新上传',
  'api.history-lora.sorry11': '上传的图片涉嫌敏感内容，请重新上传',
  'api.history-lora.sorry12': '上传图片不符要求，请上传真人或动漫人像',
  'api.history-lora.sorry13': '抱歉，您上传的图片分辨率太低，请重新上传',
  'api.history-lora.sorry14': '抱歉，您上传的图片人脸被遮挡，请重新上传',
  'api.history-lora.unknown': '上传失败，请重新上传',

  // image
  'api.image.sensitiveness1': '注意到生成的内容可能包含敏感信息，请您重新上传形象或重新生成结果。',
  'api.image.sensitiveness2': '上传的图片涉嫌敏感内容，请重新上传',

  // project
  'api.project.tag1': '草稿',
  'api.project.tag2': '私密作品',
  'api.project.tag3': '公开作品',
  'api.project.userCenterAction1': '预览',
  'api.project.userCenterAction2': '编辑',
  'api.project.userCenterAction3': '重命名',
  'api.project.userCenterAction4': '删除',
  'api.project.adminAction1': '编辑',
  'api.project.adminAction2': '预览',
  'api.project.adminAction3': '删除',
  'api.project.adminAction4': '置顶',
  'api.project.adminAction5': '精选值',
  'api.project.adminAction6': '点赞',
  'api.project.adminAction7': '修改分类',
  'api.project.adminAction8': '修改活动',
  'api.project.StepDescMap1': '生成失败',
  'api.project.StepDescMap2': '生成成功',
  'api.project.StepDescMap3': '剧情解析中',
  'api.project.StepDescMap4': '生成分镜中',
  'api.project.StepDescMap5': '队列中',
  'api.project.StepDescMap6': '视频生成中',
  'api.project.ipbibleError': '剧情解析失败',
  'api.project.errorCode1': '检测有敏感内容，请重新修改',
  'api.project.errorCode2': '网络有点小卡顿，请重试',

  // user
  'api.user.action1': '修改名称',
  'api.user.action2': '修改手机号',
  'api.user.action3': '修改性别',
  'api.user.action4': '修改自我介绍',
  'api.user.action5': '修改置顶值',
  'api.user.action6': '修改邮箱'
}
