export default {
  // index-keep-alive.vue
  'activity.likeNumber': '收获点赞 ',
  'activity.seeMore': '查看更多 >',
  'activity.competitionWorks': '最新参赛作品',
  'activity.noWorks': '暂无作品',
  'activity.newestWork': '最新作品',
  // activity-list.vue
  'activity.unopenTip': '榜单暂未开放',
  // competition.vue
  'activity.sampleTopic': '示例主题',
  'activity.submissionTopic': '投稿主题：',
  'activity.endSubmission': '结束投稿',
  // modal.vue
  'activity.submissionTopic2': '投稿主题： ',
  'activity.submissionTrack': '投稿赛道：',
  // project-active-card.vue
  'activity.secondCreation': '二创',
  'activity.ticket': '票',
  'activity.works': '作品',
  'activity.person': '人',
  // project-list.vue
  'activity.todayFinalists1': '吐槽之王今日入围作品🔥',
  'activity.awardRulesTip': ' （每日实时更新，最终获奖作品将从入围作品中评选产生）',
  'activity.seeAllFinalists': '查看全部入围作品 >',
  'activity.rankNoOpen': '~排名暂未开放~',
  'activity.waitSelect': '待评选',
  // project-rank.vue
  'activity.seeMore2': '查看更多',
  'activity.worksRank': '人气作品榜',
  'activity.recomposeRank': '人气改编榜',
  'activity.commentRank': '人气评论榜',
  // template-card.vue
  'activity.montageSameKind': '剪同款',
  // user-rank.vue
  'activity.humanPulseRank': '人脉之星榜',
  'activity.involutionRank': '无敌卷王榜'
}
