// pc components
import sideBar from './side-bar'
import actionPanel from './action-panel'
import activityRuleModal from './activity-rule-modal'
import confirmModal from './confirm-modal'
import inviterCard from './inviter-card'
import listCard from './list-card'
import myRewardModal from './my-reward-modal'
import navAvatar from './nav-avatar'
import navBar from './nav-bar'
import notification from './notification'
import userCard from './user-card'
import weeklyStarCard from './weekly-star-card'
// pc views
import about from './about'
import activity from './activity'
import admin from './admin'
import detail from './detail'
import globalSearchResult from './global-search-result'
import help from './help'
import home from './home'
import invite from './invite'
import login from './login'
import myAccount from './my-account'
import newEditor from './new-editor'
import notFound from './not-found'
import popularityDetail from './popularity-detail'
import rankDetail from './rank-detail'
import roleDetail from './role-detail'
import roleLibrary from './role-library'
import ssoLogin from './sso-login'
import template from './template'
import user from './user'
import userCenter from './user-center'
// common
import common from './common'
// components
import globalLoading from './global-loading'
// stores
import useStore from './use-store'
// utils
import utils from './utils'
// components
import components from './components'
// api
import api from './api'
// region restriction
import regionRestriction from './region-restriction'

export default {
  // pc components
  ...sideBar,
  ...actionPanel,
  ...activityRuleModal,
  ...confirmModal,
  ...inviterCard,
  ...listCard,
  ...myRewardModal,
  ...navAvatar,
  ...navBar,
  ...notification,
  ...userCard,
  ...weeklyStarCard,
  // pc views
  ...about,
  ...activity,
  ...admin,
  ...detail,
  ...globalSearchResult,
  ...help,
  ...home,
  ...invite,
  ...login,
  ...myAccount,
  ...newEditor,
  ...notFound,
  ...popularityDetail,
  ...rankDetail,
  ...roleDetail,
  ...roleLibrary,
  ...ssoLogin,
  ...template,
  ...user,
  ...userCenter,
  // common
  ...common,
  // components
  ...globalLoading,
  // stores
  ...useStore,
  // utils
  ...utils,
  // components
  ...components,
  // api
  ...api,
  // region restriction
  regionRestriction
}
