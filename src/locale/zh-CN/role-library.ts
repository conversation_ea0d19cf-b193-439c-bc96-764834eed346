export default {
  // index-keep-alive.vue
  'role-library.characterRecommended': '形象排行',
  'role-library.appearRecently': '近期出演',
  'role-library.category': '类别：',
  'role-library.gender': '性别：',
  'role-library.age': '年龄：',
  'role-library.style': '风格：',
  'role-library.searchRole': '搜索形象',
  'role-library.createRole': '创建形象',
  'role-library.noRoleContent': '~ 暂无形象内容 ~',
  'role-library.imageGenerating': '形象生成中',
  'role-library.requiredTime': '预计需要 20 分钟，完成后将弹窗提示你~',
  'role-library.gotIt': '知道了',
  'role-library.nextStep': '下一步',
  'role-library.previousStep': '上一步',
  'role-library.createActorArchives': '创建形象档案',
  'role-library.clickPlusIconGuide': '在公开形象库或我的形象库点击' + '创建形象档案',
  'role-library.newest': '最新',
  'role-library.hottest': '最热',
  'role-library.child': '儿童',
  'role-library.teenager1': '少年',
  'role-library.teenager2': '青年',
  'role-library.middleAge': '中年',
  'role-library.elderly': '老年',
  'role-library.human': '人类',
  'role-library.unhuman': '非人',
  'role-library.tab1': '公开形象库',
  'role-library.tab2': '我的形象库',
  // create.vue
  'role-library.createImage': '创建形象',
  'role-library.generateError': '生成错误',
  'role-library.roleName': '形象名',
  'role-library.roleStyle': '形象风格',
  'role-library.rolePrompt': '提示词',
  'role-library.rolePromptPlaceholder': '请添加形象的详细描述，如发型、肤色等',
  'role-library.roleEmpty': '请输入人物提示词',
  'role-library.roleDescription': '形象描述',
  'role-library.uploadRule': '该风格仅支持上传真人图片',
  'role-library.createTip2': '所选风格与原风格不一致, 生成将清除历史生成形象',
  'role-library.twoDimensionsUploadTip': '二次元风格不支持上传图片',
  'role-library.imageMaxSizeLimit': '图片不能大于 10 MB',
  'role-library.uploadFailed': '上传失败, 请重新上传',
  'role-library.uploadSuccessfully': '上传图片成功',
  'role-library.generatedFailed': '生成失败，请尝试重新生成',
  'role-library.uploadImage': '上传图片',
  'role-library.historyImage': '历史形象',
  'role-library.thisGeneratedImage': '本次生成形象',
  'role-library.keepGenTip': '退出 AI 会继续生成',
  'role-library.nowCover': '当前封面',
  'role-library.setAsCover': '设为封面',
  'role-library.generating': '生成中',
  'role-library.backEdit': '返回编辑',
  'role-library.genImage': '生成形象',
  'role-library.useThisImage': '使用该形象',
  'role-library.regen': '重新生成',
  'role-library.backPublish': '返回发布',
  'role-library.deleteThisImage': '删除该形象',
  'role-library.modalContentOne': '成功创建档案后，你可以使用该形象创建剧本。',
  'role-library.modalContentTwo': '若选择公开上传，别人也可以使用你创建的形象。',
  'role-library.modalContentThree': '使用数量多的形象将会上人气榜。',
  'role-library.privateUpload': '私密上传',
  'role-library.publicUpload': '公开上传',
  'role-library.irrecoverableTip': '删除后将无法恢复',
  'role-library.labels1': '上传正脸照（至少一张）',
  'role-library.labels2': '上传侧脸照',
  'role-library.labels3': '上传全身正面照（至少一张）',
  'role-library.labels4': '上传全身背面照',
  'role-library.labels5': '上传全身照（建议2～4张即可）',
  'role-library.labels6': '上传半身照（建议2～4张即可）',
  'role-library.labels7': '上传照片 (可选)',
  'role-library.layoutStep1title': '填写形象信息',
  'role-library.layoutStep1intro': '为你创建的形象填写姓名、简介、形象风格等相关信息',
  'role-library.layoutStep2title': '上传照片',
  'role-library.layoutStep2intro': '上传的图片需要按右侧标准提供，图片越多，生成效果越好噢~',
  'role-library.layoutStep3title': '上传图片要求',
  'role-library.layoutStep3intro': '按照正确示范上传图片，若图片不规范会影响生成效果哦～',
  'role-library.layoutStep4title': '生成形象',
  'role-library.layoutStep4intro':
    '上传完照片后可点击生成，生成等待时长约5分钟，生成完毕之后会弹窗通知你',
  'role-library.regenLayoutStep1title': '挑选历史形象',
  'role-library.regenLayoutStep1intro':
    '多次生成的记录将会被保存，选择你最喜欢的形象作为该形象形象',
  'role-library.regenLayoutStep2title': '选择封面、发布形象',
  'role-library.regenLayoutStep2intro':
    '为你创建的形象选择一个形象封面，点击使用该形象即可发布形象，之后创建作品就可以使用该形象啦～',
  'role-library.regenLayoutStep3title': '重新生成',
  'role-library.regenLayoutStep3intro': '建议上传更多符合要求的全身、半身照后再重新生成，效果更好',
  'role-library.imageUploadError': '图片上传错误',
  'role-library.inputActorName': '请输入形象名',
  'role-library.inputActorIntro': '请输入形象介绍',
  'role-library.selectAge': '请选择年龄',
  'role-library.selectGender': '请选择性别',
  'role-library.selectImageStyle': '请选择形象风格',
  'role-library.uploadPhoto': '请上传图片',
  'role-library.uploadLeastOneFacePicture': '请上传至少一张正脸照',
  'role-library.uploadLeastOneBodyPicture': '请上传至少一张全身正面照',
  'role-library.uploadLeastTwoBodyPicture': '请上传至少两张全身照',
  'role-library.uploadLeastTwoHalfBodyPicture': '请上传至少两张半身照',
  'role-library.uploadSuccessGenerating': '上传成功，正在生成中',
  'role-library.genImageFirst': '请先生成形象',
  'role-library.selectHistoryImage': '请选择历史形象',
  'role-library.createSuccess': '创建成功',
  // create-modal.vue
  'role-library.WhatImageWannaCreate': '你想创造一个什么样的形象？',
  'role-library.animal': '动物',
  'role-library.subhuman': '类人',
  'role-library.type': '类型',
  'role-library.test': '测试',
  'role-library.selectRoleType': '请选择形象类型',
  // example.vue
  'role-library.uploadPhotoDemand': '上传图片要求',
  'role-library.fullBodyPhoto': '全身照：需要站姿单人照，能看清衣服，图片上无文字',
  'role-library.faceFeature': '面部特写：需要面部清晰无遮挡，正面照，图片上无文字',
  'role-library.rightDemonstrate': '正确示范',
  'role-library.wrongDemonstrate': '错误示范'
}
