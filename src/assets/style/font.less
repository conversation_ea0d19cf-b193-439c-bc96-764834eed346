/*
 * 统一字体加载策略 - 优雅、高性能、无重复
 * 采用渐进式字体加载，确保首屏性能和用户体验
 */

/* ===== 核心字体系统 ===== */

/* 主要字体 - Work Sans (关键字体，立即加载) */
/* 注意：实际加载通过 index.html 中的 preload 完成，这里只定义回退 */

/* 系统字体栈 - 确保在任何情况下都有良好的显示效果 */
:root {
  --font-primary: 'Work Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
  --font-decorative-oleo: 'Oleo Script Swash Caps', cursive, 'Comic Sans MS', fantasy;
  --font-decorative-pacifico: 'Pacifico', cursive, 'Brush Script MT', fantasy;
  --font-decorative-rammetto: 'Rammetto One', cursive, 'Impact', fantasy;
}

/* ===== 装饰性字体定义 ===== */
/* 使用 font-display: optional 确保不阻塞渲染 */

@font-face {
  font-family: 'Oleo Script Swash Caps';
  src: url('https://fonts.gstatic.com/s/oleoscriptswashcaps/v15/Noaj6Vb-w5SFbTTAsZP_7JkCS08K-jCzDn_HMQ.woff2')
    format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: optional;
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F,
    U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: 'Pacifico';
  src: url('https://fonts.gstatic.com/s/pacifico/v22/FwZY7-Qmy14u9lezJ-6H6MmBp0u-.woff2')
    format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: optional;
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F,
    U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: 'Rammetto One';
  src: url('https://fonts.gstatic.com/s/rammettoone/v15/LDI2apCSOBg7S-QT7pasQdqW9A.woff2')
    format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: optional;
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F,
    U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

/* ===== 字体应用类 ===== */

.font-primary {
  font-family: var(--font-primary);
}

.font-oleo-script {
  font-family: var(--font-decorative-oleo);
}

.font-pacifico {
  font-family: var(--font-decorative-pacifico);
}

.font-rammetto {
  font-family: var(--font-decorative-rammetto);
}

/* ===== 字体加载状态管理 ===== */

/* 字体加载中状态 - 避免布局抖动 */
.fonts-loading {
  /* 使用系统字体确保立即显示 */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 字体加载完成状态 */
.fonts-loaded {
  font-family: var(--font-primary);
}

/* ===== 性能优化 ===== */

/* 预加载提示 - 帮助浏览器优化字体加载 */
.font-preload-hint {
  font-family: var(--font-primary);
  visibility: hidden;
  position: absolute;
  left: -9999px;
  top: -9999px;
}
