/* 可访问性优化样式 */

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  :root {
    --text-primary: #000000;
    --bg-primary: #ffffff;
    --accent-color: #0066cc;
    --border-color: #000000;
  }

  body.dark-theme {
    --text-primary: #ffffff;
    --bg-primary: #000000;
    --accent-color: #66ccff;
    --border-color: #ffffff;
  }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* 焦点指示器 */
*:focus {
  outline: 2px solid var(--accent-color);
  outline-offset: 2px;
}

*:focus:not(:focus-visible) {
  outline: none;
}

*:focus-visible {
  outline: 2px solid var(--accent-color);
  outline-offset: 2px;
}

/* 跳转链接 */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--accent-color);
  color: var(--bg-primary);
  padding: 8px 16px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 10000;
  font-weight: 600;
  transition: top 0.3s ease;
}

.skip-link:focus {
  top: 6px;
}

/* 屏幕阅读器专用内容 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 可访问的按钮样式 */
button,
[role='button'] {
  cursor: pointer;
  border: none;
  background: transparent;
  font: inherit;
  color: inherit;
  padding: 0;
  margin: 0;
  outline: none;
}

button:disabled,
[role='button'][aria-disabled='true'] {
  cursor: not-allowed;
  opacity: 0.6;
}

/* 链接样式 */
a {
  color: var(--accent-color);
  text-decoration: underline;
  text-decoration-skip-ink: auto;
}

a:hover,
a:focus {
  text-decoration-thickness: 2px;
}

a:visited {
  color: var(--accent-hover);
}

/* 表单元素 */
input,
textarea,
select {
  font: inherit;
  color: inherit;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 8px 12px;
}

label {
  font-weight: 600;
  margin-bottom: 4px;
  display: block;
}

/* 错误状态 */
[aria-invalid='true'] {
  border-color: #dc3545;
}

.error-message {
  color: #dc3545;
  font-size: 14px;
  margin-top: 4px;
}

/* 加载状态 */
[aria-busy='true'] {
  cursor: wait;
  opacity: 0.7;
}

/* 隐藏状态 */
[aria-hidden='true'] {
  display: none !important;
}

/* 展开/折叠状态 */
[aria-expanded='false'] + * {
  display: none;
}

[aria-expanded='true'] + * {
  display: block;
}

/* 选中状态 */
[aria-selected='true'] {
  background-color: var(--accent-bg);
  color: var(--text-primary);
}

/* 菜单样式 */
[role='menu'],
[role='menubar'] {
  list-style: none;
  padding: 0;
  margin: 0;
}

[role='menuitem'] {
  cursor: pointer;
  padding: 8px 16px;
  display: flex;
  align-items: center;
}

[role='menuitem']:hover,
[role='menuitem']:focus {
  background-color: var(--bg-hover);
}

/* 对话框样式 */
[role='dialog'] {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  box-shadow: 0 4px 12px var(--shadow-color);
  z-index: 1000;
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
}

/* 模态背景 */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

/* 进度条 */
[role='progressbar'] {
  background: var(--bg-tertiary);
  border-radius: 4px;
  overflow: hidden;
  height: 8px;
}

[role='progressbar']::before {
  content: '';
  display: block;
  height: 100%;
  background: var(--accent-color);
  width: var(--progress, 0%);
  transition: width 0.3s ease;
}

/* 工具提示 */
[role='tooltip'] {
  position: absolute;
  background: var(--bg-secondary);
  color: var(--text-primary);
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  box-shadow: 0 2px 8px var(--shadow-color);
  z-index: 1001;
  max-width: 200px;
  word-wrap: break-word;
}

/* 通知 */
[role='alert'],
[role='status'] {
  padding: 12px 16px;
  border-radius: 4px;
  margin: 8px 0;
  border-left: 4px solid var(--accent-color);
  background: var(--accent-bg);
}

[role='alert'] {
  border-left-color: #dc3545;
  background: rgba(220, 53, 69, 0.1);
}

/* 标签页 */
[role='tablist'] {
  display: flex;
  border-bottom: 1px solid var(--border-color);
}

[role='tab'] {
  padding: 12px 16px;
  cursor: pointer;
  border: none;
  background: transparent;
  color: var(--text-secondary);
  border-bottom: 2px solid transparent;
}

[role='tab'][aria-selected='true'] {
  color: var(--text-primary);
  border-bottom-color: var(--accent-color);
}

[role='tabpanel'] {
  padding: 16px 0;
}

/* 响应式字体大小 */
@media (max-width: 768px) {
  html {
    font-size: 16px; /* 确保移动端最小字体大小 */
  }
}

/* 打印样式 */
@media print {
  * {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }

  .skip-link,
  [aria-hidden='true'],
  .sidebar,
  .user-actions {
    display: none !important;
  }

  .main-content {
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
  }
}
