@import './font.less';

* {
  box-sizing: border-box;
  // Prevent iOS overscroll behavior
  overscroll-behavior: none;
  -webkit-overflow-scrolling: touch;
}

#app {
  -webkit-overflow-scrolling: touch;
  overflow-x: hidden;
}

body {
  font-family: var(--font-primary);

  // 移动端安全区域处理
  @media screen and (max-width: 767px) {
    // 确保body背景色延伸到安全区域
    background: #180430;

    // 处理刘海屏状态栏区域
    &::before {
      content: '';
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      height: env(safe-area-inset-top);
      background: #180430;
      z-index: 9998;
      pointer-events: none;
    }
  }
}
