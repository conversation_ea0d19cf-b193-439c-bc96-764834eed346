/// <reference types="vite/client" />

declare module '*.vue' {
  import { DefineComponent } from 'vue'
  // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/ban-types
  const component: DefineComponent<{}, {}, any>
  export default component
}

interface ImportMetaEnv {
  // Sentry 配置
  readonly VITE_SENTRY_DSN: string
  readonly VITE_SENTRY_ENVIRONMENT: string
  readonly VITE_SENTRY_ORG: string
  readonly VITE_SENTRY_PROJECT: string
  readonly VITE_SENTRY_AUTH_TOKEN: string
  readonly VITE_SENTRY_UPLOAD_SOURCEMAPS: string

  // 现有的环境变量
  readonly VITE_API_HOST: string
  readonly VITE_APP_NAME: string
  readonly VITE_WEBSITE_TITLE: string
  readonly VITE_OPENREPLAY_PROJECT_KEY: string
  readonly VITE_OPENREPLAY_INGEST_POINT: string
  readonly VITE_VOLC_APP_ID: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}
