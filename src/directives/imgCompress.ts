import type { Directive, DirectiveBinding } from 'vue'

interface ImgCompressOptions {
  format?: 'heic' | 'webp'
  quality?: number
}

const defaultOptions: ImgCompressOptions = {
  format: 'webp',
  quality: 75
}

export const imgCompress: Directive = {
  beforeMount(el: HTMLImageElement, binding: DirectiveBinding) {
    const options = { ...defaultOptions, ...(binding.value || {}) }

    // 添加一个标记来防止重复处理
    const processedFlag = 'data-img-processed'

    // 添加性能优化属性
    el.loading = 'lazy'
    el.decoding = 'async'

    // 添加错误处理
    el.onerror = () => {
      // 如果图片加载失败，尝试加载原始URL（不压缩）
      const originalSrc = el.getAttribute('data-original-src')
      if (originalSrc && el.src !== originalSrc) {
        el.src = originalSrc
      }
    }

    const processUrl = (url: string) => {
      if (!url) return url

      // 保存原始URL
      el.setAttribute('data-original-src', url)

      // 如果URL已经被处理过（包含相同格式的处理参数），直接返回
      const formatPattern = new RegExp(`x-tos-process=image/format,${options.format}`)
      if (formatPattern.test(url)) return url

      // 检查是否是支持的图片格式
      const isSupported = /\.(jpg|jpeg|png|gif|bmp)(\?.*)?$/i.test(url)
      if (!isSupported) return url

      // 如果已经有其他 x-tos-process 参数，替换格式
      if (url.includes('x-tos-process=')) {
        return url.replace(/format,[^&/]+/, `format,${options.format}`)
      }

      const separator = url.includes('?') ? '&' : '?'
      return `${url}${separator}x-tos-process=image/format,${options.format}`
    }

    // 处理图片 URL
    const handleImgSrc = () => {
      const currentSrc = el.getAttribute('src')
      if (!currentSrc) return

      // 如果当前URL已经被处理过，不再重复处理
      const processedUrl = el.getAttribute(processedFlag)
      if (processedUrl === currentSrc) return

      const newUrl = processUrl(currentSrc)
      if (newUrl !== currentSrc) {
        el.setAttribute('src', newUrl)
      }
      el.setAttribute(processedFlag, newUrl)
    }

    // 监听 src 属性变化
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === 'src') {
          handleImgSrc()
        }
      })
    })

    observer.observe(el, {
      attributes: true,
      attributeFilter: ['src']
    })

    // 处理初始 src
    handleImgSrc()
  },

  unmounted(el: HTMLImageElement) {
    // 清理 data 属性
    el.removeAttribute('data-img-processed')
  }
}

export default imgCompress
