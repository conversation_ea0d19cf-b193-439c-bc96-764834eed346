import { ref, computed, onMounted, onUnmounted } from 'vue'

/**
 * 懒加载 Composable
 * 使用 Intersection Observer API 实现图片和组件的懒加载
 */
export function useLazyLoad(options: IntersectionObserverInit = {}) {
  const isVisible = ref(false)
  const targetRef = ref<HTMLElement>()
  let observer: IntersectionObserver | null = null

  const defaultOptions: IntersectionObserverInit = {
    root: null,
    rootMargin: '50px', // 提前50px开始加载
    threshold: 0.1, // 10%可见时触发
    ...options
  }

  onMounted(() => {
    if (!targetRef.value) return

    observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          isVisible.value = true
          // 一旦可见就停止观察
          observer?.unobserve(entry.target)
        }
      })
    }, defaultOptions)

    observer.observe(targetRef.value)
  })

  onUnmounted(() => {
    if (observer) {
      observer.disconnect()
    }
  })

  return {
    isVisible,
    targetRef
  }
}

/**
 * 批量懒加载 Composable
 * 用于处理大量元素的懒加载
 */
export function useBatchLazyLoad(batchSize: number = 10) {
  const visibleItems = ref<Set<number>>(new Set())
  const loadedBatches = ref<Set<number>>(new Set())

  const isItemVisible = (index: number): boolean => {
    const batchIndex = Math.floor(index / batchSize)
    return loadedBatches.value.has(batchIndex)
  }

  const loadBatch = (batchIndex: number) => {
    if (loadedBatches.value.has(batchIndex)) return

    loadedBatches.value.add(batchIndex)

    // 标记该批次的所有项目为可见
    for (let i = batchIndex * batchSize; i < (batchIndex + 1) * batchSize; i++) {
      visibleItems.value.add(i)
    }
  }

  const observeElement = (element: HTMLElement, index: number) => {
    const batchIndex = Math.floor(index / batchSize)

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            loadBatch(batchIndex)
            // 预加载下一批
            loadBatch(batchIndex + 1)
            observer.unobserve(entry.target)
          }
        })
      },
      {
        rootMargin: '100px',
        threshold: 0.1
      }
    )

    observer.observe(element)
  }

  return {
    visibleItems,
    isItemVisible,
    observeElement,
    loadBatch
  }
}

/**
 * 虚拟滚动 Composable
 * 简化版虚拟滚动实现
 */
export function useVirtualScroll<T>(
  items: T[],
  itemHeight: number,
  containerHeight: number,
  overscan: number = 3
) {
  const scrollTop = ref(0)
  const containerRef = ref<HTMLElement>()

  const visibleRange = computed(() => {
    const start = Math.floor(scrollTop.value / itemHeight)
    const end = Math.min(start + Math.ceil(containerHeight / itemHeight) + overscan, items.length)

    return {
      start: Math.max(0, start - overscan),
      end
    }
  })

  const visibleItems = computed(() => {
    const { start, end } = visibleRange.value
    return items.slice(start, end).map((item, index) => ({
      item,
      index: start + index,
      top: (start + index) * itemHeight
    }))
  })

  const totalHeight = computed(() => items.length * itemHeight)

  const handleScroll = (event: Event) => {
    const target = event.target as HTMLElement
    scrollTop.value = target.scrollTop
  }

  return {
    containerRef,
    visibleItems,
    totalHeight,
    handleScroll,
    scrollTop
  }
}

/**
 * 图片懒加载 Composable
 */
export function useImageLazyLoad() {
  const loadedImages = ref<Set<string>>(new Set())

  const loadImage = (src: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (loadedImages.value.has(src)) {
        resolve()
        return
      }

      const img = new Image()
      img.onload = () => {
        loadedImages.value.add(src)
        resolve()
      }
      img.onerror = reject
      img.src = src
    })
  }

  const isImageLoaded = (src: string): boolean => {
    return loadedImages.value.has(src)
  }

  return {
    loadImage,
    isImageLoaded,
    loadedImages
  }
}
