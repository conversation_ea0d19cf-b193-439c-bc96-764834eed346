import { ref } from 'vue'

export interface CacheConfig {
  maxSize?: number
  ttl?: number
  persistKey?: string
}

export interface CacheItem {
  value: string
  timestamp: number
  expiresAt: number
}

export function useCache(config: CacheConfig = {}) {
  const cache = ref<Map<string, CacheItem>>(new Map())
  const maxSize = config.maxSize || 50
  const ttl = config.ttl || 30 * 60 * 1000 // 默认30分钟
  const persistKey = config.persistKey

  const persistCache = () => {
    if (persistKey) {
      try {
        localStorage.setItem(persistKey, JSON.stringify(Array.from(cache.value.entries())))
      } catch (error) {
        console.error('Failed to persist cache:', error)
        localStorage.removeItem(persistKey)
      }
    }
  }

  const cleanExpiredItems = () => {
    const now = Date.now()
    for (const [key, item] of cache.value.entries()) {
      if (now > item.expiresAt) {
        cache.value.delete(key)
      }
    }
    persistCache()
  }

  // 初始化时从localStorage加载缓存
  if (persistKey) {
    const savedCache = localStorage.getItem(persistKey)
    if (savedCache) {
      try {
        const parsed = JSON.parse(savedCache)
        cache.value = new Map(parsed)
        cleanExpiredItems()
      } catch (error) {
        console.error('Failed to load cache from localStorage:', error)
        localStorage.removeItem(persistKey)
      }
    }
  }

  const set = (key: string, value: string) => {
    cleanExpiredItems()

    // 如果达到最大大小，删除最早的项
    if (cache.value.size >= maxSize) {
      const oldestKey = cache.value.keys().next().value
      cache.value.delete(oldestKey)
    }

    const cacheItem: CacheItem = {
      value,
      timestamp: Date.now(),
      expiresAt: Date.now() + ttl
    }

    cache.value.set(key, cacheItem)
    persistCache()
  }

  const get = (key: string): string | null => {
    cleanExpiredItems()

    const item = cache.value.get(key)
    if (!item) return null

    // 检查是否过期
    if (Date.now() > item.expiresAt) {
      cache.value.delete(key)
      persistCache()
      return null
    }

    return item.value
  }

  const remove = (key: string) => {
    cache.value.delete(key)
    persistCache()
  }

  const clear = () => {
    cache.value.clear()
    persistCache()
  }

  return {
    cache,
    set,
    get,
    remove,
    clear,
    cleanExpiredItems
  }
}
