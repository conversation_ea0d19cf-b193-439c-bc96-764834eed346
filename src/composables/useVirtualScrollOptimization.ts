import { ref, computed, onMounted, onUnmounted } from 'vue'

/**
 * 虚拟滚动性能优化 Composable
 * 提供滚动性能监控和优化建议
 */
export function useVirtualScrollOptimization() {
  const scrollPerformance = ref({
    fps: 0,
    frameTime: 0,
    isSmooth: true,
    lastFrameTime: 0,
    frameCount: 0
  })

  let animationFrameId: number

  // 监控滚动性能
  const monitorScrollPerformance = () => {
    const currentTime = performance.now()
    const deltaTime = currentTime - scrollPerformance.value.lastFrameTime

    if (scrollPerformance.value.lastFrameTime > 0) {
      scrollPerformance.value.frameTime = deltaTime
      scrollPerformance.value.fps = 1000 / deltaTime
      scrollPerformance.value.frameCount++

      // 判断是否流畅（FPS > 50 为流畅）
      scrollPerformance.value.isSmooth = scrollPerformance.value.fps > 50
    }

    scrollPerformance.value.lastFrameTime = currentTime
    animationFrameId = requestAnimationFrame(monitorScrollPerformance)
  }

  // 获取性能建议
  const getPerformanceAdvice = computed(() => {
    const advice: string[] = []

    if (scrollPerformance.value.fps < 30) {
      advice.push('滚动帧率过低，建议减少 overscan 值')
    }

    if (scrollPerformance.value.fps < 45) {
      advice.push('建议禁用复杂的 hover 动画效果')
    }

    if (scrollPerformance.value.frameTime > 33) {
      advice.push('单帧渲染时间过长，建议优化组件渲染逻辑')
    }

    return advice
  })

  // 动态调整 overscan 值
  const getOptimalOverscan = computed(() => {
    if (scrollPerformance.value.fps > 55) {
      return 6 // 性能良好，可以增加预渲染
    } else if (scrollPerformance.value.fps > 45) {
      return 5 // 性能一般，适中预渲染
    } else {
      return 2 // 性能较差，减少预渲染
    }
  })

  // 检测设备性能等级
  const getDevicePerformanceLevel = (): 'low' | 'medium' | 'high' => {
    try {
      const canvas = document.createElement('canvas')
      const gl =
        canvas.getContext('webgl') ||
        (canvas.getContext('experimental-webgl') as WebGLRenderingContext | null)

      if (!gl) return 'low'

      const renderer = gl.getParameter(gl.RENDERER) as string

      // 简单的设备性能判断
      if (renderer.includes('Apple') || renderer.includes('NVIDIA') || renderer.includes('AMD')) {
        return 'high'
      } else if (renderer.includes('Intel')) {
        return 'medium'
      } else {
        return 'low'
      }
    } catch (error) {
      // 如果获取 WebGL 信息失败，返回低性能
      console.warn('Failed to detect device performance level:', error)
      return 'low'
    }
  }

  // 获取推荐的虚拟滚动配置
  const getRecommendedConfig = () => {
    const deviceLevel = getDevicePerformanceLevel()
    const screenWidth = window.innerWidth

    const config = {
      overscan: 5,
      itemHeight: 400,
      enableHover: true,
      enableAnimations: true,
      imageQuality: 'high' as const
    }

    // 根据设备性能调整
    if (deviceLevel === 'low') {
      return {
        ...config,
        overscan: 2,
        enableHover: false,
        enableAnimations: false,
        imageQuality: 'medium' as const
      }
    } else if (deviceLevel === 'medium') {
      return {
        ...config,
        overscan: 4,
        enableHover: true,
        enableAnimations: false,
        imageQuality: 'high' as const
      }
    }

    // 根据屏幕尺寸调整
    if (screenWidth < 1200) {
      return {
        ...config,
        overscan: Math.max(2, config.overscan - 2)
      }
    }

    return config
  }

  onMounted(() => {
    // 延迟启动性能监控，避免影响初始渲染
    setTimeout(() => {
      monitorScrollPerformance()
    }, 1000)
  })

  onUnmounted(() => {
    if (animationFrameId) {
      cancelAnimationFrame(animationFrameId)
    }
  })

  return {
    scrollPerformance,
    getPerformanceAdvice,
    getOptimalOverscan,
    getDevicePerformanceLevel,
    getRecommendedConfig
  }
}

/**
 * 滚动节流优化
 */
export function useScrollThrottle(callback: (...args: any[]) => void, delay: number = 16) {
  let lastTime = 0
  let animationFrameId: number

  const throttledCallback = (...args: any[]) => {
    const currentTime = performance.now()

    if (currentTime - lastTime >= delay) {
      lastTime = currentTime
      callback(...args)
    } else {
      // 使用 requestAnimationFrame 确保在下一帧执行
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId)
      }
      animationFrameId = requestAnimationFrame(() => {
        callback(...args)
      })
    }
  }

  const cleanup = () => {
    if (animationFrameId) {
      cancelAnimationFrame(animationFrameId)
    }
  }

  return {
    throttledCallback,
    cleanup
  }
}

/**
 * 虚拟滚动容器高度优化
 */
export function useOptimalContainerHeight() {
  const getOptimalHeight = () => {
    const viewportHeight = window.innerHeight
    const headerHeight = 80
    const filterHeight = 120
    const footerHeight = 60
    const safetyMargin = 40

    // 计算最优容器高度
    const optimalHeight = viewportHeight - headerHeight - filterHeight - footerHeight - safetyMargin

    // 确保最小高度
    return Math.max(optimalHeight, 400)
  }

  const getOptimalItemHeight = () => {
    const screenWidth = window.innerWidth

    // 根据屏幕宽度和设备性能动态调整
    if (screenWidth >= 1800) {
      return 460 // 减少高度，提升性能
    } else if (screenWidth >= 1600) {
      return 440
    } else if (screenWidth >= 1400) {
      return 420
    } else if (screenWidth >= 1200) {
      return 400
    } else {
      return 380
    }
  }

  return {
    getOptimalHeight,
    getOptimalItemHeight
  }
}
