/**
 * 场景事件监听组合式函数
 * 用于在组件中监听和处理场景变化事件
 */

import { onMounted, onUnmounted } from 'vue'
import { SceneType, SceneActionType, type SceneCustomEventDetail } from '@/types/chat4-scene'

/**
 * 场景事件处理器类型
 */
export type SceneEventHandler = (detail: SceneCustomEventDetail) => void

/**
 * 场景事件监听器配置
 */
export interface SceneEventListenerConfig {
  /** 好友请求事件处理器 */
  onFriendRequest?: SceneEventHandler
  /** 聊天场景变化处理器 */
  onChatSceneChange?: SceneEventHandler
  /** 默认场景变化处理器 */
  onDefaultSceneChange?: SceneEventHandler
  /** 通用场景事件处理器 */
  onSceneEvent?: (eventName: string, detail: SceneCustomEventDetail) => void
}

/**
 * 使用场景事件监听器
 */
export function useSceneEvents(config: SceneEventListenerConfig = {}) {
  // 事件监听器引用
  const eventListeners = new Map<string, EventListener>()

  /**
   * 添加事件监听器
   */
  const addEventListeners = () => {
    // 好友请求事件监听器
    if (config.onFriendRequest) {
      const friendRequestListener = (event: Event) => {
        const customEvent = event as CustomEvent<SceneCustomEventDetail>
        if (customEvent.detail?.action === SceneActionType.SHOW_FRIEND_REQUEST) {
          config.onFriendRequest!(customEvent.detail)
        }
        // 调用通用处理器
        config.onSceneEvent?.('showFriendRequest', customEvent.detail)
      }
      window.addEventListener('showFriendRequest', friendRequestListener)
      eventListeners.set('showFriendRequest', friendRequestListener)
    }

    // 聊天场景变化事件监听器
    if (config.onChatSceneChange) {
      const chatSceneListener = (event: Event) => {
        const customEvent = event as CustomEvent<SceneCustomEventDetail>
        if (customEvent.detail?.action === SceneActionType.CHAT_SCENE_CHANGE) {
          config.onChatSceneChange!(customEvent.detail)
        }
        // 调用通用处理器
        config.onSceneEvent?.('chatSceneAction', customEvent.detail)
      }
      window.addEventListener('chatSceneAction', chatSceneListener)
      eventListeners.set('chatSceneAction', chatSceneListener)
    }

    // 默认场景变化事件监听器
    if (config.onDefaultSceneChange) {
      const defaultSceneListener = (event: Event) => {
        const customEvent = event as CustomEvent<SceneCustomEventDetail>
        if (customEvent.detail?.action === SceneActionType.DEFAULT_SCENE_CHANGE) {
          config.onDefaultSceneChange!(customEvent.detail)
        }
        // 调用通用处理器
        config.onSceneEvent?.('defaultSceneAction', customEvent.detail)
      }
      window.addEventListener('defaultSceneAction', defaultSceneListener)
      eventListeners.set('defaultSceneAction', defaultSceneListener)
    }
  }

  /**
   * 移除事件监听器
   */
  const removeEventListeners = () => {
    eventListeners.forEach((listener, eventName) => {
      window.removeEventListener(eventName, listener)
    })
    eventListeners.clear()
  }

  /**
   * 手动触发场景事件（用于测试）
   */
  const triggerSceneEvent = (eventName: string, detail: SceneCustomEventDetail) => {
    const event = new CustomEvent(eventName, { detail })
    window.dispatchEvent(event)
  }

  // 组件挂载时添加监听器
  onMounted(() => {
    addEventListeners()
  })

  // 组件卸载时移除监听器
  onUnmounted(() => {
    removeEventListeners()
  })

  return {
    addEventListeners,
    removeEventListeners,
    triggerSceneEvent
  }
}

/**
 * 预定义的场景事件处理器
 */
export const SceneEventHandlers = {
  /**
   * 好友请求处理器示例
   */
  friendRequest: (detail: SceneCustomEventDetail) => {
    console.log('Friend request received:', detail)

    // 这里可以添加具体的好友请求处理逻辑
    // 例如：显示弹窗、更新UI状态等

    // 示例：显示好友请求弹窗
    if (detail.params?.sceneType === SceneType.LIVING) {
      // 在Living场景中显示好友请求弹窗
      console.log('Showing friend request popup in Living scene')
      // 实际的弹窗显示逻辑
    }
  },

  /**
   * 聊天场景变化处理器示例
   */
  chatSceneChange: (detail: SceneCustomEventDetail) => {
    console.log('Chat scene changed:', detail)

    // 这里可以添加聊天场景变化的处理逻辑
    // 例如：更新聊天界面、加载聊天历史等
  },

  /**
   * 默认场景变化处理器示例
   */
  defaultSceneChange: (detail: SceneCustomEventDetail) => {
    console.log('Default scene changed:', detail)

    // 这里可以添加默认场景变化的处理逻辑
  }
}

/**
 * 快速设置场景事件监听器
 */
export function useQuickSceneEvents() {
  return useSceneEvents({
    onFriendRequest: SceneEventHandlers.friendRequest,
    onChatSceneChange: SceneEventHandlers.chatSceneChange,
    onDefaultSceneChange: SceneEventHandlers.defaultSceneChange,
    onSceneEvent: (eventName, detail) => {
      console.log(`Scene event triggered: ${eventName}`, detail)
    }
  })
}
