import { computed } from 'vue'
import { useEditorStore } from '@/store/editor'
import type { Scene } from '@/types/editor'

export function useChapterOptions(excludeCurrentScene = true) {
  const editorStore = useEditorStore()
  const currentSceneId = computed(() => editorStore.currentSceneId)

  const chapterOptions = computed(() => {
    // 获取所有场景，排除特殊场景
    return editorStore.gameConfig.scenes
      .filter(
        (scene) =>
          scene.id !== '_BEGIN_' &&
          scene.id !== '_END_' &&
          scene.id !== '~' &&
          (!excludeCurrentScene || scene.id !== currentSceneId.value)
      )
      .map((scene) => ({
        label: scene.name || '未命名章节',
        value: scene.id
      }))
  })

  return {
    chapterOptions
  }
}
