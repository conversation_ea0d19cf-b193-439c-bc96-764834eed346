/**
 * 首屏加载状态管理 Composable
 * 专门优化首屏加载体验
 */

import { ref, computed } from 'vue'

interface FirstScreenState {
  isLoading: boolean
  loadingProgress: number
  criticalResourcesLoaded: boolean
  firstPaintTime: number | null
  contentfulPaintTime: number | null
  error: string | null
}

const state = ref<FirstScreenState>({
  isLoading: true,
  loadingProgress: 0,
  criticalResourcesLoaded: false,
  firstPaintTime: null,
  contentfulPaintTime: null,
  error: null
})

// 关键资源列表
const criticalResources = [
  'theme.less',
  'global.less',
  'work-sans-latin-400-normal.woff2',
  'logo-v2.png'
]

let loadedResources = 0

export function useFirstScreenLoading() {
  /**
   * 更新加载进度（防倒退）
   */
  const updateProgress = (progress: number) => {
    const newProgress = Math.min(100, Math.max(0, progress))

    // 只有当新进度大于当前进度时才更新，防止倒退
    if (newProgress > state.value.loadingProgress) {
      state.value.loadingProgress = newProgress

      // 同步更新 HTML 进度条
      if (typeof window !== 'undefined' && (window as any).updateLoadingProgress) {
        // @ts-ignore
        window.updateLoadingProgress(newProgress)
      }
    } else if (newProgress < state.value.loadingProgress) {
      console.warn(`⚠️ 首屏加载进度倒退被阻止: ${newProgress}% -> ${state.value.loadingProgress}%`)
    }
  }

  /**
   * 标记资源已加载
   */
  const markResourceLoaded = (resourceName: string) => {
    if (criticalResources.some((resource) => resourceName.includes(resource))) {
      loadedResources++
      const progress = (loadedResources / criticalResources.length) * 80 // 80% 为资源加载
      updateProgress(progress)

      console.log(
        `✅ 关键资源加载完成: ${resourceName} (${loadedResources}/${criticalResources.length})`
      )

      // 更宽松的条件：加载了大部分关键资源就认为完成
      const requiredResources = Math.ceil(criticalResources.length * 0.75) // 75% 的资源加载完成即可
      if (loadedResources >= requiredResources) {
        state.value.criticalResourcesLoaded = true
        updateProgress(90) // 关键资源加载完成
        console.log(
          `🎯 关键资源加载达到阈值: ${loadedResources}/${criticalResources.length} (需要${requiredResources})`
        )
        checkFirstScreenComplete()
      }
    }
  }

  /**
   * 监控性能指标（使用统一的性能监控）
   */
  const monitorPerformance = () => {
    // 使用轮询方式检查 FCP，避免重复的 PerformanceObserver
    const checkFCP = () => {
      if (state.value.contentfulPaintTime === null) {
        const fcpEntries = performance.getEntriesByName('first-contentful-paint')
        if (fcpEntries.length > 0) {
          state.value.contentfulPaintTime = fcpEntries[0].startTime
          updateProgress(70) // 首次内容绘制完成
          console.log(`🎨 FCP: ${fcpEntries[0].startTime.toFixed(2)}ms`)
          checkFirstScreenComplete()
        }
      }

      const fpEntries = performance.getEntriesByName('first-paint')
      if (fpEntries.length > 0 && state.value.firstPaintTime === null) {
        state.value.firstPaintTime = fpEntries[0].startTime
        updateProgress(60) // 首次绘制完成
      }
    }

    // 立即检查一次
    checkFCP()

    // 定期检查直到获取到 FCP
    const fcpInterval = setInterval(() => {
      checkFCP()
      if (state.value.contentfulPaintTime !== null) {
        clearInterval(fcpInterval)
      }
    }, 100)

    // 最多检查5秒
    setTimeout(() => clearInterval(fcpInterval), 5000)
  }

  /**
   * 监听资源加载
   */
  const monitorResourceLoading = () => {
    // 监听字体加载 - 委托给统一字体管理器
    if ('fonts' in document) {
      document.fonts.ready.then(() => {
        markResourceLoaded('unified-font-manager')
        console.log('✅ 统一字体管理器处理完成')
      })
    }

    // 监听图片加载
    const images = document.querySelectorAll('img')
    images.forEach((img) => {
      if (img.complete) {
        markResourceLoaded(img.src)
      } else {
        img.addEventListener('load', () => markResourceLoaded(img.src))
        img.addEventListener('error', () => {
          console.warn(`❌ 图片加载失败: ${img.src}`)
        })
      }
    })

    // 监听样式表加载
    const stylesheets = document.querySelectorAll(
      'link[rel="stylesheet"]'
    ) as NodeListOf<HTMLLinkElement>
    stylesheets.forEach((link) => {
      if (link.sheet) {
        markResourceLoaded(link.href)
      } else {
        link.addEventListener('load', () => markResourceLoaded(link.href))
        link.addEventListener('error', () => {
          console.warn(`❌ 样式表加载失败: ${link.href}`)
        })
      }
    })
  }

  /**
   * 完成首屏加载
   */
  const completeFirstScreenLoading = () => {
    state.value.isLoading = false
    state.value.loadingProgress = 100

    // 标记首屏完成时间
    const completionTime = performance.now()
    console.log(`🎯 首屏完成时间: ${completionTime.toFixed(2)}ms`)

    // 发送延迟加载完成事件给Sentry
    window.dispatchEvent(
      new CustomEvent('delayed-loading-complete', {
        detail: { completionTime }
      })
    )

    // 发送精确的性能指标到Sentry
    if (typeof window !== 'undefined') {
      // 动态导入Sentry函数以避免循环依赖
      import('@/utils/sentry')
        .then(({ reportDelayedLoadingMetrics }) => {
          reportDelayedLoadingMetrics(completionTime)
        })
        .catch(console.warn)
    }

    // 添加加载完成的视觉反馈
    const appElement = document.getElementById('app')
    if (appElement) {
      appElement.classList.add('first-screen-loaded')
    }

    // 为所有首屏元素添加 loaded 类
    const firstScreenElements = document.querySelectorAll('.stories-page, .pc-layout')
    firstScreenElements.forEach((element) => {
      element.classList.add('loaded')
    })

    // 使用丝滑加载器完成加载
    if (typeof window !== 'undefined' && (window as any).__smoothLoading) {
      // @ts-ignore
      window.__smoothLoading.complete()
    } else {
      // 备用方案：直接隐藏加载指示器
      const loadingIndicator = document.getElementById('loading-indicator')
      if (loadingIndicator) {
        loadingIndicator.style.opacity = '0'
        setTimeout(() => {
          loadingIndicator.style.display = 'none'
        }, 500)
      }
    }

    // 触发性能优化器的渐进式加载
    if (typeof window !== 'undefined' && (window as any).firstScreenPerformanceOptimizer) {
      // @ts-ignore

      window.firstScreenPerformanceOptimizer.enableProgressiveLoading()
    }

    // 输出性能建议（仅开发环境）
    // if (import.meta.env.DEV) {
    //   setTimeout(() => {
    //     if (typeof window !== 'undefined' && (window as any).firstScreenPerformanceOptimizer) {
    //       // @ts-ignore
    //       const recommendations = (
    //         window as any
    //       ).firstScreenPerformanceOptimizer.getPerformanceRecommendations()
    //       if (recommendations.length > 0) {
    //         console.log('💡 性能优化建议:', recommendations)
    //       }
    //     }
    //   }, 1000)
    // }

    console.log('🎉 首屏加载完成')
  }

  /**
   * 智能判断首屏加载完成
   */
  const checkFirstScreenComplete = () => {
    // 检查关键条件
    const conditions = [
      state.value.criticalResourcesLoaded,
      state.value.contentfulPaintTime !== null,
      document.readyState === 'interactive' || document.readyState === 'complete'
    ]

    // 更宽松的条件：如果页面已经可见且有内容绘制，即使资源没完全加载也可以完成
    const relaxedConditions = [
      state.value.contentfulPaintTime !== null,
      document.readyState === 'interactive' || document.readyState === 'complete',
      performance.now() > 2000 // 至少等待2秒
    ]

    // 添加调试信息
    // if (import.meta.env.DEV) {
    //   console.log('🔍 首屏完成条件检查:', {
    //     criticalResourcesLoaded: state.value.criticalResourcesLoaded,
    //     contentfulPaintTime: state.value.contentfulPaintTime,
    //     documentReady: document.readyState,
    //     allConditionsMet: conditions.every(Boolean),
    //     relaxedConditionsMet: relaxedConditions.every(Boolean),
    //     currentProgress: state.value.loadingProgress,
    //     loadedResourcesCount: loadedResources,
    //     totalCriticalResources: criticalResources.length,
    //     elapsedTime: performance.now()
    //   })
    // }

    // 优先使用严格条件，如果不满足则使用宽松条件
    if (conditions.every(Boolean) || relaxedConditions.every(Boolean)) {
      // 延迟一点时间确保渲染完成
      setTimeout(completeFirstScreenLoading, 200)
    }
  }

  /**
   * 设置错误状态
   */
  const setError = (error: string) => {
    state.value.error = error
    console.error('❌ 首屏加载错误:', error)
  }

  /**
   * 初始化首屏加载监控
   */
  const initializeFirstScreenLoading = () => {
    console.log('🚀 初始化首屏加载监控')

    // 立即更新进度
    updateProgress(10) // 开始加载

    // 启动监控
    monitorPerformance()
    monitorResourceLoading()

    // 定期检查完成状态
    const checkInterval = setInterval(() => {
      checkFirstScreenComplete()

      if (!state.value.isLoading) {
        clearInterval(checkInterval)
      }
    }, 100)

    // 超时保护 - 最多等待 5 秒（减少超时时间）
    setTimeout(() => {
      if (state.value.isLoading) {
        console.warn('⚠️ 首屏加载超时，强制完成', {
          currentProgress: state.value.loadingProgress,
          criticalResourcesLoaded: state.value.criticalResourcesLoaded,
          contentfulPaintTime: state.value.contentfulPaintTime,
          documentReady: document.readyState,
          loadedResourcesCount: loadedResources,
          totalCriticalResources: criticalResources.length
        })
        completeFirstScreenLoading()
      }
      clearInterval(checkInterval)
    }, 5000)

    // DOM 加载完成时更新进度
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        updateProgress(50)
        checkFirstScreenComplete()
      })
    } else {
      updateProgress(50)
      checkFirstScreenComplete()
    }

    // 页面完全加载时确保完成
    window.addEventListener('load', () => {
      updateProgress(95)
      setTimeout(checkFirstScreenComplete, 100)
    })
  }

  /**
   * 清理资源
   */
  const cleanup = () => {
    // 清理已经不需要了，因为我们不再使用 PerformanceObserver
    console.log('🧹 首屏加载监控已清理')
  }

  // 计算属性
  const isFirstScreenLoaded = computed(() => !state.value.isLoading)
  const loadingPercentage = computed(() => `${state.value.loadingProgress}%`)
  const hasError = computed(() => !!state.value.error)

  return {
    // 状态
    isLoading: computed(() => state.value.isLoading),
    loadingProgress: computed(() => state.value.loadingProgress),
    criticalResourcesLoaded: computed(() => state.value.criticalResourcesLoaded),
    firstPaintTime: computed(() => state.value.firstPaintTime),
    contentfulPaintTime: computed(() => state.value.contentfulPaintTime),
    error: computed(() => state.value.error),

    // 计算属性
    isFirstScreenLoaded,
    loadingPercentage,
    hasError,

    // 方法
    initializeFirstScreenLoading,
    completeFirstScreenLoading,
    markResourceLoaded,
    updateProgress,
    setError,
    cleanup
  }
}

// 全局首屏加载状态
export const globalFirstScreenLoading = useFirstScreenLoading()

// 自动初始化
if (typeof window !== 'undefined') {
  globalFirstScreenLoading.initializeFirstScreenLoading()
}
