import { ref, watch } from 'vue'
import { useStoryStore, useUserStore } from '@/store'
import { useRoute, useRouter } from 'vue-router'

/**
 * 标签筛选的Composable函数
 * 处理URL参数与标签筛选的逻辑
 */
export function useTagsFilter() {
  const storyStore = useStoryStore()
  const userStore = useUserStore()
  const route = useRoute()
  const router = useRouter()

  // 加载状态标志 - 初始设为false，避免阻塞页面显示
  const isLoading = ref(false)

  // 筛选状态
  const selectedPopular = ref('popular')
  const selectedGender = ref('')
  const selectedTags = ref<string[]>([])

  // 获取标签名称的辅助函数
  const getTagNameById = (tagId: string): string | null => {
    for (const category of storyStore.storyCategories) {
      if (category?.name?.toUpperCase() === 'HOBBY' && category.subcategories) {
        const tag = category.subcategories.find((subcategory) => subcategory.id === tagId)
        if (tag) {
          return tag.name
        }
      }
    }
    return null
  }

  // 获取性别名称的辅助函数
  const getGenderNameById = (genderId: string): string | null => {
    for (const category of storyStore.storyCategories) {
      if (category?.name?.toUpperCase() === 'GENDER' && category.subcategories) {
        const gender = category.subcategories.find((subcategory) => subcategory.id === genderId)
        if (gender) {
          return gender.name
        }
      }
    }
    return null
  }

  // 更新URL参数的函数
  const updateUrlParams = () => {
    const params = new URLSearchParams(route.query as Record<string, string>)

    // 更新排序参数
    if (selectedPopular.value) {
      params.set('sort', selectedPopular.value)
    } else {
      params.delete('sort')
    }

    // 更新性别参数 - 使用性别名称而不是ID
    if (selectedGender.value) {
      const genderName = getGenderNameById(selectedGender.value)
      if (genderName) {
        params.set('gender', genderName)
      } else {
        params.delete('gender')
      }
    } else {
      params.delete('gender')
    }

    // 更新标签参数 - 使用标签名称而不是ID
    if (selectedTags.value.length > 0) {
      // 将标签ID转换为标签名称
      const tagNames = selectedTags.value
        .map((tagId) => getTagNameById(tagId))
        .filter((name) => name !== null) as string[]

      if (tagNames.length > 0) {
        params.set('tags', tagNames.join(','))
      } else {
        params.delete('tags')
      }
    } else {
      params.delete('tags')
    }

    // 更新URL，不刷新页面
    router.replace({ query: Object.fromEntries(params) })
  }

  // 从URL参数初始化筛选条件
  const initFromUrlParams = async () => {
    // 检查用户是否已认证，如果未认证则不加载数据
    if (!userStore.isAuthenticated) {
      console.warn('User not authenticated, skipping data loading')
      isLoading.value = false
      return
    }

    // 设置加载状态 - 确保在初始化期间始终显示加载状态
    isLoading.value = true

    try {
      // 优化：如果类别数据已存在，跳过重复请求
      if (storyStore.storyCategories.length === 0) {
        await storyStore.getStoryCategories()
      }

      const query = route.query

      // 检查是否有URL参数，如果没有任何筛选参数，则不更新URL
      const hasUrlParams = query.sort || query.gender || query.tags

      // 获取排序方式参数
      const sortParam = query.sort as string
      if (sortParam && ['popular', 'newest'].includes(sortParam)) {
        selectedPopular.value = sortParam
      }

      // 获取性别参数
      const genderParam = query.gender as string
      if (genderParam) {
        // 尝试通过性别名称查找对应的ID
        const genderCategories = storyStore.storyCategories
          .filter((category) => category?.name?.toUpperCase() === 'GENDER')
          .flatMap((category) => category.subcategories || [])

        // 先尝试直接匹配ID
        const genderById = genderCategories.find((subcategory) => subcategory.id === genderParam)
        if (genderById) {
          selectedGender.value = genderById.id
        } else {
          // 如果不是ID，尝试通过名称匹配（不区分大小写）
          const genderByName = genderCategories.find(
            (subcategory) => subcategory.name.toLowerCase() === genderParam.toLowerCase()
          )
          if (genderByName) {
            selectedGender.value = genderByName.id
          }
        }
      }

      // 获取标签参数
      const tagsParam = query.tags as string
      if (tagsParam) {
        try {
          // 支持两种格式：逗号分隔的标签名称 或 单个标签名称
          const tagNames = tagsParam.includes(',') ? tagsParam.split(',') : [tagsParam]

          // 获取所有可用的标签
          const allTags: { id: string; name: string }[] = []
          storyStore.storyCategories
            .filter((category) => category?.name?.toUpperCase() === 'HOBBY')
            .forEach((category) => {
              if (category.subcategories) {
                category.subcategories.forEach((subcategory) => {
                  if (!allTags.some((tag) => tag.id === subcategory.id)) {
                    allTags.push({
                      id: subcategory.id,
                      name: subcategory.name
                    })
                  }
                })
              }
            })

          // 根据标签名称查找对应的ID
          const validTagIds: string[] = []
          tagNames.forEach((name) => {
            // 不区分大小写进行匹配
            const matchedTag = allTags.find((tag) => tag.name.toLowerCase() === name.toLowerCase())
            if (matchedTag) {
              validTagIds.push(matchedTag.id)
            }
          })

          if (validTagIds.length > 0) {
            selectedTags.value = validTagIds
          }
        } catch (error) {
          console.error('Error parsing tags from URL:', error)
        }
      }

      // 初始化完成后，手动触发一次数据请求
      const categoryIds = [selectedGender.value, ...selectedTags.value].filter((id) => id !== '')

      // 优化：如果已有故事数据且筛选条件未变化，跳过重复请求
      const shouldFetchStories =
        storyStore.stories.length === 0 ||
        JSON.stringify(categoryIds) !== JSON.stringify(storyStore.lastFetchParams?.categoryIds) ||
        selectedPopular.value !== storyStore.lastFetchParams?.sort
      if (shouldFetchStories) {
        // 始终等待数据加载完成，确保状态一致性
        await storyStore.fetchStories(categoryIds, selectedPopular.value)
      }

      // 只有当URL中已经有筛选参数时，才更新URL（避免首次进入时自动添加默认参数）
      if (hasUrlParams) {
        updateUrlParams()
      }

      // 关闭初始化标志，允许监听器正常工作
      isInitializing = false
    } catch (error) {
      console.error('Error initializing from URL params:', error)
      // 出错时也关闭初始化标志
      isInitializing = false
    } finally {
      // 无论成功还是失败，都将加载状态设置为false
      isLoading.value = false
    }
  }

  // 监听筛选条件变化，更新故事列表和URL参数
  // 使用一个标志位来避免初始化时触发多次请求
  let isInitializing = true

  watch([() => selectedPopular.value, () => selectedGender.value, () => selectedTags.value], () => {
    // 如果正在初始化，不触发数据请求
    if (isInitializing) return

    const categoryIds = [selectedGender.value, ...selectedTags.value].filter((id) => id !== '')
    storyStore.fetchStories(categoryIds, selectedPopular.value)

    // 更新URL参数
    updateUrlParams()
  })

  // 计算属性：标签按钮文本
  const tagsButtonText = () => {
    // 如果正在加载，返回一个空字符串或加载指示符
    if (isLoading.value) {
      return 'Tags'
    }
    return selectedTags.value.length > 0 ? `Tags (${selectedTags.value.length})` : 'Tags'
  }

  // 计算属性：性别标签文本
  const selectedGenderLabel = () => {
    if (!selectedGender.value) return 'Gender: All'
    const category = storyStore.storyCategories
      .find((c) => c?.name?.toUpperCase() === 'GENDER')
      ?.subcategories.find((c) => c.id === selectedGender.value)
    return category ? `Gender: ${category.name}` : 'Gender: All'
  }

  // 计算属性：排序方式标签文本
  const selectedPopularLabel = () => {
    switch (selectedPopular.value) {
      case 'popular':
        return 'Most popular'
      case 'newest':
        return 'Newest'
      case 'liked':
        return 'Most liked'
      default:
        return 'Most popular'
    }
  }

  // 处理筛选条件变化的函数
  const handlePopularChange = (value: string) => {
    selectedPopular.value = value
  }

  const handleGenderChange = (value: string) => {
    selectedGender.value = value
  }

  const handleTagsChange = (tags: string[]) => {
    selectedTags.value = tags
  }

  return {
    selectedPopular,
    selectedGender,
    selectedTags,
    selectedPopularLabel,
    selectedGenderLabel,
    tagsButtonText,
    handlePopularChange,
    handleGenderChange,
    handleTagsChange,
    initFromUrlParams,
    isLoading
  }
}
