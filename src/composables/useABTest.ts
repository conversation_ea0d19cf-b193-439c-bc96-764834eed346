import { useABTestStore } from '@/store/abtest'
import { trackABTestConversion } from '@/utils/abtest'
import type { ABTestVariant } from '@/utils/abtest'

/**
 * 简化的AB测试Hook，用于快速判断变体
 * 现在使用全局状态，避免重复计算
 */
export function useSimpleABTest(experimentName: string, defaultVariant: ABTestVariant = 'A') {
  const abtestStore = useABTestStore()

  const variant = abtestStore.getVariant(experimentName) || defaultVariant

  if (!variant) {
    console.warn(`实验 "${experimentName}" 不存在，使用默认变体 ${defaultVariant}`)
    return {
      variant: defaultVariant,
      isA: defaultVariant === 'A',
      isB: defaultVariant === 'B',
      isC: defaultVariant === 'C',
      isD: defaultVariant === 'D',
      trackExposure: () => {},
      trackConversion: () => {}
    }
  }

  // 上报曝光事件（通过store，避免重复）
  const trackExposure = (context?: Record<string, any>) => {
    abtestStore.trackExposure(experimentName, context)
  }

  // 上报转化事件
  const trackConversion = (conversionType: string, value?: number) => {
    trackABTestConversion(experimentName, conversionType, value)
  }

  return {
    variant,
    isA: variant === 'A',
    isB: variant === 'B',
    isC: variant === 'C',
    isD: variant === 'D',
    trackExposure,
    trackConversion
  }
}

/**
 * 推荐使用：直接使用store中的预计算结果
 * 性能最优，适合有大量组件的场景
 */
export function useOptimizedABTest() {
  const abtestStore = useABTestStore()

  return {
    storyTagsDisplay: abtestStore.storyTagsDisplay,
    storyCardDesign: abtestStore.storyCardDesign,
    trackExposure: abtestStore.trackExposure,
    trackConversion: trackABTestConversion
  }
}
