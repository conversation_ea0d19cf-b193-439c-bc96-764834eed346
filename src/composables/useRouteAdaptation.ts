import { useRoute, onBeforeRouteUpdate } from 'vue-router'
import { onMounted, onUnmounted, watch } from 'vue'

export const useRouteAdaptation = () => {
  const route = useRoute()

  const updateRouteAttribute = (route: any) => {
    // 优先使用路由名称
    if (typeof route.name === 'string') {
      document.body.setAttribute('data-route', route.name)
      document.getElementById('app')?.setAttribute('data-route', route.name)
    }

    // 同时设置基于路径的属性，提取路径的第一部分作为路由类型
    const pathParts = route.path.split('/').filter(Boolean)
    if (pathParts.length > 0) {
      const routeType = pathParts[0]
      document.body.setAttribute('data-route-type', routeType)
      document.getElementById('app')?.setAttribute('data-route-type', routeType)
    }

    // 为 PC 路由单独设置标记
    if (route.path.startsWith('/pc')) {
      document.body.setAttribute('data-is-pc', 'true')
      document.getElementById('app')?.setAttribute('data-is-pc', 'true')
    } else {
      document.body.removeAttribute('data-is-pc')
      document.getElementById('app')?.removeAttribute('data-is-pc')
    }
  }

  const cleanup = () => {
    document.body.removeAttribute('data-route')
    document.getElementById('app')?.removeAttribute('data-route')
    document.body.removeAttribute('data-route-type')
    document.getElementById('app')?.removeAttribute('data-route-type')
    document.body.removeAttribute('data-is-pc')
    document.getElementById('app')?.removeAttribute('data-is-pc')
  }

  // 监听路由变化
  watch(
    () => route,
    (newRoute) => {
      updateRouteAttribute(newRoute)
    },
    { immediate: true, deep: true }
  )

  onUnmounted(() => {
    cleanup()
  })
}
