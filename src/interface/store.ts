export enum PublishType {
  none = 'none',
  // 发布
  publish = 'publish',
  // 保存
  preview = 'preview'
}

export enum EditType {
  editImage = 'editImage',
  addImage = 'addImage',
  editPara = 'editPara',
  addPara = 'addPara',
  hide = 'hide'
}

export enum RoleModalType {
  global = 'global', // 更多全局人物编写
  select = 'select', // 分镜选择人物
  hide = 'hide' // 隐藏
}

// 错误状态
export enum ErrorStatus {
  None = 0,
  Error = 1,
  NoAccess = 2,
  NotFound = 3
}

export enum KindType {
  Unknown = '0',
  Human = '1',
  InHuman = '2',
  Animal = '3'
}

export enum TrainImgType {
  FrontFace = 0,
  SideFace = 1,
  FrontFullBody = 2,
  BackFullBody = 3,
  FullBody = 4,
  HalfBody = 5
}
// 添加实体 id
export const EntityDefaultId = '0'

export const DEFAULT_CATE = 'hot'
