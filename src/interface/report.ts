export enum ReportEvent {
  ApiError = 'ApiError', // Api 错误
  ApiUnknownError = 'ApiUnknownError', // Api 未知错误
  ReelPlayFirstVisit = 'ReelPlayFirstVisit', // 首次访问
  ApiRequest = 'ApiRequest', // Api 请求耗时
  PageView = 'PageView', // 页面访问
  Share = 'Share', // 分享
  Download = 'Download', // 下载
  GPTRewrite = 'GPTRewrite',

  // 落地页
  VisitLandingPage = 'VisitLandingPage', // 访问落地页
  StartChat = 'StartChat', // 开始聊天
  VisitIndexPage = 'VisitIndexPage', // 访问首页
  // 付费弹窗展示
  ShowPaymentModal = 'ShowPaymentModal', // 付费弹窗展示
  ClickPaymentButton = 'ClickPaymentButton', // 点击付费按钮
  PaymentSuccess = 'PaymentSuccess', // 支付成功

  ChatDuration = 'ChatDuration', // 聊天时长
  ClickChatOption = 'ClickChatOption', // 点击聊天选项
  ClickActorOption = 'ClickActorOption', // 点击演员卡片选项
  ClickChat2Option = 'ClickChat2Option', // 点击聊天卡片选项
  SendChatMessage = 'SendChatMessage', // 发送聊天消息
  BackHome = 'BackHome', // 返回首页
  ClickToPlay = 'ClickToPlay', // 点击播放
  ClickSayHiButton = 'ClickSayHiButton', // 点击打招呼按钮
  TaskListOpen = 'TaskListOpen', // 任务列表打开
  GetPaymentSuccessInfoTimeout = 'GetPaymentSuccessInfoTimeout', // 获取支付成功信息超时
  VideoSkipped = 'VideoSkipped', // 视频跳过
  VideoCompleted = 'VideoCompleted', // 视频播放完成
  VideoLoaded = 'VideoLoaded', // 视频加载
  VideoLoadFailed = 'VideoLoadFailed', // 视频加载失败
  PlayTTS = 'PlayTTS', // 播放 TTS
  StartChatFromIndexPage = 'StartChatFromIndexPage', // 从首页开始聊天
  ShowRechargeModal = 'ShowRechargeModal', // 显示充值弹窗
  ClickRechargeButton = 'ClickRechargeButton', // 点击充值按钮
  ShowRechargeSuccess = 'ShowRechargeSuccess', // 显示充值成功页面
  ClickContinueAfterRecharge = 'ClickContinueAfterRecharge', // 点击充值成功后的继续按钮
  DiscordJoinClick = 'DiscordJoinClick', // 加入 Discord
  ClickIndexPageSignIn = 'ClickIndexPageSignIn', // 点击首页登录
  ClickIndexPageStoryCard = 'ClickIndexPageStoryCard', // 点击首页故事卡片
  IndexPageStoryCardExposure = 'IndexPageStoryCardExposure', // 首页故事卡片曝光
  StoryIntroPageView = 'StoryIntroPageView', // 故事介绍页曝光
  ClickStoryIntroFavorite = 'ClickStoryIntroFavorite', // 点击故事介绍页收藏
  ClickToPlayInStoryIntro = 'ClickToPlayInStoryIntro', // 点击故事介绍页玩
  ClickStartToPlayInStoryIntro = 'ClickStartToPlayInStoryIntro', // 点击故事介绍页开始玩
  ClickVolume = 'ClickVolume', // 点击播放音量
  ChatOverlayExposure = 'ChatOverlayExposure', // 聊天弹窗曝光
  TaskTipExposure = 'TaskTipExposure', // 任务提示曝光
  ClickLeaveChat = 'ClickLeaveChat', // 点击离开聊天
  ClickKeepChatting = 'ClickKeepChatting', // 点击继续聊天
  ClickMenu = 'ClickMenu', // 点击菜单
  ProfilePageView = 'ProfilePageView', // 用户主页曝光
  ProfilePageDiamondBalance = 'ProfilePageDiamondBalance', // 用户主页钻石余额
  ClickProfilePageSignIn = 'ClickProfilePageSignIn', // 点击用户主页登录
  ClickProfilePageSignOut = 'ClickProfilePageSignOut', // 点击用户主页登出
  ClickProfilePageRecharge = 'ClickProfilePageRecharge', // 点击用户主页充值
  ClickProfilePageHistory = 'ClickProfilePageHistory', // 点击用户主页历史
  ClickProfilePageCredit = 'ClickProfilePageCredit', // 点击用户主页积分
  ClickProfilePageSettings = 'ClickProfilePageSettings', // 点击用户主页设置
  ClickProfilePageHelp = 'ClickProfilePageHelp', // 点击用户主页帮助
  ClickProfilePageTabClick = 'ClickProfilePageTabClick', // 点击用户主页标签
  ClickProfilePageStoryClick = 'ClickProfilePageStoryClick', // 点击用户主页故事
  ClickProfilePageDailyTasks = 'ClickProfilePageDailyTasks', // 点击用户主页每日任务

  // 设置页
  ClickSettingsPageSignOut = 'ClickSettingsPageSignOut', // 点击设置页登出

  // 登录页
  LoginPageView = 'LoginPageView', // 登录页曝光
  ClickLoginPageSignIn = 'ClickLoginPageSignIn', // 点击登录页登录
  ClickLoginPageSignUp = 'ClickLoginPageSignUp', // 点击登录页注册
  ClickLoginPageForgotPassword = 'ClickLoginPageForgotPassword', // 点击登录页忘记密码
  ClickLoginPageSignInWithGoogleOrDiscord = 'ClickLoginPageSignInWithGoogleOrDiscord', // 点击登录页使用 Google 或 Discord 登录
  ClickLoginPageSignInWithEmail = 'ClickLoginPageSignInWithEmail', // 点击登录页使用邮箱登录
  ClickLoginPageSignInWithPassword = 'ClickLoginPageSignInWithPassword', // 点击登录页使用密码登录
  LoginSuccess = 'LoginSuccess', // 登录成功

  // AuthDrawer
  AuthDrawerView = 'AuthDrawerView', // AuthDrawer 曝光
  AuthDrawerChangeType = 'AuthDrawerChangeType', // AuthDrawer 切换类型
  AuthDrawerClickLoginPageSignIn = 'AuthDrawerClickLoginPageSignIn', // 点击 AuthDrawer 登录页登录
  AuthDrawerClickRegisterPageSignUp = 'AuthDrawerClickRegisterPageSignUp', // 点击 AuthDrawer 注册页注册
  AuthDrawerClickLoginPageSignInWithGoogleOrDiscord = 'AuthDrawerClickLoginPageSignInWithGoogleOrDiscord', // 点击 AuthDrawer 使用 Google 或 Discord 登录
  ClickAddCreditInChat = 'ClickAddCreditInChat', // 点击聊天页添加积分

  CompleteRegistration = 'CompleteRegistration', // 完成注册

  GameEndClickRestartExposure = 'GameEndClickRestartExposure', // 游戏结束  重新开始曝光
  GameEndClickRestart = 'GameEndClickRestart', // 游戏结束  点击重新开始

  ClickSendImage = 'ClickSendImage', // 点击发送图片
  ShowFaceRequirements = 'ShowFaceRequirements', // 显示人脸要求
  ClickSelectFromAlbum = 'ClickSelectFromAlbum', // 点击从相册选择
  ClickClearUploadImage = 'ClickClearUploadImage', // 点击清除上传图片
  UploadImageSuccess = 'UploadImageSuccess', // 上传图片成功
  UploadImageFailed = 'UploadImageFailed', // 上传图片失败

  ClickLogoToHome = 'ClickLogoToHome', // 点击 Logo 返回首页

  ClickSkipUpload = 'ClickSkipUpload', // 点击跳过上传

  RatingModalExposure = 'RatingModalExposure', // 评分弹窗曝光
  RatingModalClose = 'RatingModalClose', // 评分弹窗关闭
  RatingModalSubmit = 'RatingModalSubmit', // 评分弹窗提交
  ClickLoginPageSignInWithFacebook = 'ClickLoginPageSignInWithFacebook', // 点击登录页使用 Facebook 登录

  ClickDeleteCharacter = 'ClickDeleteCharacter', // 点击删除角色
  ClickCreateCharacter = 'ClickCreateCharacter', // 点击创建角色
  UserCharacterPageView = 'UserCharacterPageView', // 用户角色页曝光
  ClickDeleteCharacterConfirm = 'ClickDeleteCharacterConfirm', // 点击删除角色确认
  UploadImageToCreateCharacterSuccess = 'UploadImageToCreateCharacterSuccess', // 上传图片到创建角色成功
  UploadImageToCreateCharacterFailed = 'UploadImageToCreateCharacterFailed', // 上传图片到创建角色失败
  ClickSelectFromAlbumToCreateCharacter = 'ClickSelectFromAlbumToCreateCharacter', // 点击从相册选择去创建角色
  ClickCreateCharacterSuccess = 'ClickCreateCharacterSuccess', // 创建角色成功
  ClickCreateCharacterFailed = 'ClickCreateCharacterFailed', // 创建角色失败
  GenerateCharacterSuccess = 'GenerateCharacterSuccess', // 生成角色成功

  CheckinModalOpen = 'CheckinModalOpen', // 签到弹窗打开
  CheckinModalClaim = 'CheckinModalClaim', // 签到弹窗领取
  CheckinModalClose = 'CheckinModalClose', // 签到弹窗关闭
  CheckinModalClaimClick = 'CheckinModalClaimClick', // 签到弹窗领取按钮点击

  // Daily Tasks Page
  DailyTasksPageView = 'DailyTasksPageView', // 每日任务页面曝光
  DailyTasksClaimClick = 'DailyTasksClaimClick', // 每日任务领取按钮点击
  DailyTasksClaimSuccess = 'DailyTasksClaimSuccess', // 每日任务领取成功
  DailyTasksPlayGameClick = 'DailyTasksPlayGameClick', // 每日任务玩游戏点击
  DailyTasksShareGameClick = 'DailyTasksShareGameClick', // 每日任务分享游戏点击
  DailyTasksInviteFriendClick = 'DailyTasksInviteFriendClick', // 每日任务邀请好友点击

  HeartValueClick = 'HeartValueClick', // 爱心值点击
  Chat2TaskTipClick = 'Chat2TaskTipClick', // 聊天页任务提示点击
  ClickChat2TaskTipSkip = 'ClickChat2TaskTipSkip', // 点击聊天页任务提示跳过

  SceneChange = 'SceneChange', // 场景变化
  SceneDuration = 'SceneDuration', // 场景耗时

  ClickChat2HistoryButton = 'ClickChat2HistoryButton', // 点击聊天页历史按钮
  ClickChatHistoryButton = 'ClickChatHistoryButton', // 旧故事(20250216)之前的点击聊天页历史按钮

  StoryCardSubscriptionClick = 'StoryCardSubscriptionClick', // 点击故事卡片订阅

  ShareButtonClick = 'ShareButtonClick', // 分享按钮点击

  VisitShareByLink = 'VisitShareByLink', // 访问分享链接
  ClickDownloadApp = 'ClickDownloadApp', // 点击下载 App
  OpenInAndroidAPKWebView = 'OpenInAndroidAPKWebView', // 打开 Android APK WebView

  InitializeChat = 'InitializeChat', // 初始化聊天

  TagRatingModalExpose = 'TagRatingModalExpose', // 标签评分弹窗曝光
  TagRatingModalClose = 'TagRatingModalClose', // 标签评分弹窗关闭
  TagRatingModalSubmit = 'TagRatingModalSubmit', // 标签评分弹窗提交

  ClickBannerToHome = 'ClickBannerToHome', // 点击 Banner 返回首页

  PaymentSuccessForStoryAndCharacter = 'PaymentSuccessForStoryAndCharacter', // 付费成功（故事和角色）

  // 技能相关
  OpenSkillSelect = 'OpenSkillSelect', // 打开技能选择
  DisplaySkills = 'DisplaySkills', // 展示技能列表
  SelectSkill = 'SelectSkill', // 选择技能
  RefreshSkill = 'RefreshSkill', // 刷新技能列表
  SkillExposure = 'SkillExposure', // 单个技能曝光

  UserAllocateAttribute = 'UserAllocateAttribute', // 用户分配的属性点数

  // 教程相关
  ShowTutorial = 'ShowTutorial', // 显示教程
  StartTutorial = 'StartTutorial', // 开始教程
  CompleteTutorial = 'CompleteTutorial', // 完成教程
  SkipTutorial = 'SkipTutorial', // 跳过教程

  // 分享相关
  EndingShareButtonClick = 'EndingShareButtonClick', // 分享按钮点击
  EndingShareButtonExpose = 'EndingShareButtonExpose', // 分享按钮曝光
  AvatarSelectExposure = 'AvatarSelectExposure', // 头像选择页曝光
  AvatarSelectClick = 'AvatarSelectClick', // 头像选择页点击
  AvatarSelectConfirm = 'AvatarSelectConfirm', // 头像选择页确认
  UserAvatarCreated = 'UserAvatarCreated', // 用户创建头像
  AvatarSelectDuration = 'AvatarSelectDuration', // 头像选择页耗时

  SettingsPageView = 'SettingsPageView', // 设置页曝光

  StoryDetailPageView = 'StoryDetailPageView',

  SocialButtonJoinClick = 'SocialButtonJoinClick',

  // Demo Modal 相关事件
  DemoModalView = 'DemoModalView', // Demo弹窗显示
  DemoModalClose = 'DemoModalClose', // Demo弹窗关闭
  DemoModalButtonClick = 'DemoModalButtonClick', // Demo按钮点击
  DemoModalCharacterSwitch = 'DemoModalCharacterSwitch', // Demo角色切换
  DemoModalLoginClick = 'DemoModalLoginClick', // Demo弹窗登录按钮点击
  DemoModalSignupLaterClick = 'DemoModalSignupLaterClick', // Demo弹窗稍后注册点击

  // Login Modal 相关事件
  LoginModalView = 'LoginModalView', // 登录弹窗显示
  LoginModalClose = 'LoginModalClose', // 登录弹窗关闭
  LoginModalButtonClick = 'LoginModalButtonClick', // 登录弹窗按钮点击
  LoginModalCharacterSwitch = 'LoginModalCharacterSwitch', // 登录弹窗角色切换
  LoginModalLoginClick = 'LoginModalLoginClick', // 登录弹窗登录按钮点击

  // 地区限制相关
  RegionRestrictionShown = 'RegionRestrictionShown', // 地区限制页面显示
  RegionRestrictionAlternativeClick = 'RegionRestrictionAlternativeClick', // 地区限制页面替代方案点击

  // AB测试相关事件
  ABTestAssigned = 'ABTestAssigned', // AB测试分组分配
  ABTestExposure = 'ABTestExposure', // AB测试曝光
  ABTestConversion = 'ABTestConversion', // AB测试转化
  ABTestError = 'ABTestError' // AB测试错误
}
