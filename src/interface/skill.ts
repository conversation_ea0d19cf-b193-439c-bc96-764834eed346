export interface SkillInfo {
  id: string
  name: string
  description: string
  image_url: string
  icon_url: string
  prompt: string
  skill_configs: SkillConfig
}

export interface SkillConfig {
  effect: Effect[]
  conditional_effect: ConditionalEffect[]
}

export interface Effect {
  attr: string
  value: string
}

export interface ConditionalEffect {
  conditions: Condition[]
  modifiers: Modifier[]
}

export interface Condition {
  attribute: string
  operator: string
  value: number
}

export interface Modifier {
  attribute: string
  value: number
}

export interface SkillInfoResponse {
  code: number
  data: {
    skills: SkillInfo[]
  }
  message: string
}
