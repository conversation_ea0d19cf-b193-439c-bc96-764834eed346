export enum UserGroup {
  Admin = 'magiclight/magiclight-admin'
}

export interface UserInfo {
  id: string
  name?: string
  desc?: string
  avatar?: string
  gender?: number
  groups?: UserGroup[]
  hadSurvey?: number
  createTime?: string
}
export interface UserInfoItem {
  label: string
  value: string | number
  description?: string
  editing: boolean
  showIcon?: boolean
  inputType?: string
  options?: { label: string; value: string }[]
}
