declare module 'vue-virtual-scroller' {
  import { DefineComponent } from 'vue'

  export interface RecycleScrollerProps {
    items: any[]
    itemSize: number
    keyField?: string
    direction?: 'vertical' | 'horizontal'
    listTag?: string
    itemTag?: string
    listClass?: string | object | any[]
    itemClass?: string | object | any[]
    buffer?: number
    pageMode?: boolean
    prerender?: number
    emitUpdate?: boolean
    updateInterval?: number
    gridItems?: number
    skipHover?: boolean
  }

  export interface DynamicScrollerProps {
    items: any[]
    minItemSize: number
    keyField?: string
    direction?: 'vertical' | 'horizontal'
    listTag?: string
    itemTag?: string
    listClass?: string | object | any[]
    itemClass?: string | object | any[]
    buffer?: number
    pageMode?: boolean
    prerender?: number
    emitUpdate?: boolean
    updateInterval?: number
  }

  export interface DynamicScrollerItemProps {
    item: any
    active: boolean
    sizeDependencies?: any[]
    watchData?: boolean
    tag?: string
    emitResize?: boolean
    onResize?: () => void
  }

  export const RecycleScroller: DefineComponent<RecycleScrollerProps>
  export const DynamicScroller: DefineComponent<DynamicScrollerProps>
  export const DynamicScrollerItem: DefineComponent<DynamicScrollerItemProps>

  export default {
    RecycleScroller,
    DynamicScroller,
    DynamicScrollerItem
  }
}
