export interface Position {
  x: number
  y: number
}

export interface MediaContent {
  url: string
  type: 'image' | 'video' | 'audio'
}

export interface PreloadAsset {
  url: string
  type: 'video' | 'image' | 'audio'
}

export interface ChatOption {
  option_id: string
  text: string
  action: 'continue' | string
  paid_required?: boolean
  coins?: number
  scene_id?: string
  is_highlight?: boolean
}

export interface MessageContent {
  text?: string
  html?: string
  media_url?: string
}

export interface TaskProgress {
  score?: number
  percent?: number
}

export interface HeartOptions {
  is_allow_message: boolean
  heart_value: number
}

export interface OverlayContent {
  button?: {
    icon: string
    text: string
    action: string
  }
  overlay?: {
    text: string
    position: 'top' | 'bottom' | 'center'
    display_time: 'before' | 'after'
  }
}

export type GameEventType =
  | 'wait'
  | 'show_tips'
  | 'show_overlay'
  | 'message'
  | 'show_image'
  | 'animated_images'
  | 'play_video'
  | 'play_audio'
  | 'show_chat_options'
  | 'show_ending'
  | 'update_user_coins'
  | 'update_task_progress'
  | 'scene_transition'
  | 'heart_value'
  | 'interactive'
  | 'voice_config'
export interface GameEventPlot {
  seconds?: number
  type?: 'fade' | 'slide'
  content?: MessageContent | string
  sender_type?: 'actor' | 'user'
  msg_type?: 'text' | 'html' | 'image' | 'audio' | 'video'
  url?: string
  urls?: string[]
  is_fullscreen?: boolean
  effect?: 'fade' | 'slide'
  is_background?: boolean
  is_bgm?: boolean
  allow_input?: boolean
  hide_chat_options?: boolean
  input_placeholder?: string
  style?: string
  options?: ChatOption[]
  coins?: number
  task_id?: string
  task_description?: string
  task_progress?: TaskProgress
  scene_transition?: {
    type: 'fade' | 'slide'
    seconds?: number
  }
  heart_options?: HeartOptions
  button?: {
    icon: string
    text: string
    action: string
  }
  overlay?: {
    text: string
    position: 'top' | 'bottom' | 'center'
    display_time: 'before' | 'after'
  }
  min_watch_duration?: number
}

export interface GameEvent {
  id: string
  type: GameEventType
  plot: GameEventPlot
}

export interface ActionHandlerParams {
  level: string
  background: string
  heart_key: string
  heart_value: number
  clean_history: boolean
  limit_chat_count: number
  agree_sentences: string[]
  streamer_tpl: string
}

export interface ActionHandler {
  type: 'ScoreLimitWithLLMChatV2'
  params: ActionHandlerParams
}

export interface Character {
  name: string
  lora_id: string
  clothes: string
}

export interface Plot {
  sentence: string
  character: Character[]
  location: string
}

export interface StoryGenerationParams {
  plots: Plot[]
}

export interface Condition {
  attribute: string
  operator: string
  value: number
}

export interface ConditionConfig {
  type: 'single' | 'and' | 'or'
  attribute?: string
  operator?: string
  value?: number
  conditions?: Condition[]
  echo: string
  expression: string
}

export interface ConditionValue {
  conditions: ConditionConfig[]
}

export interface Scene {
  id: string
  name: string
  parent_id?: string
  next_scene_id?: string
  conditions?: ConditionConfig[]
  events: GameEvent[]
  action_handlers?: ActionHandler[]
  story_generation_params?: StoryGenerationParams
  scene_group?: string
}

export interface GameConfig {
  preloads: PreloadAsset[]
  scenes: Scene[]
  daydream_user_id?: string
}
