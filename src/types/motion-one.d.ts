declare module 'motion' {
  export interface AnimationControls {
    stop: () => void
    pause: () => void
    play: () => void
    [key: string]: any
  }

  export interface AnimationOptions {
    duration?: number
    easing?: number[] | string
    repeat?: number
    times?: number[]
    onUpdate?: (latest: any) => void
    [key: string]: any
  }

  export interface AnimationKeyframes {
    transform?: string[]
    opacity?: number[]
    [key: string]: any
  }

  export type MotionTarget = string | Element | null

  export function animate(
    target: MotionTarget,
    keyframes: AnimationKeyframes,
    options?: AnimationOptions
  ): AnimationControls
}
