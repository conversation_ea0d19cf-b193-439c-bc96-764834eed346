// 聊天相关类型定义
export interface ChatOptions {
  option_id: string
  text: string
  paid_required: boolean
  coins: number
  is_purchased: boolean
  scene_id?: string
  is_highlight?: boolean
}

export interface ActorOptions {
  scene_id: string
  text: string
  paid_required: boolean
  coins: number
  is_purchased: boolean
  actor_name: string
  actor_id: string
  avatar_url: string
  is_highlight?: boolean
  option_id?: string
}

export interface MessageSender {
  avatar_url: string
  name: string
}

export interface Message {
  id: string
  msg_type: 'text' | 'image' | 'video' | 'config'
  sender_type: 'user' | 'actor' | 'system' | 'tips'
  content: {
    text?: string
    media_url?: string
    html?: string
  }
  create_time: string
  sender: MessageSender
}

export interface ChatHistoryResponse {
  history: null | []
}

export interface ChatStartResponse {
  code: string
  message: string
  data: {
    conversation_id: string
    message?: Message
  }
}

export interface SendMessageResponse {
  code: string
  message: string
  data: {
    message: Message
  }
}

export interface SSEParams {
  event_type: 'noop' | 'action' | 'chat' | 'chat:image'
  timestamp: number
  data: Record<string, any>
}

export interface StartChatParams {
  story_id: string
  game_config?: {
    skill_id: string
    numerical_values: {
      happiness: number
      intelligence: number
      strength: number
      wealth: number
    }
    user_avatar_url?: string
  }
}

export interface RatingForm {
  actor_id: string
  story_id: string
  story_rating: number
  character_rating: number
  image_rating: number
  comment?: string
}

export interface RatingHistory {
  story_rating: number
  character_rating: number
  image_rating: number
  comment: string
  is_commentable: boolean
}

export interface ChatHistory {
  messages: Message[]
  conversation_id: string
}
