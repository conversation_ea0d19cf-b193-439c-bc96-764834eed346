/**
 * AB测试系统类型定义
 */

// 实验状态枚举
export enum ExperimentStatus {
  DRAFT = 'draft',           // 草稿
  RUNNING = 'running',       // 运行中
  PAUSED = 'paused',         // 暂停
  COMPLETED = 'completed',   // 已完成
  ARCHIVED = 'archived'      // 已归档
}

// 分组类型枚举
export enum VariantType {
  CONTROL = 'control',       // 对照组
  TREATMENT = 'treatment'    // 实验组
}

// 实验变体配置
export interface ExperimentVariant {
  id: string                 // 变体ID
  name: string              // 变体名称
  type: VariantType         // 变体类型
  weight: number            // 流量权重 (0-100)
  config: Record<string, any> // 变体配置参数
  description?: string      // 变体描述
}

// 实验配置
export interface ExperimentConfig {
  id: string                    // 实验ID
  name: string                  // 实验名称
  description?: string          // 实验描述
  status: ExperimentStatus      // 实验状态
  variants: ExperimentVariant[] // 实验变体列表
  startTime?: number           // 开始时间戳
  endTime?: number             // 结束时间戳
  targetAudience?: {           // 目标受众
    userTypes?: string[]       // 用户类型过滤
    regions?: string[]         // 地区过滤
    platforms?: string[]       // 平台过滤
    customFilters?: Record<string, any> // 自定义过滤条件
  }
  metrics?: {                  // 关键指标
    primary: string[]          // 主要指标
    secondary?: string[]       // 次要指标
  }
  createdAt: number           // 创建时间
  updatedAt: number           // 更新时间
  createdBy?: string          // 创建者
}

// 用户实验分配记录
export interface UserExperimentAssignment {
  userId: string              // 用户ID
  deviceId: string           // 设备ID
  experimentId: string       // 实验ID
  variantId: string          // 分配的变体ID
  assignedAt: number         // 分配时间戳
  exposedAt?: number         // 首次曝光时间戳
  metadata?: Record<string, any> // 额外元数据
}

// 实验曝光事件
export interface ExperimentExposureEvent {
  experimentId: string       // 实验ID
  variantId: string         // 变体ID
  userId?: string           // 用户ID
  deviceId: string          // 设备ID
  timestamp: number         // 曝光时间戳
  context?: {               // 曝光上下文
    page?: string           // 页面路径
    component?: string      // 组件名称
    feature?: string        // 功能名称
    [key: string]: any     // 其他上下文信息
  }
}

// 实验转化事件
export interface ExperimentConversionEvent {
  experimentId: string       // 实验ID
  variantId: string         // 变体ID
  userId?: string           // 用户ID
  deviceId: string          // 设备ID
  timestamp: number         // 转化时间戳
  conversionType: string    // 转化类型
  value?: number            // 转化价值
  metadata?: Record<string, any> // 额外元数据
}

// AB测试客户端配置
export interface ABTestClientConfig {
  apiEndpoint?: string      // API端点
  cacheTimeout?: number     // 缓存超时时间(ms)
  enableLocalStorage?: boolean // 是否启用本地存储
  enableReporting?: boolean // 是否启用事件上报
  debugMode?: boolean       // 调试模式
  defaultVariant?: string   // 默认变体ID
}

// 实验结果统计
export interface ExperimentStats {
  experimentId: string      // 实验ID
  variantId: string        // 变体ID
  totalUsers: number       // 总用户数
  exposedUsers: number     // 曝光用户数
  conversions: number      // 转化次数
  conversionRate: number   // 转化率
  confidence?: number      // 置信度
  significance?: boolean   // 是否显著
  lastUpdated: number      // 最后更新时间
}

// 实验管理器状态
export interface ABTestManagerState {
  experiments: Record<string, ExperimentConfig>     // 实验配置缓存
  assignments: Record<string, UserExperimentAssignment> // 用户分配缓存
  isInitialized: boolean                           // 是否已初始化
  lastSyncTime?: number                           // 最后同步时间
  config: ABTestClientConfig                      // 客户端配置
}

// API响应类型
export interface ExperimentListResponse {
  experiments: ExperimentConfig[]
  total: number
  hasMore: boolean
}

export interface AssignmentResponse {
  assignment: UserExperimentAssignment
  experiment: ExperimentConfig
}

// 错误类型
export class ABTestError extends Error {
  constructor(
    message: string,
    public code: string,
    public experimentId?: string,
    public context?: Record<string, any>
  ) {
    super(message)
    this.name = 'ABTestError'
  }
}

// 实验过滤器
export interface ExperimentFilter {
  status?: ExperimentStatus[]
  tags?: string[]
  createdAfter?: number
  createdBefore?: number
  search?: string
}

// 批量分配请求
export interface BatchAssignmentRequest {
  userId?: string
  deviceId: string
  experimentIds: string[]
  context?: Record<string, any>
}

// 批量分配响应
export interface BatchAssignmentResponse {
  assignments: UserExperimentAssignment[]
  experiments: Record<string, ExperimentConfig>
}
