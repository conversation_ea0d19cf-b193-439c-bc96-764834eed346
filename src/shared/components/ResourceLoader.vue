<template>
  <Transition name="loader-fade">
    <div v-if="visible" class="resource-loader-overlay">
      <div class="loader-container">
        <!-- Logo -->
        <div class="loader-logo">
          <img :src="logoUrl" alt="Logo" />
        </div>
        
        <!-- 加载动画 -->
        <div class="loader-animation">
          <div class="loader-circle">
            <svg class="loader-svg" viewBox="0 0 100 100">
              <circle
                class="loader-bg"
                cx="50"
                cy="50"
                r="45"
                fill="none"
                stroke="rgba(255, 255, 255, 0.1)"
                stroke-width="8"
              />
              <circle
                class="loader-progress"
                cx="50"
                cy="50"
                r="45"
                fill="none"
                stroke="url(#gradient)"
                stroke-width="8"
                stroke-linecap="round"
                :stroke-dasharray="circumference"
                :stroke-dashoffset="dashOffset"
                transform="rotate(-90 50 50)"
              />
              <defs>
                <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                  <stop offset="0%" style="stop-color:#ca93f2;stop-opacity:1" />
                  <stop offset="100%" style="stop-color:#b857ff;stop-opacity:1" />
                </linearGradient>
              </defs>
            </svg>
            
            <!-- 中心百分比 -->
            <div class="loader-percentage">
              {{ Math.round(progress) }}%
            </div>
          </div>
        </div>
        
        <!-- 加载文本 -->
        <div class="loader-text">
          <div class="loader-title">Loading Resources</div>
          <div class="loader-subtitle">{{ loadingText }}</div>
        </div>
        
        <!-- 进度条 -->
        <div class="loader-bar">
          <div class="loader-bar-bg">
            <div 
              class="loader-bar-fill" 
              :style="{ width: `${progress}%` }"
            ></div>
          </div>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps<{
  visible: boolean
  progress: number // 0-100
  loadingText?: string
  logoUrl?: string
}>()

// 圆形进度条计算
const radius = 45
const circumference = 2 * Math.PI * radius

const dashOffset = computed(() => {
  return circumference - (props.progress / 100) * circumference
})
</script>

<style lang="less" scoped>
.loader-fade-enter-active,
.loader-fade-leave-active {
  transition: opacity 0.3s ease;
}

.loader-fade-enter-from,
.loader-fade-leave-to {
  opacity: 0;
}

.resource-loader-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #1f0038 0%, #2d1b69 50%, #1f0038 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000; // 高于模态框
}

.loader-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32px;
  text-align: center;
}

.loader-logo {
  img {
    height: 40px;
    opacity: 0.9;
  }
}

.loader-animation {
  position: relative;
}

.loader-circle {
  position: relative;
  width: 120px;
  height: 120px;
}

.loader-svg {
  width: 100%;
  height: 100%;
  transform: rotate(0deg);
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.loader-progress {
  transition: stroke-dashoffset 0.3s ease;
}

.loader-percentage {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-family: 'Work Sans', sans-serif;
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.loader-text {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.loader-title {
  font-family: 'Rammetto One', sans-serif;
  font-size: 20px;
  font-weight: 400;
  color: #ffffff;
  text-transform: uppercase;
  letter-spacing: 1px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.loader-subtitle {
  font-family: 'Work Sans', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.8);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.loader-bar {
  width: 280px;
}

.loader-bar-bg {
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.loader-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #ca93f2 0%, #b857ff 100%);
  border-radius: 2px;
  transition: width 0.3s ease;
  box-shadow: 0 0 8px rgba(202, 147, 242, 0.5);
}

// 移动端适配
@media (max-width: 768px) {
  .loader-container {
    gap: 24px;
    padding: 20px;
  }
  
  .loader-logo img {
    height: 32px;
  }
  
  .loader-circle {
    width: 100px;
    height: 100px;
  }
  
  .loader-percentage {
    font-size: 16px;
  }
  
  .loader-title {
    font-size: 18px;
  }
  
  .loader-subtitle {
    font-size: 13px;
  }
  
  .loader-bar {
    width: 240px;
  }
}
</style>
