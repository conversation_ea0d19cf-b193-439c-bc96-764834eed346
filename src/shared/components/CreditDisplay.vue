<template>
  <div class="credit-display" @click="handleClick">
    <img src="https://cdn.magiclight.ai/assets/mobile/diamond.png" class="credit-icon" />
    <div class="credit-content">
      <span
        class="credit-amount"
        :class="{
          increase: isIncreasing,
          decrease: isDecreasing
        }"
      >
        {{ formattedAmount }}
        <span v-if="isIncreasing" class="change-indicator increase">+{{ changeAmount }}</span>
        <span v-if="isDecreasing" class="change-indicator decrease">-{{ changeAmount }}</span>
      </span>
    </div>
    <div v-if="showAddButton" class="add-button" @click.stop="$emit('add')">
      <icon-add />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useTransition } from '@vueuse/core'
import { useRouter } from 'vue-router'
import IconAdd from '@/assets/icon/add.svg'

const router = useRouter()
const props = defineProps<{
  amount: number
  showAddButton?: boolean
}>()

const emit = defineEmits<{
  (e: 'add'): void
}>()

// 跟踪上一次的金额，用于判断增减
const previousAmount = ref(props.amount)
const isIncreasing = ref(false)
const isDecreasing = ref(false)
const changeAmount = ref(0)

// 使用 useTransition 创建平滑过渡效果
const animatedAmount = useTransition(
  computed(() => props.amount),
  {
    duration: 600, // 增加动画持续时间，使效果更明显
    transition: [0.25, 0.1, 0.25, 1.0] // 使用缓动函数让动画更自然
  }
)

// 格式化数字显示
const formattedAmount = computed(() => Math.round(animatedAmount.value))

// 监听金额变化，触发相应的动画效果
watch(
  () => props.amount,
  (newAmount, oldAmount) => {
    if (newAmount > oldAmount) {
      // 金额增加
      isIncreasing.value = true
      isDecreasing.value = false
      changeAmount.value = newAmount - oldAmount

      // 动画结束后重置状态
      setTimeout(() => {
        isIncreasing.value = false
      }, 1500)
    } else if (newAmount < oldAmount) {
      // 金额减少
      isDecreasing.value = true
      isIncreasing.value = false
      changeAmount.value = oldAmount - newAmount

      // 动画结束后重置状态
      setTimeout(() => {
        isDecreasing.value = false
      }, 1500)
    }

    previousAmount.value = newAmount
  }
)

const handleClick = () => {
  if (props.showAddButton) {
    emit('add')
  } else {
    router.push('/user/coins')
  }
}
</script>

<style lang="less" scoped>
.credit-display {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0 28px;
  background: #b48ded;
  border-radius: 20px;
  color: #fff;
  position: relative;
  height: 24px;
  cursor: pointer;
  .credit-icon {
    position: absolute;
    left: -8px;
    top: 50%;
    transform: translateY(-50%);
    width: 28px;
    height: 28px;
    z-index: 1;
  }

  .credit-content {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #1f0038;
    height: 100%;
    line-height: 1;
    position: relative;
    left: -2px;
  }

  .credit-amount {
    font-size: 13px;
    font-weight: 600;
    display: flex;
    align-items: center;
    height: 100%;
    position: relative;
    transition:
      transform 0.3s,
      color 0.3s;

    // 增加金额时的动画效果
    &.increase {
      animation: pulse-increase 0.6s ease-in-out;
      color: #00c853; // 绿色表示增加
    }

    // 减少金额时的动画效果
    &.decrease {
      animation: pulse-decrease 0.6s ease-in-out;
      color: #ff5252; // 红色表示减少
    }

    // 变化指示器样式
    .change-indicator {
      position: absolute;
      font-size: 12px;
      font-weight: bold;
      opacity: 0;
      white-space: nowrap;

      &.increase {
        color: #00c853;
        top: -16px;
        right: 0;
        animation: float-up 1.5s ease-out forwards;
      }

      &.decrease {
        color: #ff5252;
        bottom: -16px;
        right: 0;
        animation: float-down 1.5s ease-out forwards;
      }
    }
  }

  .add-button {
    position: absolute;
    right: 4px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    cursor: pointer;
    transition: background-color 0.3s;
    z-index: 1;
    svg {
      width: 24px;
      height: 24px;
    }
    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }

    &:active {
      background: rgba(255, 255, 255, 0.15);
    }
  }
}

// 定义增加金额时的动画
@keyframes pulse-increase {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

// 定义减少金额时的动画
@keyframes pulse-decrease {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.8);
  }
  100% {
    transform: scale(1);
  }
}

// 定义向上浮动的动画
@keyframes float-up {
  0% {
    opacity: 0;
    transform: translateY(0);
  }
  20% {
    opacity: 1;
  }
  80% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    transform: translateY(-20px);
  }
}

// 定义向下浮动的动画
@keyframes float-down {
  0% {
    opacity: 0;
    transform: translateY(0);
  }
  20% {
    opacity: 1;
  }
  80% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    transform: translateY(20px);
  }
}
</style>
