<template>
  <div class="region-restriction-overlay">
    <div class="restriction-modal">
      <div class="modal-content">
        <!-- Logo -->
        <div class="logo-section">
          <img :src="logoUrl" :alt="appName" class="app-logo" />
        </div>

        <!-- 主要信息 -->
        <div class="main-content">
          <div class="icon-section">
            <svg
              class="restriction-icon"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" />
              <path d="M4.93 4.93l14.14 14.14" stroke="currentColor" stroke-width="2" />
            </svg>
          </div>

          <h1 class="title">Service Unavailable</h1>

          <div class="description">
            <p class="sub-description">
              Due to regulatory restrictions, our services are not available in your region.
            </p>
          </div>

          <!-- 建议的替代方案 -->
          <!-- <div class="alternatives" v-if="showAlternatives">
            <h3>{{ $t('regionRestriction.alternativesTitle') }}</h3>
            <div class="alternative-item">
              <span class="alternative-name">ReelPlay</span>
              <span class="alternative-description">{{ $t('regionRestriction.reelplayDescription') }}</span>
              <a 
                href="https://reelplay.ai" 
                target="_blank" 
                class="alternative-link"
                @click="handleAlternativeClick('reelplay')"
              >
                {{ $t('regionRestriction.visitSite') }}
              </a>
            </div>
          </div> -->
        </div>

        <!-- 底部信息 -->
        <!-- <div class="footer-content">
          <p class="footer-text">{{ $t('regionRestriction.footerText') }}</p>
          <div class="contact-info">
            <span>{{ $t('regionRestriction.contactUs') }}: </span>
            <a href="mailto:<EMAIL>" class="contact-link"> <EMAIL> </a>
          </div>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { reportEvent } from '@/utils/report'
import { ReportEvent } from '@/interface'

interface Props {
  showAlternatives?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showAlternatives: true
})

// 从环境变量获取应用信息
const appName = computed(() => import.meta.env.VITE_APP_NAME || 'PlayShot')
const logoUrl = computed(() => import.meta.env.VITE_LOGO_URL || '')

// 处理替代方案点击
const handleAlternativeClick = (alternative: string) => {
  reportEvent(ReportEvent.RegionRestrictionAlternativeClick, {
    alternative,
    userAgent: navigator.userAgent,
    timestamp: Date.now()
  })
}

// 组件挂载时上报事件
reportEvent(ReportEvent.RegionRestrictionShown, {
  appName: appName.value,
  userAgent: navigator.userAgent,
  timestamp: Date.now()
})
</script>

<style scoped lang="less">
.region-restriction-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 20px;
}

.restriction-modal {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-content {
  padding: 40px 30px 30px;
  text-align: center;
}

.logo-section {
  margin-bottom: 30px;

  .app-logo {
    height: 60px;
    width: auto;
    object-fit: contain;
  }
}

.main-content {
  margin-bottom: 30px;
}

.icon-section {
  margin-bottom: 20px;

  .restriction-icon {
    width: 64px;
    height: 64px;
    color: #ef4444;
    margin: 0 auto;
  }
}

.title {
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 16px 0;
  line-height: 1.3;
}

.description {
  color: #6b7280;
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 30px;

  p {
    margin: 0 0 12px 0;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .sub-description {
    font-size: 14px;
    color: #9ca3af;
  }
}

.alternatives {
  background: #f9fafb;
  border-radius: 12px;
  padding: 24px;
  text-align: left;

  h3 {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 16px 0;
  }
}

.alternative-item {
  display: flex;
  flex-direction: column;
  gap: 8px;

  .alternative-name {
    font-weight: 600;
    color: #1f2937;
    font-size: 16px;
  }

  .alternative-description {
    color: #6b7280;
    font-size: 14px;
    line-height: 1.5;
  }

  .alternative-link {
    color: #3b82f6;
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
    align-self: flex-start;
    padding: 8px 16px;
    background: #eff6ff;
    border-radius: 6px;
    transition: all 0.2s ease;

    &:hover {
      background: #dbeafe;
      color: #2563eb;
    }
  }
}

.footer-content {
  border-top: 1px solid #e5e7eb;
  padding-top: 20px;

  .footer-text {
    color: #9ca3af;
    font-size: 14px;
    margin: 0 0 12px 0;
    line-height: 1.5;
  }

  .contact-info {
    font-size: 14px;
    color: #6b7280;

    .contact-link {
      color: #3b82f6;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}

// 移动端适配
@media (max-width: 640px) {
  .region-restriction-overlay {
    padding: 16px;
  }

  .modal-content {
    padding: 30px 20px 20px;
  }

  .logo-section .app-logo {
    height: 48px;
  }

  .title {
    font-size: 20px;
  }

  .description {
    font-size: 15px;
  }

  .alternatives {
    padding: 20px;

    h3 {
      font-size: 16px;
    }
  }
}
</style>
