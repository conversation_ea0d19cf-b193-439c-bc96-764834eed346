<template>
  <div
    class="story-card"
    :class="{ 'is-pc': isPc, 'is-loading': loading }"
    :data-story-id="story?.id"
    @click="handleCardClick"
  >
    <!-- 骨架屏状态 -->
    <template v-if="loading">
      <div class="story-image skeleton-mode">
        <div class="image-placeholder skeleton-shimmer" />
        <!-- 模拟 badge -->
        <div class="story-badge skeleton-badge"></div>
        <!-- 模拟订阅按钮 -->
        <div class="subscription-status skeleton-subscription">
          <div class="subscription-button-skeleton skeleton-element"></div>
        </div>
      </div>
      <div class="story-description-section skeleton-mode">
        <div class="story-name skeleton-text"></div>
        <div class="story-description skeleton-text"></div>
        <div class="story-author skeleton-text"></div>
      </div>
    </template>

    <!-- 正常内容状态 -->
    <template v-else>
      <div class="story-image" :class="{ 'image-loaded': imageLoaded }">
        <!-- 图片占位符 -->
        <div class="image-placeholder" :style="generatePlaceholderColor()" />

        <!-- 故事媒体（图片或视频） - 优化渲染性能 -->
        <component
          v-for="(url, index) in carouselUrls"
          :key="`media-${index}`"
          :is="getMediaType(url)"
          :src="url"
          :alt="story?.title || 'Story'"
          v-bind="getMediaProps(url, index)"
          class="story-media"
          :class="getMediaClasses(index)"
          :style="getMediaStyles(index)"
          @load="index === 0 ? onImageLoad() : null"
          @loadeddata="index === 0 ? onImageLoad() : null"
          @error="index === 0 ? onImageError() : null"
        />

        <!-- 收藏按钮 - 右上角 -->
        <div
          class="story-favorite-button"
          :class="{ active: isFavorited, loading: isFavoriteLoading }"
          :style="{ opacity: imageLoaded ? 1 : 0 }"
          @click.stop="toggleFavorite"
        >
          <span v-if="isFavoriteLoading" class="loading-spinner"></span>
          <span v-else class="heart-icon">
            <icon-favorited v-if="isFavorited" />
            <icon-favorite v-else />
          </span>
        </div>

        <!-- 徽章标签 - 左上角 -->
        <div v-if="story.badge" class="story-badge" :style="{ opacity: imageLoaded ? 1 : 0 }">
          {{ story.badge }}
        </div>

        <!-- Coming Soon 标签 -->
        <div
          v-if="story.status === 'preparing'"
          class="story-badge coming-soon"
          :style="{ opacity: imageLoaded ? 1 : 0 }"
        >
          Coming Soon
        </div>

        <!-- 管理员专属标签 -->
        <div
          v-if="!['normal', 'preparing'].includes(story.status) && isAdmin"
          class="story-badge admin-only"
          :style="{ opacity: imageLoaded ? 1 : 0 }"
        >
          <span class="admin-icon">👑</span>
          <span>Admin Only</span>
        </div>

        <!-- 订阅状态 -->
        <div
          class="subscription-status"
          v-if="story.status === 'preparing'"
          :style="{ opacity: imageLoaded ? 1 : 0 }"
        >
          <button
            class="subscription-button"
            :class="{ subscribed: story.is_subscribed, loading: isSubscribing }"
            :disabled="isSubscribing"
            @click.stop="handleSubscriptionToggle"
          >
            <div class="subscription-button-icon">
              <div v-if="isSubscribing" class="loading-spinner"></div>
              <template v-else>
                <CheckGrayIcon v-if="story.is_subscribed" />
                <AlarmBlackIcon v-else />
              </template>
            </div>
            <div class="subscription-button-text">
              {{ isSubscribing ? 'Loading...' : story.is_subscribed ? 'Subscribed' : 'Subscribe' }}
            </div>
          </button>
        </div>
      </div>

      <!-- 描述区域 - 白色背景，独立于图片 -->
      <div class="story-description-section" :style="{ opacity: imageLoaded ? 1 : 0 }">
        <div class="story-content">
          <!-- 标题区域 -->
          <div class="story-title-section">
            <div class="story-name">{{ story?.title || 'Untitled' }}</div>
            <div class="story-description" v-if="story.description">
              {{ story.description }}
            </div>
          </div>

          <!-- 标签区域 - StoryCardV2 固定显示标签 -->
          <div class="story-tags-section" v-if="allTags.length > 0">
            <span
              v-for="(tag, index) in allTags"
              :key="index"
              class="story-tag"
              :class="getTagClass(tag)"
            >
              {{ tag }}
            </span>
          </div>
        </div>

        <!-- 作者信息 -->
        <div class="story-author" v-if="story.author">@{{ story.author }}</div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import type { Story } from '@/api/stories'
import { subscribeStory, unsubscribeStory, getStorySubscribeStatus } from '@/api/stories'
import { computed, ref, defineEmits, onMounted, onUnmounted, watch, onBeforeUnmount } from 'vue'
import { Message } from '@/mobile/components/Message'
import { useDebounceFn } from '@vueuse/core'
import { useUserStore } from '@/store'
import { useThemeStore } from '@/store/theme'
import { useStoryStore } from '@/store/story'
import AlarmBlackIcon from '@/assets/icon/alarm-black.svg'
import CheckGrayIcon from '@/assets/icon/check-gray.svg'
import { reportEvent } from '@/utils'
import { ReportEvent } from '@/interface'
import { animate } from 'motion'
import IconFavorited from '@/assets/icon/favorited-pc.svg'
import IconFavorite from '@/assets/icon/favorite-pc.svg'
import { useABTestStore } from '@/store/abtest'
import { trackABTestConversion } from '@/utils/abtest'

const props = defineProps<{
  story?: Story
  isPc?: boolean
  loading?: boolean
}>()

const userStore = useUserStore()
const themeStore = useThemeStore()
const storyStore = useStoryStore()
const abtestStore = useABTestStore()

// StoryCardV2 专门用于设计版本AB测试的B变体，不需要标签显示AB测试

const emit = defineEmits<{
  (e: 'click', story: Story): void
  (e: 'image-loaded'): void
  (e: 'image-error'): void
  (e: 'subscription-change', story: Story): void
  (e: 'need-login'): void
  (e: 'need-email'): void
}>()

const imageLoaded = ref(false)
const hasError = ref(false)
const isSubscribing = ref(false)
const isComponentMounted = ref(false)

// 收藏相关状态
const isFavorited = ref(false)
const isFavoriteLoading = ref(false)

// 轮播相关状态
const currentCarouselIndex = ref(0)
const nextCarouselIndex = ref(0)
const carouselTimer = ref<ReturnType<typeof setInterval> | null>(null)
const isTransitioning = ref(false)
const preloadedImages = ref<Set<string>>(new Set())
const isVisible = ref(true) // 组件是否在视口内，默认可见避免初始化问题
const intersectionObserver = ref<IntersectionObserver | null>(null)

// 轮播图片/视频数组（缓存计算结果）
const carouselUrls = computed(() => {
  // 优先使用 preview_video_url，支持多个URL
  if (props.story?.preview_video_url && props.story.preview_video_url.trim()) {
    // 智能分割URL：只在http/https前分割，避免分割URL参数中的逗号
    const urls = props.story.preview_video_url
      .split(/,(?=https?:\/\/)/) // 只在逗号后跟http://或https://时分割
      .map((url) => url.trim())
      .filter((url) => url.length > 0)

    if (urls.length > 0) {
      return urls
    }
  }

  // 回退到 carousel_image_url
  if (props.story?.carousel_image_url && props.story.carousel_image_url.length > 0) {
    return props.story.carousel_image_url
  }

  // 最后回退到 preview_url
  return props.story?.preview_url
    ? [props.story.preview_url]
    : ['https://static.playshot.ai/static/images/story/default-preview.png']
})

// 性能优化：缓存是否需要轮播
const shouldShowCarousel = computed(() => carouselUrls.value.length > 1)

// 媒体类型检测（缓存结果）
const mediaTypeCache = new Map<string, string>()
const getMediaType = (url: string): string => {
  if (mediaTypeCache.has(url)) {
    return mediaTypeCache.get(url)!
  }
  const type = /\.(mp4|webm|ogg|mov|avi)(\?.*)?$/i.test(url) ? 'video' : 'img'
  mediaTypeCache.set(url, type)
  return type
}

// 媒体属性优化
const getMediaProps = (url: string, index: number) => {
  const isVideo = getMediaType(url) === 'video'
  const isFirst = index === 0

  if (isVideo) {
    return {
      muted: true,
      autoplay: true,
      loop: true,
      playsinline: true,
      preload: 'metadata'
    }
  } else {
    return {
      'v-img-compress': true,
      loading: 'lazy',
      decoding: 'async',
      fetchpriority: isFirst ? 'high' : 'low'
    }
  }
}

// 媒体样式类优化
const getMediaClasses = (index: number) => ({
  current: index === currentCarouselIndex.value,
  next: index === nextCarouselIndex.value && shouldShowCarousel.value,
  transitioning: isTransitioning.value
})

// 媒体内联样式优化
const getMediaStyles = (index: number) => ({
  opacity: index === currentCarouselIndex.value ? 1 : 0,
  display:
    index === currentCarouselIndex.value ||
    (index === nextCarouselIndex.value && isTransitioning.value)
      ? 'block'
      : 'none'
})

// 全局图片缓存，避免重复加载
const globalImageCache = new Set<string>()

// 优化的预加载图片函数
const preloadImage = (url: string, priority: 'high' | 'low' = 'low') => {
  if (globalImageCache.has(url) || /\.(mp4|webm|ogg|mov|avi)(\?.*)?$/i.test(url)) {
    return Promise.resolve()
  }

  return new Promise<void>((resolve, reject) => {
    const img = new Image()

    // 设置加载优先级
    if ('loading' in img) {
      img.loading = priority === 'high' ? 'eager' : 'lazy'
    }

    img.onload = () => {
      globalImageCache.add(url)
      preloadedImages.value.add(url)
      resolve()
    }
    img.onerror = reject
    img.src = url
  })
}

// 节流的预加载函数，避免同时加载太多图片
const throttledPreload = (() => {
  let loading = 0
  const maxConcurrent = 3 // 最多同时加载3张图片
  const queue: Array<() => Promise<void>> = []

  const processQueue = async () => {
    if (loading >= maxConcurrent || queue.length === 0) return

    loading++
    const task = queue.shift()!
    try {
      await task()
    } finally {
      loading--
      processQueue() // 处理下一个任务
    }
  }

  return (url: string, priority: 'high' | 'low' = 'low') => {
    return new Promise<void>((resolve, reject) => {
      queue.push(() => preloadImage(url, priority).then(resolve).catch(reject))
      processQueue()
    })
  }
})()

// 优化的轮播切换动画
const performCarouselTransition = async () => {
  // 性能检查：只有在组件挂载时才执行动画
  if (isTransitioning.value || carouselUrls.value.length <= 1 || !isComponentMounted.value) {
    // console.log(`⏸️ Skipping carousel transition for story ${props.story?.id}:`, {
    //   isTransitioning: isTransitioning.value,
    //   carouselLength: carouselUrls.value.length,
    //   isMounted: isComponentMounted.value
    // })
    return
  }

  isTransitioning.value = true

  // 计算下一张图片索引
  const nextIndex = (currentCarouselIndex.value + 1) % carouselUrls.value.length
  nextCarouselIndex.value = nextIndex

  // 使用节流预加载，避免网络拥塞
  try {
    await throttledPreload(carouselUrls.value[nextIndex], 'high')
  } catch (error) {
    console.warn('Failed to preload next image:', error)
    isTransitioning.value = false
    return
  }

  // 使用 requestIdleCallback 优化性能，在浏览器空闲时执行
  const executeAnimation = () =>
    new Promise<void>((resolve) => {
      const callback = async () => {
        // 获取当前组件内的媒体元素
        const storyCard = document.querySelector(`[data-story-id="${props.story?.id}"]`)
        if (!storyCard) {
          isTransitioning.value = false
          resolve()
          return
        }

        const currentMedia = storyCard.querySelector('.story-media.current') as HTMLElement
        const nextMedia = storyCard.querySelector('.story-media.next') as HTMLElement

        if (currentMedia && nextMedia) {
          // 简化的动画，减少计算量
          const animationDuration = isVisible.value ? 0.8 : 0.3 // 不可见时快速切换

          // 设置初始状态
          nextMedia.style.opacity = '0'
          nextMedia.style.transform = 'scale(1.01) translateX(4px)' // 减少变换幅度

          // 使用更轻量的动画
          await Promise.all([
            animate(
              currentMedia,
              { opacity: [1, 0], scale: [1, 1.02] },
              { duration: animationDuration }
            ),
            animate(
              nextMedia,
              { opacity: [0, 1], scale: [1.01, 1], x: [4, 0] },
              { duration: animationDuration }
            )
          ])

          // 清理样式
          currentMedia.style.transform = ''
          nextMedia.style.transform = ''
        }

        currentCarouselIndex.value = nextIndex
        isTransitioning.value = false
        resolve()
      }

      // 使用 requestIdleCallback 或 setTimeout 作为回退
      if ('requestIdleCallback' in window) {
        requestIdleCallback(callback, { timeout: 1000 })
      } else {
        setTimeout(callback, 0)
      }
    })

  await executeAnimation()
}

// 智能轮播控制函数
const startCarousel = () => {
  if (carouselUrls.value.length <= 1) return

  // 如果已经有定时器在运行，先停止
  stopCarousel()

  // 根据可见性调整轮播间隔
  const interval = isVisible.value ? 5000 : 10000 // 不可见时延长间隔

  carouselTimer.value = setInterval(() => {
    performCarouselTransition()
  }, interval)
}

const stopCarousel = () => {
  if (carouselTimer.value) {
    clearInterval(carouselTimer.value)
    carouselTimer.value = null
  }
}

// 设置可见性观察器
const setupIntersectionObserver = () => {
  if (!('IntersectionObserver' in window)) {
    isVisible.value = true // 不支持时默认可见
    return
  }

  intersectionObserver.value = new IntersectionObserver(
    (entries) => {
      const entry = entries[0]
      const wasVisible = isVisible.value
      isVisible.value = entry.isIntersecting

      // 可见性变化时重新启动轮播
      if (isVisible.value && !wasVisible && carouselUrls.value.length > 1) {
        setTimeout(() => startCarousel(), 100) // 延迟一点避免频繁切换
      } else if (!isVisible.value && wasVisible) {
        stopCarousel()
      }
    },
    {
      threshold: 0.1, // 10% 可见时触发
      rootMargin: '50px' // 提前50px开始预加载
    }
  )
}

// 预加载可见图片
const preloadVisibleImages = async () => {
  if (!isVisible.value || carouselUrls.value.length <= 1) return

  // 优先加载当前图片
  try {
    await throttledPreload(carouselUrls.value[currentCarouselIndex.value], 'high')
  } catch (error) {
    console.warn('Failed to preload current image:', error)
  }

  // 后台预加载下一张图片
  if (carouselUrls.value.length > 1) {
    const nextIndex = (currentCarouselIndex.value + 1) % carouselUrls.value.length
    throttledPreload(carouselUrls.value[nextIndex], 'low').catch(() => {
      // 静默失败，不影响用户体验
    })
  }
}

// 优化的生命周期管理
onMounted(() => {
  isComponentMounted.value = true
  // 初始化收藏状态
  isFavorited.value = props.story?.is_fav || false

  // 设置可见性观察器
  setupIntersectionObserver()

  // 观察当前组件
  const storyCard = document.querySelector(`[data-story-id="${props.story?.id}"]`)
  if (storyCard && intersectionObserver.value) {
    intersectionObserver.value.observe(storyCard)
  }

  // 延迟初始化轮播，避免首屏阻塞
  requestIdleCallback(
    () => {
      if (carouselUrls.value.length > 1) {
        nextCarouselIndex.value = 1
        preloadVisibleImages() // 预加载可见图片

        // 延迟启动轮播，给首屏渲染让路

        setTimeout(() => {
          // console.log(
          //   `🚀 Initial carousel start for story ${props.story?.id}, visible: ${isVisible.value}`
          // )
          startCarousel() // 移除可见性检查，因为初始值已经是true
        }, 1000)
      }
    },
    { timeout: 2000 }
  )

  // 上报AB测试曝光事件
  abtestStore.trackExposure('story_card_design', {
    component: 'StoryCardV2',
    story_id: props.story?.id,
    variant: 'B' // StoryCardV2 对应 B 变体
  })
})

onBeforeUnmount(() => {
  isComponentMounted.value = false
  stopCarousel()

  // 清理观察器
  if (intersectionObserver.value) {
    intersectionObserver.value.disconnect()
    intersectionObserver.value = null
  }

  // 清理缓存
  mediaTypeCache.clear()
  preloadedImages.value.clear()
})

onUnmounted(() => {
  isComponentMounted.value = false
  stopCarousel()

  // 确保清理观察器
  if (intersectionObserver.value) {
    intersectionObserver.value.disconnect()
    intersectionObserver.value = null
  }

  // 确保清理缓存
  mediaTypeCache.clear()
  preloadedImages.value.clear()
})

// 优化的监听器，减少不必要的重新计算
watch(
  carouselUrls,
  (newUrls, oldUrls) => {
    if (!isComponentMounted.value) return

    // 只有在URL数组真正变化时才重新启动
    if (JSON.stringify(newUrls) === JSON.stringify(oldUrls)) return

    stopCarousel()
    currentCarouselIndex.value = 0
    isTransitioning.value = false

    if (newUrls.length > 1 && isVisible.value) {
      nextCarouselIndex.value = 1
      // 延迟启动，避免频繁重启
      setTimeout(() => {
        if (isVisible.value) {
          preloadVisibleImages()
          startCarousel()
        }
      }, 500)
    }
  },
  { immediate: false }
)

// 监听 story 的收藏状态变化
watch(
  () => props.story?.is_fav,
  (newIsFav) => {
    isFavorited.value = newIsFav || false
  },
  { immediate: true }
)

const generatePlaceholderColor = () => {
  // 根据主题返回不同的占位符颜色
  if (themeStore.isDarkTheme) {
    // 暗色主题：使用紫色渐变
    return {
      background: '#140a29',
      backgroundImage: 'linear-gradient(234deg, #1f0038 0%, #140a29 100%)',
      animation: !imageLoaded.value ? 'placeholderShimmer 2s infinite linear' : 'none'
    }
  } else {
    // 亮色主题：使用浅灰色渐变
    return {
      background: '#f5f5f5',
      backgroundImage: 'linear-gradient(234deg, #e0e0e0 0%, #f5f5f5 100%)',
      animation: !imageLoaded.value ? 'placeholderShimmer 2s infinite linear' : 'none'
    }
  }
}

const onImageLoad = () => {
  // 检查组件是否仍然挂载，避免在组件销毁后执行回调
  if (!isComponentMounted.value) return

  // 使用 requestAnimationFrame 优化性能，避免阻塞渲染
  requestAnimationFrame(() => {
    if (!isComponentMounted.value) return
    imageLoaded.value = true
    emit('image-loaded')
  })
}

const onImageError = () => {
  // 检查组件是否仍然挂载
  if (!isComponentMounted.value) return

  hasError.value = true
  emit('image-error')
}

// 优化的点击处理 - 防抖避免重复点击
const handleCardClick = useDebounceFn((event: MouseEvent) => {
  if (!props.story) return

  // 如果故事状态为准备中，点击订阅按钮，则触发订阅状态扭转，点击其他地方，则提示故事coming soon
  const subscriptionButton = (event.target as HTMLElement)?.closest('.subscription-button')
  if (props.story.status === 'preparing') {
    if (subscriptionButton) {
      // 订阅按钮点击事件在按钮上已经绑定了，这里不需要处理
      return
    } else {
      // 获取当前卡片元素
      const card = event.currentTarget as HTMLElement
      // 添加更轻柔的摇头动画
      animate(
        card,
        {
          x: [0, -4, 4, -4, 4, -2, 2, 0],
          rotate: [0, -0.5, 0.5, -0.5, 0.5, -0.3, 0.3, 0]
        },
        {
          duration: 1.2,
          easing: [0.26, 0.09, 0.18, 0.93]
        }
      )
      Message.info('The Story is coming soon, stay tuned')
    }
  } else {
    // 上报AB测试转化事件
    trackABTestConversion('story_card_design', 'story_card_click', 1)
    emit('click', props.story)
  }
}, 200) // 200ms防抖

// 检查登录状态
const checkLoginStatus = () => {
  if (!userStore.isAuthenticated || userStore.isGuest) {
    emit('need-login')
    return true
  }
  return false
}

// 检查是否需要手动输入邮箱
const checkNeedManualEmail = () => {
  return (
    !props.story?.is_subscribed &&
    (!userStore.userInfo?.email || !validateEmail(userStore.userInfo.email))
  )
}

const validateEmail = (email: string) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// 获取用于订阅的邮箱
const getSubscriptionEmail = () => {
  return validateEmail(userStore.userInfo?.email || '') ? userStore.userInfo?.email : ''
}

// 上报订阅事件
const reportSubscriptionEvent = () => {
  if (!props.story) return

  reportEvent(ReportEvent.StoryCardSubscriptionClick, {
    story_id: props.story.id,
    is_subscribed: props.story.is_subscribed,
    is_guest: userStore.userInfo?.role === 'guest',
    user_email: userStore.userInfo?.email
  })
}

// 检查服务器端订阅状态
const checkServerSubscriptionStatus = async () => {
  if (!props.story) return false

  const { data } = await getStorySubscribeStatus(props.story.id)
  const story = data.data as unknown as Story
  if (story.is_subscribed !== props.story.is_subscribed) {
    emit('subscription-change', { ...props.story, is_subscribed: story.is_subscribed })
    return true
  }
  return false
}

// 执行订阅/取消订阅操作
const executeSubscriptionAction = async () => {
  if (!props.story) return

  if (props.story.is_subscribed) {
    await unsubscribeStory(props.story.id)
    Message.success('Unsubscribed successfully')
  } else {
    await subscribeStory(props.story.id, getSubscriptionEmail())
    Message.success("We'll email you when it's live")
  }
  emit('subscription-change', { ...props.story, is_subscribed: !props.story.is_subscribed })
}

// 确保最小loading时间
const ensureMinLoadingTime = async (startTime: number) => {
  const endTime = Date.now()
  const loadingTime = endTime - startTime
  if (loadingTime < 300) {
    await new Promise((resolve) => setTimeout(resolve, 300 - loadingTime))
  }
}

const handleSubscriptionToggle = useDebounceFn(async () => {
  if (isSubscribing.value) return

  try {
    // 1. 所有操作都需要先登录
    if (checkLoginStatus()) {
      return
    }
    // 2. 检查是否需要手动输入邮箱（已登录但邮箱格式不正确时）
    if (checkNeedManualEmail()) {
      emit('need-email')
      return
    }

    const startTime = Date.now()
    isSubscribing.value = true

    // 3. 上报事件
    reportSubscriptionEvent()

    // 4. 检查服务器端状态
    const statusMismatch = await checkServerSubscriptionStatus()
    if (statusMismatch) return

    // 5. 执行订阅/取消订阅操作
    await executeSubscriptionAction()

    // 6. 确保最小loading时间
    await ensureMinLoadingTime(startTime)
  } catch (error) {
    Message.error('Failed to update subscription')
  } finally {
    isSubscribing.value = false
  }
}, 300)

// Computed property to check if current user is an admin
const isAdmin = computed(() => {
  return userStore.userInfo?.role === 'admin'
})

// 优化标签计算 - 缓存结果避免重复计算
const storyTags = computed(() => props.story?.tags || [])
const storyCategories = computed(() => props.story?.categories || [])

// 合并所有标签（tags + categories）- tags在前
const allTags = computed(() => [...storyTags.value, ...storyCategories.value])

// 标签类型映射缓存
const tagTypeMap = computed(() => {
  const map = new Map<string, string>()
  storyTags.value.forEach((tag) => map.set(tag, 'tags-tag'))
  storyCategories.value.forEach((tag) => map.set(tag, 'category-tag'))
  return map
})

// 优化的标签类型判断
const getTagClass = (tag: string) => tagTypeMap.value.get(tag) || 'category-tag'

// 收藏功能
const toggleFavorite = async () => {
  if (!props.story) return

  // 防止重复点击
  if (isFavoriteLoading.value) return

  // 检查登录状态
  if (!userStore.isAuthenticated || userStore.isGuest) {
    emit('need-login')
    return
  }

  try {
    // 设置本地加载状态
    isFavoriteLoading.value = true

    // 上报事件
    reportEvent(ReportEvent.ClickStoryIntroFavorite, {
      storyId: props.story.id,
      isFavorited: isFavorited.value
    })

    // 乐观更新：立即更新本地状态
    const previousState = isFavorited.value
    isFavorited.value = !previousState

    // 设置当前故事到 storyStore
    storyStore.currentStory = props.story

    // 设置超时处理，防止接口响应过慢或失败导致loading状态一直为true
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Request timeout')), 10000) // 10秒超时
    })

    // 调用 storyStore 的 toggleFavorite 方法，同时设置超时
    const setSuccess = await Promise.race([storyStore.toggleFavorite(), timeoutPromise])

    // 如果API调用失败，恢复之前的状态
    if (!setSuccess) {
      isFavorited.value = previousState
      Message.error('Failed to update favorite status')
    } else {
      // 如果成功，发出事件通知父组件更新数据
      emit('subscription-change', { ...props.story, is_fav: !previousState })
      // 显示成功提示
      if (!previousState) {
        Message.success('Added to favorites')
      } else {
        Message.success('Removed from favorites')
      }
    }
  } catch (error) {
    console.error('Toggle favorite failed:', error)
    // 发生错误时，恢复之前的状态
    const previousState = !isFavorited.value
    isFavorited.value = previousState

    // 显示错误提示
    if (error.message === 'Request timeout') {
      Message.error('Request timeout, please try again')
    } else {
      Message.error('Failed to update favorite status')
    }
  } finally {
    // 确保加载状态被重置
    isFavoriteLoading.value = false
  }
}
</script>

<style lang="less" scoped>
@keyframes placeholderShimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

// 优化动画性能
@media (prefers-reduced-motion: reduce) {
  .story-card {
    transition: none;

    &:hover {
      transform: none;
    }
  }

  @keyframes placeholderShimmer {
    0%,
    100% {
      background-position: 0 0;
    }
  }
}

@keyframes skeletonPulse {
  0% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 0.3;
  }
}

@keyframes skeletonShimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes breathing {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.breathing-animation {
  animation: breathing 2s ease-in-out infinite;
}

.story-card {
  position: relative;
  border-radius: 16px;
  overflow: hidden;
  background: var(--bg-card); // 使用主题变量
  width: 100%;
  aspect-ratio: 9/16; // 移动端保持与原版相同的9:16比例
  display: flex;
  flex-direction: column; // 垂直布局：图片在上，描述在下
  transform: translateZ(0); // 强制硬件加速
  cursor: pointer;
  // 优化过渡效果，减少重排重绘
  transition:
    transform 0.2s cubic-bezier(0.4, 0, 0.2, 1),
    background-color 0.3s ease; // 添加背景色过渡
  // 性能优化
  will-change: transform; // 提示浏览器优化
  contain: layout style paint; // 限制重绘范围

  // 亮色主题下使用白色背景
  body.light-theme & {
    background: #ffffff;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  }

  // 暗色主题下使用深色背景
  body:not(.light-theme) & {
    background: var(--bg-card);
    box-shadow: 0 2px 12px var(--shadow-color);
  }

  // 只在非加载状态下启用 hover 效果
  &:hover:not(.is-loading) {
    transform: translateZ(0) translateY(-2px); // 减少位移距离

    // 使用 CSS 变量控制子元素动画，避免重复计算
    --hover-scale: 1.02; // 减少缩放比例

    .story-button img {
      transform: scale(var(--hover-scale, 1));
    }

    .story-image {
      .story-media.current {
        transform: scale(var(--hover-scale, 1));
      }
    }
  }

  // 骨架屏状态样式
  &.is-loading {
    cursor: wait;
    pointer-events: none;

    .skeleton-mode {
      .skeleton-shimmer {
        background-size: 200% 100%;
        animation: skeletonShimmer 1.5s infinite;

        // 亮色主题骨架屏
        body.light-theme & {
          background: linear-gradient(
            90deg,
            rgba(0, 0, 0, 0.05) 0%,
            rgba(0, 0, 0, 0.1) 50%,
            rgba(0, 0, 0, 0.05) 100%
          );
        }

        // 暗色主题骨架屏
        body:not(.light-theme) & {
          background: linear-gradient(
            90deg,
            rgba(255, 255, 255, 0.1) 0%,
            rgba(255, 255, 255, 0.2) 50%,
            rgba(255, 255, 255, 0.1) 100%
          );
        }
      }

      .skeleton-element {
        animation: skeletonPulse 1.5s infinite ease-in-out;

        // 亮色主题适配
        body.light-theme & {
          background: rgba(0, 0, 0, 0.08);
        }

        // 暗色主题适配
        body:not(.light-theme) & {
          background: rgba(255, 255, 255, 0.1);
        }
      }

      .skeleton-text {
        border-radius: 4px;
        animation: skeletonPulse 1.5s infinite ease-in-out;

        // 亮色主题适配
        body.light-theme & {
          background: rgba(0, 0, 0, 0.1);
        }

        // 暗色主题适配
        body:not(.light-theme) & {
          background: rgba(255, 255, 255, 0.15);
        }
      }

      .skeleton-badge {
        animation: skeletonPulse 1.5s infinite ease-in-out;
        animation-delay: 0.2s;

        // 亮色主题适配
        body.light-theme & {
          background: rgba(202, 147, 242, 0.2);
        }

        // 暗色主题适配
        body:not(.light-theme) & {
          background: rgba(202, 147, 242, 0.3);
        }
      }

      .skeleton-overlay {
        .story-name {
          height: 20px;
          width: 70%;
          margin-bottom: 8px;
        }

        .story-count {
          height: 16px;
          width: 60px;
          margin-bottom: 12px;
          animation-delay: 0.3s;
        }

        .story-button-placeholder {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          margin-top: 10px;
          animation-delay: 0.5s;
        }
      }

      .skeleton-subscription {
        .subscription-button-skeleton {
          width: 120px;
          height: 30px;
          border-radius: 16px;
          animation-delay: 0.4s;
        }
      }
    }
  }

  // PC端样式
  &.is-pc {
    aspect-ratio: 2/3; // PC端保持与原版相同的2:3比例
    border-radius: 20px;
    background: var(--bg-card); // 使用主题变量
    box-shadow: 0 4px 20px var(--shadow-color);

    // 亮色主题适配
    body.light-theme & {
      background: #ffffff;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    // 暗色主题适配
    body:not(.light-theme) & {
      background: var(--bg-card);
      box-shadow: 0 4px 20px var(--shadow-color);
    }

    &:hover {
      transform: translateY(-8px);

      // 亮色主题悬停效果
      body.light-theme & {
        box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
      }

      // 暗色主题悬停效果
      body:not(.light-theme) & {
        box-shadow: 0 12px 24px rgba(0, 0, 0, 0.4);
      }
    }

    .story-image {
      border-radius: 20px 20px 0 0; // PC端图片区域只有上方圆角
      overflow: hidden;

      .story-media {
        transition: transform 0.5s ease;

        &:not(.transitioning) {
          transition: transform 0.5s ease;
        }
      }

      &.image-loaded {
        .story-media.current {
          transform: scale(1.02); // 默认就有轻微放大效果
        }
      }
    }

    .story-favorite-button {
      top: 16px;
      right: 16px;
      width: 40px;
      height: 40px;

      .heart-icon svg {
        width: 22px;
        height: 22px;
      }
    }

    .story-badge {
      padding: 4px 12px !important;
      font-size: 14px !important;
      font-weight: 600;
      border-radius: 0 0 12px 0;

      &.coming-soon {
        background: #daff96;
        color: #1f0038;
      }

      &.admin-only {
        border-radius: 0 0 0 12px;
        padding: 6px 14px;
      }
    }

    .subscription-button {
      min-width: 160px;
      height: 40px;
      font-size: 15px;
      border-radius: 20px;
      padding: 0 20px;

      &-icon {
        width: 24px;
        height: 24px;

        :deep(svg) {
          width: 18px;
          height: 18px;
        }
      }
    }
  }

  .story-image {
    position: relative; // 改为相对定位，不再覆盖整个卡片
    width: 100%;
    flex: 1; // 占据剩余空间，让描述区域有固定高度
    border-radius: 16px 16px 0 0; // 只有上方圆角
    overflow: hidden;

    &.loading {
      .story-badge {
        opacity: 0;
        visibility: hidden;
      }
    }

    .image-placeholder {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      transition: opacity 0.3s ease;
      background-size: 200% 100%;
      z-index: 1;
      pointer-events: none;
    }

    .story-media {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: opacity 0.3s ease;
      transform: translateZ(0);
      backface-visibility: hidden;
      -webkit-backface-visibility: hidden;
      z-index: 2; // 保持在较低层级，不覆盖UI元素

      // 当前显示的媒体
      &.current {
        z-index: 2;
      }

      // 下一张媒体（用于过渡）
      &.next {
        z-index: 1; // 在当前图片下方
      }

      // 过渡状态下的样式
      &.transitioning {
        transition: none; // 禁用CSS过渡，使用JS动画
      }
    }

    &.image-loaded {
      .image-placeholder {
        opacity: 0;
      }

      .story-badge {
        opacity: 1;
        visibility: visible;
        transition:
          opacity 0.3s ease,
          visibility 0.3s ease;
      }
    }

    // 右上角收藏按钮
    .story-favorite-button {
      position: absolute;
      top: 4px;
      right: 4px;
      width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10; // 提高z-index确保在媒体元素之上
      transition: all 0.3s ease;
      will-change: opacity, transform;
      cursor: pointer;
      border-radius: 50%;

      // 主题适配
      // 亮色主题
      body.light-theme & {
        color: var(--text-primary);
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(8px);
      }

      // 暗色主题
      body:not(.light-theme) & {
        color: white;
        background: rgba(0, 0, 0, 0.3);
        backdrop-filter: blur(8px);
      }

      &:hover:not(.loading) {
        transform: scale(1.1);

        // 亮色主题悬停效果
        body.light-theme & {
          background: rgba(255, 255, 255, 1);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        // 暗色主题悬停效果
        body:not(.light-theme) & {
          background: rgba(0, 0, 0, 0.5);
        }
      }

      &.loading {
        cursor: not-allowed;
        opacity: 0.7;

        &:hover {
          transform: none;
        }
      }

      .heart-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        transition: color 0.3s ease;

        svg {
          width: 26px;
          height: 26px;
        }
      }

      .loading-spinner {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        animation: spin 0.8s linear infinite;
        margin: 0;
        padding: 0;
        display: block;
        flex-shrink: 0;

        // 亮色主题
        body.light-theme & {
          border: 2px solid rgba(0, 0, 0, 0.3);
          border-top-color: var(--accent-color);
        }

        // 暗色主题
        body:not(.light-theme) & {
          border: 2px solid rgba(255, 255, 255, 0.3);
          border-top-color: white;
        }
      }
    }

    .story-badge {
      position: absolute;
      top: 0;
      left: 0;
      border-radius: 0px 0px 10px 0px;
      background: var(--accent-color);
      display: inline-flex;
      padding: 4px 10px;
      justify-content: center;
      align-items: center;
      gap: 10px;
      color: #fff;
      font-size: 11px;
      font-weight: 600;
      z-index: 10; // 提高z-index确保在媒体元素之上
      transition:
        opacity 0.5s ease,
        background-color 0.3s ease;
      will-change: opacity;

      &.coming-soon {
        // 亮色主题
        body.light-theme & {
          background: var(--accent-color);
          color: #ffffff;
        }

        // 暗色主题 - 保持原有的绿色样式
        body:not(.light-theme) & {
          background: #daff96;
          color: #1f0038;
        }
      }

      &.admin-only {
        left: auto;
        right: 0;
        top: 0;
        background: #ff6b6b;
        border-radius: 0px 0px 0px 10px;
        padding: 2px 8px;
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 10px;
        color: #ffffff; // 确保文字可读性

        .admin-icon {
          font-size: 12px;
        }
      }
    }
  }

  .subscription-status {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    width: 80%;
    gap: 12px;
    z-index: 15; // 最高z-index确保订阅按钮在最上层
    pointer-events: auto;
    transition: opacity 0.5s ease;
    will-change: opacity;

    .subscription-button {
      width: fit-content;
      min-width: 120px;
      height: 30px;
      border-radius: 18px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;
      font-size: 12px;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;
      padding: 8px 12px;
      -webkit-tap-highlight-color: transparent;

      // 默认状态 - 未订阅
      // 亮色主题
      body.light-theme & {
        background: var(--accent-color);
        border: 1px solid var(--accent-color);
        color: #ffffff;
      }

      // 暗色主题 - 保持原有的绿色按钮样式
      body:not(.light-theme) & {
        background: #daff96;
        border: 1px solid #daff96;
        color: #1f0038;
      }

      &.subscribed {
        // 亮色主题
        body.light-theme & {
          background: var(--bg-tertiary);
          border-color: var(--border-color);
          color: var(--text-secondary);
        }

        // 暗色主题
        body:not(.light-theme) & {
          background: rgba(0, 0, 0, 0.5);
          border-color: transparent;
          color: rgba(255, 255, 255, 0.8);
        }
      }

      &.loading {
        cursor: not-allowed;
        opacity: 0.7;
      }

      &-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 20px;
        height: 20px;
        flex-shrink: 0;

        :deep(svg) {
          width: 14px;
          height: 14px;
        }
      }

      &-text {
        flex: 1;
        line-height: 1;
        text-align: left;
        padding-right: 8px;
        white-space: nowrap;
      }

      .loading-spinner {
        width: 14px;
        height: 14px;
        border: 2px solid transparent;
        border-top-color: currentColor;
        border-radius: 50%;
        animation: spin 0.8s linear infinite;
      }
    }
  }

  // 描述区域样式 - V2版本新增
  .story-description-section {
    position: relative;
    background: var(--bg-secondary); // 使用主题变量
    padding: 12px 16px 16px;
    border-radius: 0 0 16px 16px; // 只有下方圆角
    flex-shrink: 0; // 不允许收缩，保持固定高度
    min-height: 120px; // 移动端最小高度
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    transition:
      opacity 0.5s ease,
      background-color 0.3s ease; // 添加背景色过渡
    will-change: opacity;

    // 亮色主题适配
    body.light-theme & {
      background: #ffffff;
      color: var(--text-primary);
    }

    // 暗色主题适配
    body:not(.light-theme) & {
      background: var(--bg-secondary); // 使用主题的次要背景色
      color: var(--text-primary); // 使用主题的主要文字色
    }

    .story-content {
      display: flex;
      flex-direction: column;
      gap: 8px;
      flex: 1;
    }

    .story-title-section {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .story-name {
      font-size: 14px; // 移动端默认大小
      font-weight: 600;
      line-height: 1.2;
      color: inherit; // 继承父元素颜色，支持主题切换
      // // 最多显示1行
      display: -webkit-box;
      -webkit-line-clamp: 1;
      line-clamp: 1;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      transition: color 0.3s ease;
    }

    .story-description {
      font-size: 12px; // 移动端默认大小
      font-weight: 400;
      line-height: 1.3;
      // 最多显示2行
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      transition: color 0.3s ease;
      // 亮色主题
      body.light-theme & {
        color: var(--text-secondary);
      }

      // 暗色主题
      body:not(.light-theme) & {
        color: var(--text-secondary); // 使用主题的次要文字色
      }
    }

    .story-tags-section {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;
      overflow: hidden;
      // StoryCardV2 默认显示所有标签，不限制行数
      max-height: calc(2 * (10px + 4px + 6px)); // 2行 * (字体大小 + padding上下 + 行间距)
    }

    .story-tag {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 2px 6px; // 移动端默认padding
      border-radius: 12px;
      font-family: 'Work Sans', sans-serif;
      font-size: 10px; // 移动端默认大小
      font-weight: 400;
      line-height: 1.2;
      white-space: nowrap;
      flex-shrink: 0;
      transition:
        background-color 0.3s ease,
        color 0.3s ease;

      // tags标签样式
      &.tags-tag {
        // 亮色主题
        body.light-theme & {
          background: var(--accent-bg);
          color: var(--accent-color);
        }

        // 暗色主题
        body:not(.light-theme) & {
          background: var(--accent-bg);
          color: var(--accent-color);
        }
      }

      // categories标签样式
      &.category-tag {
        // 亮色主题
        body.light-theme & {
          background: var(--bg-tertiary);
          color: var(--text-secondary);
        }

        // 暗色主题
        body:not(.light-theme) & {
          background: var(--bg-tertiary);
          color: var(--text-secondary);
        }
      }
    }

    .story-author {
      font-family: 'Work Sans', sans-serif;
      font-size: 11px; // 移动端默认大小
      font-weight: 400;
      line-height: 1.2;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-top: 4px;
      transition: color 0.3s ease;

      // 亮色主题
      body.light-theme & {
        color: var(--text-tertiary);
      }

      // 暗色主题
      body:not(.light-theme) & {
        color: var(--text-tertiary);
      }
    }
  }

  // PC端样式适配
  &.is-pc {
    .story-description-section {
      padding: 16px 20px 20px;
      min-height: 140px; // PC端更大的最小高度

      .story-name {
        font-size: 16px;
      }

      .story-description {
        font-size: 14px;
      }

      .story-tag {
        padding: 3px 8px;
        font-size: 11px;
      }

      .story-author {
        font-size: 12px;
      }
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
