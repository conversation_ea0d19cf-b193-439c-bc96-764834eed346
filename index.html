<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />

  <!-- 基础SEO -->
  <title><%- title %></title>
  <meta name="description"
    content="<%- description || 'Experience immersive AI-powered interactive stories and characters. Create your own adventures with advanced AI technology.' %>" />
  <meta name="keywords" content="Spicy AI, CrushOn AI, AI characters, Character AI NSFW, AI sex chat" />
  <meta name="author" content="<%- title %>" />
  <meta name="robots" content="index, follow" />
  <meta name="ahrefs-site-verification" content="6b0165b511297476f45f0a4feb2e0f0a3caef6586dcc6a75a666bf74bcef2f65">
  <!-- 视口和设备优化 -->
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover" />
  <meta name="theme-color" content="#180430" id="theme-color-meta" />
  <meta name="color-scheme" content="dark light" />

  <!-- 图标和PWA -->
  <link rel="icon" type="image/x-icon" href="<%- iconUrl %>" />
  <link rel="apple-touch-icon" href="<%- iconUrl %>" />
  <!-- PWA manifest will be injected by vite-plugin-pwa -->

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website" />
  <meta property="og:url" content="<%- title === 'ReelPlay' ? 'https://reelplay.ai/' : 'https://playshot.ai/' %>" />
  <meta property="og:title" content="<%- title %> - AI Interactive Stories" />
  <meta property="og:description"
    content="<%- description || 'Experience immersive AI-powered interactive stories and characters' %>" />
  <meta property="og:site_name" content="<%- title %>" />

  <!-- 性能优化 -->
  <link rel="dns-prefetch" href="<%- title === 'ReelPlay' ? '//cdn.magiclight.ai' : '//static.playshot.ai' %>" />
  <link rel="dns-prefetch" href="<%- title === 'ReelPlay' ? '//api.reelplay.ai' : '//api.playshot.ai' %>" />
  <link rel="dns-prefetch" href="//storage.googleapis.com" />
  <link rel="preconnect" href="<%- title === 'ReelPlay' ? 'https://cdn.magiclight.ai' : 'https://static.playshot.ai' %>"
    crossorigin />
  <!-- 优化字体加载策略 - 使用 Google Fonts 减少对 LCP 的影响 -->
  <!-- preconnect 已经包含 DNS 查询，无需重复 dns-prefetch -->
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

  <!-- 关键资源预加载 -->
  <link rel="modulepreload" href="/src/main.ts" />
  <!-- 关键字体预加载 - 使用 Google Fonts 优化 LCP -->
  <link rel="preload" href="https://fonts.googleapis.com/css2?family=Work+Sans:wght@400;500;600&display=swap" as="style"
    onload="this.onload=null;this.rel='stylesheet'" />
  <noscript>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Work+Sans:wght@400;500;600&display=swap" />
  </noscript>

  <!-- 关键 CSS 内联 -->
  <style>
    /* 关键首屏样式 */
    * {
      box-sizing: border-box;
    }

    html,
    body {
      margin: 0;
      padding: 0;
      height: 100%;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: #fff;
      overflow-x: hidden;
    }

    /* 字体加载状态优化 */
    .fonts-loading body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    .fonts-loaded body {
      font-family: 'Work Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    #app {
      min-height: 100vh;
      display: flex;
      flex-direction: column;
    }

    /* 首屏加载优化 */
    .stories-page {
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .stories-page.loaded {
      opacity: 1;
    }

    /* PC Layout 始终可见，不受首屏加载影响 */
    .pc-layout {
      opacity: 1 !important;
    }

    /* 骨架屏样式 */
    .skeleton {
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: loading 1.5s infinite;
    }

    @keyframes loading {
      0% {
        background-position: 200% 0;
      }

      100% {
        background-position: -200% 0;
      }
    }
  </style>

  <!-- 结构化数据 -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "<%- title %>",
    "description": "<%- description || 'AI-powered interactive stories and characters platform' %>",
    "url": "<%- title === 'ReelPlay' ? 'https://reelplay.ai' : 'https://playshot.ai' %>",
    "applicationCategory": "Entertainment",
    "operatingSystem": "Any",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "author": {
      "@type": "Organization",
      "name": "<%- title %>",
      "url": "<%- title === 'ReelPlay' ? 'https://reelplay.ai' : 'https://playshot.ai' %>"
    }
  }
  </script>
</head>

<body>
  <!-- 无障碍跳转链接 -->
  <!-- <a href="#main-content" class="skip-link">Skip to main content</a> -->

  <!-- 主应用容器 -->
  <div id="app" role="application" aria-label="<%- title %> Application"></div>

  <!-- 优化的加载指示器 -->
  <div id="loading-indicator" aria-hidden="true"
    style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; z-index: 9999; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; flex-direction: column; align-items: center; justify-content: center; transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1); backdrop-filter: blur(10px);">

    <!-- 加载动画 -->
    <div
      style="width: 50px; height: 50px; border: 3px solid rgba(255,255,255,0.2); border-top: 3px solid #fff; border-radius: 50%; animation: smoothSpin 1.2s cubic-bezier(0.4, 0, 0.2, 1) infinite; margin-bottom: 24px; filter: drop-shadow(0 4px 8px rgba(0,0,0,0.1));">
    </div>

    <!-- 加载文本 -->
    <div id="loading-text"
      style="color: #fff; font-size: 18px; font-weight: 600; margin-bottom: 20px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; text-shadow: 0 2px 4px rgba(0,0,0,0.1);">
      Loading...
    </div>



    <!-- 进度条容器 -->
    <div
      style="width: 240px; height: 6px; background: rgba(255,255,255,0.15); border-radius: 3px; overflow: hidden; box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);">
      <div id="loading-progress"
        style="width: 0%; height: 100%; background: linear-gradient(90deg, #fff 0%, rgba(255,255,255,0.8) 100%); border-radius: 3px; transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1); box-shadow: 0 0 10px rgba(255,255,255,0.3);">
      </div>
    </div>

    <!-- 进度百分比 -->
    <div id="loading-percentage"
      style="color: rgba(255,255,255,0.9); font-size: 14px; margin-top: 12px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; font-weight: 500;">
      0%
    </div>


  </div>

  <script type="module" src="/src/main.ts"></script>



  <!-- 浏览器兼容性检查 -->
  <script type="text/javascript">
    (function () {
      // 检查是否为现代浏览器（支持ES6+特性）
      if (!window.Promise || !window.fetch || !Array.prototype.includes) {
        alert('Your browser is not supported. Please use a modern browser like Chrome, Firefox, Safari, or Edge.')
        return
      }

      // 进度条更新函数将由 smooth-loading.ts 提供
      // 这里只保留一个占位符，确保不会出错
      window.updateLoadingProgress = window.updateLoadingProgress || function (progress) {
        console.log('Loading progress:', progress + '%')
      }

      // 隐藏加载指示器
      window.addEventListener('load', function () {
        setTimeout(function () {
          var loader = document.getElementById('loading-indicator')
          if (loader) {
            loader.style.opacity = '0'
            setTimeout(function () {
              loader.style.display = 'none'
            }, 300)
          }
        }, 500)
      })
    })()
  </script>

  <!-- CSS动画 -->
  <style>
    @keyframes smoothSpin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    @keyframes pulse {

      0%,
      100% {
        opacity: 1;
      }

      50% {
        opacity: 0.7;
      }
    }

    @keyframes slideUp {
      from {
        transform: translateY(20px);
        opacity: 0;
      }

      to {
        transform: translateY(0);
        opacity: 1;
      }
    }

    /* 加载指示器动画增强 */
    #loading-indicator {
      animation: slideUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    }

    #loading-text {
      animation: pulse 2s ease-in-out infinite;
    }



    .skip-link {
      position: absolute;
      top: -40px;
      left: 6px;
      background: #ca93f2;
      color: white;
      padding: 8px;
      text-decoration: none;
      border-radius: 4px;
      z-index: 10000;
      transition: top 0.3s;
    }

    .skip-link:focus {
      top: 6px;
    }
  </style>
</body>

</html>