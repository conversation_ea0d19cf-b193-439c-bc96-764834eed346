#!/bin/bash

# 股票代码数组
STOCKS=("RDDT" "TSLA" "APP" "HOOD")

# 上一次查询的价格记忆
declare -A LAST_PRICE

# ANSI颜色
RESET="\033[0m"
RED="\033[31m"
GREEN="\033[32m"
YELLOW="\033[33;1m"   # 高亮黄
BOLD="\033[1m"
BOLD_RED="\033[31;1m"  # 加粗红色
BOLD_YELLOW="\033[33;1m"  # 加粗黄色

# 大波动阈值（比如 5%）
THRESHOLD=5

while true; do
  echo ""
  echo -e "🕒 更新时间: $(date '+%F %T')"
  echo "==================================================================================="
  printf "📌 股票\t⏰ 时间\t\t💵 价格\t📉 涨跌\t📈 涨跌%%\t🟢 买价\t🔴 卖价\t📊 成交量\n"
  echo "-----------------------------------------------------------------------------------"

  for SYMBOL in "${STOCKS[@]}"; do
    RESPONSE=$(curl -s "https://api.nasdaq.com/api/quote/$SYMBOL/info?assetclass=stocks&lang=zh" \
      -H 'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64)' \
      -H 'Accept: application/json, text/plain, */*' \
      -H 'Accept-Language: zh-CN,zh;q=0.9' \
      -H 'Origin: https://www.nasdaq.com' \
      -H 'Referer: https://www.nasdaq.com/market-activity/stocks/'"$SYMBOL" \
      -H 'Connection: keep-alive' \
      --compressed)

    echo "$RESPONSE" | jq -r --arg SYMBOL "$SYMBOL" '
      .data.primaryData as $p | 
      [ $SYMBOL, $p.lastTradeTimestamp, $p.lastSalePrice, $p.netChange, $p.percentageChange, $p.bidPrice, $p.askPrice, $p.volume ] | @tsv
    '
  done | while IFS=$'\t' read -r symbol time price change percent bid ask volume; do
    # 去掉 $ 符号转成 float
    numeric_price=$(echo "$price" | tr -d '$,')

    # 如果上次价格不存在或者是 0，跳过大波动计算
    prev_price=${LAST_PRICE[$symbol]}
    if [[ -z "$prev_price" || "$prev_price" == "0" || "$numeric_price" == "0" ]]; then
      price_display="$price"
    else
      # 计算波动百分比
      price_diff=$(echo "$numeric_price - $prev_price" | bc)
      percent_change=$(echo "scale=2; ($price_diff / $prev_price) * 100" | bc)

      # 判断是否为大波动
      # if (( $(echo "$percent_change > $THRESHOLD" | bc -l) )); then
      #   echo -e "${BOLD_YELLOW}⚠️ 警告：$symbol 价格波动超过 ${THRESHOLD}%！ 当前波动：$percent_change%${RESET}"
      # fi

      # 判断涨跌
      diff=$(echo "$numeric_price > $prev_price" | bc)
      if [[ $diff -eq 1 ]]; then
        price_display="${YELLOW}${BOLD}$price${RESET}"
      else
        diff_down=$(echo "$numeric_price < $prev_price" | bc)
        if [[ $diff_down -eq 1 ]]; then
          price_display="${RED}$price${RESET}"
        else
          price_display="$price"
        fi
      fi
    fi

    # 更新价格记录
    LAST_PRICE[$symbol]=$numeric_price

    # 打印表格，注意这里用 echo -e 来解析颜色
    echo -e "$symbol\t$time\t$price_display\t$change\t$percent\t$bid\t$ask\t$volume"
  done

  echo "==================================================================================="
  sleep 300
done