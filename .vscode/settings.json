{"vue3snippets.enable-compile-vue-file-on-did-save-code": false, "cSpell.words": ["ipbible"], "editor.tabSize": 2, "editor.detectIndentation": false, "typescript.tsdk": "node_modules/typescript/lib", "[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "prettier.useEditorConfig": false, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.fixAll": "always"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "workbench.editor.editorActionsLocation": "hidden", "[html]": {"editor.defaultFormatter": "vscode.html-language-features"}}