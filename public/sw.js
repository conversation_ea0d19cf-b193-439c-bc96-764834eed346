// No-op Service Worker
// This Service Worker will immediately install and activate,
// replacing any existing problematic Service Worker

console.log('No-op Service Worker starting...')

self.addEventListener('install', () => {
  // Skip over the "waiting" lifecycle state, to ensure that our
  // new service worker is activated immediately, even if there's
  // another tab open controlled by our older service worker code.
  console.log('No-op Service Worker installing...')
  self.skipWaiting()
})

self.addEventListener('activate', () => {
  console.log('No-op Service Worker activating...')

  // Optional: Get a list of all the current open windows/tabs under
  // our service worker's control, and force them to reload.
  // This can "unbreak" any open windows/tabs as soon as the new
  // service worker activates, rather than users having to manually reload.
  self.clients
    .matchAll({
      type: 'window'
    })
    .then((windowClients) => {
      windowClients.forEach((windowClient) => {
        windowClient.navigate(windowClient.url)
      })
    })

  // Take control of all clients immediately
  self.clients.claim()

  console.log('No-op Service Worker activated')
})

console.log('No-op Service Worker loaded successfully')
