#!/bin/bash

if [ "$1" == "upgrade" ]; then
  rm -rf dist && mkdir dist && cp upgrade.html ./dist/index.html
else
  config="./config/vite.config.$1.ts"
  envConfig=".env.$1"
  # 检查配置文件是否存在
  if [ ! -f "$config" ]; then
    echo "配置文件不存在: $config"
    exit 1
  fi

  # 检查环境配置文件是否存在
  if [ ! -f "$envConfig" ]; then
    echo "环境配置文件不存在: $envConfig"
    exit 1
  fi

  npx vue-tsc --noEmit && npx vite build --config $config
fi
